import { Injectable, Logger } from '@nestjs/common';
import { RepoInfo } from './repo.service';
import * as path from 'path';

/**
 * 仓库状态服务 - 管理当前选中的仓库状态
 * 避免服务间的循环依赖
 */
@Injectable()
export class RepoStateService {
  private readonly logger = new Logger(RepoStateService.name);
  private selectedRepoName: string | null = null;
  private selectedRepoInfo: RepoInfo | null = null;

  /**
   * 设置当前选中的仓库
   */
  setSelectedRepository(repoName: string, repoInfo: RepoInfo): void {
    this.selectedRepoName = repoName;
    this.selectedRepoInfo = repoInfo;
    this.logger.log(`设置选中仓库: ${repoName}`);
  }

  /**
   * 获取当前选中的仓库名称
   */
  getSelectedRepositoryName(): string | null {
    return this.selectedRepoName;
  }

  /**
   * 获取当前选中的仓库信息
   */
  getSelectedRepositoryInfo(): RepoInfo | null {
    return this.selectedRepoInfo;
  }

  /**
   * 获取当前选中仓库的工作区路径
   */
  getSelectedRepositoryWorkspaceUri(): string | null {
    if (!this.selectedRepoName) {
      return null;
    }
    return path.resolve(process.cwd(), `./repos/${this.selectedRepoName}`);
  }

  /**
   * 检查是否已选择仓库
   */
  hasSelectedRepository(): boolean {
    return this.selectedRepoName !== null;
  }

  /**
   * 清除选中的仓库
   */
  clearSelectedRepository(): void {
    this.selectedRepoName = null;
    this.selectedRepoInfo = null;
    this.logger.log('清除选中仓库');
  }

  /**
   * 获取仓库信息用于与kwaipilot子进程通信
   */
  getRepoInfoForCommunication() {
    if (!this.selectedRepoInfo) {
      return {
        git_url: "",
        dir_path: "",
        commit: "",
        branch: "",
      };
    }

    const workspaceUri = this.getSelectedRepositoryWorkspaceUri();
    return {
      git_url: this.selectedRepoInfo.gitUrl || "",
      dir_path: workspaceUri || "",
      commit: this.selectedRepoInfo.currentCommit || "",
      branch: this.selectedRepoInfo.currentBranch || "",
    };
  }
}
