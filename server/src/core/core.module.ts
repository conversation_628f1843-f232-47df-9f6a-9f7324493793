import { Global, Module } from '@nestjs/common';
import { LocalService } from './local-service';
import { AgentService } from './agent';
import { ComposerService } from './composer';
import { ProjectService } from './project.service';
import { RepoService } from './repo.service';
import { RepoStateService } from './repo-state.service';
import { RepoController } from './repo.controller';
import { EventBusService } from './event-bus';
import { DatabaseService } from './database.service';
import { ConfigService } from '../config/config.service';

/**
 * 核心模块 - 提供全局共享的服务
 * 使用 @Global() 装饰器确保服务在整个应用中是单例
 */
@Global()
@Module({
  controllers: [RepoController],
  providers: [
    ConfigService,
    ProjectService,
    RepoService,
    RepoStateService,
    DatabaseService,
    LocalService,
    AgentService,
    ComposerService,
    EventBusService,
  ],
  exports: [
    ConfigService,
    ProjectService,
    RepoService,
    RepoStateService,
    DatabaseService,
    LocalService,
    AgentService,
    ComposerService,
    EventBusService,
  ],
})
export class CoreModule {}
