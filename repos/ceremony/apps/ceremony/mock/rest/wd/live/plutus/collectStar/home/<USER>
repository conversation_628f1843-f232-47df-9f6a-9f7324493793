export default {
    result: 1,
    'host-name': 'public-xm-c-staging-kce-node109.idchb1az1.hb1.kwaidc.com',
    ktraceId: 'EKrUkYDw1piOrAEYmAMggqiY-LEyKKCctusN',
    data: {
        totalStar: 245,
        currentStar: {
            1: 1,
            2: 0,
        },
        taskList: [
            {
                type: 99,
                status: 1,
                currentScore: 15,
                targetScore: 200,
                currentScoreFormat: '0',
                targetScoreFormat: '10',
                rewardDesc: '1${starType_1} + 400流量曝光 + 道具卡红包',
                scoreUnitName: '盛典值',
                nonRevTaskCompleteTitle: null,
                titlePrefix: '累计',
                titleSuffix: '盛典值',
                taskEndTimeMs: 1731392522794,
                nextTaskCreateTimeMs: 0,
                rewardStarCount: 1,
                rewardStarType: 1,
                rightTopTip: {
                    text: '再挑战4轮可集',
                    starType: 2,
                    appendStarIcon: true,
                },
                taskInTeamInfo: {
                    title: '',
                    subTitle: '',
                    authorAvatar: '',
                    teammateAvatar: '',
                    authorComplete: true,
                    teammateComplete: false,
                    showDisbandButton: false,
                    disbandTip: {},
                    authorId: 0,
                    teammateId: 0,
                },
                taskOutTeamInfo: {
                    title: '组队挑战 额外得星',
                    subTitle:
                        '组队后每日双方各完成1次盛典值挑战额外得1${starType_1}',
                    invitationCode: '0ce843fa3dd6',
                },
            },
            {
                type: 2,
                status: 1,
                currentScore: 0,
                targetScore: 260,
                currentScoreFormat: '0',
                targetScoreFormat: '260',
                rewardDesc: '1${starType_1}',
                scoreUnitName: '点赞',
                nonRevTaskCompleteTitle: null,
                titlePrefix: '再得',
                titleSuffix: '点赞',
                taskEndTimeMs: 0,
                nextTaskCreateTimeMs: 0,
                rewardStarCount: 1,
                rewardStarType: 1,
                rightTopTip: null,
                taskInTeamInfo: null,
                taskOutTeamInfo: null,
            },
            {
                type: 3,
                status: 1,
                currentScore: 0,
                targetScore: 100,
                currentScoreFormat: '0',
                targetScoreFormat: '100',
                rewardDesc: '1${starType_1}',
                scoreUnitName: '评论',
                nonRevTaskCompleteTitle: null,
                titlePrefix: '再得',
                titleSuffix: '评论',
                taskEndTimeMs: 0,
                nextTaskCreateTimeMs: 0,
                rewardStarCount: 1,
                rewardStarType: 1,
                rightTopTip: null,
                taskInTeamInfo: null,
                taskOutTeamInfo: null,
            },
            {
                type: 1,
                status: 2,
                currentScore: 55,
                targetScore: 55,
                currentScoreFormat: '55',
                targetScoreFormat: '55',
                rewardDesc: '挑战成功',
                scoreUnitName: '直播时长',
                nonRevTaskCompleteTitle: '开播时长任务',
                titlePrefix: '再开播',
                titleSuffix: '分钟',
                taskEndTimeMs: 0,
                nextTaskCreateTimeMs: 0,
                rewardStarCount: 1,
                rewardStarType: 1,
                rightTopTip: null,
                taskInTeamInfo: null,
                taskOutTeamInfo: null,
            },
        ],
        rewardList: [
            {
                rewardId: 1,
                rewardType: 1,
                rewardName: '年度之星奖杯',
                rewardDesc: '',
                rewardIcon: '',
                worthStarNum: 288,
                rewardStarType: 0,
                status: 1,
            },
            {
                rewardId: 4,
                rewardType: 4,
                rewardName: '1k涨粉包',
                rewardDesc: '',
                rewardIcon:
                    'https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveYearCeremony2024/collectStar/reward_icon.png',
                worthStarNum: 100,
                rewardStarType: 2,
                status: 1,
            },
            {
                rewardId: 3,
                rewardType: 3,
                rewardName: '1k进房流量',
                rewardDesc: '',
                rewardIcon:
                    'https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveYearCeremony2024/collectStar/reward_icon.png',
                worthStarNum: 40,
                rewardStarType: 2,
                status: 1,
            },
            {
                rewardId: 5,
                rewardType: 5,
                rewardName: '专属礼物与装扮',
                rewardDesc: '',
                rewardIcon:
                    'https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveYearCeremony2024/collectStar/reward_icon.png',
                worthStarNum: 50,
                rewardStarType: 2,
                status: 1,
            },
            {
                rewardId: 6,
                rewardType: 6,
                rewardName: '3%加成卡',
                rewardDesc: '',
                rewardIcon:
                    'https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveYearCeremony2024/collectStar/reward_icon.png',
                worthStarNum: 70,
                rewardStarType: 2,
                status: 1,
            },
            {
                rewardId: 6,
                rewardType: 6,
                rewardName: '3%加成卡',
                rewardDesc: '',
                rewardIcon:
                    'https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveYearCeremony2024/collectStar/reward_icon.png',
                worthStarNum: 70,
                rewardStarType: 2,
                status: 1,
            },
        ],
        serverTimestamp: 1731392312336,
        authorInfo: {
            authorId: 77779999,
            authorName: '闻人功',
            authorHeadURL:
                'http://kcdn.staging.kuaishou.com/uhead/AB/2023/11/22/19/BMjAyMzExMjIxOTM3MTlfNzc3Nzk5OTlfMl9oZDI1XzI2Mg==_s.jpg',
        },
    },
};
