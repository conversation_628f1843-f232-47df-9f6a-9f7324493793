export default {
  "result": 1,
  "data": {
      "subTitle": "获得以下奖励，送积分卡给主播可增加热度值",
    //   "awards": [
    //       {
    //           "id": 1,
    //           "name": "快手12周年礼包",
    //           "count": 1,
    //           "icon": "https://static.yximgs.com/udata/pkg/livePlutusProduct/liveAutumnCeremony23/luckyBag/award_icon/1.png",
    //           "displayId": 1
    //       },
    //       {
    //           "id": 2,
    //           "name": "一分积分卡",
    //           "count": 1,
    //           "icon": "https://static.yximgs.com/udata/pkg/livePlutusProduct/liveAutumnCeremony23/luckyBag/award_icon/2.png",
    //           "displayId": 2
    //       },
    //       {
    //           "id": 3,
    //           "name": "一百积分卡",
    //           "count": 2,
    //           "icon": "https://static.yximgs.com/udata/pkg/livePlutusProduct/liveAutumnCeremony23/luckyBag/award_icon/3.png",
    //           "displayId": 3
    //       },
    //       {
    //           "id": 4,
    //           "name": "典藏头像框",
    //           "count": 3,
    //           "icon": "https://static.yximgs.com/udata/pkg/livePlutusProduct/liveAutumnCeremony23/luckyBag/award_icon/4.png",
    //           "displayId": 4
    //       },
    //       {
    //           "id": 8,
    //           "name": "iPhone14Profsf",
    //           "count": 1,
    //           "icon": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveAutumnCeremony23/luckyBag/iphone0906.png",
    //           "displayId": 8
    //       },
    //       {
    //           "id": 9,
    //           "name": "一万积分卡",
    //           "count": 2,
    //           "icon": "https://static.yximgs.com/udata/pkg/livePlutusProduct/liveAutumnCeremony23/luckyBag/award_icon/8.png",
    //           "displayId": 9
    //       },
    //       {
    //           "id": 8,
    //           "name": "iPhone14Pro",
    //           "count": 1,
    //           "icon": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveAutumnCeremony23/luckyBag/iphone0906.png",
    //           "displayId": 8
    //       },
    //       {
    //           "id": 9,
    //           "name": "一万积分卡",
    //           "count": 2,
    //           "icon": "https://static.yximgs.com/udata/pkg/livePlutusProduct/liveAutumnCeremony23/luckyBag/award_icon/8.png",
    //           "displayId": 9
    //       }
    //   ],

    "awards": [
        {
            "id": 6,
            "type": 12, // 奖励类型 2:背包礼物 3:荣誉出口 4:代币 5:实物 6:主站万能卡 7:快币 8:道具卡 9:现金 10:暴富卡 11:表情包 12:本地生活商品卡劵
            "name": "商品券",
            "count": 1,
            "icon": "https://p3-live.wskwai.com/kos/nlav12127/home/<USER>/kuaishou-nodejs/project/upload-files/files/_E8_A1_A8_E6_83_85_207.5ef465449cc4e3ae.png",
            "displayId": 9,
            "extraInfo": {
              "unit":"¥", // 单位
              "amount":"20", // 金额
              "amountTip":"满100元可用",
              "name":"电商购物卷", // 单位
              "desc":["除指定商品外全平台","有效期至2025.10.01 23:59"], // 单位
              "jumpUrl": "xxx" // 去使用跳转地址
            }
        }
    ],
      "needJumpBackPack": false,
      "needJumpWallet": false,
      "needFillAddress": false,
      "needSendEmoticon": false,
      "enableShowEmoticonTab": false
  }
}
