export default {
    data: {
        records: [
            {
                timestamp: 1729153324669,
                ruleId: 1,
                desc: '幸运奖池获得',
                items: [
                    {
                        id: 9,
                        name: '盛典表情包',
                        count: 1,
                        icon: 'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/emoticons/liveAutumnCeremony24/5-2.png',
                        displayId: 6,
                    },
                    {
                        id: 8,
                        name: '积分卡',
                        count: 1,
                        icon: 'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/emoticons/liveAutumnCeremony24/5-2.png',
                        displayId: 6,
                    },
                    {
                        id: 7,
                        name: '积分卡',
                        count: 1,
                        icon: 'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/emoticons/liveAutumnCeremony24/5-2.png',
                        displayId: 6,
                    },
                    {
                        id: 6,
                        name: '积分卡',
                        count: 1,
                        icon: 'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/emoticons/liveAutumnCeremony24/5-2.png',
                        displayId: 6,
                    },
                    {
                        id: 5,
                        name: '积分卡',
                        count: 1,
                        icon: 'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/emoticons/liveAutumnCeremony24/5-2.png',
                        displayId: 6,
                    },
                    {
                        id: 4,
                        name: '积分卡',
                        count: 1,
                        icon: 'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/emoticons/liveAutumnCeremony24/5-2.png',
                        displayId: 6,
                    },
                    {
                        id: 3,
                        name: '积分卡',
                        count: 1,
                        icon: 'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/emoticons/liveAutumnCeremony24/5-2.png',
                        displayId: 6,
                    },
                ],
            },
            {
                timestamp: 1729153324669,
                ruleId: 2,
                desc: '幸运奖池获得',
                items: [
                    {
                        id: 9,
                        name: '盛典表情包',
                        count: 1,
                        icon: 'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/emoticons/liveAutumnCeremony24/5-2.png',
                        displayId: 6,
                    },
                ],
            },
        ],
        needFillAddress: false,
    },
    result: 1,
};
