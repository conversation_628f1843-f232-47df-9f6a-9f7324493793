export default {
  "result": 1,
  "data": {
    "authorInfo": {
        "headUrl":"sss",
        "userName": "zhangs",
        "userId" : 111
    },
    "stage": 0, // 0-进行中 1-休赛 2-挂榜
    "notLiving": false, // true时，结算期间不轮询服务端
    "showIncentiveDetail": true, // true-需要外漏奖励，false-不外漏
    "statsInfo": {
        "dailyRound": "1",
        "dailyTrafficWorth": "105万",
        "dailyTrafficUnit": "流量奖励",
        "dailyRedPackWorth": "1000",
        "dailyRedPackUnit": "代币+现金(分)",
        "roundTrafficWorth": "104万",
        "roundTrafficUnit": "流量奖励",
        "roundRedPackWorth": "2000",
        "roundRedPackUnit": "代币+现金(分)"
    },
    "tabKwaiLink": "kwailive://giftpanel?tab=NormalGift&selectedGiftId=12081",
    "currentLevel": 1, // 1-4 当前关卡
    "currManual":true, // 当前是否手动领取方式 即 新版 false 则是旧
    "currFinishSeq":1, // 新版的第x轮
    "currFinishManualChallenge":true, // 当前已经完成挑战
    "manualChoicePopUpEnableDisplay":true, // 是否展示弹窗开关
    "manualChoicePopUpEnablePopUp":true, // 弹窗开关展示开
    "levelInfo": [
        {
            "level": 1,
            // "currentFailCount": 1, // 当前失败次数
            // "maxFailCount": 3, // 最多失败次数
            "status": 1, // 0-未开始 1-进行中 2-成功 3-失败
            "currentScore": 100, // 进度条
            "targetScore": 500, // 进度条,
            "currentScoreH5": "100", // 进度条
            "targetScoreH5": "500", // 进度条
            "gapScore": 400, // 进度条
            "gapScoreH5": "9999", // 进度条
            "serviceTimestamp": 1746862680000, // 服务端当前时间戳 status=1时使用
            "taskStartTimestamp": 1695807796904, // 任务开始时间戳 status=1时使用
            "taskEndTimestamp": 1747124069000, // 任务结束时间戳 status=1时使用
            "critTipTimestamp": 1695807796904, // 暴击提示开始时间戳 status=1时使用
            "critStartTimestamp": 1695807796904, // 暴击开始时间戳 status=1时使用
            "critEndTimestamp": 1695807796904, // 暴击结束时间戳 status=1时使用
            "countDownTimestamp": 1695807796904, // 任务倒计时提示时间戳 status=1时使用
            "nextTaskStartTimestamp": 1746878796000, // 下一级任务开始时间 status=2和3时使用
            "clearing": true, // 倒计时结束，但还没扭转状态
            "incentiveRedPackWorth": "20", // 2万
            "incentiveRedPackUnit": "代币", // 金币+现金(分)
            "winCountText": "连胜1关 挑战中",
            "incentiveTrafficWorth": "10万", // 流量奖励
            "incentiveTrafficUnit": "流量", // 单位
            "incentiveMultTimes": 2, // 倍数 2倍
            "incentiveTrafficWorthTotal": "20万",
            "eggNeedCount": 3, // 还需要的彩蛋数 正在执行的任务用
            "manualChallenge":true, // 是否挑战困难模式
            "finishDesc": "流量下发中",  // 任务完成的提示文案
            "notFinishDesc": "下一关开启" // 任务失败的提示文案
        },
        {
            "level": 2,
            // "currentFailCount": 1, // 当前失败次数
            // "maxFailCount": 3, // 最多失败次数
            "status": 0, // 0-未开始 1-进行中 2-成功 3-失败
            "currentScore": 100, // 进度条
            "targetScore": 500, // 进度条
            "currentScoreH5": "100", // 进度条
            "targetScoreH5": "500", // 进度条
            "gapScore": 400, // 进度条
            "gapScoreH5": "400", // 进度条
            "serviceTimestamp": 1695807796904, // 服务端当前时间戳 status=1时使用
            "taskStartTimestamp": 1695807796904, // 任务开始时间戳 status=1时使用
            "taskEndTimestamp": 1695807796904, // 任务结束时间戳 status=1时使用
            "critTipTimestamp": 1695807796904, // 暴击提示开始时间戳 status=1时使用
            "critStartTimestamp": 1695807796904, // 暴击开始时间戳 status=1时使用
            "critEndTimestamp": 1695807796904, // 暴击结束时间戳 status=1时使用
            "countDownTimestamp": 1695807796904, // 任务倒计时提示时间戳 status=1时使用
            "nextTaskStartTimestamp": 1695807796904, // 下一级任务开始时间 status=2和3时使用
            "clearing": true, // 倒计时结束，但还没扭转状态
            "incentiveRedPackWorth": "20", // 2万
            "incentiveRedPackUnit": "代币", // 金币+现金(分)
            "winCountText": "连胜1关 挑战中",
            "incentiveTrafficWorth": "10万", // 流量奖励
            "incentiveTrafficUnit": "流量", // 单位
            "incentiveMultTimes": 2, // 倍数 2倍
            "eggNeedCount": 3, // 还需要的彩蛋数 正在执行的任务用
            "finishDesc": "流量下发中",  // 任务完成的提示文案
            "notFinishDesc": "下一关开启" // 任务失败的提示文案
        },
        {
            "level": 3,
            // "currentFailCount": 1, // 当前失败次数
            // "maxFailCount": 3, // 最多失败次数
            "status": 0, // 0-未开始 1-进行中 2-成功 3-失败
            "currentScore": 100, // 进度条
            "targetScore": 500, // 进度条
            "currentScoreH5": "100", // 进度条
            "targetScoreH5": "500", // 进度条
            "gapScore": 400, // 进度条
            "gapScoreH5": "400", // 进度条
            "serviceTimestamp": 1695807796904, // 服务端当前时间戳 status=1时使用
            "taskStartTimestamp": 1695807796904, // 任务开始时间戳 status=1时使用
            "taskEndTimestamp": 1695807796904, // 任务结束时间戳 status=1时使用
            "critTipTimestamp": 1695807796904, // 暴击提示开始时间戳 status=1时使用
            "critStartTimestamp": 1695807796904, // 暴击开始时间戳 status=1时使用
            "critEndTimestamp": 1695807796904, // 暴击结束时间戳 status=1时使用
            "countDownTimestamp": 1695807796904, // 任务倒计时提示时间戳 status=1时使用
            "nextTaskStartTimestamp": 1695807796904, // 下一级任务开始时间 status=2和3时使用
            "clearing": true, // 倒计时结束，但还没扭转状态
            "incentiveRedPackWorth": "20", // 2万
            "incentiveRedPackUnit": "代币", // 金币+现金(分)
            "winCountText": "连胜1关 挑战中",
            "incentiveTrafficWorth": "10万", // 流量奖励
            "incentiveTrafficUnit": "流量", // 单位
            "incentiveMultTimes": 2, // 倍数 2倍
            "eggNeedCount": 3, // 还需要的彩蛋数 正在执行的任务用
            "finishDesc": "流量下发中",  // 任务完成的提示文案
            "notFinishDesc": "下一关开启" // 任务失败的提示文案
        },
        {
            "level": 4,
            // "currentFailCount": 1, // 当前失败次数
            // "maxFailCount": 3, // 最多失败次数
            "status": 0, // 0-未开始 1-进行中 2-成功 3-失败
            "currentScore": 100, // 进度条
            "targetScore": 500, // 进度条
            "currentScoreH5": "100", // 进度条
            "targetScoreH5": "500", // 进度条
            "gapScore": 400, // 进度条
            "gapScoreH5": "400", // 进度条
            "serviceTimestamp": 1695807796904, // 服务端当前时间戳 status=1时使用
            "taskStartTimestamp": 1695807796904, // 任务开始时间戳 status=1时使用
            "taskEndTimestamp": 1695807796904, // 任务结束时间戳 status=1时使用
            "critTipTimestamp": 1695807796904, // 暴击提示开始时间戳 status=1时使用
            "critStartTimestamp": 1695807796904, // 暴击开始时间戳 status=1时使用
            "critEndTimestamp": 1695807796904, // 暴击结束时间戳 status=1时使用
            "countDownTimestamp": 1695807796904, // 任务倒计时提示时间戳 status=1时使用
            "nextTaskStartTimestamp": 1695807796904, // 下一级任务开始时间 status=2和3时使用
            "clearing": true, // 倒计时结束，但还没扭转状态
            "incentiveRedPackWorth": "20", // 2万
            "incentiveRedPackUnit": "代币", // 金币+现金(分)
            "winCountText": "连胜1关 挑战中",
            "incentiveTrafficWorth": "10万", // 流量奖励
            "incentiveTrafficUnit": "流量", // 单位
            "incentiveMultTimes": 2, // 倍数 2倍
            "eggNeedCount": 3, // 还需要的彩蛋数 正在执行的任务用
            "finishDesc": "流量下发中",  // 任务完成的提示文案
            "notFinishDesc": "下一关开启" // 任务失败的提示文案
        }
    ],
    "dataPanel": [
        { "name": "曝光量", "value": "70万" }, // 曝光量
        { "name": "进房数", "value": "1万" }, // 进房数
        { "name": "涨粉数", "value": "1000" } // 涨粉数
    ]
  }
}
