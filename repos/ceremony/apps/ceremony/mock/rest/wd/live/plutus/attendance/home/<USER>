
export default {
    "result": 1,
    "data": {
        //涨粉任务（新增）view
        "incrFansTask": {
            "status": 2, //1-未开始 2-进行中 3-当日已达上限 4-活动周期内已达上限 5-代表所有主播解锁涨粉包达到预算
            "title": "完成每日任务打卡，可累计盛典值得涨粉包",
            "subTitle": "已累计盛典值", //2、3状态文案是：距解锁还需盛典值 4: 夏季之星 你最闪耀
            "subTitleScore": 1000,
            "statusDesc": "未开启",
            "bottomIcon": "https://p5-live.a.yximgs.com/kos/nlav12706/2025-summer/start/start-fans-bottom-info.780f7ee0c0576cb6.png",
            //进度条
            "currentLevel": 2,
            "curScore": 2950,
            "totalScore": 10000,
            "segmentList": [
                {
                    "level": 1,
                    "incrFansAward": "20万",
                    "targetScore": 1000,
                    "targetScoreDesc": "1000",
                    "unit": "盛典值",
                    "status": 1 //0 未满 ,1已满
                },
                {
                    "level": 2,
                    "incrFansAward": "20万",
                    "targetScore": 3000,
                    "targetScoreDesc": "3000",
                    "unit": "盛典值",
                    "status": 0 //0 未满 ,1已满
                },
                {
                    "level": 3,
                    "incrFansAward": "20万",
                    "targetScore": 6000,
                    "targetScoreDesc": "6000",
                    "unit": "盛典值",
                    "status": 0 //0 未满 ,1已满
                },
                {
                    "level": 4,
                    "incrFansAward": "20万",
                    "targetScore": 10000,
                    "targetScoreDesc": "1w",
                    "unit": "盛典值",
                    "status": 0 //0 未满 ,1已满
                }
            ]
        },
        //打卡
        "attendanceTask": {
            "allCheckInTaskEnd": false,
            "statisticsInfo": {
                "challengeSuccess": false, //整个活动是否打卡挑战成功
                "taskCompleteNum": 10, //当前打卡的天数
                "taskRemainingNum": 3, //距离活动打卡挑战成功剩余打卡天数
                "taskTotalNum": 24, //总打卡天数
                "todayRepairRemainingNum": 0, //当天剩余可补签的次数
                "todayTotalRepairNum": 10 //当天总共可补签的次数
            },
            "checkInTaskInfo": {
                "date": "2024-06-01",
                "complete": false, //当日的打卡任务是否完成
                "title": "打卡任务待完成",
                "ruleViews": [
                    {
                        "ruleId": 4,
                        "desc": "观众评论任务",
                        "simpleRuleName": "评论",
                        "currentScore": 10, //当前子任务的进度值
                        "currentScoreStr": "0",
                        "targetScore": 10, //当前子任务的目标值
                        "targetScoreStr": "10",
                        "gapScore": 10,
                        "gapScoreStr": "10",
                        "unit": "条",
                        "finished": 0 //0 未完成， 1 完成
                    },
                    {
                        "ruleId": 4,
                        "desc": "观众评论任务",
                        "simpleRuleName": "评论",
                        "currentScore": 0, //当前子任务的进度值
                        "currentScoreStr": "0",
                        "targetScore": 10, //当前子任务的目标值
                        "targetScoreStr": "10",
                        "gapScore": 10,
                        "gapScoreStr": "10",
                        "unit": "条",
                        "finished": 0 //0 未完成， 1 完成
                    },
                    {
                        "ruleId": 4,
                        "desc": "观众评论任务",
                        "simpleRuleName": "评论",
                        "currentScore": 0, //当前子任务的进度值
                        "currentScoreStr": "0",
                        "targetScore": 10, //当前子任务的目标值
                        "targetScoreStr": "10",
                        "gapScore": 10,
                        "gapScoreStr": "10",
                        "unit": "条",
                        "finished": 0 //0 未完成， 1 完成
                    }
                ],
                "desc": "完成以下任务，即可成功打卡今日挑战～",
                "ruleFinishNum": 0, //子任务完成的个数
                "ruleTotalNum": 4 //子任务总个数
            },
            "dateInfo": {
                "repairInfo": {
                    "desc": "活动进行${param}天才可进行补签哦",
                    "param": 10,
                    "ruleDesc": "快去补签其他的天数吧～",
                    "status": 1 //0:不可开启补签, 1: 可以补签
                },
                "dateTaskInfos": [
                    {"date": "2025-05-01", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-02", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-03", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-04", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-05", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-06", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-07", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-08", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-09", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-10", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-11", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-12", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-13", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-14", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-15", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-16", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-17", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-18", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-19", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-20", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-21", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-22", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-23", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-24", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-25", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-26", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-27", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-28", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-29", "desc": "星光赛", "status": 2},
                    {"date": "2025-05-30", "desc": "星光赛", "status": 0},
                    {"date": "2025-05-31", "desc": "星光赛", "status": 2}
                ]
            },
        },
        "serverTime": 1746695876555
    }
}