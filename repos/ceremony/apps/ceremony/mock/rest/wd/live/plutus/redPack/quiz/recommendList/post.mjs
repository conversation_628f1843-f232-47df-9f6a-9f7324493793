export default {
    result: 1,
    'host-name': 'public-fk-c-staging-kce-node92.idczw.hb1.kwaidc.com',
    ktraceId: 'EJiAgIDAsM2OrAEY-gMg0Kmj7O0vKLWA5IcL',
    data: {
        redPackList: [
            {
                liveStreamId: 'z3W_S92oKNY',
                redPackGrabTime: +new Date() + 10000000000000000000000000000000000000000000,
                redPackId: 'aerawae',
                authorInfo: {
                    headUrl:
                        'http://p2.a.yximgs.com/uhead/AB/2022/08/17/01/BMjAyMjA4MTcwMTU3MzFfMTkyNDgxNzkxM18xX2hkNjAxXzY1Ng==_s.jpg',
                    userName: '7哥（搬开闻）',
                    userId: 1924817913,
                    liveStreamId: 'z3W_S92oKNY',
                },
                audienceInfo: {
                    headUrl:
                        'http://tx2.a.kwimgs.com/uhead/AB/2022/08/02/14/BMjAyMjA4MDIxNDQ5MTNfMjc2NTg4NTE5Nl8yX2hkMjE1XzkxNg==_s.jpg',
                    userName: '搞笑女·（李極光）',
                    userId: 2765885196,
                    liveStreamId: null,
                },
                following: false,
                redPackViewType: 2,
                hasCash: true,
                redPackCashFen: 100000,
                redPackSourceType: 2,
                redPackText: '万能卡',
            },
            {
                liveStreamId: 'ZRh30JA4fpM',
                redPackGrabTime: +new Date() + 1000000000000000000000000000000,
                redPackId: 'edfgdfg',
                authorInfo: {
                    headUrl:
                        'http://p4.a.yximgs.com/uhead/AB/2020/09/25/00/BMjAyMDA5MjUwMDI1MjJfNzU5MTEyNDY1XzFfaGQxMDZfNjUz_s.jpg',
                    userName: '丽姐77177',
                    userId: 759112465,
                    liveStreamId: 'ZRh30JA4fpM',
                },
                audienceInfo: {
                    headUrl:
                        'http://tx2.a.kwimgs.com/uhead/AB/2022/10/09/00/BMjAyMjEwMDkwMDMxMjZfMjc1Nzc0MjM3Nl8xX2hkNjY3XzEy_s.jpg',
                    userName: '刻骨銘心',
                    userId: 2757742376,
                    liveStreamId: null,
                },
                following: false,
                redPackViewType: 9,
                hasCash: true,
                redPackCashFen: 4000,
                redPackSourceType: 1,
                redPackText: '暴富卡',
            },
            {
                liveStreamId: 'ozFfQyLDGpo',
                redPackGrabTime: +new Date() + 100000000000000000000000000,
                redPackId: 'wwagh',
                authorInfo: {
                    headUrl:
                        'http://p5.a.yximgs.com/uhead/AB/2022/10/13/14/BMjAyMjEwMTMxNDQ1MTlfMTEyNzU0MDk3MV8xX2hkNDdfNTg5_s.jpg',
                    userName: '✨大江文玩红串',
                    userId: 1127540971,
                    liveStreamId: 'ozFfQyLDGpo',
                },
                audienceInfo: {
                    headUrl:
                        'http://p2.a.yximgs.com/uhead/AB/2022/10/30/20/BMjAyMjEwMzAyMDA5MDZfMTgxODY4NzQzNF8xX2hkMjk1XzQ5Nw==_s.jpg',
                    userName: '✨马小妮（玩帮）',
                    userId: 1818687434,
                    liveStreamId: null,
                },
                following: false,
                redPackViewType: 1,
                hasCash: true,
                redPackCashFen: 1000,
                redPackSourceType: 1,
            }
        ],
    },
};
