export default {
    result: 1,
    data: {
        balance: 345395,
        taskItemViewList: [
            {
                typeKey: 'searchKeyWord',
                name: '完成搜索任务',
                desc: '每日去搜索，金币+10',
                rulePageLink: null,
                iconUrl:
                    'https://w1.kskwai.com/kos/nlav12127/home/<USER>/kuaishou-nodejs/project/upload-files/files/image_618.9290cf71dff4b508.png',
                finishCount: 0,
                needCount: 10,
                status: 1,
                kwaiLink:
                    'kwai://search?&source=ACT_renwu_gashapon&backRecommend=false&keyword=%E7%9B%B4%E6%92%AD%E6%B4%BB%E5%8A%A8%E6%97%A5%E5%8E%86',
                buttonText: '去搜索',
                expandMap: {
                    keyWord: '直播活动日历',
                },
                receiveTask: true,
            },
            {
                typeKey: 'dailyLogin',
                name: '每日签到',
                desc: '每日首次打开活动，金币+10',
                rulePageLink: null,
                iconUrl:
                    'https://w1.kskwai.com/kos/nlav12127/home/<USER>/kuaishou-nodejs/project/upload-files/files/_E7_99_BB_E5_BD_95_954.e628c08df9a380c0.png',
                finishCount: 4,
                needCount: 10,
                status: 1,
                kwaiLink: '',
                buttonText: '去登录',
                expandMap: {},
                receiveTask: false,
            },
            {
                typeKey: 'sendGift',
                name: '送盛典礼物',
                desc: '成功赠送盛典礼物，金币+20',
                rulePageLink: null,
                iconUrl:
                    'https://w1.kskwai.com/kos/nlav12127/home/<USER>/kuaishou-nodejs/project/upload-files/files/_E7_A4_BC_E7_89_A9_883.53b48d805bd808a5.png',
                finishCount: 0,
                needCount: 3,
                status: 1,
                kwaiLink:
                    'kwailive://giftpanel?tab=NormalGift&selectedGiftId=13690',
                buttonText: '去送礼',
                expandMap: {
                    giftIds: [
                        12726, 14094, 14380, 14368, 12241, 13765, 9, 10366,
                        12725,
                    ],
                },
                receiveTask: true,
            },
            {
                typeKey: 'joinFansGroup',
                name: '加入粉丝团',
                desc: '加入1名主播粉丝团，金币+20',
                rulePageLink: null,
                iconUrl:
                    'https://w1.kskwai.com/kos/nlav12127/home/<USER>/kuaishou-nodejs/project/upload-files/files/_E7_B2_89_E4_B8_9D_E5_9B_A2_798.f5f0296d96999063.png',
                finishCount: 0,
                needCount: 1,
                status: 1,
                kwaiLink: 'kwailive://fansgroup?source=5',
                buttonText: '去加入',
                expandMap: {},
                receiveTask: true,
            },
            {
                typeKey: 'sendPopularityGift',
                name: '送付费人气票',
                desc: '成功赠送付费人气票，金币+20',
                rulePageLink: null,
                iconUrl:
                    'https://w1.kskwai.com/kos/nlav12127/home/<USER>/kuaishou-nodejs/project/upload-files/files/_E5_BF_AB_E5_B8_81_615.f84556a25105ce4d.png',
                finishCount: 0,
                needCount: 3,
                status: 1,
                kwaiLink:
                    'kwailive://giftpanel?tab=FansGroupGift&selectedGiftId=10371',
                buttonText: '去送礼',
                expandMap: {},
                receiveTask: true,
            },
            {
                typeKey: 'recharge',
                name: '充值快币',
                desc: '完成快币充值，金币+30',
                rulePageLink: null,
                iconUrl:
                    'https://w1.kskwai.com/kos/nlav12127/home/<USER>/kuaishou-nodejs/project/upload-files/files/_E5_85_85_E5_80_BC_512.553f789d1fd860c9.png',
                finishCount: 0,
                needCount: 3,
                status: 1,
                kwaiLink: 'kwailive://recharge?',
                buttonText: '去完成',
                expandMap: {},
                receiveTask: false,
            },
            {
                typeKey: 'h5Follow',
                name: '认识新主播',
                desc: '关注1名推荐主播，金币+10',
                rulePageLink: null,
                iconUrl:
                    'https://w1.kskwai.com/kos/nlav12127/home/<USER>/kuaishou-nodejs/project/upload-files/files/_E5_85_B3_E6_B3_A8_226.bb9aa9f4faf0ec40.png',
                finishCount: 0,
                needCount: 20,
                status: 1,
                kwaiLink:
                    'kwailive://followuser?userid=<userid>&isfollow=true&followsource=171',
                buttonText: '去关注',
                expandMap: {},
                receiveTask: false,
            },
            {
                typeKey: 'watchVideo',
                name: '观看视频作品',
                desc: '成功观看视频，金币+10',
                rulePageLink: null,
                iconUrl:
                    'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveSummerCeremony24/task/watchVideoTask.png',
                finishCount: 0,
                needCount: 50,
                status: 1,
                kwaiLink: '',
                buttonText: '去观看',
                expandMap: {
                    photoId: 1940974,
                    watchDuration: 10,
                },
                receiveTask: false,
            },
            {
                typeKey: 'reservation',
                name: '预约盛典直播',
                rulePageLink: null,
                iconUrl:
                    'https://p4-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveSummerCeremony24/task/watchVideoTask.png',
                finishCount: 0,
                needCount: 1,
                status: 2,
                kwaiLink: '',
                buttonText: '去预约',
                expandMap: {
                    photoId: 1940974,
                    watchDuration: 10,
                },
                receiveTask: false,
            },
        ],
    },
};
