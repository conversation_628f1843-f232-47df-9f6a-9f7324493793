export default {
    "result": 1,
    "message": "success",
    "data": {
        "data": {
            "laneList": [
                {
                    "laneName": "王者赛区",
                    "rankListId": 31809644,
                    "rankStartTime": 1740465420000,
                    "rankEndTime": 1742884620000
                },
                {
                    "laneName": "星耀赛区",
                    "rankListId": 31809658,
                    "rankStartTime": 1740465420000,
                    "rankEndTime": 1742884620000
                },
                {
                    "laneName": "钻石赛区",
                    "rankListId": 31809677,
                    "rankStartTime": 1740465420000,
                    "rankEndTime": 1742884620000
                }
            ],
            "currOrgInfo": {
                "orgName": "小陈的测试公会09",
                "orgHead": [
                    {
                        "cdn": "blobstore-nginx.staging.kuaishou.com",
                        "url": "http://blobstore-nginx.staging.kuaishou.com/bs2/adminBlock/live_mcn_settled_37f0aa11-a17a-4b11-ab17-044873b9d0d7_1695020158770_257.png?x-oss-process=image/resize,m_lfit,w_200,h_200/format,jpg"
                    }
                ]
            },
            "competitionList": [
                {
                    "competitionName": "区县赛",
                    "activityAlias": "liveSpringCeremony2025qa20_county",
                    "state": 1,
                    "authorCount": 5
                },
                {
                    "competitionName": "区县赛",
                    "activityAlias": "liveSpringCeremony2025qa20_county",
                    "state": 2,
                    "authorCount": 5
                },
                {
                    "competitionName": "区县赛",
                    "activityAlias": "liveSpringCeremony2025qa20_county",
                    "state": 2,
                    "authorCount": 5
                }
            ],
            "displayLaneViewList": [
                {
                    "anchorId": "3459831_31523998",
                    "classifyId": 3459831,
                    "pinyin": null,
                    "rankAliasName": "liveSpringCeremony2025qa20_cpPoint",
                    "scheduleId": 31523998,
                    "rankId": 31809644,
                    "order": 1,
                    "startTime": 1740465420000,
                    "endTime": 1742884620000,
                    "displayName": "王者赛区"
                },
                {
                    "anchorId": "3459831_31524014",
                    "classifyId": 3459831,
                    "pinyin": null,
                    "rankAliasName": "liveSpringCeremony2025qa20_cpPoint",
                    "scheduleId": 31524014,
                    "rankId": 31809658,
                    "order": 2,
                    "startTime": 1740465420000,
                    "endTime": 1742884620000,
                    "displayName": "星耀赛区"
                },
                {
                    "anchorId": "3459831_31524031",
                    "classifyId": 3459831,
                    "pinyin": null,
                    "rankAliasName": "liveSpringCeremony2025qa20_cpPoint",
                    "scheduleId": 31524031,
                    "rankId": 31809677,
                    "order": 3,
                    "startTime": 1740465420000,
                    "endTime": 1742884620000,
                    "displayName": "钻石赛区"
                }
            ],
            "anchorDisplayScheduleAndLane": {
                "anchorId": "3459831_31524031",
                "classifyId": 3459831,
                "pinyin": null,
                "rankAliasName": "liveSpringCeremony2025qa20_cpPoint",
                "scheduleId": 31524031,
                "rankId": 31809677,
                "order": 3,
                "startTime": 1740465420000,
                "endTime": 1742884620000,
                "displayName": "钻石赛区"
            },
            "totalAuthorCount": 0,
            "currRankListId": 31809677,
            "currActivityAlias": "liveSpringCeremony2025qa20_cpPoint",
            "activityEnd": false,
            "rewardData": {
                "rewardTitle": "冠亚季瓜分百分现金",
                "rewardList": [
                    {
                        "rewardId": 1,
                        "rewardImageUrl": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveSpringCeremony25/cpRank/cpCash.png",
                        "rewardName": "150W现金大奖"
                    },
                    {
                        "rewardId": 1,
                        "rewardImageUrl": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveSpringCeremony25/cpRank/cpCash.png",
                        "rewardName": "150W现金大奖"
                    },
                    {
                        "rewardId": 1,
                        "rewardImageUrl": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveSpringCeremony25/cpRank/cpCash.png",
                        "rewardName": "150W现金大奖"
                    }
                ]
            },
            "rewardDataMap": {
                "钻石赛区": {
                    "rewardTitle": "冠亚季瓜分三十万现金",
                    "rewardList": [
                        {
                            "rewardId": 3,
                            "rewardImageUrl": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveSpringCeremony25/cpRank/cpCash.png",
                            "rewardName": "30W现金大奖"
                        }
                    ]
                },
                "星耀赛区": {
                    "rewardTitle": "冠亚季瓜分五十万现金",
                    "rewardList": [
                        {
                            "rewardId": 2,
                            "rewardImageUrl": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveSpringCeremony25/cpRank/cpCash.png",
                            "rewardName": "50W现金大奖"
                        }
                    ]
                },
                "王者赛区": {
                    "rewardTitle": "冠亚季瓜分百万现金",
                    "rewardList": [
                        {
                            "rewardId": 1,
                            "rewardImageUrl": "https://ali.a.yximgs.com/kos/nlav12119/YdudiyUv_2025-02-27-11-10-28.png",
                            "rewardName": "100W现金大奖"
                        },
                        {
                            "rewardId": 1,
                            "rewardImageUrl": "https://ali.a.yximgs.com/kos/nlav12119/YdudiyUv_2025-02-27-11-10-28.png",
                            "rewardName": "100W现金大奖"
                        }
                    ]
                }
            }
        }
    },
    "host-name": "public-xm-c34-kce-node-staging158.idchb1az1.hb1.kwaidc.com"
}
