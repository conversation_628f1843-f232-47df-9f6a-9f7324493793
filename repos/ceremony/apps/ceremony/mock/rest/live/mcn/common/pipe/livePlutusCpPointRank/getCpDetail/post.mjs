export default {
    result: 1,
    data: {
        data: {
            laneList: [
                {
                    laneName: '王者赛区',
                    rankListId: 31298121,
                    rankStartTime: 1739431500000,
                    rankEndTime: 1741850700000,
                },
                {
                    laneName: '星耀赛区',
                    rankListId: 31298145,
                    rankStartTime: 1739431500000,
                    rankEndTime: 1741850700000,
                },
            ],
            currOrgInfo: {
                orgName: '活动',
                orgHead: [
                    {
                        cdn: 'blobstore-nginx.staging.kuaishou.com',
                        url: 'http://blobstore-nginx.staging.kuaishou.com/bs2/adminBlock/operation_org_CyulTCPTaYWclfFo.png?x-oss-process=image/resize,m_lfit,w_200,h_200/format,jpg',
                    },
                ],
            },
            competitionList: [
                {
                    competitionName: '城市大师赛',
                    activityAlias: 'liveSpringCeremony2025qa2_city',
                    state: 1,
                    authorCount: 3,
                },
                {
                    competitionName: 'pk赛一期',
                    activityAlias: 'liveSpringCeremony2025qa2_pkS1',
                    state: 2,
                    authorCount: 0,
                },
                {
                    competitionName: '城市弹幕联赛',
                    activityAlias: 'liveSpringCeremony2025qa2_screenPlayS1',
                    state: 2,
                    authorCount: 0,
                },
                {
                    competitionName: '城市精英赛',
                    activityAlias: 'liveSpringCeremony2025qa2_cityA2',
                    state: 2,
                    authorCount: 0,
                },
                {
                    competitionName: '城市星团战',
                    activityAlias: 'liveSpringCeremony2025qa2_showPartyS1',
                    state: 2,
                    authorCount: 0,
                },
                {
                    competitionName: '城市宫格赛',
                    activityAlias: 'liveSpringCeremony2025qa2_gridChatS1',
                    state: 2,
                    authorCount: 0,
                },
            ],
            displayLaneViewList: [
                {
                    anchorId: '3379602_31012525',
                    classifyId: 3379602,
                    pinyin: null,
                    rankAliasName: 'liveSpringCeremony2025qa2_cpPoint',
                    scheduleId: 31012525,
                    rankId: 31298121,
                    order: 1,
                    startTime: 1739431500000,
                    endTime: 1741850700000,
                    displayName: '王者赛区',
                },
                {
                    anchorId: '3379602_31012551',
                    classifyId: 3379602,
                    pinyin: null,
                    rankAliasName: 'liveSpringCeremony2025qa2_cpPoint',
                    scheduleId: 31012551,
                    rankId: 31298145,
                    order: 2,
                    startTime: 1739431500000,
                    endTime: 1741850700000,
                    displayName: '星耀赛区',
                },
            ],
            totalAuthorCount: 0,
            currRankListId: 31298121,
            currActivityAlias: 'liveSpringCeremony2025qa2_cpPoint',
            activityEnd: false,
            rewardData: {
                rewardTitle: '公会积分赛，赢超额奖励激励',
                rewardList: [
                    {
                        rewardId: 1,
                        rewardImageUrl:
                            'https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/liveSpringCeremony25/cpRank/150w.png',
                        rewardName: '超额现金',
                    },
                ],
            },
            bottomInfo: {
                itemRankInfo: {
                    item: {
                        itemId: 0,
                        itemName: '活动',
                        headUrl:
                            'http://blobstore-nginx.staging.kuaishou.com/bs2/adminBlock/operation_org_CyulTCPTaYWclfFo.png?x-oss-process=image/resize,m_lfit,w_200,h_200/format,jpg',
                        liveStreamId: null,
                        mysteryMan: false,
                    },
                    score: 0,
                    expScore: 0,
                    rankShowIndex: 1,
                    liveStreamId: null,
                    followStatus: false,
                    h5ShowScore: '100000',
                    performanceScore: null,
                    originalScore: null,
                    creationCampH5ShowScore: null,
                    teamName: null,
                    creationCampDisplayHint: null,
                    creationCampHintScore: null,
                    bulletPlayAppName: null,
                    showRankIndexType: 'NORMAL',
                    revOpRecommendType: 0,
                    addition: null,
                    hourlyRankAdditionDesc: null,
                    sponsors: [
                        {
                            item: {
                                itemId: 2197029957,
                                itemName: '罗齐',
                                headUrl:
                                    'http://kcdn.staging.kuaishou.com/uhead/AB/2023/09/27/20/BMjAyMzA5MjcyMDA4MTNfMjE5NzAyOTk1N18yX2hkNTc4XzIzMQ==_s.jpg',
                                liveStreamId: null,
                                mysteryMan: false,
                            },
                            score: 0,
                            expScore: 0,
                            rankShowIndex: 1,
                            liveStreamId: '',
                            followStatus: false,
                            h5ShowScore: '',
                            performanceScore: null,
                            originalScore: null,
                            creationCampH5ShowScore: null,
                            teamName: null,
                            creationCampDisplayHint: null,
                            creationCampHintScore: null,
                            bulletPlayAppName: null,
                            showRankIndexType: 'NORMAL',
                            revOpRecommendType: 0,
                            addition: null,
                            hourlyRankAdditionDesc: null,
                            teamLeader: false,
                            itemId: 2197029957,
                            itemName: '罗齐',
                            headUrl:
                                'http://kcdn.staging.kuaishou.com/uhead/AB/2023/09/27/20/BMjAyMzA5MjcyMDA4MTNfMjE5NzAyOTk1N18yX2hkNTc4XzIzMQ==_s.jpg',
                            displayScore: '0',
                            h5RankShowIndex: '1',
                            mysteryMan: false,
                        },
                    ],
                    audienceSponsorList: [],
                    addCardTimes: '',
                    currentItem: false,
                    topUserDirectPromotionTips: '',
                    histParticipatedInRepechageTips: null,
                    hotPhotos: null,
                    cpPrivateCpOrgInfo: null,
                    itemId: 0,
                    itemName: '活动',
                    headUrl:
                        'http://blobstore-nginx.staging.kuaishou.com/bs2/adminBlock/operation_org_CyulTCPTaYWclfFo.png?x-oss-process=image/resize,m_lfit,w_200,h_200/format,jpg',
                    displayScore: '0',
                    h5RankShowIndex: '1',
                    mysteryMan: false,
                },
                displayHint: '领先第二名${hintScore}',
                hintScore: 0,
                h5ShowHintScore: '0',
                showBottom: true,
                showPromotion: false,
                directPromotionFinal: false,
                cpPrivateCpOrgInfo: null,
                fightTextTemplate: null,
                fightTextParam: null,
                inPromotionRank: false,
                showBigRList: false,
                showNewBottom: true,
                showCpMoneyTask: false,
                cpMoneyTaskCountText: null,
                cpMoneyTaskCount: 0,
                showCpMoneyProgress: false,
                showCpMoneyReward: false,
                moneyRewardDesc: null,
                moneyRewardDesc2: null,
                exceedBaseScore: false,
                cpMoneyProgressStatus: 0,
                cpMoneyScoreDiffToBaoji: null,
                cpMoneyBaojiQuota: null,
                cpMoneyBaojiRadio: null,
                cpMoneyProgressText1Tmpl: null,
                cpMoneyProgressText2: null,
                flagChallengeTargetRank: -1,
                enableShowFlagChallengeBottom: false,
                showCashRankCard: false,
                cashRankThreshold: 0,
                cashRankThresholdH5Show: null,
                cashRankPlayStatus: 0,
                cashRankCurrBonusValue: null,
                cashRankBonusWinnerDesc: null,
                cashRankCurrBonusDesc: null,
                cashRankMaxBonusValue: null,
                cashRankCurrBonusDesc2: null,
                cashRankBonusIcon: null,
                scoreSum: 0,
                scoreSumH5Show: null,
                authorRankTip: '',
                authorAdditionInfo: null,
            },
            rankStartTime: 1739604300000,
            rankEndTime: 1739863500000,
            pointRule: [
                {
                    title: '第1名',
                    pointDesc: '积分+100000',
                },
                {
                    title: '第2名',
                    pointDesc: '积分+2',
                },
                {
                    title: '第3名',
                    pointDesc: '积分+3',
                },
                // {
                //     title: '第4-10名',
                //     pointDesc: '积分+5',
                // },
            ],
            rankList: [
                // {
                //     item: {
                //         mysteryMan: false,
                //         itemId: 2198662516,
                //         itemName: '东门停肾',
                //         headUrl:
                //             'http://kcdn.staging.kuaishou.com/uhead/AB/2025/02/10/11/BMjAyNTAyMTAxMTIxMDFfMjE5ODY2MjUxNl8yX2hkODQxXzU3OQ==_s.jpg',
                //         liveStreamId: 'bxU1ydoaUgI',
                //     },
                //     h5ShowIndex: '65',
                //     itemId: 2198662516,
                //     laneName: '北京_北京一区',
                // },
                // {
                //     item: {
                //         mysteryMan: false,
                //         itemId: 2198649418,
                //         itemName: '魏误',
                //         headUrl:
                //             'http://kcdn.staging.kuaishou.com/uhead/AB/2025/02/12/16/BMjAyNTAyMTIxNjIzMzJfMjE5ODY0OTQxOF8yX2hkNzQzXzU1Nw==_s.jpg',
                //         liveStreamId: '1wajIAx0O2U',
                //     },
                //     h5ShowIndex: '61',
                //     itemId: 2198649418,
                //     laneName: '北京_北京一区',
                // },
                // {
                //     item: {
                //         mysteryMan: false,
                //         itemId: 2197029957,
                //         itemName: '罗齐',
                //         headUrl:
                //             'http://kcdn.staging.kuaishou.com/uhead/AB/2023/09/27/20/BMjAyMzA5MjcyMDA4MTNfMjE5NzAyOTk1N18yX2hkNTc4XzIzMQ==_s.jpg',
                //         liveStreamId: '',
                //     },
                //     h5ShowIndex: '1',
                //     itemId: 2197029957,
                //     laneName: '北京_北京一区',
                // },
            ],
        },
    },
};
