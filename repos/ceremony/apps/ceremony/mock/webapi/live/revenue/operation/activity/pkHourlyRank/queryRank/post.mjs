import mockjs from 'mockjs';
export default mockjs.mock({
  "result": "@integer(1, 100)",
  "msg": "@string",
  "host-name": "@string",
  "ktraceId": "@integer(1, 100000)",
  "data": {
    "rounds": "@integer(1, 100)",
    "leftRounds": "@integer(1, 100)",
    "privilegeText": "@string",
    "appointRound": {
      "index": "@integer(1, 100)",
      "startTime": "@dateTime",
      "matchEndTime": "@dateTime",
      "pkTime": "@dateTime",
      "endTime": "@dateTime",
      "settleEndTime": "@dateTime",
      "status": "@integer(1, 100)",
      "matching": "@boolean"
    },
    "curAuthorInfo": {
      "userId": "@integer(1, 100000)",
      "userName": "@string",
      "headUrl": "@url(https)",
      "liveStreamIdStr": "@string",
      "score": "@integer(1, 100)",
      "h5ShowScore": "@string",
      "rankIndex": "@integer(1, 100)",
      "followStatus": "@boolean",
      "additionRatio": "@integer(1, 100)"
    },
    "rankList": [
      {
        "userId": "@integer(1, 100000)",
        "userName": "@string",
        "headUrl": "@url(https)",
        "liveStreamIdStr": "@string",
        "score": "@integer(1, 100)",
        "h5ShowScore": "@string",
        "rankIndex": "@integer(1, 100)",
        "followStatus": "@boolean",
        "additionRatio": "@integer(1, 100)"
      },
      {
        "userId": "@integer(1, 100000)",
        "userName": "@string",
        "headUrl": "@url(https)",
        "liveStreamIdStr": "@string",
        "score": "@integer(1, 100)",
        "h5ShowScore": "@string",
        "rankIndex": "@integer(1, 100)",
        "followStatus": "@boolean",
        "additionRatio": "@integer(1, 100)"
      },
      {
        "userId": "@integer(1, 100000)",
        "userName": "@string",
        "headUrl": "@url(https)",
        "liveStreamIdStr": "@string",
        "score": "@integer(1, 100)",
        "h5ShowScore": "@string",
        "rankIndex": "@integer(1, 100)",
        "followStatus": "@boolean",
        "additionRatio": "@integer(1, 100)"
      },
      {
        "userId": "@integer(1, 100000)",
        "userName": "@string",
        "headUrl": "@url(https)",
        "liveStreamIdStr": "@string",
        "score": "@integer(1, 100)",
        "h5ShowScore": "@string",
        "rankIndex": "@integer(1, 100)",
        "followStatus": "@boolean",
        "additionRatio": "@integer(1, 100)"
      },
      {
        "userId": "@integer(1, 100000)",
        "userName": "@string",
        "headUrl": "@url(https)",
        "liveStreamIdStr": "@string",
        "score": "@integer(1, 100)",
        "h5ShowScore": "@string",
        "rankIndex": "@integer(1, 100)",
        "followStatus": "@boolean",
        "additionRatio": "@integer(1, 100)"
      }
    ],
    "lastRankList": [
      {
        "userId": "@integer(1, 100000)",
        "userName": "@string",
        "headUrl": "@url(https)",
        "liveStreamIdStr": "@string",
        "score": "@integer(1, 100)",
        "h5ShowScore": "@string",
        "rankIndex": "@integer(1, 100)",
        "followStatus": "@boolean",
        "additionRatio": "@integer(1, 100)"
      },
      {
        "userId": "@integer(1, 100000)",
        "userName": "@string",
        "headUrl": "@url(https)",
        "liveStreamIdStr": "@string",
        "score": "@integer(1, 100)",
        "h5ShowScore": "@string",
        "rankIndex": "@integer(1, 100)",
        "followStatus": "@boolean",
        "additionRatio": "@integer(1, 100)"
      }
    ]
  }
});
export const disable = true;
            