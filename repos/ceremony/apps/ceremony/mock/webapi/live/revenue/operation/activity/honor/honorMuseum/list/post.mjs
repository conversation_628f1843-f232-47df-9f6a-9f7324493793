export default {
    "result": 1,
    "msg": "s",
    "total": 8,
    "data": {
        "authorMuseumName": "班开文的荣誉",
        "headUrl": "http://p2.a.yximgs.com/uhead/AB/2021/09/17/14/BMjAyMTA5MTcxNDM4MTVfMjk0ODQxODA3XzJfaGQ0NThfNjg1_s.jpg",
        "honorRank": "第100+名",
        "honorScore": "62",
        "honorCollectCount": 8,
        "liveStreamId": "",
        "defaultMuseumText": "未获得荣誉 积极参与活动争取吧",
        "honorList": [
            {
                "honorId": 1237,
                "title": "城市赛五强",
                "subTitle": "2025夏季盛典",
                "honorTime": 1749355557000,
                "iconUrl": [
                    {
                        "cdn": "p1-live.a.yximgs.com",
                        "url": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-5q.png"
                    },
                    {
                        "cdn": "p2-live.a.yximgs.com",
                        "url": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-5q.png"
                    }
                ]
            },
            {
                "honorId": 1218,
                "title": "游戏巅峰赛亚军",
                "subTitle": "2025夏季盛典",
                "honorTime": 1749701157000,
                "iconUrl": [
                    {
                        "cdn": "p2-live.a.yximgs.com",
                        "url": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/renqisai/renqisai-5q.png"
                    },
                    {
                        "cdn": "p1-live.a.yximgs.com",
                        "url": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/renqisai/renqisai-5q.png"
                    }
                ]
            },
            {
                "honorId": 690,
                "title": "家乡荣耀赛10强",
                "subTitle": "2024夏季盛典",
                "honorTime": 1749701157000,
                "iconUrl": [
                    {
                        "cdn": "p1-live.a.yximgs.com",
                        "url": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/quxiansai/quxiansai-yajun-v2.png"
                    },
                    {
                        "cdn": "p2-live.a.yximgs.com",
                        "url": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/quxiansai/quxiansai-yajun-v2.png"
                    }
                ]
            },
            {
                "honorId": 322,
                "title": "品类赛10强",
                "subTitle": "2023秋季盛典",
                "honorTime": 1749701157000,
                "iconUrl": [
                    {
                        "cdn": "p2-live.a.yximgs.com",
                        "url": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/quxiansai/quxiansai-yajun-v2.png"
                    },
                    {
                        "cdn": "p1-live.a.yximgs.com",
                        "url": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/quxiansai/quxiansai-yajun-v2.png"
                    }
                ]
            },
            {
                "honorId": 232,
                "title": "十大金牌主播赛冠军",
                "subTitle": "2023夏季盛典",
                "honorTime": 1749701157000,
                "iconUrl": [
                    {
                        "cdn": "p1-live.a.yximgs.com",
                        "url": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-10q.png"
                    },
                    {
                        "cdn": "p2-live.a.yximgs.com",
                        "url": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-10q.png"
                    }
                ]
            },
            {
                "honorId": 154,
                "title": "地区巅峰赛10强",
                "subTitle": "2022年度盛典",
                "honorTime": 1749701157000,
                "iconUrl": [
                    {
                        "cdn": "p1-live.a.yximgs.com",
                        "url": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-10q.png"
                    },
                    {
                        "cdn": "p2-live.a.yximgs.com",
                        "url": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-10q.png"
                    }
                ]
            },
            {
                "honorId": 105,
                "title": "钻石赛五强",
                "subTitle": "2022秋季盛典",
                "honorTime": 1749701157000,
                "iconUrl": [
                    {
                        "cdn": "p2-live.a.yximgs.com",
                        "url": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-yajun.png"
                    },
                    {
                        "cdn": "p1-live.a.yximgs.com",
                        "url": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-yajun.png"
                    }
                ]
            },
            {
                "honorId": 71,
                "title": "总决赛冠军",
                "subTitle": "2022夏季盛典",
                "honorTime": 1749701157000,
                "iconUrl": [
                    {
                        "cdn": "p1-live.a.yximgs.com",
                        "url": "https://p1-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-5q.png"
                    },
                    {
                        "cdn": "p2-live.a.yximgs.com",
                        "url": "https://p2-live.a.yximgs.com/udata/pkg/livePlutusProduct/honor23/honorInfo/chenshisai/chenshisai-5q.png"
                    }
                ]
            }
        ],
        "rankListInfo": [
            {
                "userInfo": {
                    "itemId": 415571676,
                    "itemName": "唐薇1月15号年货节",
                    "headUrl": "http://p3.a.yximgs.com/uhead/AB/2024/12/09/22/BMjAyNDEyMDkyMjM4MDJfNDE1NTcxNjc2XzJfaGQ4MjlfNzA0_s.jpg",
                    "liveStreamId": null,
                    "mysteryMan": false
                },
                "realScore": 13431,
                "score": "13431",
                "realRank": 1,
                "rank": "第1名",
                "followingStatus": false
            },
            {
                "userInfo": {
                    "itemId": 184212117,
                    "itemName": "马坤一路格桑花与亚纱祺",
                    "headUrl": "http://p2.a.yximgs.com/uhead/AB/2024/12/05/19/BMjAyNDEyMDUxOTE1MzFfMTg0MjEyMTE3XzJfaGQyM184ODg=_s.jpg",
                    "liveStreamId": null,
                    "mysteryMan": false
                },
                "realScore": 11527,
                "score": "11527",
                "realRank": 2,
                "rank": "第2名",
                "followingStatus": false
            },
            {
                "userInfo": {
                    "itemId": 59594616,
                    "itemName": "王四妹\uD83C\uDF3B没烦恼",
                    "headUrl": "http://p2.a.yximgs.com/uhead/AB/2024/12/31/13/BMjAyNDEyMzExMzE2MTVfNTk1OTQ2MTZfMV9oZDk2XzgwNg==_s.jpg",
                    "liveStreamId": null,
                    "mysteryMan": false
                },
                "realScore": 10111,
                "score": "10111",
                "realRank": 3,
                "rank": "第3名",
                "followingStatus": false
            }
        ],
        "isFollowing": false,
        "honorTitleToStageTypeMap": {
            "总决赛冠军":"190",
            "总决赛亚军":"190",
            "总决赛季军":"190",
            "总决赛五强":"190",
            "总决赛10强":"190",
            "钻石赛冠军":"210",
            "钻石赛亚军":"210",
            "钻石赛季军":"210",
            "钻石赛五强":"210",
            "钻石赛10强":"210",
            "品类赛冠军":"150",
            "品类赛亚军":"150",
            "品类赛季军":"150",
            "品类赛五强":"150",
            "品类赛10强":"150",
            "品类巅峰赛冠军":"200",
            "品类巅峰赛亚军":"200",
            "品类巅峰赛季军":"200",
            "品类巅峰赛五强":"200",
            "品类巅峰赛10强":"200",
            "地区巅峰赛冠军":"60",
            "地区巅峰赛亚军":"60",
            "地区巅峰赛季军":"60",
            "地区巅峰赛五强":"60",
            "地区巅峰赛10强":"60",
            "十大金牌主播赛冠军":"170",
            "十大金牌主播赛季军":"170",
            "十大金牌主播赛亚军":"170",
            "十大金牌主播赛五强":"170",
            "十大金牌主播赛10强":"170",
            "挑战者杯冠军":"540",
            "挑战者杯亚军":"540",
            "挑战者杯季军":"540",
            "挑战者杯五强":"540",
            "挑战者杯10强":"540",
            "人气赛品类冠军":"110",
            "人气赛品类亚军":"110",
            "人气赛品类季军":"110",
            "人气赛品类五强":"110",
            "人气赛品类10强":"110",
            "人气赛决赛冠军":"120",
            "人气赛决赛亚军":"120",
            "人气赛决赛季军":"120",
            "人气赛决赛五强":"120",
            "人气赛决赛10强":"120",
            "游戏巅峰赛冠军":"50",
            "游戏巅峰赛亚军":"50",
            "游戏巅峰赛季军":"50",
            "游戏巅峰赛五强":"50",
            "游戏巅峰赛10强":"50",
            "地区赛冠军":"40",
            "地区赛季军":"40",
            "地区赛亚军":"40",
            "地区赛五强":"40",
            "地区赛10强":"40",
            "星光总决选冠军":"180",
            "星光总决选亚军":"180",
            "星光总决选季军":"180",
            "星光总决选五强":"180",
            "星光总决选10强":"180",
            "家乡荣耀赛冠军":"20",
            "家乡荣耀赛亚军":"20",
            "家乡荣耀赛季军":"20",
            "家乡荣耀赛五强":"20",
            "家乡荣耀赛10强":"20"
        }
    },
    "pageSize": 60,
    "page": "1",
    "host-name": "public-bjy-c34-kce-node1789.idchb1az2.hb1.kwaidc.com"
}
