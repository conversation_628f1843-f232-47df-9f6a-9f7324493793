export default {
    result: 1, // 响应码 1-成功
    msg: '成功',
    data: {
        // 主播信息
        authorInfo: {
            userId: 1, // 用户id
            userName: 'A', // 用户昵称
            headUrls: [
                {
                    url: '', // 头像url
                },
            ], // 用户头像
        },
        // 当前可用加成卡
        currentCanUseCardList: [
            {
                cardId: 1, //加成卡id
                cardName: 'x分钟1.5倍加成卡', //加成卡名称
                cardTimes: '1.5', // 倍数
                count: 1, // 数量
                canUseStartTime: 1632888613000, // 可使用的开始时间
                canUseEndTime: 1632888613000, // 可使用的结束时间
                iconUrl: 'xxx', //图标url
                sourceDesc: 'xx赛决赛topN获得', // 获得来源文案
                effectiveDuration: 86400000, // 有效期，毫秒
                showPriority: 2, // 展示优先级
            },
        ],
        // 未来可用加成卡
        futureCanUseCardList: [
            {
                cardId: 1, //加成卡id
                cardName: 'x分钟1.5倍加成卡', //加成卡名称
                cardTimes: '1.5', // 倍数
                count: 1, // 数量
                canUseStartTime: 1632888613000, // 可使用的开始时间
                canUseEndTime: 1632888613000, // 可使用的结束时间
                iconUrl: 'xxx', //图标url
                sourceDesc: 'xx赛决赛topN获得', // 获得来源文案
                effectiveDuration: 86400000, // 有效期，毫秒
                showPriority: 2, // 展示优先级
            },
        ],
        // 已过期加成卡
        hasExpiredCardList: [
            {
                cardId: 1, //加成卡id
                cardName: 'x分钟1.5倍加成卡', //加成卡名称
                cardTimes: '1.5', // 倍数
                count: 1, // 数量
                canUseStartTime: 1632888613000, // 可使用的开始时间
                canUseEndTime: 1632888613000, // 可使用的结束时间
                iconUrl: 'xxx', //图标url
                sourceDesc: 'xx赛决赛topN获得', // 获得来源文案
                effectiveDuration: 86400000, // 有效期，毫秒
                showPriority: 2, // 展示优先级
            },
        ],
        // 已使用加成卡
        hasUsedCardList: [
            {
                cardId: 1, //加成卡id
                cardName: 'x分钟1.5倍加成卡', //加成卡名称
                cardTimes: '1.5', // 倍数
                count: 1, // 数量
                canUseStartTime: 1632888613000, // 可使用的开始时间
                canUseEndTime: 1632888613000, // 可使用的结束时间
                iconUrl: 'xxx', //图标url
                sourceDesc: 'xx赛决赛topN获得', // 获得来源文案
                effectiveDuration: 86400000, // 有效期，毫秒
                showPriority: 2, // 展示优先级
            },
        ],
        allCardList: [
            {
                iconUrl:
                    'https://ali.a.yximgs.com/kos/nlav12119/njaozfWU_2024-11-01-20-31-32.png', // 图标URL
                title: '盛典加成卡', // 卡片标题
                cardTimes: '5分钟', // 卡片次数（显示在左上角）
                sourceDesc: 'xx:xx分将自动使用即生效', // 加成卡描述
                addCardStatus: 0, // 加成状态 (0: 未使用, 1: 加成中, 2: 已使用, 3: 未获得)
                addCardLoseTime: 1729928386000, // 剩余加成时间(秒)
                cardId: 12345, // 用户加成卡ID
                biz: 'ceremony', // 业务标识
                previewText: '有机会获得', // 预览文案
            },
        ],
    },
};
