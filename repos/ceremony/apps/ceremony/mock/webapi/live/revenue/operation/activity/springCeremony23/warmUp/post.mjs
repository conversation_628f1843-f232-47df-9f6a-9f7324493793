const res = {
    "result": 1, // 响应码 1-成功, 100-限流
    "msg": '成功',
    "data": {
        "authorInfo": {
            "headUrl": "http://blobstore-nginx.staging.kuaishou.com/uhead/AB/2022/05/16/14/BMjAyMjA1MTYxNDE4MzNfMjE5NDUzMTkwMl8yX2hkNzBfODMy_s.jpg", //用户头像
            "userName": "尉迟鞍", //用户名
            "userId": 2194531902 //用户id
        },
        "modifyLabelTip": [
            "城市赛和地区赛的报名赛道会根据区县赛所报名地域而定",
            "11月17日20:00之前可调整赛道"
        ], // 提示文案
        "categoryLaneName": "颜值", // 品类赛
        "cityLaneName": "香港", // 城市赛
        "areaLaneName": "辽宁", // 地区赛
        "countyLaneName": "北京", // 区县赛
        "starlightLaneName": "哈哈", // 星光总决选
        "singerLaneName": "", // 歌手大赛
        "customLabelView": { // 主播可以修改的赛道范围
            // 主播可以修改的赛道范围
            "countyLaneName": {
                "alias": "liveSummerCeremony24County",
                "labels": [
                    // 区县赛可以修改的赛道范围，为空则不允许修改
                    {
                        "originName": "安徽-安庆-安庆综合", // 后续请求接口用这个
                        "showName": "安徽" // 展示用的
                    },
                    {
                        "originName": "北京-北京-顺义区", // 后续请求接口用这个
                        "showName": "北京" // 展示用的
                    },
                    {
                        "originName": "上海-上海-静安区", // 后续请求接口用这个
                        "showName": "上海" // 展示用的
                    },
                ]
            },
            "cityLaneName": {
                "alias": "liveSummerCeremony24City",
                "labels": [
                    // 城市赛可以修改的赛道范围，为空则不允许修改
                    {
                        "originName": "香港-香港", // 后续请求接口用这个
                        "showName": "香港" // 展示用的
                    },
                    {
                        "originName": "澳门-澳门", // 后续请求接口用这个
                        "showName": "澳门" // 展示用的
                    },
                    {
                        "originName": "台湾-台湾", // 后续请求接口用这个
                        "showName": "台湾" // 展示用的
                    }
                ]
            },
            "areaLaneName": {
                "alias": "liveSummerCeremony24Area",
                "labels": [
                    // 地区赛可以修改的赛道范围，为空则不允许修改
                    {
                        "originName": "辽宁", // 后续请求接口用这个
                        "showName": "辽宁" // 展示用的
                    },
                    {
                        "originName": "河北-河北", // 后续请求接口用这个
                        "showName": "河北" // 展示用的
                    },
                    {
                        "originName": "石家庄-石家庄", // 后续请求接口用这个
                        "showName": "石家庄" // 展示用的
                    }
                ]
            },
            "categoryLaneName": {
                "alias": "liveSummerCeremony24Category",
                "labels": [
                    // 品类赛可以修改的赛道范围，为空则不允许修改
                    {
                        "originName": "唱歌-王牌歌手", // 后续请求接口用这个
                        "showName": "唱歌" // 展示用的
                    },
                    {
                        "originName": "舞蹈-王牌舞者", // 后续请求接口用这个
                        "showName": "舞蹈" // 展示用的
                    },
                    {
                        "originName": "颜值-王牌舞者", // 后续请求接口用这个
                        "showName": "颜值" // 展示用的
                    }
                ]
            },
            // "starlightLaneName": {
            //     "alias": "liveSummerCeremony24AllStarLight",
            //     "labels": [
            //         // 星光总决选可以修改的赛道范围，为空则不允许修改
            //         {
            //             "originName": "唱歌-百变歌神", // 后续请求接口用这个
            //             "showName": "唱歌" // 展示用的
            //         },
            //         {
            //             "originName": "脱口秀男主播-王牌", // 后续请求接口用这个
            //             "showName": "脱口秀男主播" // 展示用的
            //         }
            //     ]
            // }
        }
    }
}
export default res;