import  cohesionData from '../../complex/rankFrameworkMoreTab/cohesion.mjs'
const additionStartTime = new Date().getTime() + 10000;
export default function (req, res) {

    const additionEndTime = new Date().getTime() + 10000000000;
    res.end(
        JSON.stringify({
            "result": 1,
            "host-name": "public-xm-d74-kce-node-staging67.idchb1az1.hb1.kwaidc.com",
            "ktraceId": "EOCBgICQr5iOrAEYpQMgwpv9vOUyKIfXtfYL",
            data: cohesionData
            // "data": {
            //     "isAuthor": true, //是否主播端
            //     "levelId": 3, //任务id
            //     "status": 4, //待定:1未开始、2失败、3成功、4进行中、5加成中（未完成、进行中、完成），6已结束
            //     "startTime": new Date().getTime() + 100000, //任务开始时间
            //     "endTime": new Date().getTime() + 200000, //任务结束时间
            //     "finishTime": 111111111, //任务完成时间
            //     "targetScore": 1111, //任务最高的目标值
            //     "currentScore": 400, //任务当前值
            //     "gradientItemViews": [
            //         {
            //             "levelId": 1, //任务id
            //             "targetScore": 200, //当前任务目标分
            //             "additionDurationMs": 10, //加成延长时长
            //             "previewIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "successIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.5bf2365b45d3ffb3.png",
            //             "runningIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "failIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.03c8f3c2f95a000c.png"
            //         },
            //         {
            //             "levelId": 2, //任务id
            //             "targetScore": 400, //当前任务目标分
            //             "additionDurationMs": 20, //加成延长时长
            //             "previewIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "successIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.5bf2365b45d3ffb3.png",
            //             "runningIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "failIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.03c8f3c2f95a000c.png"
            //         },
            //         {
            //             "levelId": 3, //任务id
            //             "targetScore": 600, //当前任务目标分
            //             "additionDurationMs": 30, //加成延长时长
            //            "previewIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "successIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.5bf2365b45d3ffb3.png",
            //             "runningIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "failIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.03c8f3c2f95a000c.png"
            //         },
            //         {
            //             "levelId": 4, //任务id
            //             "targetScore": 800, //当前任务目标分
            //             "additionDurationMs": 40, //加成延长时长
            //             "previewIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "successIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.5bf2365b45d3ffb3.png",
            //             "runningIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "failIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.03c8f3c2f95a000c.png"
            //         },
            //         {
            //             "levelId": 5, //任务id
            //             "targetScore": 1000, //当前任务目标分
            //             "additionDurationMs": 60, //加成延长时长
            //             "previewIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "successIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.5bf2365b45d3ffb3.png",
            //             "runningIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.0b4d54f24b7db30e.png",
            //             "failIcon": "https://p4-live.a.yximgs.com/kos/nlav12706/ceremony-summer-25/jia-cheng-xi-shu_2x.03c8f3c2f95a000c.png"
            //         }
            //     ],
            //     "additionRate": 0.32,
            //     "additionStartTime":additionStartTime,
            //     "additionEndTime": new Date().getTime() + 10000000000,
            //     "nextStartTime":  new Date().getTime() + 200000,
            //     "assistNum": 123, //助力人数
            //     "additionScore": 234, //额外加分
            //     "sendGiftId": 123, //礼物id
            //     "sendGiftName": "战旗", //礼物名称
            //     "giftUnitPrice": 10, //礼物快币数
            //     "addGiftScore": 1, //送出一个礼物加的分数
            //     "giftToken": ""
            // }

        }),
    );
};
