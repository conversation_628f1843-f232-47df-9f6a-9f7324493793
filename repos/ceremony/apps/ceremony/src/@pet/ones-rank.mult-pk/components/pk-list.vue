<template>
    <div v-if="isShow" class="pk-list">
        <div
            v-for="(item, idx) in renderList"
            :key="`${item?.userInfo?.userId}-${idx}`"
            class="pk-list__item"
        >
            <div class="rank-icon">
                <Rank1Static v-if="idx + 1 === 1" class="icon-item" />
                <Rank2Static v-if="idx + 1 === 2" class="icon-item" />
                <Rank3Static v-if="idx + 1 === 3" class="icon-item" />
                <img
                    v-if="idx + 1 === 4"
                    class="icon-item"
                    src="./rank4_2x.png"
                />
            </div>
            <template v-if="item">
                <div class="head-container">
                    <APAvatar
                        v-click-log="{
                            action: 'OP_ACTIVITY_MORE_PLAY_CARD',
                            params: {
                                type: '四人巅峰对决',
                                btn_type: 'AUTHOR_HEAD',
                                author_id: item.userInfo.userId,
                            },
                        }"
                        size="2xs"
                        :user-id="item.userInfo.userId"
                        :head-url="item.userInfo.headUrl"
                        :live-stream-id="item.liveStreamId"
                    ></APAvatar>
                    <ATag
                        v-if="item.isCurrAuthor"
                        class="current-anchor"
                        corner="left"
                        >当前</ATag
                    >
                </div>
                <slot name="desc">
                    <div class="addition text-bold">
                        <span class="a-text-highlight">{{
                            item.additionRatio
                        }}</span>
                        <span class="text-content a-text-main">{{
                            item.additionDesc
                        }}</span>
                    </div>
                    <div class="cnt a-text-main">{{ item.scoreH5Show }}</div>
                </slot>
            </template>
            <template v-else>
                <slot name="empty">
                    <div class="empty-item a-text-main">本轮轮空</div>
                </slot>
            </template>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
// import LiveIcon from '@pet/ones-ui.dynamic-living-icon/index.vue';
import { Avatar as APAvatar } from '@alive-ui/pro';
import {
    Rank1Static,
    Rank2Static,
    Rank3Static,
    // LivingStatic,
} from '@alive-ui/icon';
import { ATag } from '@alive-ui/base';
import type { IPkItem } from './interface';
const props = defineProps<{
    list: IPkItem[];
}>();
const isShow = computed(() => props.list?.length > 0);
const renderList = computed(() => {
    const len = props.list.length; // 传进来的数组长度
    const nullList = Array(4 - len).fill(null);
    return props.list.concat(nullList);
});
</script>

<style lang="less" scoped>
.pk-list {
    display: flex;
    &__item {
        position: relative;
        width: 83.5px;
        height: 84px;
        padding: 8px 0;
        border-radius: 8px;
        background: rgba(242, 242, 255, 0.06);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .head-container {
            position: relative;
            .current-anchor {
                position: absolute;
                padding: 0;
                right: -14px;
                top: -6px;
                width: 24px;
                height: 14px;

                :deep(div) {
                    font-size: 10px;
                    line-height: 12px;
                }
            }
        }
        .addition {
            margin-top: 4px;
            font-size: 12px;
            // font-weight: 500;
            line-height: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
            .text-content {
                margin-left: 2px;
            }
        }
        .cnt {
            font-size: 10px;
            line-height: 14px;
            opacity: 0.6;
        }
        .rank-icon {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            .icon-item {
                height: 20px;
                width: 20px;
            }
        }
    }
    &__item:not(:first-child) {
        margin-left: 8px;
    }
    .empty-item {
        font-size: 12px;
        font-weight: normal;
        opacity: 0.6;
    }
}
</style>
