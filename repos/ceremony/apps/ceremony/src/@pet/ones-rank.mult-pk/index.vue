<template>
    <div v-if="sourceData.source === 2" class="mult-pk">
        <!-- <div class="mult-pk__header">
            <div class="mult-pk__header__left">
                <div class="title">{{ sourceData.title }}</div>
                <div class="time">{{ formatTime(countTime) }}</div>
            </div>
            <AMarquee class="mult-pk__header__right">
                <div
                    v-for="(d, i) in sourceData.desc"
                    :key="i"
                    class="tip-text a-text-main"
                >
                    {{ d }} <Right class="entry-icon" />
                </div>
            </AMarquee>
        </div> -->
        <div class="mult-pk__content">
            <div v-if="!sourceData?.peakBattleInfo?.join" class="no-fight">
                <img src="./mult_2x.png" />
                <div class="text-content">
                    <div class="text-content__main a-text-main text-bold">
                        {{ sourceData.peakBattleInfo.desc }}
                    </div>
                    <div class="text-content__desc a-text-main">
                        {{ sourceData.peakBattleInfo.supplementDesc }}
                    </div>
                </div>
            </div>
            <div v-if="sourceData?.peakBattleInfo?.join" class="fight">
                <PkList :list="pkList" />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, type PropType } from 'vue';
// import { Right } from '@alive-ui/icon';
// import { Marquee as AMarquee } from '@alive-ui/base';
import PkList from './components/pk-list.vue';
import type { DefaultSelectedAdditionInfo } from '@pet/ones-rank.schema/query-rank';

// enum EStatus {
//     GET = 'get', // 获得
//     MISS = 'miss', // 错失
//     FIGHT = 'fight', // 对决中
//     NO_ATTEND = 'no-attend', // 对决中但未参加
//     NO_START = 'no-start', // 未开始
//     NO_SHOW = 'no-show', // 不展示
// }
const props = defineProps({
    sourceData: {
        type: Object as PropType<DefaultSelectedAdditionInfo>,
        default: () => ({}),
    },
});
// const showStatus = computed<EStatus>(() => {
//     if (
//         props.sourceData.status === 3 &&
//         props.sourceData.peakBattleInfo.join
//     ) {
//         // 对决中
//         return EStatus.FIGHT;
//     }
//     if (
//         props.sourceData.status === 3 &&
//         !props.sourceData.peakBattleInfo.join
//     ) {
//         // 没参加
//         return EStatus.NO_ATTEND;
//     }
//     if (props.sourceData.status === 4) {
//         // 未开始
//         return EStatus.NO_START;
//     }
//     if (props.sourceData.status === 1) {
//         // 错失
//         return EStatus.MISS;
//     }
//     if (props.sourceData.status === 2) {
//         // 获得
//         return EStatus.GET;
//     }
//     return EStatus.NO_SHOW;
// });
// const isShow = computed(() => {
//     return (
//         props.sourceData.source === 2 &&
//         showStatus.value !== EStatus.NO_SHOW
//     );
// }); // 1凝聚力,2小时榜,3,车轮战,当为小时榜并且展示该组件
// const countTime = ref(0); // 倒计时
// const handleCountDown = () => {
//     // 倒计时
//     const time = props.sourceData.peakBattleInfo.pkEndTime - Date.now();
//     if (time <= 0) {
//         // 扔出事件,停止计时
//         ctx.emit('timeover');
//         return;
//     }
//     countTime.value = time;
//     setTimeout(handleCountDown, 1000);
// };
// handleCountDown();
// function formatTime(ms: number) {
//     const totalSeconds = Math.floor(ms / 1000); // 将毫秒转换为秒
//     const minutes = Math.floor(totalSeconds / 60); // 计算分钟
//     const seconds = totalSeconds % 60; // 计算剩余秒数

//     // 使用 padStart 补零，确保两位数格式
//     const formattedMinutes = String(minutes).padStart(2, '0');
//     const formattedSeconds = String(seconds).padStart(2, '0');

//     return `${formattedMinutes}:${formattedSeconds}`;
// }
// const textList = props.sourceData.desc?.map((text) => {
//     return {
//         text,
//         keywords: text,
//     };
// });
// const isShowTextPanel = computed(() => {
//     return (
//         showStatus.value === EStatus.NO_START ||
//         showStatus.value === EStatus.NO_ATTEND ||
//         showStatus.value === EStatus.MISS
//     );
// });
// const isShowPk = computed(() => {
//     return (
//         showStatus.value === EStatus.FIGHT ||
//         showStatus.value === EStatus.GET
//     );
// });
const pkList = computed(() => {
    const list = props.sourceData?.peakBattleInfo?.rankUserList?.map(
        (item: {
            currAuthor: any;
            userInfo: { user_id: any; headurl: any; user_name: any };
        }) => {
            return {
                ...item,
                isCurrAuthor: item.currAuthor,
                userInfo: {
                    userId: item.userInfo.user_id,
                    headUrl: item.userInfo.headurl,
                    userName: item.userInfo.user_name,
                },
            };
        },
    );
    return list || [];
});
</script>

<style lang="less" scoped>
.mult-pk {
    // width: 382px;
    // height: 148px;
    // padding: 16px 12px;
    border-radius: 0 0 16px 16px;
    display: flex;
    flex-direction: column;
    // &__header {
    //     display: flex;
    //     justify-content: space-between;
    //     align-items: center;
    //     &__left {
    //         display: flex;
    //         align-items: center;
    //         .title {
    //             height: 20px;
    //             font-family: HYYakuHei;
    //             font-weight: 900;
    //             font-size: 16px;
    //             line-height: 20px;
    //             background: linear-gradient(
    //                 89.98deg,
    //                 #ffffff 0.03%,
    //                 #ffc4a3 95.69%
    //             );
    //             -webkit-background-clip: text;
    //             background-clip: text;
    //             -webkit-text-fill-color: transparent;
    //         }
    //         .time {
    //             margin-left: 4px;
    //             width: 44px;
    //             height: 20px;
    //             display: flex;
    //             justify-content: center;
    //             align-items: center;
    //             font-size: 12px;
    //             font-weight: 500;
    //             line-height: 16px;
    //             border-radius: 10px;
    //             text-align: center;
    //             color: #ffd400;
    //             background: rgba(255, 212, 0, 0.1);
    //         }
    //     }
    //     &__right {
    //         height: 18px;
    //         overflow: hidden;
    //         font-size: 12px;
    //         .tip-text {
    //             display: flex;
    //             align-items: center;
    //             justify-content: flex-end;
    //         }
    //         .entry-icon {
    //             width: 10px;
    //             height: 10px;
    //         }
    //     }
    // }
    &__content {
        // margin-top: 12px;
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        .no-fight {
            min-height: 84px;
            height: 100%;
            width: 100%;
            background: rgba(217, 221, 255, 0.06);
            border-radius: 8px;
            display: flex;
            align-items: center;
            // justify-content: center;
            padding: 0 12px;
            img {
                width: 51px;
            }
            .text-content {
                font-family: PingFang SC;
                margin-left: 13px;
                &__main {
                    height: 21px;
                    font-size: 14px;
                    // font-weight: 500;
                    line-height: 21px;
                    overflow: hidden;
                }
                &__desc {
                    margin-top: 4px;
                    font-size: 12px;
                    line-height: 18px;
                    overflow: hidden;
                    opacity: 0.6;
                }
            }
        }
    }
}
</style>
