// 临时TS类型

export interface UserInfo {
    following: boolean;
    profilePagePrefetchInfo: {
        profilePageType: number;
    };
    eid: string;
    user_sex: string; // 'M' 或 'F'
    headurl: string;
    visitorBeFollowed: boolean;
    headurls: Array<{
        cdn: string;
        url: string;
    }>;
    user_id: number;
    user_name: string;
}

export interface RankUser {
    userInfo: UserInfo;
    score: number;
    scoreH5Show: string;
    additionDesc: string; // "加成"
    additionRatio: string; // 例如 "+2%"
    // isCurrAuthor: boolean;
    currAuthor: boolean;
    liveStreamId: string; // 直播间ID
}

export interface AdditionInfo {
    addCardStatus: number; // 1未使用, 2使用中
    addCardEffectiveDuration: number; // 有效时间 (秒)
    addCardRemainDuration: number; // 剩余时间 (秒)
    addCardLoseEffectiveTime: number;
    addCardLevel: string; // 例如 "1.1"
}

export interface PeakBattleInfo {
    additionInfo: AdditionInfo;
    rankUserList: RankUser[];
    join: boolean; // 是否参与
    desc: string; // 未开始或对决中但未参加的描述
    supplementDesc: string; // 补充描述
    pkEndTime: number; // 对决结束时间
}

export interface BattleData {
    id: number;
    time: number; // 时间戳
    isCurrent: boolean;
    status: number; // 1错失, 2获得, 3对决中, 4未开始
    statusDesc: string; // 电池下面的描述
    source: number; // 小时榜则使用多人对决组件
    desc: string[]; // 描述数组
    title: string; // 标题
    peakBattleInfo: PeakBattleInfo;
}
