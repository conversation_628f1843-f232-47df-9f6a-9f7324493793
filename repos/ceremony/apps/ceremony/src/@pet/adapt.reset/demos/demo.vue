<pet-info lang="json">
{ "title": "reset默认标签样式" }
</pet-info>
<script lang="ts" setup>
import '@pet/adapt.reset/reset.css';
</script>
<template>
    <div>
        <div class="title">reset样式</div>
        <div class="panel">
            <h1>h1标题</h1>
            <h2>h2标题</h2>
            <h3>h3标题</h3>
            <h4>h4标题</h4>
            <h5>h5标题</h5>
            <h6>h6标题</h6>
        </div>
        <div class="panel">
            <ul>
                <li>ul列表项1</li>
                <li>ul列表项2</li>
            </ul>
        </div>
        <div class="panel">
            <ol>
                <li>ol列表项1</li>
                <li>ol列表项2</li>
            </ol>
        </div>

        <button class="button">一个按钮</button>
    </div>
</template>
<style lang="scss" scoped>
.title {
    display: flex;
    font-size: 20px;
    font-weight: bold;
}
.panel {
    margin-top: 20px;
    padding: 20px;
    border-radius: 20px;
    background: rgba(0, 0, 0, 0.2);
}
.button {
    margin-top: 20px;
    height: 60px;
    padding: 10px;
    border-radius: 20px;
    background: pink;
}
</style>
