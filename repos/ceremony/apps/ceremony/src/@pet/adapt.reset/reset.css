html,
body {
    margin: 0 auto;
    padding: 0;
    height: 100%;
    width: 100%;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-font-smoothing: antialiased;
    /* 主文字色 */
    color: var(--adapt-global-font-color);
    line-height: normal;
}

body {
    font-size: 14px;
    font-family:
        'PingFang SC',
        'Segoe UI',
        miui,
        system-ui,
        -apple-system,
        Roboto,
        'Helvetica Neue',
        'Noto Sans',
        'Liberation Sans',
        Arial,
        'Apple Color Emoji',
        'Segoe UI Emoji',
        'Segoe UI Symbol',
        'Noto Color Emoji',
        sans-serif;
}

#app {
    min-height: 100%;
}

/* 只针对iOS16.0-iOS16.2系统的css hack， 主要全局处理客户端无法禁止弹性回馈的问题 */
/* 对于已知单屏游戏场景 仍然推荐对html 设置overflow:hidden的操作，保证页面绝对静止 */
@supports (-webkit-hyphens: none) and (overscroll-behavior: none) {
    html {
        overscroll-behavior: none;
    }
    /* 触发页面滚动 hack */
    /* prettier-ignore */
    /* 单页面状态既有不可滚动又有可滚动时，简单处理可以使用触发滚动的hack */
    /* #app {
        min-height: calc(100vh + 0.6PX);
    } */
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
    padding: 0;
    font-weight: 700;
}

ul,
ol {
    list-style: none;
    padding: 0;
    margin: 0;
}

button {
    background: transparent;
    border: none;
    padding: 0;
}

a {
    text-decoration: none;
}

img,
picture {
    user-select: none;
    -webkit-user-drag: none;
}

@media only screen and (min-width: 500px) {
    html {
        background-color: #222;
    }
}

/* 横屏强制按照414尺寸展示 保证会场可用 */
/* postcss-pxtorem 要写PX才能ignore 目前先写大写浏览器都会识别 */
@media (orientation: landscape) {
    html {
        /* prettier-ignore */
        max-width: 414PX !important;
        /* prettier-ignore */
        font-size: 100PX !important;
    }
}
