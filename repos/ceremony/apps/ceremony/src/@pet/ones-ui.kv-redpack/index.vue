<template>
    <RedPackGroup
        v-if="redPkgList && redPkgList.length"
        :live-stream-id-list="liveStreamIdList"
        :pack-list="data"
        :pack-type="packType"
        :self-refresh="selfRefresh"
        :live-square-source="dataConfig?.liveSquareSource"
        :new-storm-style="newStormStyle"
        :time-stamp="timeStamp"
        @count-end="endRefresh"
    />
</template>

<script lang="ts" setup>
import { ref, watch, onUnmounted, computed, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useVisibility } from '@alive-ui/actions';
import pkgStore from './store/index';
import RedPackGroup from './components/redpack-group/index.vue';
import type { RecommendSource } from './interface/type';
import type { KvPackType } from './components/redpack-group/type';

interface DataConfig extends Record<string, unknown> {
    liveSquareSource: number;
}

const props = withDefaults(
    defineProps<{
        dataConfig: DataConfig;
        selfRefresh?: boolean;
        packType?: KvPackType;
        newStormStyle?: boolean;
        recommendSource: RecommendSource;
    }>(),
    {
        packType: 1,
        selfRefresh: false,
        newStormStyle: false,
    },
);
//  TODO 事件命名修改，改成 xxx-onLoad，与 dom 本身生命周期一致
const emits = defineEmits<{
    (e: 'render-finish', playload: boolean): void;
}>();

const store = pkgStore();
const { redPkgList, liveStreamIdList, timeStamp } = storeToRefs(store);
const isLeave = ref(false);

const data = computed(() => {
    return props.packType === 1
        ? redPkgList.value.slice(0, 3).reverse()
        : redPkgList.value;
});

useVisibility({
    visibleHandler: () => {
        isLeave.value = false;
    },
    hiddenHandler: () => {
        isLeave.value = true;
    },
});

watch(
    () => isLeave.value,
    () => {
        if (!isLeave.value) {
            store.queryInitInfo(props.recommendSource);
        }
    },
    { immediate: true },
);

watch(
    () => data.value,
    (val, old) => {
        if (old && (!val?.length || val?.length !== old?.length)) {
            emits('render-finish', !!val.length);
        }
    },
    {
        deep: false,
        immediate: true,
    },
);
const endRefresh = () => {
    if (isLeave.value) {
        return;
    }
    store.handleRefresh(props.recommendSource);
};

onUnmounted(() => {
    redPkgList.value = [];
});
</script>
