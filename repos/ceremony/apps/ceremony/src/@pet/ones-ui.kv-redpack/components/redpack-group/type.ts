export type PackItem = {
    authorInfo: {
        headUrl: string;
        userId: number;
    };
    redPackGrabTime: number;
    liveStreamId: string;
    redPackId: string;
    redPackSourceType: number;
    // 1 普通，2 特殊，9 道具/答题红包
    redPackViewType: 1 | 2 | 9;
    hasCash: boolean; // true-有现金 false-无现金
    redPackCashFen: number;
    redPackText?: string; // 22.12.20扩展新字段，用于红包下方按钮文案
};

// 1 代表 kv 垂直红包，2代表水平红包，3 代表 kv 水平红包（盛典新增）
export type KvPackType = 1 | 3 | 2;

// 1 代表 kv 垂直红包，2代表水平红包，3 代表 kv 水平红包（盛典新增）
export type PackType = KvPackType | 2;
