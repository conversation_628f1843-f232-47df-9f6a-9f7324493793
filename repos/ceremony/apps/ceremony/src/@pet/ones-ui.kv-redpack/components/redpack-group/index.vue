<template>
    <div
        class="red-pack-group"
        :class="{
            'kv-red-pack-group': packType === 3,
            'horizontal-red-pack-group': packType === 2,
            'vertical-red-pack-group': packType === 1,
        }"
    >
        <RedPackItem
            v-for="(item, index) in data"
            :key="item.redPackId + timeStamp"
            :item="item"
            :pack-type="packType"
            :index="index"
            :live-stream-id-list="liveStreamIdList"
            :self-refresh="selfRefresh"
            :live-square-source="liveSquareSource"
            :new-storm-style="newStormStyle"
            @count-end="countEnd"
        />
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import RedPackItem from './redpack-item.vue';
import type { PackItem, PackType } from './type';

const emit = defineEmits(['count-end']);
const props = withDefaults(
    defineProps<{
        selfRefresh?: boolean;
        packType?: PackType;
        liveStreamIdList?: string[];
        packList?: PackItem[];
        liveSquareSource?: number;
        newStormStyle?: boolean;
        timeStamp: number;
    }>(),
    {
        selfRefresh: false,
        packType: 1,
        liveStreamIdList: () => [],
        packList: () => [],
        liveSquareSource: undefined,
        activityBiz: '',
        newStormStyle: false,
        timeStamp: 0,
    },
);

const data = computed(() => {
    return props.packList.filter((item) => item.authorInfo) || [];
});

const countEnd = () => {
    emit('count-end');
};
</script>

<style lang="less" scoped>
.horizontal-red-pack-group {
    display: flex;
    justify-content: center;
}

.kv-red-pack-group {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}
.vertical-red-pack-group {
    padding-top: 4px;
}
</style>
