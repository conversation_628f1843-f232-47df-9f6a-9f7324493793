<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import CountDown from '@pet/ones-ui.countdown/index.vue';
import { Toast } from '@lux/sharp-ui-next';
import {
    activityBiz,
    liveStreamId as ownLiveStreamId,
    msToSecondString,
    openUpDown,
    getPageCode,
    isYodaPCContainer,
    query,
    getServerTime,
} from '@alive-ui/actions';
import type { PackItem, PackType } from './type';
import { sendTask } from '@/common/logger';

const emit = defineEmits(['count-end', 'btn-click']);

const props = withDefaults(
    defineProps<{
        item?: PackItem;
        selfRefresh?: boolean;
        packType?: PackType;
        liveStreamIdList?: string[];
        index?: number;
        liveSquareSource?: number;
        newStormStyle: boolean;
    }>(),
    {
        item: () => ({}) as unknown as PackItem,
        liveStreamIdList: () => [],
        selfRefresh: false,
        packType: 1,
        index: 0,
        liveSquareSource: undefined,
        newStormStyle: false,
    },
);

// 这里是迁移的
// TODO 倒计时组件不应该默认为true,应该取props.redPackGrabTime - new Date().getTime() > 0
// 默认为true时如果props.redPackGrabTime小于当前时间,会触发count-end，导致接口轮询
const isShowCount = ref(true);

// 道具红包展示静态帧，还是展示倒计时帧
const redpack9ShowTimer = ref(false);

// 是否是道具卡红包
const isPropertyRedpack = computed(() => {
    return +props.item.redPackViewType === 9;
});

const hasItemAnim = computed(() => {
    // packType 1 3，1 是垂直排列，3 是水平排列；2 9 为后端 redPackViewType 类型
    return (
        [1, 3].indexOf(props.packType) > -1 &&
        [2, 9].indexOf(props.item.redPackViewType) > -1
    );
});

const { entry_src } = query;

const redLayoutMap = {
    // 竖屏
    1: 2,
    // 横屏
    2: 1,
    3: 1,
};

const taskPkgLog = () => {
    return {
        action: 'OP_ACTIVITY_RED_PACKET_ITEM',
        params: {
            activity_name: activityBiz,
            index: props.index + 1,
            has_cash: !!props.item.redPackCashFen,
            live_stream_id: props.item.liveStreamId,
            anchor_user_id: props.item?.authorInfo?.userId,
            red_pack_id: props.item.redPackId,
            type: props.item.redPackViewType,
            entry_src,
            red_layout: redLayoutMap[props.packType],
        },
    };
};

const openPack = () => {
    if (isYodaPCContainer) {
        Toast.info('请到手机端进行查看');

        return;
    }
    emit('btn-click');
    sendTask('CLICK', taskPkgLog());

    if (props.item.liveStreamId === ownLiveStreamId) {
        Toast.info('已在直播间，不可跳转');

        return;
    }

    if (props.item.liveStreamId) {
        // 跳转红包直播间额外参数
        const extraInfo = {
            source_type: 269,
            liveSource: 'LIVE_REDPACK_ITEM_SRC',
            activity_name: activityBiz,
            page_code: getPageCode(),
            hasCash: !!props.item.redPackCashFen,
            redPackViewType: props.item.redPackViewType,
            redPackSourceType: props.item.redPackSourceType,
            redPackCashFen: props.item.redPackCashFen,
            exp_tag: (window as any).GLOBAL_EXPTAG || activityBiz,
            redPackId: props.item.redPackId,
            disableLoadMore: true,
            disablePullRefresh: true,
            liveSquareSource: props.liveSquareSource,
        };
        const res = openUpDown(
            props.liveStreamIdList,
            props.item.liveStreamId,
            extraInfo,
        );

        if (res?.errMsg) {
            Toast.info(res?.errMsg);
        }
    } else {
        Toast.info('暂无可抢的红包，稍后再来~ ');
    }
};

const timeEnd = () => {
    isShowCount.value = false;
    if (props.selfRefresh) {
        emit('count-end');
    }
};

// 如果刷新的时候 userId 没变，就会导致倒计时失效，所以需要监听 redPackGrabTime，并评断是否需要倒计时
watch(
    [() => props.item.authorInfo.userId, () => props.item.redPackGrabTime],
    async () => {
        const serverTime = await getServerTime()
            .then((ms) => {
                return ms.serverTimeStamp || +new Date();
            })
            .catch(() => +new Date());
        isShowCount.value = serverTime < props.item.redPackGrabTime;
    },
    {
        immediate: true,
    },
);

const unitText = computed(() => {
    const unit = Math.floor(props.item.redPackCashFen / 100 / 100); // 百元最小单位
    const unitMap: { [key: string]: string } = {
        0: '',
        1: '百',
        2: '千',
        3: '万',
    };

    return unitMap[unit > 0 ? String(unit).length : 0];
});

const btnText = computed(() => {
    if (isPropertyRedpack.value) {
        return '领复活卡';
    }
    return props.item.liveStreamId
        ? props.item.redPackText ||
              (props.item.redPackCashFen > 0 ? '抢现金' : '立即抢')
        : '未开启';
});

const countdownFn = (t: number) => msToSecondString(t, 'ss');
const redpack9CountdownFn = (t: number) => {
    redpack9ShowTimer.value = !redpack9ShowTimer.value;
    const secondStr = msToSecondString(t, 'ss');
    return `${secondStr}s`;
};
</script>

<template>
    <div
        v-show-log="taskPkgLog()"
        class="redpack-item"
        :class="{
            'vertical-pkg': packType === 1,
            'horizontal-pkg': packType === 2,
            'kv-pkg': packType === 3,
        }"
        @click="openPack"
    >
        <!-- 倒计时和封面皮，动历史代码看 gis-pack-lock 只用在间外水平大红包 -->
        <div
            v-if="!newStormStyle"
            class="grab-item"
            :class="
                item.liveStreamId
                    ? `gis-red-pkg-${item.redPackViewType}`
                    : 'gis-pack-lock'
            "
        >
            <!-- 扫光动效 -->
            <template v-if="hasItemAnim">
                <div class="item-animation gis-lizi-pkg" />
                <div class="item-animation gis-saoguang" />
            </template>
            <div
                v-if="isPropertyRedpack"
                v-show="!redpack9ShowTimer || !isShowCount"
                class="gis-red-pkg-9-words"
            ></div>
            <template v-if="item.liveStreamId">
                <div v-if="isPropertyRedpack" class="redpack-9-count-style">
                    <CountDown
                        v-if="isShowCount"
                        v-show="redpack9ShowTimer"
                        :to="item.redPackGrabTime"
                        :interval-ms="1000"
                        immediate-emit
                        :transform="redpack9CountdownFn"
                        @end="timeEnd"
                    />
                </div>
                <div v-else class="count-style">
                    <CountDown
                        v-if="isShowCount"
                        :to="item.redPackGrabTime"
                        immediate-emit
                        :transform="countdownFn"
                        @end="timeEnd"
                    />
                    <span v-else>开</span>
                </div>
            </template>
            <div
                v-if="packType == 1 && unitText && !isPropertyRedpack"
                class="pack-unit-style"
            >
                {{ unitText }}元红包
            </div>
        </div>
        <div v-if="!newStormStyle" class="gis-grab-btn grab-button">
            <!-- 头像元素 -->
            <img
                v-if="item?.authorInfo?.headUrl && !isPropertyRedpack"
                class="redpack-item-avatar"
                :src="item?.authorInfo?.headUrl"
            />
            <span class="redpack-item-btn-text">{{ btnText }}</span>
        </div>
        <!-- 来自内容页的红包 -->
        <div
            v-if="newStormStyle"
            class="new-style-grab-item"
            :class="[
                item.liveStreamId
                    ? `gis-red-pkg-${item.redPackViewType}`
                    : 'gis-pack-lock',
                index % 2 === 0
                    ? 'new-style-grab-item-first'
                    : 'new-style-grab-item-second',
                item.redPackViewType === 2 ? 'new-style-grab-item-specal' : '',
            ]"
        >
            <img
                v-if="item?.authorInfo?.headUrl"
                class="new-style-redpack-item-avatar"
                :src="item?.authorInfo?.headUrl"
            />
            <div v-if="item.liveStreamId" class="count-new-style">
                <CountDown
                    v-if="isShowCount"
                    :to="item.redPackGrabTime"
                    immediate-emit
                    :transform="countdownFn"
                    @end="timeEnd"
                />
                <span v-if="!isShowCount"> 开 </span>
            </div>
        </div>
    </div>
</template>

<style lang="less" scope>
.redpack-item {
    .gis-grab-btn {
        background: url('./assets/grab-btn_2x.png') center / 100% no-repeat;
    }
    .gis-lizi-pkg {
        // width: 72px;
        // height: 90px;
        background: url('./assets/lizi-pkg_2x.min.png') center / 100% no-repeat;
    }
    .gis-red-pkg-1 {
        background: url('./assets/red-pkg-1_2x.png') center top / 100% no-repeat;
    }
    .gis-red-pkg-2 {
        background: url('./assets/red-pkg-2_2x.png') center top / 100% no-repeat;
    }
    .gis-red-pkg-9 {
        background: url('./assets/red-pkg-9_2x.png') center top / 100% no-repeat;
    }
    .gis-red-pkg-9-words {
        background: url('./assets/red-pkg-9-words.png') center top / 100%
            no-repeat;
    }
    .gis-saoguang {
        // width: 72px;
        // height: 90px;
        background: url('./assets/saoguang_2x.min.png') center / 100% no-repeat;
    }
    .redpack-9-count-style {
        background: url('./assets/red-pkg-9-timer.png') center -0.5px / 100% no-repeat;
    }

    @keyframes breathAni {
        0% {
            transform: scale(1);
        }
        15% {
            transform: scale(0.95);
        }
        30% {
            transform: scale(1);
        }
        45% {
            transform: scale(0.95);
        }
        60%,
        75%,
        100% {
            transform: scale(1);
        }
    }

    .grab-item {
        animation: breathAni 1050ms cubic-bezier(0.33, 0, 0.67, 1) infinite;
    }
}
.kv-pkg {
    position: relative;
    width: 40px;
    height: 48px;
    margin-left: 6px;
    .grab-item {
        position: relative;
        margin-left: 3px;
        width: 35px;
        height: 43px;
    }
    .item-animation {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 44px;
        height: 46px;
        z-index: 1;
    }
    .count-style {
        position: absolute;
        width: 10px;
        height: 10px;
        line-height: 10px;
        top: 15px;
        left: 12.5px;
        font-size: 6px;
        @apply text-din text-bold;
        color: #c83c48;
        text-align: center;
    }
    .redpack-9-count-style {
        position: absolute;
        top: 11px;
        width: 100%;
        height: 12px;
        line-height: 12px;
        font-size: 12px;
        @apply flex-center-center text-bold text-din;
        color: #fffefd;
    }
    .gis-red-pkg-9-words {
        position: absolute;
        left: 5px;
        top: 7px;
        width: 22px;
        height: 22px;
    }
    .grab-button {
        position: absolute;
        @apply text-0 flex-center-center;
        left: 0;
        bottom: 0;
        width: 40px;
        height: 16px;
        line-height: 16px;
    }

    .redpack-item-avatar {
        box-sizing: border-box;
        border: 0.5px solid #eb9994;
        width: 10px;
        height: 10px;
        margin-right: 1px;
        border-radius: 100%;
    }

    .redpack-item-btn-text {
        font-size: 6px;
        @apply text-bold;
        color: #fffaec;
    }
}
.vertical-pkg {
    position: relative;
    width: 62px;
    height: 75px;
    margin-bottom: 6px;
    .grab-item {
        position: relative;
        margin-left: 4px;
        width: 54px;
        height: 66px;
    }
    .item-animation {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 72px;
        height: 90px;
        z-index: 1;
    }
    .count-style {
        position: absolute;
        width: 16px;
        height: 14px;
        line-height: 14px;
        top: 25px;
        left: 19px;
        font-size: 10px;
        @apply text-din text-bold;
        color: #c83c48;
        text-align: center;
    }
    .redpack-9-count-style {
        position: absolute;
        left: 0;
        top: 18px;
        width: 100%;
        height: 19px;
        line-height: 19px;
        font-size: 19px;
        @apply flex-center-center text-bold text-din;
        color: #fffefd;
    }
    .gis-red-pkg-9-words {
        position: absolute;
        left: 11.5px;
        top: 12px;
        width: 28px;
        height: 28px;
    }
    .grab-button {
        position: absolute;
        @apply text-0 flex-center-center;
        left: 0;
        bottom: 0;
        width: 62px;
        height: 24px;
        line-height: 24px;
    }

    .redpack-item-avatar {
        box-sizing: border-box;
        border: 1px solid #eb9994;
        width: 15px;
        height: 15px;
        margin-right: 2px;
        border-radius: 100%;
    }

    .redpack-item-btn-text {
        font-size: 10px;
        @apply text-bold;
        color: #fffaec;
    }
    .pack-unit-style {
        position: absolute;
        top: 4px;
        left: 0;
        width: 62px;
        font-family: 'Alibaba PuHuiTi 2.0';
        @apply text-10 text-bold;
        line-height: 14px;
        text-align: center;
        text-shadow: 5px 0 42px rgba(237, 112, 26, 70%);
        background: linear-gradient(180deg, #fff 25%, #fff093 92.86%);
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}

.horizontal-pkg {
    position: relative;
    margin: 0 3px;
    height: 86px;
    margin-bottom: 6px;
    .new-style-grab-item {
        position: relative;

        width: 66px;
        height: 86px;
        background: url('./assets/newstyle-red-bg.png') center top / 100%
            no-repeat;
        padding-top: 7px;
        &.new-style-grab-item-specal {
            background: url('./assets/new-style-special-red-bg.png') center top /
                100% no-repeat;
        }
        &.new-style-grab-item-first {
            transform-origin: 30px 39.25px;
            animation: newStyleFirstKeyframe 2s 0s cubic-bezier(0.4, 0, 0.2, 1)
                /* forwards */;
            animation-iteration-count: infinite;
        }

        @keyframes newStyleFirstKeyframe {
            0% {
                opacity: 1;
                transform: scale(1, 1);
            }
            50% {
                opacity: 0.6;
                transform: scale(0.8, 0.8);
            }
            100% {
                opacity: 1;
                transform: scale(1, 1);
            }
        }

        &.new-style-grab-item-second {
            transform-origin: 30px 39.25px;
            animation: newStyleFirstKeyframe 2s 1s cubic-bezier(0.4, 0, 0.2, 1)
                forwards;
            animation-iteration-count: infinite;
        }

        @keyframes newStyleSecondKeyframe {
            0% {
                opacity: 0.6;
                transform: scale(0.8, 0.8);
            }
            50% {
                opacity: 1;
                transform: scale(1, 1);
            }
            100% {
                opacity: 0.6;
                transform: scale(0.8, 0.8);
            }
        }
    }
    .count-new-style {
        position: absolute;
        width: 16px;
        height: 14px;
        line-height: 14px;
        top: 54px;
        left: 25px;
        font-size: 12px;
        @apply text-din text-bold;
        color: #af550d;
        text-align: center;
    }
    .new-style-redpack-item-avatar {
        width: 32px;
        height: 32px;
        display: block;
        border-radius: 50%;
        box-sizing: border-box;
        border: 1px solid #eb9994;
        margin: 0 auto;
    }
}
</style>
