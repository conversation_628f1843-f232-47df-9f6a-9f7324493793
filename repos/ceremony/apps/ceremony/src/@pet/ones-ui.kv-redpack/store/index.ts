/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineStore } from 'pinia';
import { postRedPkg } from '../api/index';
import type { RecommendSource } from '../interface/type';
import type { PackItem } from '../components/redpack-group/type';

export default defineStore('redPkg', {
    state: () => {
        return {
            tagLoading: false,
            redPkgList: [] as PackItem[],
            timeStamp: 0,
        };
    },
    getters: {
        liveStreamIdList: (state) => {
            const list = [...state.redPkgList];

            return list?.reverse()?.map((item: PackItem) => {
                return item.liveStreamId;
            });
        },
    },
    actions: {
        async queryInitInfo(recommendSource: RecommendSource) {
            if (this.tagLoading) {
                return;
            }
            this.tagLoading = true;

            try {
                const res = await postRedPkg(recommendSource);
                this.redPkgList = (res.data?.redPackList ||
                    []) as unknown as PackItem[];
                // this.redPkgList = []
                this.timeStamp = new Date().getTime();

                // setTimeout(() => {
                //     this.redPkgList = []
                //     console.log(1212211212)
                // }, 3000)
            } catch (error) {
                console.error(' error:', error);
            }
            this.tagLoading = false;
        },
        handleRefresh(recommendSource: RecommendSource) {
            this.queryInitInfo(recommendSource);
        },
    },
});
