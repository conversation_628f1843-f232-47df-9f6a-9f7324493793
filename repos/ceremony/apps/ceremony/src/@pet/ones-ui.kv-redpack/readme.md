以下内容也是2024 年度盛典迁移过来的

#### 红包流量位玩法

![image](https://s2-10567.kwimgs.com/kos/nlav10567/yjfaxhh1646204728629.png)

#### 玩法介绍

1.当无超级红包的时候，始终展示三个红色封面皮的普通任务红包，点击红包封面跳转上下滑红包直播间；2.当有超级(定榜)红包出现的时候，最后一个普通任务红包替换成金色的超级红包，同时金色超级(定榜)红包有粒子动效氛围渲染；点击同样是跳转至上下滑直播间；3.扩展玩法，跳转到指定的红包直播间之后自动关注主播(弹窗二次提醒跳转直播间将自动关注主播，下次是否提示)

#### 逻辑介绍

##### 1.跳转直播间协议

-   间内：kwailive://specifiedslid (该方法是滑完所有指定的红包直播间之后无法再继续滑动)

-   间外：kwai://liveaggregatesquare (该方法是先上下滑跳转指定的红包直播间，滑完之后跳转直播广场)

    -   直播间外跳转上下滑接入文档： https://docs.corp.kuaishou.com/k/home/<USER>/fcACEsapV22M5NloMD8P_bPZd#

-   跳转上下滑直播间重要参数

    -   livestreamids(直播间内): 多个直播id字符串，逗号分隔例如："123,234"
    -   targetposition(直播间内): 打开的直播间在livestreamids中的下标索引 默认从0开始
    -   sourcetype(直播间内): 来源类型(必须) 通用填269

    -   internaljump(直播间外):
        -   1.跳转直播间后执行某个行为，比如打开xx面板，关注主播等；详见路由表: https://docs.corp.kuaishou.com/k/home/<USER>/fcACRlearpMYvruRRuzfYoG3e
        -   2.internaljump 只在kwai协议中生效， kwailive只会执行一次，不会解析里面的字段二次跳转
    -   liveStreamId(直播间外): 多个直播的id字符串，用逗号分隔例如："123,234"
    -   sourceType(直播间外): 来源类型(必须)
        > 注意事项：对于Kwai scheme方式，由于数据侧后续对h5活动类跳转会直接解析整个sourceUrl，从中读取liveSource字段来区分场景，对于这类诉求scheme不必拼接sourceType。但端上为了区分这类场景，会在解析时人为兜底，判断未传sourceType时强制赋值为269(通用h5活动跳转source)
    -   selectedIndex(直播间外): 通过selectedIndex，定位到指定直播间Android: 8.3.60 IOS: 9.1.20

##### 2.前端逻辑

-   默认逻辑跳转红包直播间无需自动关注主播扩展逻辑，直播间内跳转走 kwailive://specifiedslid 协议，直播间外跳转走 kwai://liveaggregatesquare 协议 ;
-   当需要跳转自动关注主播逻辑时，因 kwailive://specifiedslid 协议不支持加internaljump参数实现自动关注，所以该情况间内间外都走kwai://liveaggregatesquare 协议;

##### 3. props

-   autoFollow
    -   是否需要自动关注主播逻辑，默认false不需要

#### 4.二维码
