import {
    activityBiz,
    activityId,
    authorId,
    liveStreamId,
    request,
} from '@alive-ui/actions';
import type { RecommendSource } from '../interface/type';
import type { PackItem } from '../components/redpack-group/type';

export interface RedPkgInfo {
    redPackList?: PackItem[];
}
const params = {
    liveStreamId,
    activityId,
    authorId,
};

export const postRedPkg = async (recommendSource: RecommendSource) => {
    const res = await request.post<RedPkgInfo>(
        '/rest/wd/live/plutus/redPack/quiz/recommendList',
        {
            ...params,
            biz: activityBiz,
            recommendSource,
        },
    );

    return res;
};
