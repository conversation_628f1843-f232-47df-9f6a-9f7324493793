<!-- eslint-disable vue/no-v-html -->
<template>
    <!-- 这两个弹窗专门是任务消费，放到任务组件中 -->
    <div class="task-modal">
        <FollowModal
            v-if="taskStore.followModal"
            :custom-class="customClass"
            :auto-close-time="autoCloseTime"
            title="关注主播领取抽奖机会"
            :biz="TAB4_BIZ"
            :show="taskStore.followModal"
            @close="taskStore.closeModal"
            @refresh="taskStore.refetchTask"
        />
        <ReservationModal
            v-if="taskStore.reservationModal"
            :show="taskStore.reservationModal"
            :auto-close-time="autoCloseTime"
            :custom-class="customClass"
            :biz="TAB4_BIZ"
            @close="taskStore.closeModal"
            @refresh="taskStore.refetchTask"
        />
    </div>
</template>

<script lang="ts" setup>
import useTaskStore from '@pet/ones-use.useTask/index';
import ReservationModal from '@pet/ones-ui.DrawMachine/modules/popups/reservation-modal.vue';
import FollowModal from '@pet/ones-ui.DrawMachine/modules/popups/follow-modal.vue';
import { TAB4_BIZ } from '@/const';

const props = withDefaults(
    defineProps<{
        customClass: string;
        autoCloseTime: number;
    }>(),
    {
        customClass: 'draw-lottery',
        autoCloseTime: 0,
    },
);

const taskStore = useTaskStore();
</script>

<style lang="less" scoped></style>
