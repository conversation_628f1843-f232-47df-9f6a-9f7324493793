<!-- eslint-disable vue/no-v-html -->
<template>
    <div class="task-container">
        <div class="task-area mt-12px mr-12px ml-12px">
            <div
                v-if="
                    taskStore.taskList.length &&
                    !taskStore.pageStatus.loading &&
                    !taskStore.pageStatus.error
                "
                class="task-list-area"
            >
                <div
                    v-for="(item, index) in taskStore.taskList"
                    :key="index"
                    class="flex task-item flex-between align-center mb-8px"
                    :class="{
                        'task-item-finished': item.status !== 1,
                    }"
                >
                    <div
                        v-show-log="taskBtnLog(item.buttonText, item.typeKey)"
                        class="left-area flex-center"
                    >
                        <div class="icon-img">
                            <img :src="item.iconUrl" :alt="item.typeKey" />
                        </div>
                        <div class="title-desc">
                            <div>
                                <span class="first-title text-16 a-text-main">{{
                                    item.name
                                }}</span>
                                <span
                                    v-show="showCount"
                                    class="task-value text-regular text-16 task-name-extra-color"
                                >
                                    {{ item.finishCount }}/{{ item.needCount }}
                                </span>
                            </div>
                            <!-- eslint-disable-next-line vue/no-v-html -->
                            <div class="sub-reward-desc-box">
                                <div
                                    class="sub-reward-desc text-regular text-12 a-text-main"
                                    v-html="itemDesc(item)"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="right-action">
                        <div
                            class="flex-center gis-action-btn text-bold text-14 a-text-button"
                            :class="{
                                'gis-action-btn-finished a-text-main':
                                    item.status !== 1,
                            }"
                            @click="
                                taskStore.handleTaskClick(
                                    item,
                                    emits,
                                    canDotask,
                                    activityId,
                                )
                            "
                        >
                            {{ item.buttonText }}
                        </div>
                    </div>
                </div>
            </div>
            <LoadingAndError
                v-else
                page-case="main"
                :page-status="taskStore.pageStatus"
                @refresh="taskStore.refetchTask"
            />
        </div>
        <TaskModal :custom-class="customClass" />
    </div>
</template>

<script lang="ts" setup>
import { watch } from 'vue';
import { TaskType } from '@pet/ones-use.useTask/services/index';
import { taskBtnLog } from '@pet/ones-use.useTask/logger/index';
import useTaskStore from '@pet/ones-use.useTask/index';
import LoadingAndError from '@pet/ones-ui.DrawMachine/components/loading-and-error/index.vue';
import TaskModal from './task-modal/index.vue';
import type { TaskItem } from '@pet/ones-use.useTask/services/index';

const props = withDefaults(
    defineProps<{
        needCoin?: boolean;
        needDailyTask?: boolean;
        needDataInit?: boolean;
        taskList?: TaskItem[];
        canDotask: boolean;
        activityId: string;
        showCount: boolean;
        customClass: string;
    }>(),
    {
        needCoin: true,
        needDailyTask: true,
        needDataInit: true,
        canDotask: true,
        activityId: '',
        taskList: () => {
            return [];
        },
        showCount: true,
        customClass: 'draw-lottery',
    },
);

const taskStore = useTaskStore();
taskStore.watchUpdate?.();

const emits = defineEmits<{
    (e: 'task:update', val: TaskItem, extra?: any): void;
}>();

// 搜索描述数据
const itemDesc = (item: any) => {
    let { desc } = item;

    if (item.typeKey === TaskType.SearchKeyWord) {
        desc = item.desc.replace(
            /%k/g,
            `<span class="strong">${item.expandMap.keyWord}</span>`,
        );
    }

    return desc;
};

watch(
    () => props.taskList,
    (val) => {
        taskStore.init(props.needDailyTask, props.needDataInit, val);
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.rotate90 {
    transform: rotate(90deg);
}
.gis-action-btn {
    width: 82px;
    height: 36px;
    background: url('./assets/action-btn_2x.png') center / 100% no-repeat;
    line-height: 36px;
    &.gis-action-btn-finished {
        background: none;
        border: 1px solid rgba(255, 223, 191, 60%);
        border-radius: 30px;
    }
}
.gis-icon-currency {
    margin-right: 4px;
}

.task-value {
    margin-left: 10px;
    font-family: 'AlteDIN1451Mittelschrift';
    line-height: 20px;
}

.task-area {
    z-index: 11;
    // min-height: 294px;
    border-radius: 8px;
    .icon-img {
        width: 60px;
        height: 60px;
        img {
            width: inherit;
            height: inherit;
        }
    }
    .first-title {
        line-height: 24px;
        font-weight: 500;
    }
    .sub-reward-desc-box {
        display: flex;
        margin-top: 6px;
        align-items: center;
    }
    .sub-reward-desc {
        display: inline-block;
        line-height: 18px;
        opacity: 0.6;
    }
    .task-item {
        width: 100%;
        padding: 10px 0;
        &:last-child {
            margin-bottom: 0;
        }
        .title-desc {
            margin-left: 8px;
        }
        .strong {
            font-size: bold;
            color: #ff5477;
        }

        &.task-item-finished {
            .left-area {
                opacity: 60%;
            }

            .gis-action-btn {
                opacity: 50%;
            }
        }
    }
}
</style>
