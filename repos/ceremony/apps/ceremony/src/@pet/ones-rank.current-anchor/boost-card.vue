<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';
import { BoostCardStatus, BoostCardType } from './schema';
import { AddCardStatus } from '@/modules/main/components/buffer-card/schemas';

const idPrefix = ref('every-card-tool-id');
const props = defineProps<{
    data: any;
}>();
const emits = defineEmits<{
    (e: 'change', index: number): void;
}>();
const selectIndex = ref(0);
const onCardClick = (index: number) => {
    selectIndex.value = index;
    emits('change', index);
};
const overlayLeft = ref(false);
const overlayRight = ref(true);
const boxRef = ref<HTMLElement | null>(null);
const showBufferCard = (item: {
    status: BoostCardStatus;
    source: BoostCardType;
}) => {
    return (
        item.status === BoostCardStatus.obtained &&
        item.source === BoostCardType.carousel
    );
};
watch(
    () => props.data,
    (val, oldVal) => {
        console.log('data', val);
        if (val?.length && !oldVal?.length) {
            const index = val.findIndex(
                (item: { status: BoostCardStatus }) =>
                    item.status === BoostCardStatus.underWay,
            );
            // 最后一个结果项
            const overList = val.filter(
                (item: { status: BoostCardStatus }) =>
                    item.status !== BoostCardStatus.notOpen &&
                    item.status !== BoostCardStatus.underWay,
            );
            const overIndex = overList.length ? overList.length - 1 : 0;
            selectIndex.value = index === -1 ? overIndex : index;
            emits('change', selectIndex.value);
            // 自动滚动到显示位置
            nextTick(() => {
                const id = `${idPrefix.value}${selectIndex.value}`;
                const el = document.getElementById(id);
                const { left = 0, width = 0 } =
                    el?.getBoundingClientRect() || {};
                if (el && left) {
                    boxRef.value?.scrollTo({
                        left: left - width,
                        behavior: 'smooth',
                    });
                }
            });
        }
    },
    { immediate: true, deep: true },
);
const onScroll = (e: { target: any }) => {
    const dom = e.target;
    if (!dom) {
        return;
    }

    const { scrollLeft, scrollWidth } = dom;

    const { width } = dom.getBoundingClientRect() || {};

    const toLeft = 10;
    if (scrollLeft < toLeft) {
        overlayLeft.value = false;
    } else {
        overlayLeft.value = true;
    }
    if (scrollWidth - width - scrollLeft < toLeft) {
        overlayRight.value = false;
    } else {
        overlayRight.value = true;
    }
    return;
};
// onMounted(() => {
//     boxRef.value?.addEventListener('scroll', onScroll);
// });
// onBeforeMount(() => {
//     boxRef.value?.removeEventListener('scroll', onScroll);
// });
</script>
<template>
    <div v-if="data?.length" class="boost-card-box">
        <div
            :class="{
                overlay: true,
                'overlay-left': overlayLeft,
            }"
        ></div>
        <div
            :class="{
                overlay: true,
                'overlay-right': overlayRight,
            }"
        ></div>
        <div
            ref="boxRef"
            v-pcDirectives:scroll="onScroll"
            class="boost-card"
            @scroll="onScroll"
        >
            <div
                v-for="(item, index) in data"
                :id="`${idPrefix}${index}`"
                :key="index"
                :class="{
                    'every-card': true,
                    'a-bg-substrate': selectIndex === index,
                }"
                @click="() => onCardClick(index)"
            >
                <div
                    :class="{
                        [`every-card-bg-${item.status}`]: true,
                        'buffer-card-no-play':
                            showBufferCard(item) &&
                            item?.peakBattleInfo?.additionInfo
                                ?.addCardStatus === AddCardStatus.NotObtained,
                        'buffer-card-play':
                            showBufferCard(item) &&
                            item?.peakBattleInfo?.additionInfo
                                ?.addCardStatus !== AddCardStatus.NotObtained,
                    }"
                ></div>
                <div
                    :class="{
                        'status-desc': true,
                        'a-text-main': true,
                        'select-status-desc': selectIndex === index,
                    }"
                >
                    {{ item.statusDesc }}
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.boost-card-box {
    position: relative;
}
.boost-card {
    width: 100%;
    overflow-x: auto;
    display: flex;
    padding: 0;
    position: relative;
    &::-webkit-scrollbar {
        width: 0;
        background: transparent;
    }
}
.every-card {
    width: 64px;
    height: 64px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 8px;
    flex-shrink: 0;
}
.every-card-bg-1,
.every-card-bg-2,
.every-card-bg-3,
.every-card-bg-4,
.every-card-bg-5 {
    width: 40px;
    height: 40px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    margin: 2px auto;
}
.every-card-bg-1 {
    background-image: url('./assets/boost-card/not-get.png');
}
.every-card-bg-2 {
    background-image: url('./assets/boost-card/obtained.png');
}
.every-card-bg-3 {
    background-image: url('https://p4-live.wskwai.com/kos/nlav12706/ceremony2024/effect.c2e28a4c6a0eb9fb.png');
}
.every-card-bg-4 {
    background-image: url('./assets/boost-card/not-open.png');
}
.every-card-bg-5 {
    background-image: url('./assets/boost-card/under-way.png');
}
.buffer-card-no-play {
    background-image: url('./assets/boost-card/no-play-icon.png');
}
.buffer-card-play {
    background-image: url('./assets/boost-card/paly-icon.png');
}
.status-desc {
    font-size: 10px;
    line-height: 14px;
    height: 14px;
}
.select-status-desc {
    font-size: 10px;
    font-weight: bold;
}
.overlay {
    position: absolute;
    display: none;
    top: 0;
    background-image: url(https://p4-live.wskwai.com/kos/nlav12706/ceremony2024/zhe-zhao.935bcf64478efa1e.png);
    height: 100%;
    width: 18px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 1;
}
.overlay-left {
    left: -1px;
    display: block;
}
.overlay-right {
    right: -1px;
    transform: rotateY(180deg);
    display: block;
}
</style>
