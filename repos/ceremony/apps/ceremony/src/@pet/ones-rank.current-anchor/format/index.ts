export const currentAnchorFormat = (
    rankInfo: any = {},
    indexInfoData: any = {},
) => {
    return {
        anchorRank: {
            h5RankShowIndex:
                rankInfo?.bottomInfo?.itemRankInfo?.h5RankShowIndex,
            h5ShowHintScore: rankInfo?.bottomInfo?.h5ShowHintScore,
            displayHint: rankInfo?.bottomInfo?.displayHint,
            authorRankTip: rankInfo?.bottomInfo?.authorRankTip,
        },
        additionInfo: rankInfo?.extraData?.additionInfo,
        authorAdditionInfo: rankInfo.bottomInfo?.authorAdditionInfo,
        anchorData: {
            ...(rankInfo?.bottomInfo?.itemRankInfo?.item ||
                rankInfo?.extraData?.currentAuthorInfo ||
                {}),
            liveStreamId: rankInfo?.bottomInfo?.itemRankInfo?.liveStreamId,
        },
        isReplay: rankInfo?.extraData?.isReplay,
        isJoinCurrRank: rankInfo?.extraData?.isJoinCurrRank,
        isDirectPromotionFinal: rankInfo?.extraData?.isDirectPromotionFinal,
        isRepechageActivity: rankInfo?.extraData?.isRepechageActivity,
        isJoinActivity: rankInfo?.extraData?.isJoinActivity,
        isJoinShadowActivity: rankInfo?.extraData?.isJoinShadowActivity,
        changeLane: rankInfo?.extraData?.changeLane,
        repechageResult: rankInfo?.extraData?.repechageResult,
        clearingEndTime: rankInfo?.extraData?.clearingEndTime,
        haveRankData: rankInfo?.rankList?.length > 0,
        extraData: rankInfo?.extraData,
        rankId: rankInfo?.rankId,
        additionCardShowRefreshTime:
            rankInfo?.extraData?.additionCardShowRefreshTime,
        rankInfo,
        stageType: indexInfoData.anchorStageType,
        isAfter23: rankInfo?.bottomInfo?.isAfter23,
        showWithExtraData: !!rankInfo?.extraData,
    };
};

export const homePkDataFormat = (info: any) => {
    let { rankUserList = [{}, {}] } = info?.peakBattleInfo || {};
    rankUserList = rankUserList || [{}, {}];

    const authorInfo =
        rankUserList.find((item: { currAuthor: any }) => item.currAuthor) || {};
    const opponentInfo =
        rankUserList.find((item: { currAuthor: any }) => !item.currAuthor) ||
        {};

    return {
        authorInfo: {
            user_id: authorInfo?.userInfo?.user_id,
            headurl: authorInfo?.userInfo?.headurl,
            user_name: authorInfo?.userInfo?.user_name,
        },
        authorLiveStreamId: authorInfo?.liveStreamId,
        raceStatus: info?.peakBattleInfo?.raceStatus,
        status: info?.peakBattleInfo?.status,
        authorScore: authorInfo?.score,
        authorScoreH5Show: authorInfo?.scoreH5Show,
        opponentScore: opponentInfo?.score,
        opponentScoreH5Show: opponentInfo?.scoreH5Show,
        opponentInfo: {
            user_id: opponentInfo?.userInfo?.user_id,
            headurl: opponentInfo?.userInfo?.headurl,
            user_name: opponentInfo?.userInfo?.user_name,
        },
        opponentLiveStreamId: opponentInfo?.liveStreamId,
        addCardTimes: info?.baoJiCoefficient,
        baoJiTime: info?.peakBattleInfo.baoJiTimes,
    };
};

export const homePkDataFormat2 = (info: any) => {
    const hotRaces = info?.peakBattleInfo.hotPeakBattle.hotRaces || [];
    // rankUserList = rankUserList || [{}, {}];
    // 只展示第一个
    const reaceInfo = hotRaces?.length > 0 ? hotRaces[0] : {};

    return {
        authorInfo: {
            user_id: reaceInfo?.authorInfo?.user_id,
            headurl: reaceInfo?.authorInfo?.headurl,
            user_name: reaceInfo?.authorInfo?.user_name,
        },
        authorLiveStreamId: reaceInfo?.authorLiveStreamId,
        raceStatus: reaceInfo?.raceStatus,
        authorScore: reaceInfo?.authorScore,
        authorScoreH5Show: reaceInfo?.authorScoreH5Show,
        opponentScore: reaceInfo?.opponentScore,
        opponentScoreH5Show: reaceInfo?.opponentScoreH5Show,
        opponentInfo: {
            user_id: reaceInfo?.opponentInfo?.user_id,
            headurl: reaceInfo?.opponentInfo?.headurl,
            user_name: reaceInfo?.opponentInfo?.user_name,
        },
        opponentLiveStreamId: reaceInfo?.opponentLiveStreamId,
        source: info?.source,
        // addCardTimes: info?.baoJiCoefficient,
        // baoJiTime: info?.baoJiTimes,
    };
};
