export enum BoostCardStatus {
    // 1错失,2获得,3对决中,4未开始,5获得
    notGet = 1,
    obtained = 2,
    underWay = 3,
    notOpen = 4,
    finished = 5,
}

export enum BoostCardType {
    // 1凝聚力,2小时榜,3,车轮战
    aggregation = 1,
    hourRank = 2,
    carousel = 3,
}

export interface AuthorAdditionInfo {
    additionInfo: {
        showAdditionRate: string;
        additionRate: string;
        additionFactor: string;
    };
    state: BoostState;
    tips: string;
    prefixText: string;
}

export enum BoostState {
    // 0 待生效 1 进行中 2加成中
    notEffective = 0,
    underWay = 1,
    boosting = 2,
}
// export enum BoostCardStatusClass {
//     // 1错失,2获得,3对决中,4未开始
//      = 'not-get',
// }

export enum StageTypes {
    // 公会赛
    guild = 130,
    // 公会赛弹幕赛道
    guildBullet = 140,
    // 战队赛
    team = 220,
}
