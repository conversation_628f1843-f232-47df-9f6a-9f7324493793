<script lang="ts" setup>
import { useRouter, type LocationQueryValueRaw } from 'vue-router';
import { computed, ref, defineAsyncComponent, onMounted, watch } from 'vue';
import { uniqueId } from 'lodash-es';
// import LiveIcon from '@pet/ones-ui.dynamic-living-icon/index.vue';
import { isDef } from '@pet/ones-use.usePageModulsConfig';
import useKConfBatch from '@pet/ones-use.useKconfBatch';
import { sendClickLogImmediately as sendClick } from '@gundam/weblogger';
import { Avatar as APAvatar, GiftButton } from '@alive-ui/pro';
import { Right } from '@alive-ui/icon';
import { Marquee as AMarquee } from '@alive-ui/base';
import { authorId, isOutLiveRoom, liveStreamId } from '@alive-ui/actions';
import { BoostCardType, BoostCardStatus, BoostState } from './schema';
import { StageTypes, type AuthorAdditionInfo } from './schema';
import AttritionWarfare from './branch-line/attrition-warfare.vue';
import BoostCard from './boost-card.vue';
import type {
    PeakRankTask,
    TrafficPrivilegeUnlock,
} from '@pet/ones-rank.schema/query-rank';

const router = useRouter();
const kconf = useKConfBatch();
const guildTypes = computed(() => {
    return kconf.kconfData?.common?.stageGroup?.gonghuiGroup ?? [];
});
const assistConfig = computed(() => {
    return (
        kconf.kconfData?.mainPage?.curAnchorCard ?? {
            giftId: '',
            assistBtn: '助力入场',
        }
    );
});

const Game = defineAsyncComponent(() => {
    return import('@pet/ones-ui.cur-anchor-game-card/index.vue');
});
const MultPk = defineAsyncComponent(() => {
    return import('@pet/ones-rank.mult-pk/index.vue');
});
const CohesionCard = defineAsyncComponent(() => {
    return import('@/modules/main/components/cohesion-card/index.vue');
});

const BufferCard = defineAsyncComponent(() => {
    return import('@/modules/main/components/buffer-card/index.vue');
});
const TeamCard = defineAsyncComponent(() => {
    return import('@pet/ones-rank.anchor-team-card/index.vue');
});

enum TASK_LOCK {
    // 未解锁
    NONE = 0,
    // 已解锁
    DOING = 1,
    // 任务完成
    DONE = 2,
}

const emits = defineEmits<{
    (e: 'timeover'): void;
}>();
const props = withDefaults(
    defineProps<{
        data: {
            rankId: LocationQueryValueRaw;
            anchorData: {
                itemId: string;
                headUrl: string;
                liveStreamId: string;
                liveStreamIdList: string[];
                mysteryMan: boolean;
            };
            anchorRank: {
                displayHint: string;
                h5ShowHintScore: string;
                h5RankShowIndex: string;
                authorRankTip: string;
            };
            additionInfo?: any;
            authorAdditionInfo: AuthorAdditionInfo;
            isReplay: boolean;
            isJoinCurrRank: boolean;
            isDirectPromotionFinal: boolean;
            isRepechageActivity: boolean;
            isJoinActivity: boolean;
            isJoinShadowActivity: boolean;
            changeLane: boolean;
            repechageResult: string;
            clearingEndTime: boolean;
            haveRankData: boolean;
            extraData?: any;
            additionCardShowRefreshTime?: number;
            rankInfo?: any;
            showWithExtraData?: boolean;
            inGame?: boolean;
            authorStatusTip?: string;
            // isAfter23: boolean;
        };
        teamData?: any;
        stageType: any;
        /**
         * 决赛奖励
         */
        peakRankTask?: PeakRankTask;
        useCardClass?: boolean;
        /**
         * 隐藏任务解锁
         */
        trafficPrivilegeUnlock?: TrafficPrivilegeUnlock;
        pageType?: string;
    }>(),
    {
        teamData: () => ({}),
        rankInfo: () => ({}),
        trafficPrivilegeUnlock: () => ({}) as TrafficPrivilegeUnlock,
        useCardClass: true,
        pageType: '',
    },
);
// 区分双人和多人对决当前是否已开始
const isStart = ref(true);
const hintScoreText = computed(() => {
    const { displayHint = '', h5ShowHintScore = '' } =
        props.data?.anchorRank || {};
    return displayHint?.replace('${hintScore}', h5ShowHintScore);
});

const boostCardIndex = ref(0);
const logs = (logTitle = '', extra = {}) => {
    return {
        action: 'OP_ACTIVITY_MORE_PLAY_CARD',
        params: {
            type: logTitle,
            live_stream_id: liveStreamId,
            ...extra,
        },
    };
};
const anchorData = computed(() => props.data?.anchorData);

// 决赛日未达门槛
const isNotReachFinal = computed(() => {
    return (
        !!props.peakRankTask &&
        Object.keys(props.peakRankTask).length &&
        !props.peakRankTask.finishTask
    );
});

//  是否淘汰
const isNotInGame = computed(() => {
    const { isJoinActivity, isDirectPromotionFinal, inGame, isReplay } =
        props.data || {};
    return (
        !isReplay &&
        isDef(inGame) &&
        isJoinActivity &&
        !isDirectPromotionFinal &&
        !inGame
    );
});
const showCurrentAnchor = computed(() => {
    const {
        isReplay,
        isJoinActivity,
        isJoinCurrRank,
        isDirectPromotionFinal,
        isRepechageActivity,
        isJoinShadowActivity,
        clearingEndTime,
        haveRankData,
        showWithExtraData,
    } = props.data || {};
    if (isOutLiveRoom || clearingEndTime) {
        return false;
    }

    if (isNotReachFinal.value) {
        return true;
    }

    if (!haveRankData || !showWithExtraData) {
        return false;
    }
    // 逻辑流程图 https://docs.corp.kuaishou.com/d/home/<USER>
    return (
        (!isReplay && isJoinActivity && isJoinCurrRank) ||
        (!isReplay &&
            isJoinActivity &&
            !isJoinCurrRank &&
            isDirectPromotionFinal) ||
        (isReplay && isRepechageActivity && isJoinCurrRank) ||
        (!isReplay && !isJoinActivity && !isJoinShadowActivity) ||
        isNotInGame.value
    );
});
// 是否是复活赛
const authorAdditionInfo = computed(() => {
    return props.data?.authorAdditionInfo;
});
const isRepechageActivityText = computed(() => {
    const { isReplay, isJoinCurrRank, isRepechageActivity } = props.data || {};
    return isReplay && isRepechageActivity && isJoinCurrRank;
});
const countTime = ref(0); // 倒计时
let timer: any = null;
const getTimeDetail = (item: {
    status?: BoostCardStatus;
    source?: BoostCardType;
    peakBattleInfo?: any;
}) => {
    const { pkEndTime } = item?.peakBattleInfo || {};
    const nowTime = Date.now();
    const time = pkEndTime - nowTime;
    return {
        time,
        isVacancyTime: nowTime > pkEndTime,
    };
};
const clearTimer = () => {
    if (timer) {
        clearTimeout(timer);
        timer = null;
    }
};

const handleCountDown = (index: number) => {
    // 倒计时
    const { additionInfo, additionCardShowRefreshTime } = props.data || {};
    if (!additionInfo?.[index]?.peakBattleInfo) {
        return;
    }
    const { time } = getTimeDetail(additionInfo[index]);
    // 时间差超过1s就定义为超时了
    if (time < -1000) {
        return;
    }
    // 时间差在0-1s氛围内定义为结束
    if (
        time <= 0 &&
        time > -1000 &&
        additionInfo[index].status === BoostCardStatus.underWay
    ) {
        // 扔出事件,停止计时
        setTimeout(() => {
            emits('timeover');
        }, additionCardShowRefreshTime || 0);
        clearTimer();
        return;
    }
    clearTimer();
    countTime.value = time;
    timer = setTimeout(() => {
        handleCountDown(index);
    }, 1000);
};
const onBoostCardChange = (index: number) => {
    boostCardIndex.value = index;
    handleCountDown(index);
};

// 获取卡片索引
const getCardIndex = (list: any[]) => {
    // 获取进行中的卡片索引
    const underWayIndex = list.findIndex(
        (item: { status: BoostCardStatus }) =>
            item.status === BoostCardStatus.underWay,
    );

    // 获取已完成的卡片列表
    const overList = list.filter(
        (item: { status: BoostCardStatus }) =>
            item.status !== BoostCardStatus.notOpen &&
            item.status !== BoostCardStatus.underWay,
    );

    // 如果没有进行中的卡片,则显示最后一个已完成的卡片
    const overIndex = overList.length ? overList.length - 1 : 0;
    if (underWayIndex === -1) {
        isStart.value = false;
    }
    return underWayIndex === -1 ? overIndex : underWayIndex;
};

// 定位卡片
const initCard = () => {
    const { additionInfo } = props.data || {};
    if (additionInfo?.length) {
        boostCardIndex.value = getCardIndex(additionInfo);
        onBoostCardChange(boostCardIndex.value);
    }
};

// 监听数据变化
watch(
    () => props.data?.additionInfo,
    (val, oldVal) => {
        // 先前有&& !oldVal?.length
        if (val?.length) {
            boostCardIndex.value = getCardIndex(val);
            onBoostCardChange(boostCardIndex.value);
        }
    },
    { immediate: true, deep: true },
);

initCard();

function formatTime(ms: number) {
    const totalSeconds = Math.floor(ms / 1000); // 将毫秒转换为秒
    const minutes = Math.floor(totalSeconds / 60); // 计算分钟
    const seconds = totalSeconds % 60; // 计算剩余秒数

    // 使用 padStart 补零，确保两位数格式
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(seconds).padStart(2, '0');

    return `${formattedMinutes}:${formattedSeconds}`;
}
const showLoopTime = (item: {
    status: BoostCardStatus;
    source: BoostCardType;
}) => {
    const { isVacancyTime } = getTimeDetail(item);
    if (isVacancyTime) {
        return false;
    }
    if (
        item.status === BoostCardStatus.underWay &&
        (item.source === BoostCardType.hourRank ||
            item.source === BoostCardType.carousel)
    ) {
        return true;
    }
    return false;
};
const showTag = (item: {
    [
        x: string
    ]: // / <reference types="../../../node_modules/.vue-global-types/vue_3.4_false.d.ts" />
    any;
    source: any;
    status: any;
}) => {
    // 凝聚力不显示
    if (item.source === BoostCardType.aggregation) {
        return false;
    }
    // 车轮战显示逻辑
    if (
        item.source === BoostCardType.carousel &&
        (item.status === BoostCardStatus.finished ||
            item.status === BoostCardStatus.obtained)
    ) {
        return false;
    }
    // 小时榜显示逻辑
    if (item.source === BoostCardType.hourRank) {
        return !!item?.smallTitle;
    }
    return !!item?.smallTitle;
};
const goPage = (item: { source: BoostCardType }) => {
    // 小时榜
    // 车轮战
    const { stageType } = props;
    if (
        item.source === BoostCardType.hourRank ||
        item.source === BoostCardType.carousel
    ) {
        router.push({
            name: 'peak-battle',
            query: {
                peakType: 0,
                stageType,
            },
        });
        const logTitle =
            item.source === BoostCardType.hourRank
                ? '四人巅峰对决'
                : '双人巅峰对决';

        // 点击事件埋点
        sendClick(
            logs(logTitle, {
                btn_type: 'TOP_RIGHT_BUTTON',
            }),
        );
    }
    if (item.source === BoostCardType.aggregation) {
        router.push({
            name: 'cohesion',
            query: {
                showBack: 'true',
                stageType,
            },
        });
    }
};

/**
 * 是否在决赛任务阶段
 */
const isInPeakTask = computed(() => {
    const { trafficPrivilegeUnlock } = props;
    return isDef(trafficPrivilegeUnlock.status);
});

const jumpToAdditionPage = () => {
    router.push({
        name: 'author-addition',
        query: {
            rankId: props.data.rankId,
        },
    });
    if (isInPeakTask.value) {
        let type: number;
        switch (props.trafficPrivilegeUnlock.status) {
            case TASK_LOCK.DOING:
                type = 3;
                break;
            case TASK_LOCK.DONE:
                type = 4;
                break;
            default:
                type = 2;
                break;
        }

        sendClick({
            action: 'OP_ACTIVITY_HIDDEN_TASK_CARD',
            params: {
                type,
                btn_type: 'MORE',
            },
        });
    } else {
        sendClick(logs('加成明细', { btn_type: 'BOOST_BUTTON' }));
    }
};
const onBufferCardEnd = () => {
    emits('timeover');
};
const showTeamCard = computed(() => {
    // 预热：isPublicityPeriod： false
    // 正式：competing true
    // 公示期：isPublicityPeriod true && competing false
    const { teamData } = props;

    // 活动期有战队成员信息
    const { isReplay, isJoinActivity, isJoinCurrRank } = props.data || {};
    if (!teamData?.bottomInfo || !teamData?.teamPlay) {
        return false;
    }
    // 首页卡，只有预热和比赛期，才有
    if (props.pageType === 'home') {
        // 正赛期，且有sponsors
        if (
            teamData.teamPlay?.extInfo?.competing &&
            teamData.bottomInfo?.itemRankInfo?.sponsors?.length
        ) {
            return true;
        }
        // 预热期 reurn true
        if (
            teamData.teamPlay?.extInfo &&
            !teamData.teamPlay?.extInfo?.isPublicityPeriod
        ) {
            return true;
        }
        // 其他情况不展示
        return false;
    }
    // 二级页卡
    if (
        !isReplay &&
        isJoinActivity &&
        isJoinCurrRank &&
        teamData.bottomInfo?.itemRankInfo?.sponsors?.length
    ) {
        return true;
    }
    // 正赛期需要有bottomInfo?.itemRankInfo?.sponsors才返回 true
    if (
        teamData.teamPlay?.extInfo?.competing &&
        !teamData.bottomInfo?.itemRankInfo?.sponsors?.length
    ) {
        return false;
    }
    if (teamData.teamPlay) {
        return true;
    }
    return false;
});
// 以上为代码老逻辑，以下为GPT逻辑梳理，上面逻辑暂时不动
// const showTeamCard = computed(() => {
//     const { teamPlay, bottomInfo, showTeamCard } = props.teamData;

//     if (!teamPlay?.extInfo) return false; // 基础条件

//     const { competing } = teamPlay.extInfo;

//     // 条件1：如果正在比赛但不显示队伍卡片
//     if (competing && !showTeamCard) return false;

//     // 条件2：如果正在比赛但没有赞助商
//     if (competing && !(bottomInfo?.itemRankInfo?.sponsors?.length)) return false;

//     return true; // 通过所有条件，显示队伍卡片
// });
// const selectData = computed(() => {
//     return props.data?.additionInfo[boostCardIndex.value];
// });
// 是否是公会赛
const isGuildRank = computed(() => {
    return guildTypes.value.includes(props.stageType);
});

// 23点之后的状态
// const isAfter23 = computed(() => {
//     return props.data?.authorAdditionInfo?.state === BoostState.underWay;
// });
</script>

<template>
    <!-- 如果战队赛和主播卡片都不显示，背景图不渲染 -->
    <div
        v-if="showCurrentAnchor || showTeamCard"
        class="current-anchor"
        :class="{ 'card-bg-img': useCardClass }"
    >
        <div v-if="showCurrentAnchor" class="top-content">
            <div class="left-content">
                <!-- 公会赛不能跳转p页和直播间 -->
                <APAvatar
                    size="xs"
                    :user-id="isGuildRank ? null : anchorData?.itemId"
                    :head-url="anchorData?.headUrl"
                    :live-stream-id="
                        isGuildRank ? null : anchorData?.liveStreamId
                    "
                    :is-mystery-man="anchorData?.mysteryMan"
                >
                    <!-- <template #bottomRight>
                    <LiveIcon v-if="anchorData?.liveStreamId" />
                </template> -->
                </APAvatar>
                <div
                    class="rank-detail"
                    :style="{
                        justifyContent: hintScoreText
                            ? 'space-between'
                            : 'space-around',
                    }"
                >
                    <div class="current-ranking a-text-main">
                        <!-- 决赛日未达门槛 -->
                        <div
                            v-if="isNotReachFinal"
                            class="leading-24px text-14px font-bold flex-start-center"
                        >
                            {{ peakRankTask?.desc }}
                        </div>
                        <!-- 是否是复活赛 -->
                        <div v-else-if="isRepechageActivityText">
                            {{ data?.repechageResult }}
                        </div>
                        <div
                            v-else-if="
                                !data?.isJoinActivity &&
                                !data?.isJoinShadowActivity
                            "
                            class="text-bold"
                        >
                            主播未参赛
                        </div>
                        <!-- 直通决赛 -->
                        <div
                            v-else-if="
                                data?.isDirectPromotionFinal &&
                                !data?.isJoinCurrRank
                            "
                            class="text-bold"
                        >
                            恭喜直通决赛
                        </div>
                        <div v-else-if="isNotInGame" class="text-bold">
                            主播未晋级
                        </div>
                        <!-- 23点之后 -->
                        <!-- <div v-else-if="isAfter23" class="left-top">
                                <div
                                    class="addition-rate-num a-text-highlight text-din"
                                >
                                    {{
                                        data.authorAdditionInfo.additionInfo
                                            .showAdditionRate
                                    }}
                                </div>
                                <div class="text-14 ml-2px">
                                    {{ authorAdditionInfo?.prefixText }}
                                </div>
                            </div> -->
                        <!-- 排名 -->
                        <div v-else class="left-top">
                            <div>
                                第<span class="text-din">
                                    {{
                                        data?.anchorRank?.h5RankShowIndex ||
                                        '99+'
                                    }} </span
                                >名
                            </div>
                            <div class="author-rank-tip a-bg-substrate">
                                {{ data?.anchorRank?.authorRankTip }}
                            </div>
                        </div>
                    </div>
                    <slot name="ca-hint">
                        <div
                            v-if="isNotReachFinal"
                            class="flex-start-center text-12px leading-18px"
                        >
                            <div class="a-text-main-o2">
                                {{ peakRankTask?.h5ShowScore }}/
                            </div>
                            <div class="a-text-main">
                                {{ peakRankTask?.targetH5ShowScore }}
                            </div>
                        </div>
                        <!-- <div
                            v-else-if="isNotInGame"
                            class="rank-gap a-text-main"
                        >
                            {{ data.authorStatusTip }}
                        </div> -->
                        <div
                            v-else-if="hintScoreText && !isNotInGame"
                            class="rank-gap a-text-main"
                        >
                            {{ hintScoreText }}
                        </div>
                    </slot>
                </div>
            </div>
            <slot name="ca-right-content">
                <div
                    v-if="
                        authorAdditionInfo?.additionInfo?.showAdditionRate ||
                        isNotReachFinal
                    "
                    class="right-content"
                >
                    <!-- 未达决赛任务门槛，助力入场 -->
                    <GiftButton
                        v-if="isNotReachFinal"
                        v-pcDirectives:hide
                        v-click-log="{
                            action: 'OP_ACTIVITY_HIDDEN_TASK_CARD',
                            params: {
                                btn_type: 'HELP',
                                type: 1,
                            },
                        }"
                        :gift-id="assistConfig.giftId"
                        class="reach-final-btn"
                    >
                        {{ assistConfig.assistBtn }}
                    </GiftButton>
                    <div
                        v-else
                        class="jump-entrance a-text-main"
                        @click="jumpToAdditionPage()"
                    >
                        <!-- 23点～24点 加成生效时不展示具体加成率 -->
                        <div
                            class="addition-rate-num a-text-highlight text-din mr-2px"
                        >
                            {{
                                authorAdditionInfo?.additionInfo
                                    ?.showAdditionRate
                            }}
                        </div>
                        <!-- {{
                            isAfter23
                                ? '加成明细'
                                : authorAdditionInfo?.prefixText
                        }} -->
                        <span class="text-bold">
                            {{ authorAdditionInfo?.prefixText }}</span
                        >
                        <Right class="title-arrow ml-4px" />
                    </div>
                    <div
                        v-if="
                            authorAdditionInfo?.tips?.length && !isNotReachFinal
                        "
                        class="marquee-list-box"
                    >
                        <AMarquee>
                            <div
                                v-for="(
                                    item, index
                                ) of authorAdditionInfo?.tips"
                                :key="index"
                                class="score-text a-text-main"
                            >
                                {{ item }}
                            </div>
                        </AMarquee>
                    </div>
                </div>
            </slot>
        </div>
        <!-- <BoostCard
            class="content"
            :data="data?.additionInfo"
            @change="onBoostCardChange"
        /> -->
        <!-- <div v-for="(item, index) in data?.additionInfo" :key="index">
            <div
                v-if="index === boostCardIndex"
                class="content-card-box"
            >
                header
                <div class="content-card-box__header">
                    <div class="content-card-box__header__left">
                        <div class="top-title">{{ item.title }}</div>
                        小时榜标签
                        <div
                            v-if="showTag(item)"
                            class="mult-pk-tag a-text-highlight"
                        >
                            {{ item.smallTitle }}
                        </div>
                        倒计时
                        <div v-if="showLoopTime(item)" class="time">
                            {{ formatTime(countTime) }}
                        </div>
                    </div>
                    <div
                        class="content-card-box__header__right opacity-60"
                        @click="() => goPage(item)"
                    >
                        <AMarquee>
                            <div
                                v-for="d in item.desc"
                                :key="uniqueId(d)"
                                class="tip-text a-text-main"
                            >
                                {{ d }}
                                <Right class="title-arrow" />
                            </div>
                        </AMarquee>
                    </div>
                </div>
                车轮战 1v1
                <AttritionWarfare
                    v-if="
                        item.source === BoostCardType.carousel &&
                        item.status !== BoostCardStatus.obtained
                    "
                    v-show-log="
                        logs('双人巅峰对决', {
                            status: isStart,
                        })
                    "
                    v-click-log="
                        logs('双人巅峰对决', {
                            btn_type: 'DOUBLE_CLICK_BUTTON',
                        })
                    "
                    :stage-type="data.stageType"
                    :data="item"
                />
                小时榜 多人对决
                <MultPk
                    v-if="item.source === BoostCardType.hourRank"
                    v-show-log="
                        logs('四人巅峰对决', {
                            status: isStart,
                        })
                    "
                    v-click-log="logs('四人巅峰对决')"
                    :source-data="item"
                />
                凝聚力
                <CohesionCard
                    v-if="item.source === BoostCardType.aggregation"
                    v-show-log="logs('凝聚力挑战')"
                    v-click-log="logs('凝聚力挑战')"
                    :cohesion-info="item?.cohesionChallengeInfo"
                />
                加成卡
                <BufferCard
                    v-if="
                        item.status === BoostCardStatus.obtained &&
                        item.source === BoostCardType.carousel
                    "
                    v-show-log="logs('加成卡')"
                    v-click-log="logs('加成卡')"
                    :item="item?.peakBattleInfo?.additionInfo"
                    @end-time="onBufferCardEnd"
                    @use-buff-card="onBufferCardEnd"
                />
                <slot name="branch-line" :branch-line="item"></slot>
            </div>
        </div> -->

        <!-- <Game
            v-if="data?.extraData?.showSmallBell"
            v-show-log="logs('小铃铛')"
            v-click-log="logs('小铃铛')"
            :rank-info="data.rankInfo"
        /> -->
        <div v-if="showTeamCard" class="forward-anchor-bottom-area">
            <slot name="forward-anchor-bottom-area"></slot>
        </div>
        <div v-if="showCurrentAnchor">
            <slot name="anchor-bottom-area"></slot>
        </div>
        <div v-if="showTeamCard" class="behind-anchor-bottom-area">
            <slot name="behind-anchor-bottom-area"> </slot>
        </div>
    </div>
    <div v-else>
        <slot name="other-bottom-area"></slot>
    </div>
</template>

<style lang="less" scoped>
.behind-anchor-bottom-area,
.forward-anchor-bottom-area {
    margin-top: 20px;
}
.current-anchor {
    @apply a-bg-part;
    --top-anchor-area-width: 358px;
    margin: 10px 16px;
    padding: 16px 12px;
    box-sizing: border-box;
    background-size: 100%;
    background-repeat: no-repeat;
    border-radius: 16px;

    .top-content {
        height: 44px;
        width: var(--top-anchor-area-width);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left-content {
            display: flex;
            align-items: center;
            :deep(.gis-live-corner) {
                zoom: 0.75;
            }
        }
        .rank-detail {
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .current-ranking {
            font-size: 16px;
            font-weight: 400;
            line-height: 1;
            text-align: left;
        }
        .rank-gap {
            font-size: 12px;
            font-weight: 400;
            line-height: 1;
            text-align: left;
            opacity: 0.6;
            margin-top: 8px;
        }
        .rank-detail {
        }
    }
    .right-content {
        text-align: right;

        display: flex;
        flex-direction: column;
        justify-content: space-between;
        line-height: 1;
        height: 36px;

        .jump-entrance {
            font-weight: 400;
            font-size: 14px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }
        .score-text {
            font-size: 12px;
            opacity: 0.6;
            padding: 1px 0;
        }
    }
    .addition-rate-num {
        font-size: 16px;
    }
    .marquee-list-box {
        height: 14px;
        overflow: hidden;
    }
    .left-top {
        font-family: PingFang SC;
        display: flex;
        align-items: center;
        font-weight: 500;
        white-space: nowrap;
    }
    .author-rank-tip {
        margin-left: 4px;
        font-size: 10px;
        line-height: 16px;
        padding: 0 4px;
        background: #d9ddff0f;
        border-radius: 8px;
    }
    .content {
        margin-top: 10px;
    }
    .mult-pk-tag {
        background: #ff54771a;
        margin-left: 4px;
        // width: 48px;
        height: 20px;
        flex-shrink: 0;
        padding: 2px 6px 2px 6px;
        // gap: 2px;
        border-radius: 10px;
        //styleName: 中黑体 bold/辅助文本 font-bold-12;
        font-family: PingFang SC;
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        text-align: center;
    }
}
.branch-line-title {
    font-family: HYYakuHei;
    font-size: 16px;
    font-weight: 900;
    line-height: 20px;
    text-align: left;
    margin-bottom: 12px;
    display: inline-flex;
}
.branch-line-title-left {
}
.title-arrow {
    font-size: 10px;
}
.content-card-box {
    padding: 20px 0px 0 0;
    background: #141522;
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        &__left {
            display: flex;
            align-items: center;
            .top-title {
                flex-shrink: 0;
                height: 20px;
                font-family: HYYakuHei;
                font-size: 16px;
                line-height: 20px;
                background: linear-gradient(
                    89.98deg,
                    #ffffff 0.03%,
                    #ffc4a3 95.69%
                );
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .time {
                margin-left: 4px;
                height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 2px 6px;
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                border-radius: 10px;
                text-align: center;
                color: #ffd400;
                background: rgba(255, 212, 0, 0.1);
            }
        }
        &__right {
            height: 18px;
            overflow: hidden;
            font-size: 12px;
            .tip-text {
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }
            .entry-icon {
                width: 10px;
                height: 10px;
            }
        }
    }
}
.content-card-box__header__right {
    font-size: 12px;
}
</style>
