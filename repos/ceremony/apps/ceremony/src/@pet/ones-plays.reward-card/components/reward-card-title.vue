<template>
    <div>
        <ACardTitle class="title">
            {{ title }}
        </ACardTitle>
        <div
            v-clickLog="{
                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                params: {
                    btn_type: '获取金币',
                },
            }"
            class="title-more a-text-main-o2"
            @click="onMorePlay"
        >
            <span>获取金币</span>
            <Right class="right-icon" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { Right } from '@alive-ui/icon';
import { ACardTitle } from '@alive-ui/base';

const emit = defineEmits(['onMorePlay']); // 定义事件

const props = withDefaults(
    defineProps<{
        title: string;
    }>(),
    {
        title: '快乐抽大奖',
    },
);
const onMorePlay = () => {
    emit('onMorePlay');
};
</script>

<style scoped lang="less">
.title {
    margin-bottom: 20px;
}
.title-more {
    position: absolute;
    top: 25px;
    right: 12px;
    display: flex;
    align-items: center;
    height: 18px;
    margin-left: 46px;
    font-family: 'PingFang SC', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    .right-icon {
        margin-left: 2px;
        width: 10px;
        height: 10px;
    }
}
</style>
