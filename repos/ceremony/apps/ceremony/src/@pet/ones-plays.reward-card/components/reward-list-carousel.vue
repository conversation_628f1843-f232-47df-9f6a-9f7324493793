<template>
    <div v-if="reward?.length" class="topPanel">
        <div class="tp-cover-block tp-left" />
        <!-- 奖励区域 -->
        <div class="swiper swiper-block-reward">
            <div class="reward-list swiper-wrapper">
                <div
                    v-for="item in reward"
                    :key="item.id"
                    class="reward a-bg-substrate swiper-slide"
                >
                    <img class="reward-img" :src="item?.icon" alt="" />
                    <div class="reward-title a-text-main">{{ item?.name }}</div>
                </div>
            </div>
        </div>
        <div class="tp-cover-block tp-right" />
    </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';
import { onBeforeUnmount, onMounted, ref, watch, type PropType } from 'vue';
import { Autoplay, FreeMode } from 'swiper/modules';
import Swiper from 'swiper';
import { Report } from '@alive-ui/actions';
import 'swiper/css';
import 'swiper/css/autoplay';
import 'swiper/css/free-mode';
import { UDevice } from '@ad/utils';
import type { RewardItemType } from '@/modules/new-storm/schemas';

const props = defineProps({
    reward: {
        type: Array as PropType<RewardItemType[]>,
        default: () => [], // 设置默认值为一个空数组
    },
});

const mySwiper = ref<any>(null);

const route = useRoute();

const emits = defineEmits(['update-balance']);
// 监听从扭蛋机跳转回来的：from还是new-storm
watch(route, (to, from) => {
    if (mySwiper.value && from?.path === '/new-storm') {
        mySwiper.value?.slideTo(1, 1, false);
        mySwiper.value?.autoplay?.start();
    }
    if (from?.path === '/new-storm') {
        emits('update-balance'); // 更新余额
    }
});

onMounted(() => {
    if (props.reward.length > 0) {
        initSwiper();
    }
    try {
        UDevice?.isIOS() && window.addEventListener('pageshow', rewardOnShow);
    } catch (err) {
        console.error(err);
        Report.biz.error('【内容页：抽奖组件】：监听组件是否可见异常', {
            error: err,
        });
    }
});

const rewardOnShow = (event: PageTransitionEvent) => {
    // 如果是从缓存中来：更新余额&滑动轮播
    if (event.persisted) {
        emits('update-balance');
        if (mySwiper.value) {
            mySwiper.value?.slideTo(1, 1, false);
            mySwiper.value?.autoplay?.start();
        }
    }
};

const initSwiper = () => {
    try {
        mySwiper.value = new Swiper('.swiper-block-reward', {
            modules: [Autoplay, FreeMode],
            slidesPerView: 'auto',
            loop: true,
            spaceBetween: 0,
            observer: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
            },
            speed: 200 * props.reward?.length || 10000,
            freeMode: {
                enabled: true,
                momentum: false,
            },
            allowTouchMove: true,
            preventInteractionOnTransition: false,
            on: {
                touchEnd: () => {
                    if (mySwiper.value) {
                        mySwiper.value.autoplay?.start(); // 用户松开后恢复自动播放
                    }
                },
            },
        });
    } catch (error) {
        console.error('Swiper initialization failed', error);
    }
};

onBeforeUnmount(() => {
    if (mySwiper.value) {
        mySwiper.value.destroy();
    }
    try {
        UDevice?.isIOS() &&
            window.removeEventListener('pageshow', rewardOnShow);
    } catch (err) {
        console.error(err);
        Report.biz.error('【内容页：抽奖组件】：移除监听组件是否可见异常', {
            error: err,
        });
    }
});
</script>

<style scoped lang="less">
.swiper-wrapper {
    transition-timing-function: linear; /* 确保平滑 */
    will-change: transform; /* 告诉浏览器优化滑动性能 */
}
.topPanel {
    display: flex;
    position: relative;
    padding-right: 1px;
    .reward-list {
        display: flex;
        margin-bottom: 12px;
        .reward {
            display: flex;
            min-width: 76px;
            max-width: 76px;
            height: 76px;
            margin-right: 10px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-radius: 8px;
            padding: 5px;
            overflow: hidden;
            &:first-child {
                margin-left: 12px;
            }
            &:last-child {
                margin-right: 12px;
            }
            .reward-img {
                width: 52px;
                height: 52px;
            }
            .reward-title {
                width: 100%;
                text-align: center;
                font-family: 'PingFang SC', sans-serif;
                font-size: 10px;
                font-style: normal;
                font-weight: 400;
                line-height: 14px;
                opacity: 0.6;
                white-space: nowrap;
                overflow: hidden;
            }
        }
    }
    .tp-cover-block {
        width: 24px;
        height: 76px;
        z-index: 2;
        position: absolute;
    }
    .tp-left {
        left: 0;
        background: linear-gradient(
            90deg,
            #13174a 0%,
            rgba(19, 23, 74, 0) 100%
        );
    }
    .tp-right {
        right: 0;
        background: linear-gradient(
            270deg,
            #13174a 0%,
            rgba(19, 23, 74, 0) 100%
        );
    }
}
</style>
