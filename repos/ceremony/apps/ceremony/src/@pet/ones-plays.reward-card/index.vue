<template>
    <ACard v-if="Object.keys(luckyBag).length">
        <rewardCardTitle
            :title="title"
            @on-more-play="onMorePlay"
        ></rewardCardTitle>
        <AInfo v-if="marqueeList?.length" class="info" type="solid">
            <MQ
                v-if="marqueeList && marqueeList.length"
                :marquee-list="marqueeList"
            >
                <template #default="{ item }">
                    <div
                        v-if="item.type === 'txt'"
                        class="info-marquee-item a-text-main-o2"
                    >
                        {{ item.value }}
                    </div>
                    <!-- eslint-disable-next-line vue/no-v-html -->
                    <div
                        v-else
                        class="info-marquee-item a-text-main-o2"
                        v-html="item.value"
                    />
                </template>
            </MQ>
            <template #extra>
                <div class="info-coin">
                    <img
                        class="info-coin-img"
                        src="./assets/goldcoin.png"
                        alt=""
                    />

                    <span class="info-coin-span a-text-main">
                        <span class="info-coin-span12">X</span>{{ coinCount }}
                    </span>
                </div>
            </template>
        </AInfo>
        <rewardListCarousel
            :reward="luckyBag.reward"
            @update-balance="updateBalance"
        />
        <AButton
            v-clickLog="activityDrawButton"
            v-show-log="activityDrawButton"
            class="button-support"
            type="primary"
            @click="onPrizeDraw"
        >
            <div class="a-button-support">
                <div class="a-botton-support-main storm-draw-btn a-text-button">
                    点击抽奖
                </div>
                <div class="a-botton-support-sub a-text-button">
                    {{ luckyBag?.prize }}币/次
                </div>
            </div>
        </AButton>
        <!-- 弹窗 -->
        <!-- :address="address" -->
        <ResultModal
            :show="resultPopShow"
            :sub-title="subTitle"
            :page-status="pageStatusPop"
            :award-list="resultList"
            :need-fill-address="needFillAddress"
            :need-jump-back-pack="needJumpBackPack"
            :need-jump-wallet="needJumpWallet"
            :need-send-emoticon="needSendEmoticon"
            :enable-show-emoticon-tab="enableShowEmoticonTab"
            @close="switchResultPop(false)"
        />
    </ACard>
</template>
<script setup lang="ts">
import { computed, ref } from 'vue';
import { debounce } from 'lodash-es';
import { usePopAdapter } from '@pet/ones-use.usePopAdapter';
import { DrawResult } from '@pet/ones-ui.DrawMachine/views/magic/hooks/useBoxInfo';
import ResultModal from '@pet/ones-ui.DrawMachine/modules/popups/result-modal.vue';
import {
    drawReward,
    getBalance,
    type AwardItem,
    type AwardMarqueeInfo,
} from '@pet/ones-ui.DrawMachine/api';
import { Toast } from '@lux/sharp-ui-next';
import { isYodaPCContainer } from '@alive-ui/system';
import { ACard, AInfo, AButton } from '@alive-ui/base';
import {
    layoutType,
    appendParam,
    liveStreamId,
    authorId,
    entry_src,
    activityBiz,
    bolIsAuthor,
    Report,
} from '@alive-ui/actions';
import rewardListCarousel from './components/reward-list-carousel.vue';
import rewardCardTitle from './components/reward-card-title.vue';
import MQ from '@/@pet/ones-ui.DrawMachine/components/marquee/index.vue';

const { adapter } = usePopAdapter('left');
const props = withDefaults(
    defineProps<{
        luckyBag: any;
        title?: string;
    }>(),
    {
        title: '快乐抽大奖',
    },
);
const coinCount = ref(props.luckyBag.coinCount);

// 处理轮播文案
const dealData = (data: AwardMarqueeInfo) => {
    const { records, mustWinToast1, mustWinToast2 } = data;
    const arr = [
        {
            type: 'must',
            value: mustWinToast1,
        },
        {
            type: 'must',
            value: mustWinToast2,
        },
    ];

    records?.slice(0, 50).forEach((element, index) => {
        arr.push({
            type: 'txt',
            value: element,
        });

        arr.push({
            type: 'must',
            value: index % 2 ? mustWinToast2 : mustWinToast1,
        });
    });

    return arr.filter((item) => item.value);
};
const marqueeList = computed(() => dealData(props.luckyBag?.winnerBroadcast));

// 跳转扭蛋机
const onMorePlay = (isTask = false) => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
        return;
    }
    const url = props.luckyBag?.morePlayWays ?? '';
    if (url) {
        const params = {
            liveStreamId,
            authorId,
            layoutType,
            entry_src,
            showFirstBackBtn: true,
        };
        const reviseUrl = appendParam(url, params);
        const jumpUrl = !isTask ? reviseUrl : `${reviseUrl}&anchor=task`;
        window.location.href = jumpUrl;
    }
};

// 弹窗相关逻辑
const resultPopShow = ref(false);
const subTitle = ref('');
const pageStatusPop = ref({
    loading: false,
    error: false,
});
const resultList = ref([] as AwardItem[]);
const needFillAddress = ref(false);
const needJumpBackPack = ref(false);
const needJumpWallet = ref(false);
const needSendEmoticon = ref(false);
const enableShowEmoticonTab = ref(false);

const switchResultPop = (status: boolean) => {
    if (!status && resultPopShow.value) {
        updateBalance();
    }
    resultPopShow.value = status;
};

const checkBalance = () => {
    if (props.luckyBag?.prize > coinCount.value) {
        return false;
    }

    return true;
};

const onPrizeDraw = debounce(async () => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
        return;
    }
    try {
        //  这里前端自己先判断下金币余额够不够，不够就不调借口，否则服务端有报警
        if (!checkBalance()) {
            onMorePlay(true);
            return;
        }
        pageStatusPop.value.loading = true;
        pageStatusPop.value.error = false;
        const prizeSource = props.luckyBag?.prizeSource ?? '';
        const prizeStyle = +(props.luckyBag?.prizeStyle ?? 1);
        const ruleld = props.luckyBag?.ruleld ?? 1;

        // 1.三个参数确认：入口，是否有底部tab：1,2,3、几连抽
        const res = await drawReward(prizeSource, prizeStyle, ruleld);
        switchResultPop(true);
        updateBalance();
        if (res?.toast) {
            Toast.info(res.toast);
            return DrawResult.offseason;
        }
        subTitle.value = res?.subTitle || '';
        resultList.value = res?.awards;
        needFillAddress.value = res?.needFillAddress;
        needJumpBackPack.value = res?.needJumpBackPack;
        needJumpWallet.value = res?.needJumpWallet;
        pageStatusPop.value.loading = false;
        needSendEmoticon.value = res?.needSendEmoticon;
        enableShowEmoticonTab.value = res?.enableShowEmoticonTab;
        setTimeout(() => {
            adapter();
        }, 0);
        return DrawResult.success;
    } catch (err: any) {
        const { error_msg, result } = err?.data || {};
        pageStatusPop.value.error = true;
        pageStatusPop.value.loading = false;

        if (result === 81381) {
            // 3.金币不足跳转页面，
            onMorePlay(true);
            return DrawResult.offseason;
        }
        Report.biz.error('预热页抽大奖失败', {
            error: err,
        });
        error_msg && Toast.info(error_msg);
        !error_msg && Toast.info('网络出错，请重试');

        return DrawResult.error;
    }
}, 300);

// 查询余额
const updateBalance = async () => {
    try {
        if (props.luckyBag) {
            const res = await getBalance();
            coinCount.value = res?.balance || 0;
        }
    } catch (error) {
        Report.biz.error('预热页抽大奖查询余额失败', {
            error,
        });
        console.error('刷新余额失败');
    }
};

// 埋点
const activityDrawButton = {
    action: 'OP_ACTIVITY_DRAW_BUTTON',
    params: {
        activity_name: activityBiz,
        anchor_user_id: authorId,
        live_stream_id: liveStreamId,
        entry_src,
        user_type: bolIsAuthor ? 'AUTHOR' : 'USER',
    },
};
</script>
<style lang="less" scoped>
.info {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    height: 32px;
    overflow: hidden;
    .info-ma {
        height: 32px;
    }
    .info-marquee-item {
        height: 32px;
        font-family: 'PingFang SC', sans-serif;
        font-size: 12px;
        font-weight: 400;
        line-height: 32px;
    }
    .info-coin {
        display: flex;
        align-items: flex-end;
        margin: 7px 0;
        .info-coin-img {
            width: 18px;
            height: 18px;
        }
        .info-coin-span {
            margin-left: 4px;
            font-size: 14px;
            font-weight: 400;
            font-family: 'Alte DIN 1451 Mittelschrift', sans-serif;
            letter-spacing: 0.06px;
            line-height: 16px;
            .info-coin-span12 {
                font-size: 12px;
            }
        }
    }
}
.button-support {
    margin: 0 auto;
    .a-botton-support-main {
        text-align: center;
        font-family: 'PingFang SC', sans-serif;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
    }
    .a-botton-support-sub {
        text-align: center;
        font-family: 'PingFang SC', sans-serif;
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
        opacity: 0.8;
    }
    .storm-draw-btn {
        font-weight: bold;
    }
}
</style>
