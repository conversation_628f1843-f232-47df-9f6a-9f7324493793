import type { AwardMarqueeInfo } from '@pet/ones-ui.DrawMachine/api';

export interface RewardItemType {
    id: number;
    name: string;
    icon: string;
}
export interface LuckyBagType {
    morePlayWays: string; // 更多玩法的链接
    coinCount: number; // 代币余额
    winnerBroadcast: AwardMarqueeInfo; // 获奖用户广播
    reward: RewardItemType[]; // 奖品列表
    prize: number; // 每次抽奖花费代币价格
    prizeSource: string;
    prizeStyle: number;
    ruleld: number;
}
