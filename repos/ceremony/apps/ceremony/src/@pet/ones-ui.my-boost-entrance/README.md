# My Boost Entrance 组件文档

## 功能概述
`My Boost Entrance` 是一个用于展示用户身份加成信息的组件。根据用户是否为作者（`bolIsAuthor`），组件会显示不同的内容。如果是作者，组件将显示“身份加成”按钮；如果不是作者，则显示用户的头像和加成值。

## 属性和方法

### Props
- **rankId** (`number`, 必填): 排行榜ID，用于跳转到加成二级页面时传递参数。

## 使用示例

```html
<template>
  <MyBoostEntrance :rankId=\"123\" />
</template>

<script setup lang="ts">
import MyBoostEntrance from '@pet/ones-ui.my-boost-entrance/index.vue';
</script>
```

## 注意事项
- 组件内部使用了 `pinia` 和 `vue-router`，确保项目中已正确配置这些库。
- 组件依赖于 `kconfData` 和 `currentAudienceInfo`，这些数据从 `pinia` 的存储中获取。
- 组件在点击按钮时会发送点击事件，并根据用户是否登录进行不同的处理。

## 依赖项
- `vue`
- `pinia`
- `@pet/ones-use.useKconf`
- `@lux/sharp-ui-next`
- `@alive-ui/pro`
- `@alive-ui/icon`
- `@alive-ui/actions`
- `@/components/person-buff-card-pop/store/index`
- `@/components/person-buff-card-pop/index.vue`
- `@/assets/defalutAvt.png`