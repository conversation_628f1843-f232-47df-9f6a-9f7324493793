<template>
    <div
        :class="{
            'my-boost-entrance': true,
            'my-boost-entrance-author': bolIsAuthor,
        }"
    >
        <div
            v-if="bolIsAuthor"
            v-show-log="{
                action: 'OP_ACTIVITY_GAME_TAB',
                params: {
                    tab_type: 4,
                },
            }"
            class="my-andience-btn"
            @click="clickBuffCard"
        >
            <div class="text-area">
                <!-- <div class="top-text a-text-main-o2">查看粉丝的</div> -->
                <div class="bottom-text a-text-main mt-2px">
                    身份加成
                    <Right v-if="showAdditionDetail" class="ml-2px" />
                </div>
            </div>
        </div>
        <div
            v-else
            v-show-log="{
                action: 'OP_ACTIVITY_GAME_TAB',
                params: {
                    tab_type: 3,
                },
            }"
            class="my-andience-btn"
            @click="clickBuffCard"
        >
            <APAvatar size="3xs" :src="currentAudienceInfo?.audienceHeadURL" />
            <div class="text-area flex-center-start">
                <div class="top-text a-text-main-o2">我的加成</div>
                <div class="bottom-text a-text-main mt-2px">
                    {{
                        currentAudienceInfo?.additionFactor
                            ? `+${currentAudienceInfo?.additionFactor}`
                            : '查看'
                    }}
                    <Right v-if="showAdditionDetail" />
                </div>
            </div>
        </div>
        <PersonBuffCardPop :show="showBuffCardMyDetail" @hide="onSwitch" />
    </div>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance } from 'vue';
import { storeToRefs } from 'pinia';
import useKConf from '@pet/ones-use.useKconfBatch';
import { Toast } from '@lux/sharp-ui-next';
import { sendClickLog } from '@gundam/weblogger';
import { Avatar as APAvatar } from '@alive-ui/pro';
import { Right } from '@alive-ui/icon';
import { bolIsAuthor, userId } from '@alive-ui/actions';
import { useIdentityAdditionStore } from '@/components/person-buff-card-pop/store/index';
import PersonBuffCardPop from '@/components/person-buff-card-pop/index.vue';
const { kconfData } = storeToRefs(useKConf());
const identityStore = useIdentityAdditionStore();
identityStore.initData();
const { currentAudienceInfo, showAdditionDetail } = storeToRefs(identityStore);

const showBuffCardMyDetail = ref(false);

const instance = getCurrentInstance()?.proxy;
const props = defineProps<{
    rankId: number;
}>();

const clickBuffCard = () => {
    const ruleUrl = kconfData.value?.mainPage?.cBuffCardRules?.additionRule;
    if (bolIsAuthor) {
        location.href = ruleUrl;
        return;
    }
    if (!userId || userId === '0') {
        Toast.info('请先登录');

        return;
    }

    sendClickLog({
        action: 'OP_ACTIVITY_GAME_TAB',
        params: {
            tab_type: bolIsAuthor ? 4 : 3,
            // 我的加成3
            // 粉丝加成4
        },
    });
    // 跳转加成二级页面
    // instance?.$router.push({
    //     name: 'author-addition',
    //     query: {
    //         rankId: props.rankId,
    //     },
    // });
    onSwitch();
};
const onSwitch = () => {
    showBuffCardMyDetail.value = !showBuffCardMyDetail.value;
};
</script>

<style lang="less" scoped>
.my-boost-entrance {
    padding: 5px 0 5px 5px;
    width: 91px;
    height: 38px;
    border-radius: 18px 0 0 18px;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
}
.my-andience-btn {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    // display: flex;
    // width: 120px;
    // height: 32px;
    // padding-left: 20px;
    // justify-content: flex-start;
    // align-items: center;

    .text-area {
        display: flex;
        flex-direction: column;
        margin-left: 4px;
        align-items: center;
        .top-text {
            font-family: 'PingFang SC';
            font-size: 10px;
            font-weight: 400;
            line-height: 14px;
        }
        .bottom-text {
            display: flex;
            font-family: 'PingFang SC';
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
            height: 16px;
            align-items: center;
        }
    }
}
.my-boost-entrance-author {
    padding: 5px 0 5px 12px;
}
</style>
