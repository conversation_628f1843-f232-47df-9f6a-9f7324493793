import { computed, ref, nextTick, onMounted, onUnmounted } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { getWindow } from '@alive-ui/ssr-window';
import {
    isICFO,
    isLowDevice,
    countsCT,
    useLowPowerMode,
    dealPowerModel,
} from '@alive-ui/actions';

export const useDowngrade = () => {
    const { isLowPowerModeStatus } = storeToRefs(useLowPowerPromise());

    return computed(() => {
        return (
            isLowPowerModeStatus.value ||
            isICFO() ||
            isLowDevice() ||
            countsCT() > 1
        );
    });
};

export const useLowPowerPromise = defineStore('low-power-promise', () => {
    const isLowPowerModeStatus = ref(false);
    const isAndroid = /\bAndroid\b/.test(getWindow().navigator.userAgent);
    const res = dealPowerModel(isLowPowerModeStatus);

    const resolveFn = ref();
    const pFinished = ref(false);
    const lowPowerPromise = new Promise((r) => {
        resolveFn.value = r;
    });

    const lowPromiseFunc = () => {
        return lowPowerPromise;
    };

    const finishP = () => {
        resolveFn.value?.();
        setTimeout(() => {
            pFinished.value = true;
        });
    };

    onMounted(async () => {
        try {
            if (isAndroid) {
                finishP();
                return;
            }

            const { getBatteryInfo } = res;
            await getBatteryInfo();
            finishP();
        } catch (error) {
            console.log('error', error);
            finishP();
        }
    });

    onUnmounted(() => {
        if (isAndroid) {
            return;
        }

        const { removeBatteryListener } = res;
        removeBatteryListener();
    });

    return {
        isLowPowerModeStatus,
        lowPowerPromise,
        pFinished,
        lowPromiseFunc,
    };
});
