import {
    type Ref,
    type ComponentPublicInstance,
    unref,
    ref,
    onUnmounted,
} from 'vue';
import { throttle } from 'lodash-es';
import { isOutLiveRoom } from '@alive-ui/actions';
import { getCurrentWidth } from '@/utils/tools';

type MaybeRef<T> = T | Ref<T>;

type VueElement =
    | HTMLElement
    | ComponentPublicInstance
    | undefined
    | null
    | Window;

export const stickLockScroll = ref(false);

export function useStickyElement(
    ele: MaybeRef<VueElement>,
    threshold:
        | {
              top?: number;
              bottom?: number;
          }
        | Ref<{
              top?: number;
              bottom?: number;
          }>,
    cb?: (pos: number, scrollTop: number) => void,
): {
    isSticky: Ref<boolean>;
    outLiveRoomTopHeight: number;
    ratio: number;
    usePos: Ref<number>;
    scrollTop: Ref<number>;
    toBottom: Ref<boolean>;
} {
    const OutCutHeight = 30;
    const w = getCurrentWidth();
    const ratio = w / 414;
    const outLiveRoomTopHeight = isOutLiveRoom ? OutCutHeight * ratio : 0;
    const isSticky = ref(false);
    const usePos = ref(0);
    const scrollTop = ref(0);
    const toBottom = ref(false);

    const listener = throttle(
        () => {
            if (!stickLockScroll.value) {
                const plainEle = unref(ele);
                const plainThreshold = unref(threshold);
                const useEle =
                    (plainEle as ComponentPublicInstance)?.$el ?? plainEle;
                if (useEle && (plainThreshold.top || plainThreshold.bottom)) {
                    scrollTop.value =
                        window.pageYOffset ||
                        document.documentElement.scrollTop ||
                        document.body.scrollTop;
                    const { bottom, top } = useEle.getBoundingClientRect();
                    const useThreshold =
                        plainThreshold.bottom ?? plainThreshold.top!;
                    usePos.value = plainThreshold?.bottom ? bottom : top;
                    isSticky.value =
                        usePos.value <=
                        useThreshold * ratio + outLiveRoomTopHeight;
                    const { clientHeight, scrollHeight } =
                        document.documentElement;
                    toBottom.value =
                        scrollTop.value + clientHeight >= scrollHeight - 0.1;
                    cb?.(usePos.value, scrollTop.value);
                }
            }
        },
        5,
        {
            trailing: true,
        },
    );

    threshold && window.addEventListener('scroll', listener);
    onUnmounted(() => {
        window.removeEventListener('scroll', listener);
    });

    return {
        isSticky,
        outLiveRoomTopHeight,
        ratio,
        usePos,
        scrollTop,
        toBottom,
    };
}
