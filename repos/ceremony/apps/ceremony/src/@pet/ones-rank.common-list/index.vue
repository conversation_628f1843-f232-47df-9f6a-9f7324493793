<script lang="ts" setup>
import { computed } from 'vue';
import RankTopTips from '@pet/ones-rank.top-tips/index.vue';
import RankItemTeam from '@pet/ones-rank.team-item/index.vue';
import RankItemRightScore from '@pet/ones-rank.right-score-item/index.vue';
import RankItemGuild from '@pet/ones-rank.guild-item/index.vue';
import RankItemDouble from '@pet/ones-rank.double-avatar-item/index.vue';
import Divider from '@pet/ones-rank.divider/index.vue';
import RankItemBase from '@pet/ones-rank.base-item/index.vue';
import AdditionTips from '@pet/ones-rank.addition-tips/index.vue';
import { BaseRankList as APBaseRankList } from '@alive-ui/pro';
import { nameSlice, useShowNum } from '@alive-ui/actions';
import type { RankConfigInfo } from '@pet/ones-rank.schema/global';
interface RankData {
    // 榜单相关数据: 列表，关注id，分数标签名称
    data: RankConfigInfo;
    // 榜单宽度是否自定义
    needWidth?: boolean;
    // 榜单单项组件 （类型多，有单头像，双头像，三头像等，分数值分布排列位置不同，可以自定义传入）
    rankType: string;
    // 注入上下文
    contextName: symbol;
    // 是否需要分页
    needPage?: boolean;
    // 是否需要top3 特殊背景
    hasTop3Bg?: boolean;
    doubleAvatarSize: string;
    needTopIndex?: boolean; // 头像顶部是否需要展示排名
    needTopLive?: boolean; // 头像顶部是否需要展示直播icon
}
const props = withDefaults(defineProps<RankData>(), {
    data: () => {
        return {
            list: [],
            // 榜单分段顶部提示文案
            topTipsText: '',
            // 是否需要榜单顶部提示
            needTop: false,
            // 埋点类型
            logType: '',
            // 直播间id列表
            liveStreamIdList: [],
            // 榜单分值标签名称，如盛典值
            scoreLabel: '',
            // 榜单关注id
            focusActivityId: '',
            // 是否需要展示分割线
            isNeedDivider: true,
            // 是否需要流量提示条
            isNeedTraffic: false,
        };
    },
    needPage: false,
    needWidth: false,
    rankType: 'base',
    contextName: Symbol.for('ctx'),
    doubleAvatarSize: 'lg',
    needTopIndex: false,
    needTopLive: true,
});

const componentType: { [key: string]: object } = {
    team: RankItemTeam,
    guild: RankItemGuild,
    base: RankItemBase,
    rightScore: RankItemRightScore,
    double: RankItemDouble,
};

const emits = defineEmits(['goPage', 'success', 'fail']);
const rankList = computed(() => {
    return props.data?.list || [];
});
const { showNum } = useShowNum(rankList);

const list = computed(() => {
    return props.needPage
        ? rankList.value?.slice(0, showNum.value)
        : rankList.value;
});
const goPage = () => {
    emits('goPage');
};
const onSuccess = () => {
    emits('success');
};
const onFail = () => {
    emits('fail');
};
</script>

<template>
    <!-- 通用榜单: 包含结算状态、晋级分割线，榜单列表及分页能力，支持榜单单项组件自定义 -->
    <div class="rank-common-list relative">
        <slot name="self-list">
            <APBaseRankList
                v-if="list?.length"
                :has-top3-bg="!data.needTop && hasTop3Bg"
                :log-params="data.logParams"
            >
                <RankTopTips
                    :need-top="data.needTop"
                    :top-tips-text="data.topTipsText"
                    :log-type="data.logType"
                    @go-page="goPage"
                >
                    <template
                        v-for="(item, index) in list"
                        :key="`${index}-${item?.itemId}-${item?.h5ShowScore}`"
                    >
                        <component
                            :is="componentType[rankType]"
                            :need-width="needWidth"
                            :item="{
                                ...item,
                                scheduleId: data?.scheduleId,
                                liveStreamIdList: data.liveStreamIdList,
                                scoreLabel: data.scoreLabel,
                                activityId: data.focusActivityId,
                                logParams: data.logParams,
                                avatarSize: data.avatarSize,
                            }"
                            :need-top-index="needTopIndex"
                            :need-top-live="needTopLive"
                            avatar-size="lg"
                            :avatar-size-left="doubleAvatarSize"
                            :avatar-size-right="doubleAvatarSize"
                            @success="onSuccess"
                            @fail="onFail"
                        >
                            <template v-if="item?.addition" #extra>
                                <!-- 加成进度 -->
                                <AdditionTips
                                    class="ml-3px"
                                    :value="item?.addition?.progress"
                                    :max="100"
                                >
                                    <template #text>
                                        {{ item?.addition?.additionStatusDesc }}
                                    </template>
                                </AdditionTips>
                            </template>
                            <template #right>
                                <slot
                                    v-if="
                                        item?.extraData?.showCpOrgInfoLabel &&
                                        rankType !== 'guild'
                                    "
                                    name="right"
                                >
                                    <div
                                        v-if="
                                            item?.extraData?.cpOrgName ||
                                            item?.extraData?.areaInfo ||
                                            item?.extraData?.operationsInfo
                                        "
                                        class="new-cp-label"
                                    >
                                        <div
                                            v-if="item?.extraData?.cpOrgName"
                                            class="group-label-section mt-2px"
                                        >
                                            {{
                                                nameSlice(
                                                    item?.extraData?.cpOrgName,
                                                    11,
                                                )
                                            }}
                                        </div>
                                        <div
                                            v-if="item?.extraData?.areaInfo"
                                            class="group-label-section mt-2px"
                                        >
                                            {{ item?.extraData?.areaInfo }}
                                        </div>
                                        <div
                                            v-if="
                                                item?.extraData?.operationsInfo
                                            "
                                            class="group-label-section mt-2px"
                                        >
                                            {{
                                                item?.extraData?.operationsInfo
                                            }}
                                        </div>
                                    </div>
                                    <span v-else></span>
                                </slot>
                            </template>
                        </component>
                        <!-- 晋级主播分割线提示 -->
                        <Divider
                            v-if="data?.isNeedDivider"
                            :need-width="needWidth"
                            :context-name="contextName"
                            :rank-show-index="item.rankShowIndex"
                        />
                    </template>
                </RankTopTips>
            </APBaseRankList>
        </slot>
    </div>
</template>

<style lang="less" scoped>
.group-label-section {
    width: fit-content;
    border-radius: 2px;
    padding: 1px 3px;
    background: rgba(224, 237, 255, 0.1);
    opacity: 0.9;
    color: #fff;
    font-size: 8px;
}
.new-cp-label {
    display: grid;
    justify-items: flex-end;
}
</style>
