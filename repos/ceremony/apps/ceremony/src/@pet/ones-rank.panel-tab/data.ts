import { computed, inject } from 'vue';
import type { Ref } from 'vue';
// 消费的顶层数据源是rank接口的就填QueryRankPostResponse，
// 如果是home接口的就填import type { RankFrameworkPostResponse } from '@pet/ones-rank.schema/index-home';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';
export const dataFun = (contextName: symbol) => {
    const rankInfo = contextName
        ? inject<Ref<QueryRankPostResponse>>(contextName)
        : null;
    const data = computed(() => {
        return {
            text: rankInfo?.value?.extraData?.privilegeDesc,
        };
    });

    return data;
};
