<template>
    <ATabcard v-model:active-index="activeIndex" @change="onChange">
        <ATabcardItem v-for="tabItem in data.list" :key="tabItem.stageType">
            <span>{{ tabItem.stageName }}</span>
            <template #extra>
                <ATabcardIcon :src="iconSrc" />
            </template>
        </ATabcardItem>
    </ATabcard>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { Tabcard } from '@alive-ui/base';
import { dataFun } from './data';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
interface ComponentSchema extends PropsCommonParams {
    data: {
        tabIndex: number;
        currentAnchorStageType: number;
        list: {
            stageType: number;
            stageName: string;
        }[];
    };
    iconSrc: string;
}
const props = withDefaults(defineProps<ComponentSchema>(), {
    data: () => {
        return {
            tabIndex: 0,
            currentAnchorStageType: 0,
            list: [],
        };
    },
    useProps: false,
    // 组件的上下文名称,在页面容器层
    contextName: Symbol.for('ctx'),
    iconSrc: '',
});
// 组件提供两种消费数据方式，一种是可以通过props传入，一种是可以通过data.ts，消费组件的页面级别provideroot数据 inject给组件
const newData = props.useProps
    ? computed(() => props.data)
    : dataFun(props.contextName);

const emit = defineEmits(['change']);

const onChange = (activeIndex: number) => {
    emit('change', activeIndex);
};

const {
    Tabcard: ATabcard,
    TabcardIcon: ATabcardIcon,
    TabcardItem: ATabcardItem,
} = Tabcard;

const activeIndex = ref(props.data?.tabIndex || 0);
watch(props.data, (newVal, oldVal) => {
    if (newVal.tabIndex !== oldVal.tabIndex)
        activeIndex.value = newVal.tabIndex;
});
</script>
<style lang="less" scoped>
.tab-wrapper {
    display: flex;
    width: 382px;
    height: 56px;
    background-image: url('./assets/tab-1_2x.png');
    background-size: 100% 100%;
    flex-wrap: wrap-reverse;
    margin: 0 auto 20px;
}
.tab-wrapper-1 {
    background-image: url('./assets/tab-2_2x.png');
}
.tab-item {
    display: flex;
    width: 40%;
    height: 48px;
    font-family: HYYakuHei;
    font-size: 14px;
    text-align: center;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    opacity: 0.4;

    .tab-icon {
        display: none;
    }
}
.tab-item-selected {
    width: 60%;
    height: 56px;
    font-size: 20px;
    opacity: 1;
    .tab-icon {
        display: inline-block;
        width: 40px;
        height: 40px;
        // background: url('./assets/tab-icon.png') center / 100% no-repeat;
    }
}
</style>
