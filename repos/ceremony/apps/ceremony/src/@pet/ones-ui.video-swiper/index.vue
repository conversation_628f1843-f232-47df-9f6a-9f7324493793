<template>
    <div
        class="w-[100%] overflow-hidden party-swiper-container"
        @touchstart="lock4Tab"
        @touchmove="onMoveLock"
        @touchend="unlock4Tab"
        @touchcancel="unlock4Tab"
    >
        <!--
           不能在 swiper 里使用 vue 属性, 因为他们不再是响应式的。
           可以在这里写成 css 变量透传进去。
         -->
        <div
            class="relative mb-[12px] w-[180px] mx-auto"
            :style="`--swiper-card-default-image-url: url('${conf4Tab?.swiperCardDefaultImageUrl}'); --play-icon-visibility: ${videoStore.playStatus === 'normal' ? '' : 'hidden'}; --play-icon-background-url: url('${videoStore.playing ? conf4Tab?.liveOrVideo?.iconAudioClose : conf4Tab?.liveOrVideo?.iconAudioOpen}')`"
        >
            <sp-swiper
                loop
                :is-overflow-hidden="false"
                :indicator-show="false"
                :follow-config="{
                    isFollow: true,
                    scale: {
                        minScale: 0.795,
                    },
                }"
                @change="
                    (idx) =>
                        videoStore.changeSwiper(
                            idx,
                            homeData?.headPhotoFeedData,
                        )
                "
            >
                <div
                    v-for="photoItem in homeData?.headPhotoFeedData?.photoFeeds"
                    :key="photoItem?.photoId"
                    class="h-[330px] w-[180px] rounded-[9px] swiper-box"
                    :style="`--cover-url: url('${photoItem?.cover}')`"
                >
                    <div
                        class="h-[330px] w-[180px] rounded-[8px] absolute top-0 overflow-hidden"
                    >
                        <!-- v-show="playStatus === 'normal'" -->
                        <div
                            class="absolute h-[100%] w-[100%] party-video-dom"
                            @click="openDetailPage(photoItem)"
                        >
                            <!-- useH5FallbackPlayer: true 时, <video> 标签 会被渲染到这个 dom 里 -->
                        </div>
                        <!-- 右下角的▶️⏸按钮 -->
                        <div
                            class="play-icon-for-yoda w-[24px] h-[24px] absolute bottom-[29px] right-[26px]"
                            @click="
                                () =>
                                    canToggle
                                        ? videoStore.togglePlay()
                                        : openDetailPage(photoItem)
                            "
                        ></div>
                    </div>
                </div>
            </sp-swiper>
        </div>
    </div>
</template>

<script lang="ts" setup>
// # if (target === '4tab')
import { Container } from 'typedi';
// # endif
// eslint-disable-next-line import/order
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { throttle } from 'lodash-es';
import { invoke } from '@yoda/bridge';
import { usePartySwitch } from '@pet/ones-use.usePartySwitch';
import useKconf from '@pet/ones-use.useKconf/index';
import { useDowngrade } from '@pet/ones-use.useDowngrade';
import useYodaVideoPlayer from '@pet/ones-use.use-yoda-video-play/video';
import { is4Tab } from '@pet/ones-use.is4Tab';
import { Toast } from '@lux/sharp-ui-next';
import {
    goMoreVideo,
    isOutLiveRoom,
    userId,
    osName,
    bolIsAuthor,
} from '@alive-ui/actions';

const videoStore = useYodaVideoPlayer();

const props = defineProps({
    homeData: {
        type: Object,
        default: () => null,
    },
});

const { conf4Tab } = storeToRefs(useKconf());
const partySwitch = usePartySwitch();
const systemDowngrade = useDowngrade();

const canToggle = computed(() => {
    return (
        isOutLiveRoom &&
        !partySwitch.value.videoDowngradeSwitch &&
        !systemDowngrade.value
    );
});

function onMountedHandler() {
    startMutationObserver((newContainer) => {
        videoStore.updateContainer(newContainer);
    });
    videoStore.changeSwiper(
        videoStore.swiperIdx,
        props.homeData?.headPhotoFeedData,
    );
}

// # if (target === '4tab')
Container.set({
    id: 'on_native_visible_show',
    multiple: true,
    value: () => {
        onMountedHandler();
        // videoStore.onTabEnter();
    },
});
Container.set({
    id: 'on_native_visible_hide',
    multiple: true,
    value: () => {
        disposeMutationObserver();
        // videoStore.onTabLeave();
    },
});
// # endif

// # if (target !== '4tab')
onMounted(() => {
    onMountedHandler();
});
// # endif

const isFirstMove = ref(false);
const lock4Tab = async (e: TouchEvent) => {
    if (is4Tab && osName === 'ios') {
        try {
            await invoke('feed.gifLock4tabScroll', { lock: true });
        } catch (error) {
            console.error(error);
        }
    }
    // if (is4Tab && osName === 'android') {
    //     e.preventDefault();
    // }
};
const onMoveLock = async (e: TouchEvent) => {
    if (!isFirstMove.value) {
        isFirstMove.value = true;
        await lock4Tab(e);
    }
};
const unlock4Tab = async () => {
    if (is4Tab && osName === 'ios') {
        try {
            await invoke('feed.gifLock4tabScroll', { lock: false });
        } catch (error) {
            console.error(error);
        }
    }
};

// 析构
let disposeMutationObserver = () => {};

onUnmounted(() => disposeMutationObserver);

// 开始监听 Swiper 关键 dom 变动
function startMutationObserver(
    onContainerUpdate: (
        newContainer: HTMLDivElement | null | undefined,
    ) => void,
) {
    disposeMutationObserver();

    const swiperInner = document.querySelector('.spu-swiper-inner');
    if (!swiperInner) return;

    /**
     * 方案: 给 yoda player video 传固定的 div, dom 变动时, 这个 div 会被挪到正确的位置上。
     */
    const container: HTMLDivElement = document.createElement('div');
    container.classList.add('fixed-element-for-yoda');

    // pl8('进入 startMutationObserver, 创建了元素:');
    // pv8(container);

    // 首次查询
    let curActiveItem = getActiveItem();
    curActiveItem?.appendChild(container);

    // let t = 0;
    // 监听变动
    const mutationObserver = new MutationObserver((ml) => {
        const newActiveItem = getActiveItem();
        // 如果 active item 发生了变动
        if (newActiveItem !== curActiveItem) {
            // 将 container 挂载到正确的位置
            newActiveItem?.appendChild(container);
            // gl8(`第 ${++t} 次修正了 container 的挂载位置`)
            curActiveItem = newActiveItem;
        }
    });
    mutationObserver.observe(swiperInner, {
        attributes: true,
        characterData: false,
    });
    disposeMutationObserver = () => {
        mutationObserver.disconnect();
        onContainerUpdate(null);
        disposeMutationObserver = () => {};
    };

    // gl8('首次设置了 container:');
    onContainerUpdate(container);

    /**
     * 这个查询语句在首次或者高频切换时查到的 dom 不符合预期(sp-swiper 组件 bug),
     * 下面会监听 dom 变动, 以新的 item 为准, 作为 yoda player video 的容器。
     */
    function getActiveItem(): HTMLDivElement | null | undefined {
        return swiperInner?.querySelector(
            '.spu-swiper__item-active .party-video-dom',
        );
    }
}

onUnmounted(() => {
    videoStore.destroy();
});

const allVideoIdList = computed<number[]>(() => {
    return (
        props.homeData?.headPhotoFeedData?.photoFeeds?.map(
            (item: any) => item.photoId,
        ) || []
    );
});

const openDetailPage = throttle((photoItem?: any) => {
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    const finalPhotoItem =
        photoItem ||
        props.homeData?.headPhotoFeedData?.photoFeeds?.[videoStore.swiperIdx];
    if (!finalPhotoItem) return;
    goMoreVideo(
        { photoId: finalPhotoItem?.photoId, userId },
        allVideoIdList.value,
        {
            // 目前这样写是可以用的，后面会更改
            path: '/rest/n/live/revenue/operation/activity/yearendceremony/hotTopicFeed/photoFeed',
            sourceType: '2022yearendceremony',
            hostKey: '2022yearendceremony',
        },
    );
}, 500);
</script>

<style lang="less">
// 给 yoda player video 传的固定的元素
.fixed-element-for-yoda {
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
    //background: rgba(255, 0, 0, 0.8);
}
</style>

<style lang="less" scoped>
.party-swiper-container {
    overscroll-behavior-x: contain;
}
.play-icon-for-yoda {
    display: none;
    background: var(--play-icon-background-url) center / 100% no-repeat;
    visibility: var(--play-icon-visibility);
}
.spu-swiper__item-active .play-icon-for-yoda {
    display: block;
}
/deep/.spu-swiper-inner {
    overscroll-behavior-x: contain;
}
.swiper-box {
    // 兼容ios的圆角问题
    transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    overflow: hidden;

    // 视频卡片的背景: 优先级: 封面 > KConf 上配置的 > 代码层面的兜底图
    background:
        // 封面
        var(--cover-url) center / cover no-repeat,
        // kconf 上的兜底图
        var(--swiper-card-default-image-url) center / cover no-repeat,
        // 代码层面的兜底图
        url('./assets/default.jpg') center / cover no-repeat;
}
</style>
