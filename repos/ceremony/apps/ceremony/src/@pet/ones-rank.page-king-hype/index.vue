<script lang="ts" setup>
import { defineAsyncComponent } from 'vue';
import { storeToRefs } from 'pinia';
import { useContextFun } from '@pet/ones-use.data-context/index';
// import useEntryStore from '@pet/ones-rank.secondary-entry/models/index';
import ProcessTab from '@pet/ones-rank.process-tab/index.vue';
import HourRotate from '@pet/ones-rank.hour-rotate/index.vue';
import RankFrame from '@pet/ones-rank.frame/index.vue';
import './assets/imgs.less';
// import BackTrack from '@pet/ones-rank.back-track/index.vue';
import { sendFmp } from '@gundam/weblogger';
import useRankStore from './models/query-rank';
import useHomeStore from './models/index-home';
// const SecondaryEntry = defineAsyncComponent(() => {
//     return import('@pet/ones-rank.secondary-entry/index.vue');
// });
const RankTrackSwitch = defineAsyncComponent(() => {
    return import('@pet/ones-rank.hour-tab/index.vue');
});
const AwardCollapse = defineAsyncComponent(() => {
    return import('@pet/ones-rank.award-collapse/index.vue');
});
const AuthorBase = defineAsyncComponent(() => {
    return import('@pet/ones-rank.current-author-king/index.vue');
});
const RankPanel = defineAsyncComponent(() => {
    return import('@pet/ones-rank.common-list/index.vue');
});
const homeStore = useHomeStore();
const rankStore = useRankStore();
// const entryStore = useEntryStore();
const { homeData, indexInfoData } = storeToRefs(homeStore);
const { rankData, rankInfo } = storeToRefs(rankStore);
// const { secondaryEntryInfo } = storeToRefs(entryStore);
homeStore.initPage().finally(() => {
    sendFmp();
    rankStore.init();
});
const contextName = useContextFun('kingHyperIndex', indexInfoData);
const contextRankName = useContextFun('kingHyperRank', rankData);
</script>

<template>
    <RankFrame
        class="rank-page-king"
        :data="{
            ...homeData.frameData,
            headerData: {
                title: rankData.titleSchedule,
            },
        }"
        :context-name="contextRankName"
        @home-refresh="homeStore.refresh"
        @rank-refresh="rankStore.refresh"
    >
        <template #left-top>
            <!-- <SecondaryEntry
                flex-gap="8px"
                :entry-data="secondaryEntryInfo?.topLeftArea"
                need-margin
            /> -->
        </template>
        <template #right-top>
            <!-- <SecondaryEntry
                flex-gap="8px"
                :entry-data="secondaryEntryInfo?.topRightArea"
                need-margin
            /> -->
        </template>
        <template #rest-container>
            <!-- 休赛或者放置未开启兜底 -->
        </template>
        <template #page-center>
            <!-- 赛程tab -->
            <ProcessTab
                class="process-margin"
                :context-name="contextName"
                @change="homeStore.processChange"
            />
            <!-- 返回当前主播所在赛道, 非回看且不在主播当前赛道下时才展示 -->
            <!-- <BackTrack
                v-if="!homeData.isCurrentRank"
                @back="homeStore.refresh"
            /> -->
            <HourRotate
                :context-name="contextName"
                @change="homeStore.hourChange"
            />
        </template>
        <template #rank-header>
            <component
                :is="RankTrackSwitch"
                :context-name="contextName"
                @change="homeStore.tabChange"
            />
            <!-- 其他组件： 自定义当前主播 -->
        </template>
        <template #rank-list>
            <AwardCollapse :context-name="contextRankName" />
            <AuthorBase
                v-if="rankData?.bottomInfo"
                :context-name="contextRankName"
            />
            <!-- 榜单列表区, 可向ones-rank贡献新的列表组件（基于已有的组件修改） -->
            <RankPanel
                need-page
                :data="rankData.configData"
                :context-name="contextRankName"
                rank-type="base"
            />
        </template>
    </RankFrame>
</template>
<style lang="less" scoped>
.rank-page-king {
    --marginTop: -102px;
    --rankCardMarginTop: -17px;
    min-height: 600px;
    --yImgHeight: 390px;
    --yImgWidth: 414px;
    :deep(.rank-frame-container) {
        padding-bottom: 0;
    }
}
</style>
