<script lang="ts" setup>
import DoubleAvatarItem from '@pet/ones-rank.double-avatar-item/index.vue';
import RankList from '@pet/ones-rank.common-list/index.vue';
import type { PropType } from 'vue';
const props = defineProps({
    data: {
        type: Object as PropType<{
            focusActivityId: string;
            list: any[];
            scoreLabel: string;
            clearData: any;
            dividerData: any;
            logData: any;
            logParams: any;
        }>,
        default: () => {
            return {
                focusActivityId: '',
                list: [],
                dividerData: {},
                scoreLabel: '',
            };
        },
    },
});
</script>

<template>
    <div class="component">
        <RankList :data="data" :rank-item-comp="DoubleAvatarItem">
            <template #bottomInner> 可扩展头像底部信息：如+号关注等 </template>
            <template #info>
                <!-- 放榜单项主播或xx名称下的信息内容 todo：自定义 -->
            </template>
            <template #right>
                <!-- 定义榜单右侧内容 todo：自定义 -->
            </template>
        </RankList>
    </div>
</template>

<style lang="less" scoped></style>
