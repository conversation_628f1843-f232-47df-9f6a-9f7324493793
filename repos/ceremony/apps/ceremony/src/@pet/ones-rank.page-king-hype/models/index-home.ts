import { useRouter } from 'vue-router';
import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { rankFramework } from '@pet/ones-rank.frame/services/index-home';
import { Toast } from '@lux/sharp-ui-next';
import { query, getPageStatus, Report } from '@alive-ui/actions';
import { finallySchedule } from '../const/index';
import useRankStore from './query-rank';
import type {
    TabItem,
    ScheduleViewList,
    StageLineViewList,
} from '@pet/ones-rank.schema/index-home';
import type { HomeInfoData } from '@pet/ones-rank.schema/global';
// eslint-disable-next-line max-lines-per-function
export default defineStore('kingHypeHome', () => {
    const router = useRouter();
    const currentAnchorStageType = ref(Number(query.stageType) || 10);
    const rankAlias = ref('');
    const appointTime = ref(0);
    const kconf = useKconfStore();
    const rankStore = useRankStore();
    const status = ref(getPageStatus('init')); // 页面状态数据定义
    const indexInfoData = ref<HomeInfoData>(); // 初始化接口数据源
    const clickLoadingFlag = ref(false);
    const currentRankId = ref<number>(); // 主播所在赛道的ID
    const kvData = computed(() => {
        return {
            showBack: false, // 自定义返回条件
            backAction: 'exit',
            image: finallySchedule.includes(
                indexInfoData.value?.anchorDisplayScheduleAndLane
                    ?.scheduleType || 0,
            )
                ? kconf.kconfData?.mainPage?.kingKv
                : kconf.kconfData?.mainPage?.kingNormalKv,
            // todo kconf rule key配置
            rule: 'mainSchedulePage',
        };
    });
    // 主赛程home相关数据处理（kv、规则、赛程、赛道、）
    const homeData = computed(() => {
        return {
            status: status.value,
            ...indexInfoData.value,
            frameData: {
                ...kvData.value,
                needRefresh: true,
                homeStatus: status.value,
                rankStatus: rankStore.rankData?.status,
                isClearing: rankStore.rankData.isClearing,
                showBack: true,
                backAction: 'back',
            },
            rankAliasName:
                indexInfoData.value?.anchorDisplayScheduleAndLane
                    ?.rankAliasName || '',
        };
    });
    // 初始化页面，获取接口数据
    const init = async (isTabChange?: boolean) => {
        if (clickLoadingFlag.value) return;
        clickLoadingFlag.value = true;
        if (isTabChange) {
            Toast.loading('正在加载', 0, true);
        } else {
            status.value = getPageStatus('loading');
        }
        try {
            const res = await rankFramework({
                type: currentAnchorStageType.value,
                appointTime: appointTime.value,
            });
            indexInfoData.value = res;
            // eslint-disable-next-line prettier/prettier
            status.value = getPageStatus(res?.anchorDisplayScheduleAndLane?.rankId ? 'success' : 'nodata');
            const { rankId, rankAliasName } =
                res?.anchorDisplayScheduleAndLane || {};
            currentRankId.value = rankId; // 主播所在赛道的ID
            currentAnchorStageType.value = res?.anchorStageType;
            rankAlias.value = rankAliasName;
            rankStore.setDyncData({ rankId, bizName: res.queryRankBiz });
        } catch (error) {
            status.value = getPageStatus('error');
            Report.biz.error('热度王初始化异常', {
                error,
            });
        } finally {
            Toast.hide();
            clickLoadingFlag.value = false;
        }
    };
    // 刷新整个页面
    const refresh = () => {
        init().finally(async () => await rankStore.init());
    };
    const processChange = (item: ScheduleViewList) => {
        appointTime.value = item.scheduleEndTime;
        init(true).finally(async () => {
            await rankStore.init();
        });
    };
    const changeDisplayName = (params: {
        rankId: number;
        classifyId?: number;
    }) => {
        if (indexInfoData.value?.anchorDisplayScheduleAndLane) {
            if (params.classifyId) {
                indexInfoData.value.anchorDisplayScheduleAndLane.classifyId =
                    params.classifyId;
            }
            if (params.rankId) {
                indexInfoData.value.anchorDisplayScheduleAndLane.rankId =
                    params.rankId;
            }
        }
    };
    const changeScheduleViewData = () => {
        if (indexInfoData.value) {
            indexInfoData.value.scheduleViewList =
                indexInfoData.value.scheduleViewList?.map((elem: any) => {
                    return {
                        ...elem,
                        promotionDesc:
                            rankStore.rankData?.extraData
                                ?.scheduleViewNameMap?.[elem?.scheduleType]
                                ?.promotionDesc || elem.promotionDesc,
                    };
                });
        }
    };
    // 轮盘
    const hourChange = (item: TabItem) => {
        const list = item?.subLaneViews || [];
        const rankId = list?.[0]?.rankId;
        const classifyId = list?.[0]?.classifyId;
        changeDisplayName({ rankId, classifyId });
        rankStore.setDyncData({ rankId });
        rankStore.init(true);
    };
    // 赛程切换
    const change = (item: StageLineViewList) => {
        currentAnchorStageType.value = item.stageType;
        init(true).finally(async () => {
            await rankStore.init();
        });
    };
    // 榜单tab切换
    const tabChange = (rankId: number, periodId?: number) => {
        rankStore.setDyncData({ rankId, periodId });
        rankStore.init(true).finally(() => {
            changeScheduleViewData();
        });
    };
    const initPage = async () => {
        appointTime.value = 0;
        currentAnchorStageType.value =
            Number(router.currentRoute.value.query.stageType) || 0;
        await init();
    };
    return {
        change,
        tabChange,
        status,
        homeData,
        indexInfoData,
        processChange,
        hourChange,
        init,
        initPage,
        refresh,
        currentAnchorStageType,
    };
});
