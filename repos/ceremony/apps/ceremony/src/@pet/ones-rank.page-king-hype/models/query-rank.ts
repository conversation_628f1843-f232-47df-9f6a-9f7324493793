/**
 * 榜单列表 store
 */
import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { queryRank } from '@pet/ones-rank.frame/services/query-rank';
import { Toast } from '@lux/sharp-ui-next';
import { getPageStatus, Report } from '@alive-ui/actions';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';
import { focusActivityId } from '@/const';

// eslint-disable-next-line max-lines-per-function
export default defineStore('kingHype-rank', () => {
    const dyncData = ref({
        rankId: 0,
        periodId: 0,
        bizName: '',
    });
    const rankInfo = ref<QueryRankPostResponse>();

    const status = ref(getPageStatus('init'));

    // 直播间id列表,点击头像上下滑直播间所需
    const liveStreamIdList = computed(() => {
        return (
            rankInfo.value?.rankList?.map((item: any) => {
                return item?.liveStreamId;
            }) || []
        );
    });
    // 榜单赛程标题
    const titleSchedule = computed(() => {
        return `${rankInfo.value?.laneClassifyName || ''}${rankInfo.value?.extraData?.scheduleRankName || ''}争夺`;
    });
    // 榜单主体数据
    const rankData = computed(() => {
        return {
            ...rankInfo.value, // 榜单接口返回的所有数据
            liveStreamIdList: liveStreamIdList.value,
            status: status.value, // 榜单接口状态
            isClearing: rankInfo.value?.extraData?.isClearing,
            dyncData: dyncData.value, // 榜单的请求参数,从这里拿到当前榜单rankId的好处在于不用等榜单接口返回
            // 榜单赛程标题
            titleSchedule: titleSchedule.value,
            configData: {
                list: rankInfo.value?.rankList || [],
                scheduleId: rankInfo.value?.scheduleId,
                liveStreamIdList: liveStreamIdList.value,
                currentRankId: rankInfo.value?.rankId || 0,
                // 榜单分值标签名称，如盛典值
                scoreLabel: '热度值',
                // 埋点额外参数
                logParams: {
                    scheduleId: rankInfo.value?.scheduleId,
                    rankId: rankInfo.value?.rankId,
                },
                // 榜单关注id
                focusActivityId, // 全局的constant文件
                isNeedDivider: true,
                isNeedTraffic: true,
            },
        };
    });

    let clickLoadingFlag = false;

    // 榜单获取接口数据
    const init = async (isTabChange?: boolean) => {
        if (!dyncData.value.rankId) {
            Report.biz.error('热度王接口未返回rankId', {
                rankId: dyncData.value.rankId,
            });
            return;
        }
        try {
            if (clickLoadingFlag) {
                return;
            }
            clickLoadingFlag = true;

            if (isTabChange) {
                Toast.loading('正在加载', 0, true);
            } else {
                status.value = getPageStatus('loading');
            }
            const { rankId, periodId, bizName } = dyncData.value;

            const res = await queryRank({
                rankId,
                periodId,
                bizName,
            });
            status.value = getPageStatus(
                res?.extraData?.isClearing || res?.rankList?.length
                    ? 'success'
                    : 'nodata',
            );
            rankInfo.value = res;
        } catch (error) {
            status.value = getPageStatus('error');
            // todo 不同榜单接口error名称需要调整
            Report.biz.error('热度王榜单异常', {
                error,
            });
        } finally {
            Toast.hide();
            clickLoadingFlag = false;
        }
    };
    // 榜单刷新
    const refresh = () => {
        init();
    };

    // TODO：大对象会随小数据变化而变
    const setDyncData = (params: {
        rankId?: number;
        periodId?: number;
        bizName?: string;
    }) => {
        dyncData.value = {
            ...dyncData.value,
            ...params,
        };
    };

    return {
        liveStreamIdList,
        status,
        rankData,
        rankInfo,
        init,
        refresh,
        setDyncData,
    };
});
