<template>
    <div
        class="award-marquee-wrap"
        :class="{
            'marquee-margin-1': `${giftsList && giftsList.length > 0}`,
        }"
    >
        <div class="award-marquee a-text-main">
            <div class="award-trumpet-icon" :style="noticeStyle"></div>
            <MQ
                v-if="marqueeList && marqueeList.length"
                :marquee-list="marqueeList"
            >
                <template #default="{ item }">
                    <div v-if="item.type === 'txt'" class="msg-item">
                        {{ item.value }}
                    </div>
                    <div v-else class="msg-item" v-html="item.value" />
                </template>
            </MQ>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, defineExpose, toRef } from 'vue';
import { activityBiz } from '@alive-ui/actions';
import { queryAwardMarquee } from './services/index';
import MQ from './marquee/index.vue';
import NoticeIcon from './assets/trumpet.png';
import type { PropType } from 'vue';
import type { AwardMarqueeInfo } from './services/index';

export interface LotteryMarqueeExpose {
    refresh: () => void;
}

export default defineComponent({
    components: {
        MQ,
    },
    props: {
        giftsList: {
            type: Array as PropType<Record<string, any>[]>,
            default: () => [],
        },
        noticeIcon: {
            type: String,
            default: NoticeIcon,
        },
        biz: {
            type: String,
            default: activityBiz,
        },
        useInit: {
            type: Boolean,
            default: true,
        },
        marqueeData: {
            type: Object as PropType<AwardMarqueeInfo>,
            default: () => ({}),
        },
    },
    setup(props) {
        const innerMarqueeData = toRef(props, 'marqueeData');

        const noticeStyle = computed(() => {
            return {
                background: `url(${props.noticeIcon}) center / 100% no-repeat`,
            };
        });

        const refresh = async () => {
            try {
                const res = await queryAwardMarquee(props.biz);
                marqueeList.value = dealData(res);
            } catch (error) {
                console.log('error', error);
            }
        };
        if (props.useInit) {
            refresh();
        }

        const dealData = (data: AwardMarqueeInfo) => {
            const { records, mustWinToast1, mustWinToast2 } = data;
            const arr = [
                {
                    type: 'must',
                    value: mustWinToast1,
                },
                {
                    type: 'must',
                    value: mustWinToast2,
                },
            ];

            records?.slice(0, 50).forEach((element, index) => {
                arr.push({
                    type: 'txt',
                    value: element,
                });

                arr.push({
                    type: 'must',
                    value: index % 2 ? mustWinToast2 : mustWinToast1,
                });
            });

            return arr.filter((item) => item.value);
        };

        const marqueeList = ref(
            dealData(innerMarqueeData.value) as {
                type: string;
                value: string;
            }[],
        );

        defineExpose<LotteryMarqueeExpose>({
            refresh,
        });

        return {
            marqueeList,
            noticeStyle,
            refresh,
        };
    },
});
</script>

<style lang="less" scoped>
.award-marquee-wrap {
    display: flex;
    align-items: center;
    height: 24px;
    margin: 5px 0;
    background: rgba(0, 0, 0, 0.15);
    border-radius: 18.46px;
}
.marquee-margin-1 {
    margin-bottom: 4.5px;
}
.award-marquee {
    position: relative;
    width: 270px;
    height: 16px;
    margin: 0 auto;
    line-height: 16px;
    border-radius: 3px;
    .award-trumpet-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 12px;
        width: 12.31px;
        height: 10.79px;
    }
    :deep(.marquee) {
        height: 100%;
        overflow: hidden;
        margin-left: 29.54px;
        width: 223px;
        .list-wrap {
            position: relative;
            height: 100%;
            overflow: hidden;
            box-sizing: border-box;
        }
        .msg-item {
            height: 16px;
            font-size: 12px;
            line-height: 16px;
            color: #fff;
            text-align: left;

            @apply line-cut1;
        }
    }
}
</style>
