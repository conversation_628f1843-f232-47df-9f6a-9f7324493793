import {
    authorId,
    liveStreamId,
    activityBiz,
    request,
} from '@alive-ui/actions';

export type AwardMarqueeInfo = {
    records: string[];
    mustWinToast1: string;
    mustWinToast2: string;
};

export const queryAwardMarquee = async (biz = activityBiz) => {
    const res = await request.post<AwardMarqueeInfo>(
        '/rest/wd/live/plutus/luckyBag/getTopBanner',
        {
            liveStreamId,
            authorId,
            biz,
        },
    );

    return res.data;
};
