/**
 * 请求: 打榜赛事玩法-榜单页
 */

export interface QueryRankPostRequest {
    // 明文主播id,和liveStreamId作用相同，优先取liveStreamId，liveStreamId为空时，使用该值
    authorId: number;
    // 榜单业务类型  summerCeremony22Author-普通主播榜 summerCeremony22Friend-"亲友团"主播榜
    bizName: string;
    // 加密的直播间id,不能为0，如果为空表示非直播间入口,返回值就没有主播相关数据
    liveStreamId: string;
    // 周期id,首页接口中有这个字段,带过来就行
    periodId: number;
    // 排行榜id,首页接口中有这个字段,带过来就行
    rankId: string;
    // 是否属于刷新操作,点击"刷新按钮"来查询的话传true, 其他情况传false
    refreshFlag: boolean;
}

/**
 * 响应: 打榜赛事玩法-榜单页
 */
export interface QueryRankPostResponse {
    // 赛程id
    scheduleId: number;
    activityId: number;
    bizName: string;
    bottomInfo: BottomInfo; // 主播信息
    extraData: ExtraData; // 额外信息
    laneClassifyName: string; // 当前赛道品类名称
    laneName: string; // 赛道名称
    h5ShowLaneName: string; // 赛道品类名称
    pageScroll: {
        // 时段切换列表数据
        pageViews: any[];
        currPage: {
            periodStartTime: number; // 开始时间
            periodEndTime: number; // 结束时间
            periodId: number; // 周期id
            rankId: number; // 榜单id 用于切换榜单接口调用
        };
    };
    periodId: number; // 周期id
    promotionCount: number; // 晋级数
    rankId: number; // 当前赛道rankid
    rankList: ItemRankInfo[]; // 榜单列表信息
    configData: any;
}

export interface BottomInfo {
    authorAdditionInfo: any;
    authorRankTip: any;
    displayHint: string; // 主播信息文案
    h5ShowHintScore: string; // h5 页面展示的分数
    hintScore: number; // 主播实际信息分数
    itemRankInfo: ItemRankInfo; // 主播信息
    showPromotion: boolean; // 是否展示晋级数
    // 十二时辰使用
    nextLane?: string; // 下一场赛程
    nextTimeHint?: string; // 下一场赛程提示文案
    cashRankPlayStatus: 0 | 1 | 2;
    cashRankCurrBonusDesc: string;
    cashRankCurrBonusValue: string;
    cashRankBonusWinnerDesc: string;
    cashRankBonusIcon: string;
    scoreSumH5Show: string;
    cashRankThresholdH5Show: string;
    cashRankThreshold: number;
    scoreSum: number;
    cashRankCurrBonusDesc2: string;
    cashRankMaxBonusValue: string;
    extraData?: any; // 扩展字段
}
export interface ItemRankInfo {
    addition?: {
        progress: number;
        showAdditionRate: string;
        additionStatusDesc?: string;
    };
    addCardTimes?: string; // 加成卡信息（旧）
    displayScore?: string; // 展示分数
    item?: ItemRankInfoItem; // 主播信息
    followStatus?: boolean; // 关注状态
    headUrl?: string; // 用户头像
    itemId: number; // 用户id
    itemName?: string; // 用户名
    liveStreamId?: string; // 直播间id
    rankShowIndex: number; // 实际排名不使用
    h5RankShowIndex?: string; // h5 页面展示的排名
    score?: number; // 实际分数不使用
    h5ShowScore?: string; // h5 页面展示的分数
    sponsors?: Sponsor[]; // 守护位
    mysteryMan?: boolean; // 是否是神秘人
    histParticipatedInRepechageTips?: string; // 历史参与过复活赛的提示
    extraData?: any; // 扩展字段
}

export interface ItemRankInfoItem {
    headUrl: string; // 用户头像的URL
    itemId: number; // 用户ID
    itemName: string; // 用户名
    liveStreamId?: string; // 直播间ID（可选）
    mysteryMan?: boolean; // 是否是神秘人
}

export interface Sponsor extends ItemRankInfo {
    item: SponsorItem; // 主播信息
    h5ShowScore: string; // h5 页面展示的分数
}

export interface SponsorItem {
    headUrl: string; // 用户头像的URL
    itemId: number; // 用户ID
    itemName: string; // 用户名
    liveStreamId?: string; // 直播间ID（可选）
    mysteryMan?: boolean; // 是否是神秘人
}

export interface ChampionAward {
    awardIcon: string;
    awardId: number;
    awardImg: string;
    awardName: string;
    awardTip?: string;
    status: AwardStatusEnum;
    score: number;
    h5ShowScore: string;
    targetScore: number;
    targetH5ShowScore: string;
}

export interface AwardItemIcon {
    awardIcon: string;
    awardId: number;
    awardImg: string;
    awardName: string;
    awardDesc: string;
}
export interface TopAward {
    awardItemIcons: AwardItemIcon[];
    rankName: string;
}

export interface PrivilegeOverview {
    championAwards: ChampionAward[];
    topAwards: TopAward[];
}
export interface TopNTrafficAward {
    rankPointEnd: string; // 排名点结束
    timeStr: string; // 时间字符串
    count: string; // 数量
    textTemplate: string; // 文本模板
}
export interface SpecialTip {
    rank: number; // 排名
    descList: string[]; // 描述列表
}
export interface ScheduleViewNameMap {
    [key: string]: ScheduleDetails;
}

interface ScheduleDetails {
    scheduleName: string;
    promotionDesc: string;
}

export interface ExtraData {
    notSupportAuthorAdditionInfo?: boolean;
    authorStatusTip: string;
    inGame?: boolean;
    isJoinActivity?: boolean;
    isJoinShadowActivity?: boolean;
    repechageResult?: any;
    currentAuthorInfo?: any;
    scheduleRankNameLeft?: string; // 赛程榜单左侧标题名称
    scheduleRankName?: string; // 赛程榜单名称
    isRepechageActivity?: boolean; // 是否是复活赛
    isDirectPromotionFinal?: boolean;
    clearingEndTime?: number; // 结算结束时间
    isClearing?: boolean; // 是否正在结算
    privilegeDesc?: string; // 特权奖励描述
    privilegeIconUrl?: string; // 特权奖励图标URL
    // 新增流量提示条
    rankRoundTip?: {
        directPromotionTip: string;
        homeTrafficAwardText?: string;
    };
    topNTrafficAward?: TopNTrafficAward;
    topNTrafficAwardTips?: TopNTrafficAward[];
    promotionText?: string; // 晋级文本
    /** 特殊提示 */
    specialTip?: SpecialTip[];
    // 是否展示战队赛卡片
    showTeamRankCard?: boolean;
    // 新增榜单定段需求，将榜单分成好几块
    rankTips?: {
        startClose: number; // 开始关闭数字
        endClose: number; // 结束关闭数字
        content: string; // 内容
    }[];
    scheduleViewNameMap?: ScheduleViewNameMap;
    privilegeOverview?: PrivilegeOverview; // 特权奖励
    isActivityFinalSchedule?: boolean; // 是否是最后一日赛程
    changeLane?: boolean; // 提示切换赛道
    isJoinCurrRank?: boolean; // 主播是否参加了当前赛道
    isReplay?: boolean; // 是否是回看
    // 加成流量的展示位置
    addCardPrivilegeShowIndex?: number;
    // 加成流量文案
    addCardPrivilegeTip?: string;
    rankH5PayLink?: string;
    defaultSelectedAdditionInfo?: DefaultSelectedAdditionInfo;
    peakBattleSignStatus?: number;
    countyChampionAuthor?: CountyChampionAuthor;
}

export interface DefaultSelectedAdditionInfo {
    id?: number;
    time?: number;
    current?: boolean;
    status?: number;
    statusDesc?: string;
    smallTitle?: string;
    cohesionChallengeInfo?: any;
    source?: number;
    desc?: string[];
    title?: string;
    peakBattleInfo?: any;
}

export interface QueryRankExtraPostResponse {
    dataDetailMap: DataDetailMap;
    requestScatterTimeMs: number;
}

export interface DataDetailMap {
    pk: ExtraData & { showConfig: ShowConfig };
    propsCard: PropsCard;
    randomGift: RandomGift;
    hotListTab: HotListTab;
    strategyGuideTab: StrategyGuideTab;
    trafficRedPack: TrafficRedPack;
    activityRedPack: ActivityRedPack;
    queryRank: QueryRank; //  跟原来 /v3/queryrank 接口保持一致
    peakPrivilege: PeakPrivilege;
    peakRankTask: PeakRankTask;
    trafficPrivilegeUnlock: TrafficPrivilegeUnlock;
    growFansGame: GrowFansGame;
    countyChampionAuthor: CountyChampionAuthor;
    checkIn: CheckIn;
}

export interface CheckIn {
    status: TaskStatusEnum;
    cupTask: CupTask;
    fansTask: FansTask;
    limitTask: LimitTask;
    showConfig: ShowConfig;
    giftLink: string;
}

export enum TaskStatusEnum {
    CUP = 1, // 奖杯任务
    FANS = 2, // 涨粉任务
    LIMIT = 3, // 限时任务
}

interface CupTask {
    totalDayCount: number;
    finishDayCount: number;
}

interface FansTask {
    h5ShowFansCount: string;
    h5ShowNeedScore: string;
}

interface LimitTask {
    h5ShowNeedScore: string;
    subTitle: string;
}

export interface TrafficPrivilegeUnlock {
    showScene: number;
    status: number;
    score: string;
    showScore: string;
    taskAllFinishTotalPrivilegeCount: string;
    targetShowScore: string;
    taskList: TaskList[];
    showConfig: ShowConfig;
    trafficPrivilegeUnlockPreviewInfo: TrafficPrivilegeUnlockPreviewInfo;
}

export interface TrafficPrivilegeUnlockPreviewInfo {
    tabName: string;
    previewTitle: string;
    previewSubTitle: string;
    previewPrivilegeIcon: string;
}

export interface TaskList {
    targetShowScore: string;
    targetScore: string;
    finish: boolean;
    finishPrivilegeDesc: string;
    privilegeDesc: string;
}

export interface PeakRankTask {
    finishTask: boolean;
    desc: string;
    targetScore: string;
    score: string;
    h5ShowScore: string;
    targetH5ShowScore: string;
    showConfig: ShowConfig;
}

// 0-静态展示，1-展示解锁进度
export enum ShowPrivilegeEnum {
    STATIC = 0,
    DYNAMIC = 1,
}

// 0-仅展示今日奖励 1-仅展示决赛奖励 2-同时展示今日奖励和决赛奖励
export enum PrivielgeShowEnum {
    TODAY = 0,
    FINAL = 1,
    TODAY_FINAL = 2,
}

//  0和1-无需解锁(此时不展示进度和解锁状态) 2-解锁中 3-已解锁
export enum AwardStatusEnum {
    NO_NEED_UNLOCK_ONE = 0,
    NO_NEED_UNLOCK_TWO = 1,
    UNLOCKING = 2,
    UNLOCKED = 3,
}

export const AwardStatusMap: { [key in AwardStatusEnum]: string } = {
    [AwardStatusEnum.NO_NEED_UNLOCK_ONE]: '无需解锁',
    [AwardStatusEnum.NO_NEED_UNLOCK_TWO]: '无需解锁',
    [AwardStatusEnum.UNLOCKING]: '解锁中',
    [AwardStatusEnum.UNLOCKED]: '已解锁',
};

export interface PeakPrivilege {
    privilegeTitle: string;
    privilegeDetails: privilegeDetailsType[];
    showPrivilegeType: ShowPrivilegeEnum;
    privilegeShowStatus: PrivielgeShowEnum;
    topAwards: TopAward[];
    showConfig: ShowConfig;
}

export interface privilegeDetailsType {
    awardIcon: string;
    awardImg: string;
    awardTip: string;
    awardName: string;
    status: AwardStatusEnum;
    score: number;
    h5ShowScore: string;
    targetScore: number;
    targetH5ShowScore: string;
    awardId: number;
}

export interface GrowFansGame {
    privilegeIcon: string;
    title: string;
    joinTip: string;
    rankList: Rank[];
    rewardTip: string;
    showConfig: ShowConfig;
    author2LaneNameMap: { [key: string]: string };
    [x: string]: any;
}

export interface ChampionList {
    scheduleStartTime: number;
    status: number;
    item: Item;
}
export interface CountyChampionAuthor {
    championList: ChampionList[];
    showConfig: ShowConfig;
}

export interface Rank {
    item: Item;
    rankShowIndex: number;
    liveStreamId: string;
    followStatus: boolean;
}

export interface Item {
    itemId: number;
    itemName: string;
    headUrl: string;
    liveStreamId: string;
    mysteryMan: boolean;
}

export interface privilegeDetails {
    awardIcon: string;
    awardImg: string;
    awardTip: string;
    awardName: string;
    status: string;
    score: string;
    h5ShowScore: string;
    targetScore: string;
    targetH5ShowScore: string;
}

export interface QueryRank {
    todayPrivilegeTitle: string;
    queryRankV3: QueryRankPostResponse;
    showConfig: ShowConfig;
}

export interface StrategyGuideTab {
    strategyGuide: StrategyGuide[];
    showConfig: ShowConfig;
}

export interface StrategyGuide {
    imgUrl: Icon[];
    photoId: string;
    name: string;
}

export interface HotListTab {
    hotList: HotList[];
    showConfig: ShowConfig;
}

export interface HotList {
    tiele: string;
    subTitle: string;
    icon: Icon[];
    jumpUrl: string;
}

export interface Icon {
    cdn: string;
    url: string;
}

export interface ActivityRedPack {
    grabRedpack: GrabRedpack[];
    showConfig: ShowConfig;
}

export interface GrabRedpack {
    liveStreamId: string;
    redPackGrabTime: number;
    authorInfo: AuthorInfo;
    following: boolean;
    redPackId: string;
    redPackViewType: number;
    hasCash: boolean;
    redPackCashFen: number;
    redPackSourceType: number;
    redPackText: string;
}

export interface AuthorInfo {
    headUrl: string;
    userName: string;
    userId: number;
    liveStreamId: string;
}

export interface TrafficRedPack {
    icon: string;
    giftName: string;
    trafficTip: string;
    value: number;
    userTips: string[];
    showConfig: ShowConfig;
}

export interface RandomGift {
    giftList: GiftList[];
    showConfig: ShowConfig;
}

export interface GiftList {
    giftId: number;
    icon: string;
    tip: string;
    giftDesc: string;
    randomDesc: string;
    buttonText: string;
    value: number;
    order: number;
    token: string;
}

export interface PropsCard {
    totalCardCount?: number;
    authorCardCount?: number;
    cardList?: CardList[];
    showConfig: ShowConfig;
}

export interface CardList {
    icon: string;
    count: number;
    cardType: number;
    cardStatus: number;
    popView: PopView;
}

export interface PopView {
    title: string;
    desc: string;
    popIcon: string;
}

// 0-全部展示 1-客户端 2-PC
export enum PlatformEnum {
    ALL = 0,
    CLIENT = 1,
    PC = 2,
}

// 0-全部展示 1-仅主播侧 2-仅观众侧
export enum UserRoleEnum {
    ALL = 0,
    AUTHOR = 1,
    AUDIENCE = 2,
}

export interface ShowConfig {
    source: PlatformEnum[];
    supportOutLiveStream: boolean;
    supportIdentity: UserRoleEnum[];
}
