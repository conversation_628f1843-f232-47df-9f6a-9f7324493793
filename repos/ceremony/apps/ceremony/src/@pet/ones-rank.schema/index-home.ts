import type { DataDetailMap } from './query-rank';

/**
 * 请求: 首页赛程数据
 */
export interface RankFrameworkPostRequest {
    // 活动业务标识,h5 url下发activityBiz参数，直接取用
    activityBiz: string;
    // 直播间id,通常url下发liveStreamId参数，直接取用
    liveStreamId: string;
    // 赛程时间，仅当赛程回看切换时可能用到
    appointTime: number;
    // stageType 赛程阶段
    type: number;
}

/**
 * 响应: 首页赛程数据
 */
export interface RankFrameworkPostResponse {
    navigationDisplayName: string;
    // 是否为会看
    lookingBack: boolean;
    // 主播默认赛道信息等, 待后端补充
    anchorDisplayScheduleAndLane: AnchorDisplayScheduleAndLane;
    // 当前赛程
    anchorStageType: number;
    // 当前赛程下的赛段列表信息：如N进几，淘汰赛
    scheduleViewList?: ScheduleViewList[];
    // 赛道列表
    displayLaneViewList?: TabItem[];
    // 查询榜单接口所需rank biz
    queryRankBiz: string;
    stageLineViewList?: StageLineViewList[];
    stageTableViewList?: StageTableViewList[];
    offseason: boolean; // 休赛
    secondLevelStageTableView?: SecondLevelStageTableView;
    currAuthorInfo?: CurrAuthorInfo;
    // 额外信息
    extraInfo?: {
        additionInfo?: {
            dailyAddition: {
                showAdditionRate: string;
                additionRate: string;
                additionFactor: string;
            };
            additionTabs: [];
        };
        repechageStage?: boolean; // 是否是复活赛段
        repechageInfo?: {
            schedules: number;
            state: number;
            currentTimeAnchorRankId: number;
            serverTimestamp: number;
            repechageStage: boolean;
        };
        extraTabGroupInfo: ExtraTabGroupInfo;
        navigationDisplayName: string;
    };
}

// SubStageTableView 表示子阶段的视图信息
export interface SubStageTableView {
    stageName: string; // 子阶段的名称
    stageDesc: string; // 子阶段的描述
    stageType: number; // 子阶段的类型
}
// SecondLevelStageTableView 表示第二级赛程的视图信息，包含子阶段列表
export interface SecondLevelStageTableView {
    rootStageType: number; // 父赛事的stageType
    iconPic: string; // 第二阶段的图标URL
    stageName: string; // 第二级阶段的名称
    stageDesc: string; // 第二级阶段的描述
    stageStatus: number; // 第二级阶段的状态
    stageType: number; // 第二级阶段的类型
    stageStartTime: number; // 第二级阶段的开始时间（时间戳）
    stageEndTime: number; // 第二级阶段的结束时间（时间戳）
    subStageTableViewList: SubStageTableView[]; // 子阶段的列表
}
// CurrAuthorInfo表示当前作者的信息
export interface CurrAuthorInfo {
    itemId: number; // 主播ID
    itemName: string; // 主播名称
    headUrl: string; // 作者头像URL
    liveStreamId: string; // 直播流ID
    mysteryMan: boolean; // 是否为神秘人
}
// StageLineViewList 赛程全景列表
export interface StageLineViewList {
    iconPic: string; // 图标URL
    rankAlias?: string; // 排名别名（可选）
    stageEndTime: number; // 阶段结束时间（时间戳）
    stageName: string; // 阶段名称
    stageStartTime: number; // 阶段开始时间（时间戳）
    stageStatus: number; // 阶段状态
    stageType: number; // 阶段类型
    jumpUrl?: string; // 外跳链接
    stageDesc: string;
}
// StageTableViewList 并列赛程展示
export interface StageTableViewList {
    stageEndTime: number; // 阶段结束时间（时间戳）
    stageName: string; // 阶段名称
    stageStartTime: number; // 阶段开始时间（时间戳）
    stageStatus: number; // 阶段状态
    stageType: number; // 阶段类型
}

// AnchorDisplayScheduleAndLane表示主播赛程和赛道信息
export interface AnchorDisplayScheduleAndLane {
    displayName: string; // 赛程名称
    classifyId: number; // 分类ID，用于标识不同的分类
    rankActivityId: number; // 排名活动ID，用于标识特定的排名活动
    rankId: number; // 排名ID，用于标识具体的排名
    scheduleType: number; // 赛段，用于标识不同类型的赛段
    rankAliasName: string; // 排名别名，用于显示排名的别名 榜单接口用
    anchorId?: number; // 主播ID（可选），用于标识特定的主播
    laneClassifyName?: string; // 赛道分类名称（可选），用于标识赛道的分类名称
    laneName?: string; // 赛道名称（可选），用于标识具体的赛道名称
    stageType: number; // 阶段类型，用于标识不同的阶段类型
    curStageType: number;
    joinActivity?: boolean; // 是否参与活动
}

export interface ScheduleViewList {
    scheduleEndTime: number; // 赛程结束时间
    scheduleName: string; // 赛程名称
    status: number; // 1=未开始，2=进行中，3=已结束
    scheduleStartTime: number; // 赛程开始时间
    scheduleType: number; // 赛段类型
    stageType: number; // 赛程类型
    promotionDesc: string; // 新增，之前未下发
    preHeatInfo: {
        status: number; // 2展示预热
        mainTitle: string;
        subTitle: string;
        bgUrl: string;
    };
}

export interface BaseItem {
    scheduleId?: number; // 赛程ID（可选），
    classifyId: number; // 分类ID（可选），
    displayName: string; // 赛程名称
    rankId: number; // 赛道ID， 调用榜单接口用
    desc?: string; // 描述（可选），用于提供额外的描述信息
    order?: number; // 顺序（可选），用于标识排序顺序
    startTime?: number; // 开始时间（可选），用于标识计划或活动的开始时间
    rankAliasName: string; // 排名别名，用于显示排名的别名
    anchorId?: number; // 做默认赛道选中用,具有唯一性
    pinyin?: string; // 做默认赛道选中用,具有唯一性
}

export interface TabItem extends BaseItem {
    subLaneViews?: BaseItem[]; // 子赛道列表
}

export interface ExtraTabGroupInfo {
    tabGroupList: TabGroupList[];
}

export interface TabGroupList {
    title: string;
    subTitle: string;
    groupType: string;
    extraTabDetailList: {
        extraTabDataType: string;
        extraTabDataKey: keyof DataDetailMap;
    }[];
}
