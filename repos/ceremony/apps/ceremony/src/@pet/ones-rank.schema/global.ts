import type { BaseData } from '@pet/ones-ui.page-frame/schema';
import type {
    QueryRankPostResponse,
    ItemRankInfo,
} from '@pet/ones-rank.schema/query-rank';
import type {
    RankFrameworkPostResponse,
    BaseItem,
} from '@pet/ones-rank.schema/index-home';
export interface PropsCommonParams {
    //  依赖注入上下文，可不填
    useProps?: boolean;
    contextName?: symbol;
}
export interface RankInfoData extends QueryRankPostResponse {
    status: Status;
    focusActivityId?: string;
}

export interface HomeInfoData extends RankFrameworkPostResponse {
    // 规则处理
    rule?: string;
    // 类似十二时辰这种
    subTabs?: BaseItem[];
    cityType?: number[];
    trackText?: string;
}

export interface Status {
    init: boolean;
    loading: boolean;
    error: boolean;
    success: boolean;
}

export interface RankFrameBaseData extends BaseData {
    // 榜单容器标题，可选
    headerData?: {
        title?: string;
        subTitle?: string;
    };
    showRefresh?: boolean;
    // 整体页面状态
    homeStatus?: Status;
    // 榜单整体状态
    rankStatus: Status;
    // 是否在结算
    isClearing?: boolean;
    // 无赛事的提示
    noGameTip?: string;
}
export interface RankFrameSchema {
    data: RankFrameBaseData;
    needRefresh?: boolean;
}
export enum SpecialRankExtraType {
    SCORE = 'score',
    FOCUS = 'focus',
    ALL = 'all',
}
export interface RankConfigInfo {
    // 榜单列表
    list: ItemRankInfo[];
    // 当前赛程
    scheduleId?: number;
    // 当前赛道id
    currentRankId?: number;
    // 直播间上下滑id列表
    liveStreamIdList?: string[];
    // 埋点类型
    logType?: string;
    // 榜单分段顶部提示文案
    topTipsText?: string;
    // 是否需要榜单顶部提示
    needTop?: boolean;
    // 榜单分值标签名称，如盛典值
    scoreLabel?: string;
    // 榜单通用埋点
    logParams?: any;
    // 榜单关注id
    focusActivityId?: string;
    // 是否需要展示流量提示条信息
    isNeedTraffic?: boolean;
    // 是否需要展示分割线
    isNeedDivider?: boolean;
    avatarSize?: 'lg' | '2xs' | 'xs' | 'sm';
}
