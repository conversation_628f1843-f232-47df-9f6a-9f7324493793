<script lang="ts" setup>
import { ref } from 'vue';
import Modal from '@pet/ones-ui.use-modal/index.vue';
import { Right } from '@alive-ui/icon';
import { bolIsAuthor } from '@alive-ui/actions';
import Popup from '../components/popup.vue';
import Button from '../components/button.vue';
import type { PropsCard, CardList } from '@pet/ones-rank.schema/query-rank';

const props = withDefaults(
    defineProps<{
        propsCard?: PropsCard;
    }>(),
    {
        propsCard: () => ({
            totalCardCount: 0,
            authorCardCount: 0,
            cardList: [],
            showConfig: {
                source: [],
                supportOutLiveStream: false,
                supportIdentity: [],
            }, // Provide a valid default value for showConfig
        }),
    },
);

const popupStatus = ref(false);

const handlePropClick = () => {
    // TODO:跳转道具卡二级页
};

const openPopup = () => {
    popupStatus.value = true;
};

const visible = ref(false);
const modalProps = ref({});

const openUseModal = (cardItem: CardList) => {
    visible.value = true;
    modalProps.value = {
        title: cardItem?.popView?.title,
        iconUrl: cardItem?.popView?.popIcon,
        desc: cardItem?.popView?.desc,
    };
};
</script>

<template>
    <div class="prop-card-wrapper">
        <div class="head-box">
            <div class="left">
                <span class="title">我的道具卡</span>
                <span class="count a-text-main"
                    >共{{ propsCard?.totalCardCount }}张</span
                >
            </div>

            <div class="right a-text-main-o2" @click="handlePropClick">
                <span v-if="bolIsAuthor">玩转道具卡</span>

                <span v-else>
                    主播有<span class="count">{{
                        propsCard?.authorCardCount
                    }}</span
                    >张道具卡
                </span>

                <Right class="title-arrow ml-2px" />
            </div>
        </div>
        <div class="prop-card-list">
            <div
                v-for="(cardItem, index) in propsCard?.cardList"
                :key="index"
                class="prop-card"
            >
                <div v-if="cardItem?.count > 0" class="prop-card-tag">
                    x{{ cardItem?.count }}
                </div>

                <img class="prop-card-icon" :src="cardItem?.icon" />

                <Button
                    :card-item="cardItem"
                    @open-popup="openPopup"
                    @open-use-modal="openUseModal"
                />
            </div>
        </div>
        <Popup :show-popup="popupStatus" @popup-hide="popupStatus = false" />
        <!-- <Modal
            v-model:visible="visible"
            :info="modalProps"
            @ok="handleSubmit"
        ></Modal> -->
    </div>
</template>

<style lang="less" scoped>
.prop-card-wrapper {
    width: 358px;
    min-height: 126px;
    border-radius: 8px;
    background: rgba(154, 189, 255, 0.08);
    padding: 16px 0 16px 12px;
    margin: 0 auto;

    .head-box {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 20px;

        .left {
            display: flex;
            align-items: center;
            .title {
                font-family: 'MF YuanHei';
                font-size: 16px;
                font-weight: 400;
                line-height: 20px;
                background: linear-gradient(
                    95deg,
                    #d8ecff 7.58%,
                    #fff 55.12%,
                    #ffdac6 100%
                );
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .count {
                font-family: 'PingFang SC';
                font-size: 12px;
                font-weight: 400;
                margin-left: 4px;
                line-height: 20px;
            }
        }

        .right {
            font-family: 'PingFang SC';
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            display: flex;
            align-items: center;
            margin-right: 12px;

            .count {
                color: #ff5477;
                font-family: 'PingFang SC';
                font-size: 12px;
                font-weight: 600;
                line-height: 18px;
                margin-left: 2px;
                margin-right: 2px;
            }

            .title-arrow {
                font-size: 10px;
            }
        }
    }

    .prop-card-list {
        width: 346px;
        height: 64px;
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        overflow-x: auto;

        .prop-card {
            width: 44px;
            height: 64px;
            display: flex;
            flex-direction: column;
            position: relative;
            margin-right: 20px;

            .prop-card-tag {
                position: absolute;
                top: 0;
                right: 0;
                padding-left: 2px;
                padding-right: 2px;
                font-family: 'PingFang SC';
                font-size: 8px;
                font-weight: 500;
                line-height: 10px;
                height: 10px;
                color: #fff;
                border-radius: 6px 6px 6px 0px;
                background: #ff556f;
            }

            .prop-card-icon {
                width: 44px;
                width: 44px;
                height: 44px;
            }
        }
    }
}
</style>
