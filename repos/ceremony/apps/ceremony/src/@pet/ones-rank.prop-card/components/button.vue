<script lang="ts" setup>
import { AButton } from '@alive-ui/base';
import { bolIsAuthor } from '@alive-ui/actions';
import type { CardList } from '@pet/ones-rank.schema/query-rank';

const emit = defineEmits(['openPopup', 'openUseModal']);

const props = withDefaults(
    defineProps<{
        cardItem?: CardList | { cardStatus?: number; count?: number };
    }>(),
    {
        cardItem: () => ({ cardStatus: 0, count: 0 }),
    },
);

// 点击去使用，调用接口，如果可以使用主播端展示二次确认弹窗，观众端去使用拉背包面板tabId
const useClick = () => {
    // TODO:调接口
    if (bolIsAuthor) {
        emit('openUseModal', props.cardItem);
    }
};

// 点击去获取打开半屏弹窗
const getClick = () => {
    emit('openPopup');
};
</script>

<template>
    <!-- 去使用按钮 -->
    <AButton
        v-if="cardItem.cardStatus === 0 && (cardItem.count ?? 0) > 0"
        size="xs"
        type="primary"
        class="prop-card-btn"
        @click="useClick"
    >
        使用
    </AButton>
    <!-- 去获取按钮 -->
    <AButton
        v-if="cardItem.cardStatus === 0 && cardItem.count === 0"
        size="xs"
        class="prop-card-btn"
        @click="getClick"
    >
        去获取
    </AButton>
    <!-- 使用中按钮 -->
    <AButton
        v-if="cardItem.cardStatus === 1"
        size="xs"
        type="primary"
        class="prop-card-btn opactiy-5"
    >
        使用中
    </AButton>
</template>

<style lang="less" scoped>
.prop-card-btn {
    width: 42px;
    height: 20px;
    font-size: 10px;
}
.opactiy-5 {
    opacity: 0.5;
}
</style>
