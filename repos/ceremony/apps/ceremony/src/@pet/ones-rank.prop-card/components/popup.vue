<script lang="ts" setup>
import { ref, computed } from 'vue';
import useKconfBatch from '@pet/ones-use.useKconfBatch/index';
import { Popup } from '@lux/sharp-ui-next';
import { ACard, ACardContent, ACardTitle, AButton } from '@alive-ui/base';
import { bolIsAuthor } from '@alive-ui/actions';

const kconfStore = useKconfBatch();

const emit = defineEmits(['popupHide']);

const props = withDefaults(
    defineProps<{
        showPopup: boolean;
    }>(),
    {
        showPopup: false,
    },
);

const popupStatus = computed(() => props.showPopup);

const getPropCardClick = () => {
    // TODO:跳转盛典守护页
};
</script>

<template>
    <Popup
        v-if="popupStatus"
        v-model="popupStatus"
        :show-mask="true"
        position="bottom"
        :mask-closeable="true"
        class="prop-card-popup"
        @hide="$emit('popupHide')"
    >
        <ACard class="prop-card-container">
            <ACardTitle>
                {{ kconfStore.kconfData.propCard.cardGetDescTitle }}
            </ACardTitle>
            <ACardContent class="card-content">
                <img
                    class="card-img"
                    :src="kconfStore.kconfData.propCard.cardGetDescImg"
                />

                <!-- 仅观众端展示 -->
                <AButton
                    v-if="!bolIsAuthor"
                    size="xl"
                    type="primary"
                    class="card-btn"
                    @click="getPropCardClick"
                >
                    去获取
                </AButton>
            </ACardContent>
        </ACard>
    </Popup>
</template>

<style lang="less" scoped>
.prop-card-popup {
    :deep(.spu-popup__box) {
        width: 100%;
        height: 347px;
        border-radius: 16px 16px 0px 0px;
        background: #141522;
    }

    .prop-card-container {
        margin: 0;
        height: 100%;
        width: 100%;
    }

    .card-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        .card-img {
            width: 380px;
            margin-top: 2px;
        }

        .card-btn {
            margin-top: 19px;
        }
    }
}
</style>
