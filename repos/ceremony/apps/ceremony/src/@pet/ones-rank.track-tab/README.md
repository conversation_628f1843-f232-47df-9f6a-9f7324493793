
## 功能概述
 组件用于展示一个带有多级选项卡的界面，主要功能包括：

显示主赛道列表，支持在不同赛道之间切换。
如果主赛道下存在子赛道，则显示子赛道列表，并支持在子赛道之间切换。
支持城市切换功能。

## Props
contextName (String): 用于提供上下文名称的字符串，默认为空字符串。

## 事件
change: 当选项卡发生变化时触发，传递参数包括：
rankId: 当前选中的赛道ID。
subLaneViews: 当前选中的子赛道视图数组或索引。

## 组件说明
ATabs: 用于包裹选项卡的容器组件。
ATabList: 选项卡列表组件。
ATab: 单个选项卡组件。
ATabToggle: 切换城市的组件，只有在data.tabType为city时才会显示。

## 主要逻辑
数据初始化：通过trackTabFun函数根据contextName获取数据。
主赛道显示：通过data.list动态生成主赛道选项卡。
城市切换：如果data.tabType为city，则显示城市切换按钮。
子赛道显示：如果当前选中的主赛道包含多个子赛道，则显示子赛道选项卡。
选项卡切换：通过tabChange函数处理选项卡切换逻辑，并触发change事件。
