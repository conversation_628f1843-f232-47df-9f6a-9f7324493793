<template>
    <div v-if="tabList?.length" class="track-tab mb-1">
        <ATabs
            :key="randomKey"
            type="solid"
            :selected-index="selectedIndex"
            @change="tabChange"
        >
            <ATabList
                key="first-track"
                :class="{
                    'is-city-width': tabList?.length === 3,
                    'is-city-active-2':
                        tabList?.length === 3 && selectedIndex === 1,
                    'is-city-active-3':
                        tabList?.length === 3 && selectedIndex === 2,
                }"
            >
                <ATab
                    v-for="(item, index) in tabList"
                    :key="index + item?.displayName + item?.rankId"
                    class="track-tab-w"
                >
                    <!-- 赛道名称 -->
                    {{
                        nameSlice(
                            item.displayName,
                            newData.tabType === 'city' ? 6 : 5,
                        )
                    }}
                </ATab>
                <template v-if="newData.tabType === 'city'" #toggle>
                    <ATabToggle class="flex-none" @toggle="onToggle">
                        <div class="flex-center">
                            <div class="icon"></div>
                            <span class="text a-text-main text-12"
                                >切换{{ newData.btnText }}</span
                            >
                        </div>
                    </ATabToggle>
                </template>
            </ATabList>
        </ATabs>
        <div v-if="subLaneData?.length > 1" class="my-14px">
            <ATabs
                :key="JSON.stringify(subLaneData)"
                :selected-index="subSelectedIndex"
                @change="subChange"
            >
                <ATabList key="second-track">
                    <ATab v-for="subLane in subLaneData" :key="subLane.rankId">
                        {{ nameSlice(subLane.displayName, 5) }}
                    </ATab>
                </ATabList>
            </ATabs>
        </div>
        <CitySearch
            v-if="newData.tabType === 'city'"
            ref="citySearchRef"
            :context-name="contextName"
            :schedule-type="newData.anchorScheduleType"
            :data="cityData"
            @change="handleChangeCity"
        ></CitySearch>
    </div>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import CitySearch from '@pet/ones-rank.city-search/index.vue';
import { ATabs, ATabList, ATab, ATabToggle } from '@alive-ui/base';
import { nameSlice } from '@alive-ui/actions';
import { dataFun } from './data';
// import type { Schedule } from '@pet/ones-rank.city-search/schema';
import type { TabItem } from '@pet/ones-rank.schema/index-home';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
import type { ScheduleInfoPostResponse } from '@pet/ones-rank.city-search/schema';
import type { TabSchema } from './schema';
interface TabData extends PropsCommonParams {
    data?: TabSchema;
    // eslint-disable-next-line vue/require-default-prop
    cityData?: ScheduleInfoPostResponse | undefined;
}
const props = withDefaults(defineProps<TabData>(), {
    data: () => {
        return {
            defaultIndex: 0,
            defaultSubIndex: 0,
            list: [],
            anchorScheduleType: 0,
            tabType: 'city',
            // 二级赛道的初始展示数据
            subLaneData: [],
            btnText: '切换地区',
        };
    },
    useProps: false,
    contextName: Symbol.for('ctx'),
});
const newData = props.useProps
    ? computed(() => props.data)
    : dataFun(props.contextName);

const tabList = ref<TabItem[]>([]);

const emits = defineEmits(['change']);
const selectedIndex = ref();
const subSelectedIndex = ref();
const randomKey = ref();
watch(
    () => newData.value?.list,
    () => {
        tabList.value = [...newData.value?.list];
        selectedIndex.value = newData.value?.defaultIndex;
        subSelectedIndex.value = newData.value?.defaultSubIndex;
        randomKey.value = +new Date();
    },
    { immediate: true },
);
watch(
    () => newData.value?.defaultIndex,
    () => {
        selectedIndex.value = newData.value?.defaultIndex;
    },
    { immediate: true },
);
const subLaneData = computed(() => {
    return tabList.value[selectedIndex.value]?.subLaneViews || [];
});
const citySearchRef = ref();
const tabChange = (index: number) => {
    if (selectedIndex.value === index) {
        return;
    }
    const item = tabList.value[index];
    selectedIndex.value = index;
    if (!item.rankId) {
        // 二级
        const elem = item.subLaneViews?.[0];
        subSelectedIndex.value = 0;
        emits('change', elem?.rankId);
    } else {
        emits('change', item.rankId);
    }
};
const subChange = (index: number) => {
    subSelectedIndex.value = index;
    const elem = subLaneData.value[index];
    emits('change', elem.rankId);
};
const onToggle = () => {
    citySearchRef.value?.open();
};
const handleChangeCity = (item: TabItem) => {
    // console.log('TabItem', item);
    const find = tabList.value?.findIndex((elem: TabItem) => {
        return elem.anchorId === item.anchorId;
    });
    if (find === -1) {
        selectedIndex.value = 0;
        tabList.value?.splice(2, 1);
        tabList.value?.unshift(item);
        tabList.value = [...tabList.value];
    } else {
        selectedIndex.value = find;
    }

    emits('change', item.rankId);
};
</script>
<style lang="less" scoped>
/*
 3个tab调整样式，只能通过deep覆盖，后面沉淀到alive-ui中
*/
.is-city-width {
    :deep(.tabs-toogled-xs-list-w) {
        width: 280px;
    }
    :deep(.track-tab-w) {
        width: 90px;
    }
    :deep(.tabs-solid-xs-tab-active-bg) {
        width: 90px;
    }
}
.is-city-active-2 {
    /deep/ .tabs-solid-tab-indicator {
        margin-left: -2.5px;
    }
}
.is-city-active-3 {
    /deep/ .tabs-solid-tab-indicator {
        margin-left: -5px;
    }
}

.text {
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    text-align: center;
}
.icon {
    height: 10px;
    width: 10px;
    margin-right: 2px;
    background: url(./ic-switch.png) center / 100%;
}
</style>
