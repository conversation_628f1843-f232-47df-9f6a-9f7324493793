import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { HomeInfoData } from '@pet/ones-rank.schema/global';

export const dataFun = (contextName: symbol) => {
    const homeData = inject<Ref<HomeInfoData>>(contextName);
    const data = computed(() => {
        const {
            anchorDisplayScheduleAndLane,
            displayLaneViewList,
            anchorStageType,
        } = homeData?.value || {};
        const tabList = displayLaneViewList || [];
        const stage = anchorStageType || 0;
        const tabType = homeData?.value?.cityType?.includes(stage)
            ? 'city'
            : 'normal';

        // 一级默认tab 选中序号
        const index =
            tabList?.findIndex((elem: any) => {
                return anchorDisplayScheduleAndLane?.anchorId === elem.anchorId;
            }) || 0;
        const defaultIndex = index < 0 ? 0 : index;

        // 二级默认tab 选中序号
        const subIndex =
            tabList?.[defaultIndex]?.subLaneViews?.findIndex((elem: any) => {
                return anchorDisplayScheduleAndLane?.rankId === elem.rankId;
            }) || 0;
        const defaultSubIndex = subIndex < 0 ? 0 : subIndex;

        return {
            defaultIndex,
            defaultSubIndex,
            list: tabType === 'city' ? tabList?.slice(0, 3) : tabList,
            anchorScheduleType: anchorDisplayScheduleAndLane?.scheduleType,
            tabType,
            // 二级赛道的初始展示数据
            subLaneData: tabList?.[defaultIndex]?.subLaneViews || [],
            btnText: homeData?.value?.trackText || '赛道',
        };
    });
    return data;
};
