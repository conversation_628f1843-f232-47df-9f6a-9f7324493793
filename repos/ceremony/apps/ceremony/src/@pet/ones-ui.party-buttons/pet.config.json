{"$schema": "https://static.yximgs.com/udata/pkg/WEB-LIVE/pet/pet-config.schema-v4.json", "name": "@pet/ones-ui.party-buttons", "description": "线下晚会按钮组-预约/进直播/分享/节目单", "version": "0.2.0", "noCompiler": true, "docLink": "https://alive-ui.led.staging.kuaishou.com/pro", "documentFor": ["index.vue"], "dependencies": {"@alive-ui/actions": "^3.1.23", "@pet/ones-use.goLiveRoom": "0.0.1", "@pet/ones-use.useKconf": "0.3.0", "@pet/ones-use.useReservation": "0.1.0", "@pet/ones-use.useShare": "0.0.1", "@pet/ones-use.useTask": "0.0.2", "@yoda/bridge": "2.0.12", "dayjs": "^1.11.13", "lodash-es": "4.17.21", "pinia": "2.1.7"}}