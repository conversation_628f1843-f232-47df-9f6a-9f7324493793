<template>
    <div class="flex justify-between items-center party-buttons">
        <!-- 右侧按钮 -->
        <div
            v-click-log="
                btnLogParams(homeData?.showHeadLiveStream ? 'LIST' : 'FORESHOW')
            "
            v-show-log="
                btnLogParams(homeData?.showHeadLiveStream ? 'LIST' : 'FORESHOW')
            "
            class="h-[64px] leading-[64px] w-[100px] text-[16px] text-center bg-cover main-btn-l"
            @click="handler.program"
        >
            <div class="text-transparent bg-clip-text main-btn-text">
                {{
                    homeData?.showHeadLiveStream
                        ? reservationTextMap.program
                        : reservationTextMap.preview
                }}
            </div>
        </div>

        <!-- 中间按钮 -->
        <div
            class="h-[80px] w-[182px] text-center bg-cover main-btn-m"
            :class="{
                'leading-[80px]': homeData?.showHeadLiveStream,
                'pt-[16px]': !homeData?.showHeadLiveStream,
            }"
        >
            <div
                v-if="homeData?.showHeadLiveStream"
                v-click-log="btnLogParams('ENTER_LIVE')"
                v-show-log="btnLogParams('ENTER_LIVE')"
                class="text-[22px] text-transparent bg-clip-text main-btn-text"
                @click="handler.toLive"
            >
                <!-- 直播中 -->
                {{ homeData?.headLiveStreamData?.buttonText }}
            </div>
            <template
                v-else-if="homeData?.headLiveStreamData?.reservationLiveStream"
            >
                <!-- 已预约 -->
                <div
                    class="text-transparent bg-clip-text text-[20px] main-btn-text"
                >
                    {{ reservationTextMap.booked }}
                </div>
                <div v-if="leftMsText" class="text-[12px] a-text-main-o2">
                    <!-- 倒计时 -->
                    {{ leftMsText }}后开始
                </div>
            </template>
            <div
                v-else
                v-click-log="btnLogParams('ORDER')"
                v-show-log="btnLogParams('ORDER')"
                @click="handler.reservation"
            >
                <!-- 未预约 -->
                <div
                    class="text-transparent bg-clip-text text-[20px] main-btn-text"
                >
                    {{ reservationTextMap.unbooked }}
                </div>
                <div class="text-[12px] a-text-main-o2">
                    {{ homeData?.headLiveStreamData?.reservationCount }}人已预约
                </div>
            </div>
        </div>
        <!-- 左侧按钮 -->
        <div
            v-click-log="btnLogParams('SHARE')"
            v-show-log="btnLogParams('SHARE')"
            class="h-[64px] leading-[64px] w-[100px] text-[16px] text-center bg-cover main-btn-r"
            @click="handler.share"
        >
            <div class="text-transparent bg-clip-text main-btn-text">
                {{ reservationTextMap.share }}
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { throttle, debounce } from 'lodash-es';
import durationPlugin from 'dayjs/plugin/duration';
import dayjs from 'dayjs';
import useTask from '@pet/ones-use.useTask/index';
import { useShare, ShareMode } from '@pet/ones-use.useShare/index';
import { useReservation } from '@pet/ones-use.useReservation/index';
import useKconf from '@pet/ones-use.useKconf/index';
import { goToLiveRoom } from '@pet/ones-use.goLiveRoom';
import { goOtherPage } from '@alive-ui/actions';
import useCountdown from './useCountdown';

dayjs.extend(durationPlugin);

const props = defineProps({
    homeData: {
        type: Object,
        default: () => null,
    },
});
const emit = defineEmits(['update', 'refreshAll']);

const { conf4Tab } = storeToRefs(useKconf());
const taskStore = useTask();

const shareParams = computed(() => {
    return conf4Tab.value?.shareParams ?? {};
});

const reservationTextMap = computed(() => {
    return conf4Tab.value?.reservation ?? {};
});

const threshold = 500;
const handler = computed(() => {
    return {
        share: throttle(
            () => {
                useShare(ShareMode.StaticMode, shareParams.value);
                taskStore.refetchTask();
            },
            threshold,
            {
                trailing: false,
            },
        ),
        toLive: () => {
            goToLiveRoom(
                [props.homeData?.headLiveStreamData?.liveStreamIdStr],
                props.homeData?.headLiveStreamData?.liveStreamIdStr,
                {},
            );
            sessionStorage.setItem('fromVideoLive', 'true');
        },
        reservation: throttle(async () => {
            const { reservation } = useReservation();
            const res = await reservation();
            taskStore.refetchTask();
            emit('update', {
                headLiveStreamData: {
                    reservationLiveStream: res.reservationLiveStream,
                    reservationCount: res.reservationCount,
                },
            });
        }, threshold),
        program: throttle(
            () => goOtherPage('jimu', conf4Tab?.value.liveOrVideo?.programUrl),
            threshold,
        ),
        countdownEndRefresh: throttle(() => {
            if (!props.homeData?.showHeadLiveStream) {
                emit('refreshAll');
            }
        }, threshold),
    };
});

const btnLogParams = (btnType: string) => {
    return {
        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
        params: { btn_type: btnType },
    };
};

const computedStartTime = computed(() => {
    let to = 0;
    // 非直播
    if (!props.homeData?.showHeadLiveStream) {
        to = props.homeData?.headLiveStreamData?.startTime;
    }
    return to;
});

const countdown = useCountdown(
    computedStartTime.value,
    handler.value.countdownEndRefresh,
);

// 直播或预约状态切换时，重置倒计时
watch(computedStartTime, (to) => countdown.reset(to));

// 倒计时文字
const leftMsText = computed(() => {
    // 已预约，只有已经预约了直播之后才展示倒计时
    if (
        computedStartTime.value &&
        countdown.leftMs.value &&
        props.homeData?.headLiveStreamData?.reservationLiveStream
    ) {
        const duration = dayjs.duration(countdown.leftMs.value);

        // 动态拼接显示格式
        let format = '';
        if (duration.asMinutes() >= 1) {
            // 大于等于1分钟时，不展示秒
            if (duration.days() > 0) {
                format += 'DD天';
            }
            if (duration.hours() > 0 || duration.days() > 0) {
                format += 'HH时';
            }
            if (
                duration.minutes() > 0 ||
                duration.hours() > 0 ||
                duration.days() > 0
            ) {
                format += 'mm分';
            }
        } else {
            // 小于1分钟时，仅展示秒
            format = 'ss秒';
        }

        return duration.format(format);
    }
    return '';
});
</script>

<style lang="less" scoped>
.party-buttons {
    .main-btn-l {
        background-image: url('https://p4-live.wskwai.com/kos/nlav12706/ceremony/party_btn-l.70d64c16a14cd665.png');
    }
    .main-btn-m {
        background-image: url('https://p4-live.wskwai.com/kos/nlav12706/ceremony/party_btn_m.png');
    }
    .main-btn-r {
        background-image: url('https://p4-live.wskwai.com/kos/nlav12706/ceremony/party_btn_r.a6417e02a91a55c0.png');
    }
    .main-btn-text {
        // 将文字标签设置成行内样式，渐变背景色才能够覆盖全
        display: inline;
        font-family: HYYaKuHei;
        background-image: linear-gradient(
            141.56deg,
            #ffffff 27.32%,
            #ffc4a3 82.93%
        );
    }
}
</style>
