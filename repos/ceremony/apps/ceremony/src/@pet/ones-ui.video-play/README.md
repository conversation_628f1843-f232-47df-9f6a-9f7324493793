# Video Play 组件文档
## 功能概述
`Video Play` 是一个用于播放视频的 Vue 组件，支持自动播放、循环播放、范围播放等功能。该组件能够根据不同的设备类型进行优化，并处理视频播放中的各种事件。
## 属性和方法
### Props
| 属性名 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| `videoUrl` | `Array` | - | 必填，视频 URL 列表，每个元素包含 `url` 属性。 |
| `loopVideoUrl` | `Array` | `undefined` | 可选，循环播放的视频 URL 列表，每个元素包含 `url` 属性。 |
| `posterUrl` | `String` | `''` | 可选，视频封面图的 URL。 |
| `autoplay` | `Boolean` | `true` | 可选，是否自动播放视频。 |
| `objectFit` | `String` | `'cover'` | 可选，视频对象的填充方式，可选值为 `'contain'`, `'cover'`, `'fill'`, `'none'`, `'scale-down'`。 |
| `loop` | `Boolean` | `undefined` | 可选，是否循环播放视频。 |
| `muted` | `Boolean` | `true` | 可选，是否静音播放视频。 |
| `range` | `Object` | `{ from: 0, to: -1 }` | 可选，视频播放的范围，包含 `from` 和 `to` 两个属性，分别表示起始时间和结束时间。 |
| `isLowDev` | `Boolean` | `false` | 可选，是否为低性能设备。 |
### Emit Events
| 事件名 | 参数 | 描述 |
| --- | --- | --- |
| `click` | `videoInstance` | 视频被点击时触发，参数为当前视频的 DOM 实例。 |
| `video-end` | - | 视频播放结束时触发。 |
## 使用示例
```vue
<template>
  <div>
    <video-play
      :videoUrl=\"videoUrls\"
      :loopVideoUrl=\"loopVideoUrls\"
      :posterUrl=\"posterUrl\"
      :autoplay=\"true\"
      :objectFit=\"'cover'\"
      :loop=\"true\"
      :muted=\"true\"
      :range=\"{ from: 0, to: 10 }\"
      :isLowDev=\"false\"
      @click=\"handleClick\"
      @video-end=\"handleVideoEnd\"
    />
  </div>
</template>
<script setup>
import { ref } from 'vue';
import VideoPlay from '@pet/ones-ui.video-play';
const videoUrls = ref([
  { url: 'https://example.com/video1.mp4' },
  { url: 'https://example.com/video2.mp4' }
]);
const loopVideoUrls = ref([
  { url: 'https://example.com/loopVideo1.mp4' }
]);
const posterUrl = ref('https://example.com/poster.jpg');
const handleClick = (videoInstance) => {
  console.log('Video clicked:', videoInstance);
};
const handleVideoEnd = () => {
  console.log('Video ended');
};
</script>
```
## 注意事项
1. **自动播放**：如果 `autoplay` 设置为 `true`，组件会在视频准备好后自动播放。
2. **循环播放**：如果 `loop` 设置为 `true`，视频会循环播放。
3. **范围播放**：通过 `range` 属性可以指定视频播放的起始时间和结束时间。
4. **低性能设备**：如果 `isLowDev` 设置为 `true`，组件会进行一些优化以适应低性能设备。
5. **事件处理**：`click` 事件会传递当前视频的 DOM 实例，`video-end` 事件在视频播放结束后触发。
## 依赖项
- `vue`
- `@alive-ui/actions`
- `@ad/utils`
