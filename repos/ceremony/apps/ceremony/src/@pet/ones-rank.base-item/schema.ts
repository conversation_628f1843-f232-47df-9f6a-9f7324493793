export interface ItemRankInfo {
    addCardTimes?: string;
    displayScore?: string;
    item?: ItemRankInfoItem;
    followStatus: boolean; // 关注状态
    headUrl: string; // 用户头像
    itemId: number; // 用户id
    itemName: string; // 用户名
    liveStreamId: string; // 直播间id
    rankShowIndex: number; // 实际排名不使用
    h5RankShowIndex: string; // h5 页面展示的排名
    score: number; // 实际分数不使用
    h5ShowScore: string; // h5 页面展示的分数
    sponsors?: Sponsor[]; // 守护位
    mysteryMan?: boolean; // 是否是神秘人
    histParticipatedInRepechageTips?: string; // 历史参与过复活赛的提示
    teamLeader?: boolean; // 是否是队长
    extraData?: any;
}
export interface ItemRankInfoItem {
    headUrl: string; // 用户头像
    itemId: number; // 用户id
    itemName: string; // 用户名
    liveStreamId: string; // 直播间id
}

export interface ItemSchema extends ItemRankInfo {
    // 分数标签
    scoreLabel?: string;
    // 上下滑直播间
    liveStreamIdList?: string[];
    // 关注id，无关注按钮时非必需
    activityId?: string;
    // 埋点所需参数
    rankId?: number;
    // 其他埋点参数
    otherLogParams?: Record<string, any>;
    periodEndTime?: number;
}
export interface Sponsor extends ItemRankInfo {
    item: SponsorItem;
    h5ShowScore: string; // h5 页面展示的分数
}

export interface SponsorItem {
    headUrl: string;
    itemId: number;
    itemName: string;
    liveStreamId: string;
}
export interface ItemCommonParams {
    // 是否可点击
    disableClick?: boolean;
    // 头像大小
    avatarSize?: '3xs' | '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
    // 是否自定义单项宽，正常不需要
    needWidth?: boolean;
    // 是否展示右侧内容
    needShowRight?: boolean;
    // 是否是当前主播
    isCurrent?: boolean;
    // 当前主播标签
    currentLabel?: string;
}
