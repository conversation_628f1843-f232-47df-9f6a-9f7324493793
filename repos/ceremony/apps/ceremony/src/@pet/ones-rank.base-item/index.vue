<!-- 基础榜单框架 -->
<template>
    <APBaseRankItem
        :class="needWidth ? 'rank-width' : ''"
        :order="item.h5RankShowIndex"
        :is-current="isCurrent"
        :current-label="currentLabel"
        :log-params="{
            item: {
                itemId: item?.itemId,
                rankId: item?.rankId,
            },
            liveStreamId: item?.liveStreamId,
            itemId: item?.itemId,
            extra: {
                rank_show_index: item.h5RankShowIndex,
                is_follow: item.followStatus,
                ...otherLogParams,
            },
        }"
    >
        <template #left>
            <slot name="left">
                <APAvatar
                    class="avatar-deep-live-icon"
                    :class="{ 'error-avatar': !item.headUrl }"
                    :disable-click="item.disableClick"
                    :size="item.avatarSize"
                    :user-id="item.itemId"
                    :head-url="
                        !item.headUrl
                            ? 'https://p4-live.wskwai.com/kos/nlav12706/2025-summer/error-default.png'
                            : item.headUrl
                    "
                    :live-stream-id-list="item.liveStreamIdList"
                    :live-stream-id="item.liveStreamId"
                    :is-mystery-man="item.mysteryMan"
                >
                    <template #bottomInner>
                        <slot name="bottomInner" />
                    </template>
                    <!-- <template #bottomRight>
                        <LiveIcon v-if="item.liveStreamId" />
                    </template> -->
                </APAvatar>
            </slot>
        </template>
        <div
            class="rank-user-name"
            :class="{
                'equal-line-flex':
                    item?.histParticipatedInRepechageTips ||
                    item?.extraData?.honorTitle,
            }"
        >
            <slot name="user-name">
                <Username v-if="item?.itemName">
                    {{
                        nameSlice(
                            item.itemName,
                            item?.extraData?.honorTitle ? 3 : 6,
                        )
                    }}
                </Username>
            </slot>
            <slot name="extra-label"></slot>
            <div
                v-if="item?.histParticipatedInRepechageTips"
                class="is-revive-tag flex-center-center a-text-main opacity-60 text-10px mb-4px"
            >
                {{ item.histParticipatedInRepechageTips }}
            </div>
            <div
                v-if="item?.extraData?.honorTitle"
                class="hornor-title-label ml-2px text-10px mb-4px"
            >
                {{ item?.extraData?.honorTitle }}
            </div>
        </div>
        <!-- 新增白名单tag位 -->
        <slot name="tag"></slot>
        <slot name="info">
            <InfoItem v-if="item.scoreLabel">
                <template #key>
                    <slot name="key"> {{ item.scoreLabel }}： </slot>
                </template>
                <template #value>
                    <div class="flex-center">
                        <div class="score-value">
                            <slot name="value">
                                {{ item.h5ShowScore }}
                            </slot>
                        </div>
                        <slot name="extra"> </slot>
                    </div>
                </template>
            </InfoItem>
        </slot>
        <template #right>
            <!-- 不展示右侧区域信息 -->
            <div v-if="!needShowRight" />
            <slot v-else name="right">
                <APFocusButton
                    v-show="!(bolIsAuthor && item.itemId === Number(authorId))"
                    :focus-params="{
                        userId: item.itemId.toString(),
                        isfollow: true,
                        activityId: item.activityId,
                    }"
                    :need-shine="item.rankShowIndex < 4"
                    :action-status="item.followStatus"
                />
            </slot>
        </template>
    </APBaseRankItem>
</template>

<script lang="ts" setup>
// import LiveIcon from '@pet/ones-ui.dynamic-living-icon/index.vue';
import {
    BaseRankItem as APBaseRankItem,
    Avatar as APAvatar,
    FocusButton as APFocusButton,
} from '@alive-ui/pro';
// 备注： 关注按钮的样式需要单独引入，否则扫光无效
import '@alive-ui/pro/es/focus-button/styles/index';
import { RankList } from '@alive-ui/base';
import { nameSlice, bolIsAuthor, authorId } from '@alive-ui/actions';
import type { PropType } from 'vue';
import type { ItemRankInfo } from '@pet/ones-rank.schema/query-rank';
const { Username, InfoItem } = RankList;
defineProps({
    item: {
        type: Object as PropType<
            ItemRankInfo & {
                disableClick?: boolean;
                avatarSize?: 'lg' | '2xs' | 'xs' | 'sm';
                liveStreamIdList?: string[]; // 上下滑直播间列表
                activityId?: string;
                scheduleId?: string;
                rankId?: number;
                scoreLabel?: string;
            }
        >,
        default: () => {
            return {};
        },
    },
    otherLogParams: {
        type: Object,
        default: () => {
            return {};
        },
    },
    needWidth: {
        type: Boolean,
        default: false,
    },
    needShowRight: {
        type: Boolean,
        default: true,
    },
    isCurrent: {
        type: Boolean,
        default: false,
    },
    currentLabel: {
        type: String,
        default: '',
    },
});
</script>
<style lang="less" scoped>
.avatar-deep-live-icon {
    :deep(.i-alive-living) {
        width: 100%;
        height: 100%;
    }
}
.rank-width {
    width: 358px;
    padding-right: 9px;
    padding-left: 4px;
}
.is-revive-tag {
    width: fit-content;
    padding: 1px 4px;
    font-weight: 400;
    background: #ffffff0f;
    border-radius: 10px;
    white-space: nowrap;
    margin-left: 4px;
}
.equal-line-flex {
    display: flex;
    align-items: center;
}
.rank-username-attr {
    width: fit-content;
}
.hornor-title-label {
    width: fit-content;
    background: linear-gradient(264.25deg, #ffe4cb 0%, #fee1ba 93.85%);
    border-radius: 9px;
    color: #310b0f;
    opacity: 0.9;
    font-weight: 500;
    font-family: PingFang SC;
    letter-spacing: 0px;
    line-height: 18px;
    text-align: center;
    padding: 0px 3px;
    white-space: nowrap;
}
</style>
