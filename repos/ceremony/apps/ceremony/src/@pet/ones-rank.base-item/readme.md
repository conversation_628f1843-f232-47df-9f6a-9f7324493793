# 基础榜单框架组件

## 功能描述
该组件用于展示榜单中的单个项目，包括用户头像、用户名、分数信息、关注按钮等。用户可以通过该组件查看榜单项的详细信息，并进行关注操作。

## Props 提取
该组件接收以下 props：

| Prop Name     | Type     | Default Value | Description                               |
|---------------|----------|---------------|-------------------------------------------|
| item          | Object   | {}            | 榜单项信息，包含用户头像、用户名、分数等，遵循 `ItemRankInfo` 接口定义。 |
| otherLogParams| Object   | {}            | 其他日志参数                             |
| needWidth     | Boolean  | false         | 是否需要自适应宽度                       |
| needShowRight | Boolean  | true          | 是否展示右侧区域信息                     |
| isCurrent     | Boolean  | false         | 是否为当前项                             |
| currentLabel  | String   | ''            | 当前项的标签                             |

### `item` 的 Schema 定义
`item` 对象遵循以下接口定义：

```typescript
export interface ItemRankInfo {
    addCardTimes?: string;
    displayScore: string; // 分数
    item: ItemRankInfoItem;
    followStatus: boolean; // 关注状态
    headUrl: string; // 用户头像
    itemId: number; // 用户id
    itemName: string; // 用户名
    liveStreamId: string; // 直播间id
    rankShowIndex: number; // 实际排名不使用
    h5RankShowIndex: string; // h5 页面展示的排名
    score: number; // 实际分数不使用
    h5ShowScore: string; // h5 页面展示的分数
    sponsors?: Sponsor[]; // 守护位
    mysteryMan?: boolean; // 是否是神秘人
    histParticipatedInRepechageTips?: string; // 历史参与过复活赛的提示
}

export interface ItemRankInfoItem {
    headUrl: string; // 用户头像
    itemId: number; // 用户id
    itemName: string; // 用户名
    liveStreamId: string; // 直播间id
}
```

## Vue 使用代码 Demo
以下是如何在 Vue 组件中使用 BaseRankItem 组件的示例：

```vue
<script lang="ts" setup>
import BaseRankItem from '@pet/ones-rank.base-item/index.vue';
const rankItem = {
    itemId: 1,
    rankId: 1,
    itemName: '用户名称',
    h5ShowScore: '100',
    headUrl: 'https://example.com/avatar.jpg',
    liveStreamId: '12345',
    followStatus: false,
    rankShowIndex: 1,
    h5RankShowIndex: '1',
    score: 100,
    displayScore: '100',
};

const otherLogParams = {
    someParam: 'value',
};

</script>

<template>
  <BaseRankItem
    :item="rankItem"
    :otherLogParams="otherLogParams"
    :needWidth="true"
    :needShowRight="true"
    :isCurrent="true"
    currentLabel="当前"
  >
    <template #left>
      <div>自定义左侧内容</div>
    </template>
    <template #info>
      <div>自定义信息内容</div>
    </template>
    <template #right>
      <div>自定义右侧内容</div>
    </template>
  </BaseRankItem>
</template>

<style lang="less" scoped>
/* 添加样式 */
</style>
```
