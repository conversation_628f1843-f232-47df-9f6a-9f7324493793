<template>
    <ACard
        v-show-log="{
            action: 'OP_ACTIVITY_MORE_BOOST_CARD',
            params: {
                gift_id: curHonor?.id,
            },
        }"
    >
        <div class="honor-title-container">
            <ACardTitle class="title a-text-title">
                {{ kconfData?.extra?.glory?.title }}
            </ACardTitle>
            <div
                v-if="!isOutLiveRoom"
                v-click-log="{
                    action: 'OP_ACTIVITY_MORE_BOOST_CARD',
                    params: {
                        btn_type: '查看详情',
                        gift_id: curHonor?.id,
                    },
                }"
                class="title-more a-text-main-o2"
                @click="goDetail"
            >
                <span>查看详情</span>
                <Right class="right-icon" />
            </div>
        </div>
        <ACardContent>
            <div class="honor-swiper-container">
                <div class="swiper-one">
                    <div class="swiper-two">
                        <sp-swiper
                            ref="swiper"
                            loop
                            :init-index="currentTabIndex"
                            :is-overflow-hidden="false"
                            :indicator-show="false"
                            :follow-config="{
                                isFollow: true, // 是否开启跟手
                                scale: {
                                    // 缩放跟手效果配置
                                    minScale: 0.75, // 最小缩放尺寸
                                },
                            }"
                            @change="onChange"
                        >
                            <div
                                v-for="(item, index) in honorInfo
                                    ?.randomGiftInfo?.giftTabList"
                                :key="item.id"
                                class="normal"
                                :class="{
                                    'is-active': index === currentTabIndex,
                                    'is-next': index !== currentTabIndex,
                                }"
                            >
                                <div v-if="item.homeTabLabel" class="tag">
                                    {{ item.homeTabLabel }}
                                </div>
                                <div class="honor-img">
                                    <img :src="item.pic" alt="" />
                                </div>
                                <div class="honor-title a-text-title">
                                    {{ item.name }}
                                </div>
                                <div
                                    class="honor-subTitle a-text-white text-12"
                                >
                                    {{ item.button.tip }}
                                </div>
                            </div>
                        </sp-swiper>
                    </div>
                </div>
                <GloryartifactBtn
                    :id="curHonor?.id"
                    :source="'home'"
                    :text="curHonor?.button?.text"
                    :value="curHonor?.button?.value"
                    :tab-name="curHonor?.name"
                    :token="curHonor?.button?.token"
                    class="mt-14px"
                />
                <div
                    v-if="showBtn"
                    class="a-text-main text-12 mt-[14px] opacity-60 flex-center-center"
                >
                    {{ kconfData?.extra?.glory?.multipleTip }}
                </div>
            </div>
        </ACardContent>
    </ACard>
</template>

<script lang="ts" setup>
import { useRoute } from 'vue-router';
import { getCurrentInstance, ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { Right } from '@alive-ui/icon';
import { ACard, ACardTitle, ACardContent } from '@alive-ui/base';
import { isOutLiveRoom } from '@alive-ui/actions';
import useGloryStore from './stores/index';
import GloryartifactBtn from './page/components/honor-btn.vue';
import type { HemoResponse } from './schemas/index';

const route = useRoute();
const kconfStore = useKconfStore();
const { kconfData } = storeToRefs(kconfStore);
const gloryStore = useGloryStore();
const { showBtn } = storeToRefs(gloryStore);

const { proxy } = getCurrentInstance() as any;

const props = defineProps<{ serviceData: HemoResponse }>();

const honorInfo = ref<HemoResponse>({
    randomGiftInfo: {
        giftTabList: [],
        serverTime: 0,
        explosionTime: {
            text: '',
            periodText: '',
            label: '',
            type: 1,
            startTime: 0,
            endTime: 0,
        },
        currentTab: 0,
    },
});

watch(
    () => props.serviceData,
    (newVal) => {
        honorInfo.value = newVal;
    },
    {
        deep: true,
        immediate: true,
    },
);

// 跳转到荣耀神器二级页
const goDetail = () => {
    proxy.$router.push({
        name: 'honor',
        query: {
            entrySource: 'home',
        },
    });
};

// 当前索引
const currentTabIndex = ref(0);

const defaultIndex = computed<number>(() => {
    // 优先使用路由参数中的currentTab
    const currentTab =
        route?.query?.currentTab || honorInfo.value?.randomGiftInfo?.currentTab;

    // 如果没有任何tab数据或currentTab不存在，返回0
    if (!honorInfo.value?.randomGiftInfo?.giftTabList?.length || !currentTab) {
        return 0;
    }

    // 查找匹配的tab索引，找不到则返回0
    const index = honorInfo.value?.randomGiftInfo?.giftTabList.findIndex(
        (item) => item.id === Number(currentTab),
    );

    return index >= 0 ? index : 0; // 明确处理 -1 情况
});

// 初始化时设置正确的索引
watch(
    () => honorInfo.value,
    (newVal) => {
        if (newVal) {
            currentTabIndex.value = defaultIndex.value;
        }
    },
    { immediate: true },
);

// 当前神器数据
const curHonor = computed(() => {
    return honorInfo.value?.randomGiftInfo?.giftTabList[currentTabIndex.value];
});

// 轮播图切换时触发; index 当前索引; lastIndex 上一个索引
const onChange = (index: number, lastIndex: number) => {
    currentTabIndex.value = index;
};
</script>

<style scoped lang="less">
.honor-title-container {
    .title {
        margin-bottom: 20px;
    }
    .title-more {
        position: absolute;
        top: 25px;
        right: 12px;
        display: flex;
        align-items: center;
        height: 18px;
        margin-left: 46px;
        font-family: 'PingFang SC', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        .right-icon {
            margin-left: 2px;
            width: 10px;
            height: 10px;
        }
    }
}

.honor-swiper-container {
    .swiper-one {
        display: flex;
        align-items: center;
        width: 100%;
        overflow: hidden;
        .swiper-two {
            width: 192px;
            margin: 0 auto;
            .normal {
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 170px;
                background: url(./assets/download.png) center/cover no-repeat;
                border-radius: 8px;
                .tag {
                    background: linear-gradient(
                        88.24deg,
                        #ffb470 1.42%,
                        #ffe8be 100.84%
                    );
                    border-radius: 6px 0;
                    position: absolute;
                    top: 0;
                    left: 0;
                    color: #450e0e;
                    font-size: 10px;
                    padding: 0 4px;
                    line-height: 15px;
                    text-align: center;
                    vertical-align: middle;
                }
                .honor-img {
                    width: 124px;
                    height: 124px;
                    display: flex;
                    align-items: center;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        margin-top: 30px;
                    }
                }
                .honor-title {
                    background: linear-gradient(
                        78.49deg,
                        #f6fdff 15.2%,
                        #ffc4a3 87.91%
                    );
                    font-family: HYYakuHei;
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    font-size: 16px;
                    margin: 4px 0 2px 0;
                }
                .honor-subTitle {
                    opacity: 0.5;
                    padding-bottom: 8px;
                }
                &.is-active {
                    opacity: 1;
                }
                &.is-next {
                    opacity: 0.5;
                }
            }
        }
    }
}
</style>
