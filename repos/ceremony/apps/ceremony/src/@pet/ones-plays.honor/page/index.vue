<template>
    <APageAdaptor class="glory-page pt-30px px-16px a-bg-part">
        <template v-if="pageStatus.success">
            <ACard class="m-0 w-full no-bg">
                <div class="title-box">
                    <div class="left-icon-box">
                        <!-- 返回按钮 -->
                        <BackStatic
                            v-if="showBack"
                            v-pcDirectives:hide
                            @click="handleBack"
                        />
                        <img
                            v-if="showMain"
                            src="../assets/leftTopIcon.png"
                            alt=""
                            class="gis-small-main mb-14px"
                            @click="goMain"
                        />
                    </div>
                    <ACardTitle class="glory-card a-text-title">
                        {{ kconfData?.extra?.glory?.title }}
                    </ACardTitle>
                    <RuleIcon rule-key="gloryArtifact" />
                </div>

                <ACardContent>
                    <!-- 暴奖时刻 -->
                    <HonorMoment v-if="gloryInfo" />

                    <!-- tabs && swiper -->
                    <HonorContent />
                </ACardContent>
            </ACard>
        </template>
        <template v-else>
            <ElseStatus
                class="else-status"
                :page-status="pageStatus"
                :is-show-refresh="true"
                :no-data="false"
                @refresh="gloryStore.init()"
            ></ElseStatus>
        </template>
    </APageAdaptor>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { computed, defineAsyncComponent } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { BackStatic } from '@alive-ui/icon';
import {
    APageAdaptor,
    ACard,
    ACardTitle,
    ACardContent,
    ElseStatus,
} from '@alive-ui/base';
import { getLiveRoomInfo, getQuery, Report } from '@alive-ui/actions';
import useGloryStore from '../stores';
import { EntrySource } from '../schemas/index.js';
import HonorMoment from './components/honor-moment.vue';
import HonorContent from './components/honor-content.vue';
import { sendFmp } from '@/common/logger';

const RuleIcon = defineAsyncComponent(
    () => import('@pet/ones-ui.rule-icon/index.vue'),
);

const kconfStore = useKconfStore();
const { kconfData } = storeToRefs(kconfStore);

const gloryStore = useGloryStore();
const router = useRouter();
const route = useRoute();

const { gloryInfo, pageStatus } = storeToRefs(gloryStore);

gloryStore.init().finally(() => {
    sendFmp();
});

const goMain = () => {
    router.push({
        name: 'home',
        query: {
            // 跳转的时候去处链接中的sourceEntrance参数
            sourceEntrance: '',
        },
    });
};

const showBack = computed(() => {
    return EntrySource.HOME === route?.query?.entrySource;
});

const showMain = computed(() => {
    return !showBack.value && getQuery('entry_src') !== 'giftBanner';
});

const handleBack = () => {
    router.back();
};
</script>

<style lang="less" scoped>
.glory-page {
    .title-box {
        display: flex;
        :deep(.rule-icon) {
            width: 32px;
            height: 32px;
        }
    }

    .left-icon-box {
        width: 32px;
        height: 32px;
    }
    .glory-card {
        flex-grow: 1;
        text-align: center;
    }
    .rule-btn {
        flex-shrink: 0;
    }
    .no-bg {
        background: none;
    }
    .glory-card {
        margin-bottom: 18px;
    }
    .else-status {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translateX(-50%) translateY(-50%);
    }
    .gis-small-main {
        width: 40px;
        height: 40px;
    }
}
</style>
