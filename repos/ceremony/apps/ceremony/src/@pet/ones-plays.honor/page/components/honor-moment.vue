<template>
    <div v-if="gloryInfo && !!gloryInfo.explosionTime.type" class="reward-time">
        <div v-if="gloryInfo?.explosionTime.label" class="reward-tag">
            <span class="up-icon"></span>
            <span
                v-for="(tag, index) in tagList"
                :key="index"
                :class="{ 'highlight-text': tag.type }"
            >
                {{ tag.text }}
            </span>
        </div>
        <!-- 预告时刻 -->
        <!-- (剩余毫秒数 > 结束时间 - 开始时间 || 剩余毫秒数为0) && 倒计时已经结束  -->
        <div
            v-if="
                leftMs >
                    gloryInfo.explosionTime.endTime -
                        gloryInfo.explosionTime.startTime ||
                (leftMs && loadingDelay)
            "
            class="pre-text a-text-button"
        >
            {{ gloryInfo.explosionTime.text }}
        </div>
        <!-- 倒计时刻 -->
        <div v-else class="count-area px-16px">
            <div class="left">
                <div class="left-text">
                    <div class="main-text a-text-button">爆奖时刻</div>
                    <div class="sub-text mt-7px a-text-button">
                        {{
                            gloryInfo.explosionTime.periodText ||
                            gloryInfo.explosionTime.text
                        }}
                    </div>
                </div>
            </div>

            <div class="right text-din a-text-button mt-6px">
                <div class="number">
                    {{ endCount[0] }}
                </div>
                <div class="maohao mx-5px"></div>
                <div class="number">
                    {{ endCount[1] }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, computed, watch, onUnmounted, type Ref, unref } from 'vue';
import { storeToRefs } from 'pinia';
import { useCountdownTo, convertSToMinusSecond } from '@alive-ui/actions';
import useGloryStore from '../../stores/index';

const gloryStore = useGloryStore();
const { gloryInfo } = storeToRefs(gloryStore);
const useCountdownText = ref<{
    text: Ref<string>;
    reset: (_t: number) => void;
    leftMs: Ref<number>;
}>();
const loadingDelay = ref(false);
let timer: ReturnType<typeof setTimeout>;

watch(
    () => gloryInfo.value?.explosionTime.endTime,
    (newVal, oldVal) => {
        if (newVal && newVal !== oldVal) {
            useCountdownText.value = useCountdownTo(newVal, {
                immediateEmit: true,
                useServerTime: true,
                transformFn: (t) => convertSToMinusSecond(t / 1000),
                onEnd: () => {
                    console.log('倒计时结束');
                    loadingDelay.value = true;
                    timer && clearTimeout(timer);
                    // 防止前端倒计时，请求后端接口时，与server端endTime有误差
                    // 误差场景：网络延迟、js（setInterval）执行机制
                    timer = setTimeout(async () => {
                        await gloryStore.getGloryInfo();
                        loadingDelay.value = false;
                    }, 1500);
                },
            });
        }
    },
    {
        immediate: true,
        deep: true,
    },
);

const endCount = computed(() => {
    const text = unref(useCountdownText.value?.text) || '00:00';
    const [first = '00', second = '00'] = text.split(':');
    return [first, second];
});

const leftMs = computed(() => {
    // Vue会自动解构, 使用unref显示解构，预防eslint报错
    return unref(useCountdownText.value?.leftMs) ?? 0;
});

const tagList = computed(() => {
    return gloryInfo?.value?.explosionTime?.label
        .split(/(\$[^$]*\$)/)
        .filter((part) => part !== '') // 过滤空字符串
        .map((part) => ({
            text: part.replace(/\$/g, ''), // 移除$符号
            type: /^\$.*\$$/.test(part), // 判断是否被$包裹
        }));
});

onUnmounted(() => {
    timer && clearTimeout(timer);
});
</script>

<style lang="less" scoped>
.reward-time {
    width: 382px;
    height: 80px;
    background: url('./../../assets/topBg.png') center / 100% no-repeat;
    position: relative;
    .reward-tag {
        display: flex;
        position: absolute;
        right: 0;
        top: 0;
        background: linear-gradient(122.14deg, #fe5050 30.71%, #d71d1d 88%);
        border-image-source: linear-gradient(
                0deg,
                rgba(255, 255, 255, 0.16),
                rgba(255, 255, 255, 0.16)
            ),
            linear-gradient(0deg, #ffddb2, #ffddb2);
        height: 26px;
        border-radius: 0 12px;
        border: 1px solid;
        padding: 0 10px;
        font-family: HYYakuHei;
        font-size: 15px;
        text-align: left;
        align-items: center;
        justify-content: center;
        color: #fff4e4;
        .up-icon {
            width: 18px;
            height: 18px;
            background: url('./../../assets/up-icon.png') center / 100%
                no-repeat;
        }
        .highlight-text {
            color: #ffe500;
        }
    }
    .pre-text {
        width: 100%;
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 31px;
        font-family: HYYakuHei;
        font-size: 22px;
        line-height: 28px;
        opacity: 0.8;
        text-align: center;
        .title-icon {
            width: 24px;
            height: 24px;
            background: url('./../../assets/modal-title-icon_1_2x.png') center /
                100% no-repeat;
        }
    }
    .count-area {
        width: 100%;
        position: absolute;
        display: flex;
        justify-content: space-between;
        top: 19px;
        height: 44px;
        .left {
            display: flex;
            .gift-small-bg {
                width: 44px;
                height: 44px;
            }
            .left-text {
                opacity: 0.8;
                .main-text {
                    font-family: HYYakuHei;
                    font-size: 22px;
                    line-height: 24px;
                    text-align: left;
                }
                .sub-text {
                    font-family: PingFang SC;
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 14px;
                    text-align: left;
                }
            }
        }
        .right {
            display: flex;
            align-items: center;
            position: relative;
            top: 6px;
            .number {
                height: 32px;
                width: 30px;
                line-height: 32px;
                border-radius: 5px;
                font-size: 22px;
                font-weight: 400;
                text-align: center;
                background: #ffffff66;
            }
            .maohao {
                width: 2px;
                height: 7px;
                background: url('./../../assets/maohao.png') center / 100%
                    no-repeat;
            }
        }
    }
}
</style>
