<template>
    <div
        v-if="showBtn"
        v-show-log="{
            action: 'OP_ACTIVITY_SEND_BUTTON',
            params: {
                count: value,
                gift_id: id,
                tab_name: tabName,
            },
        }"
        v-click-log="SendBtnLog()"
        class="glory-artifact-bottom"
        :class="bottomClass"
    >
        <AButton type="primary" size="lg" @click="sendGift">
            <div class="a-text-button">
                {{ text }}
                <div class="sub-text">{{ value }}快币</div>
            </div>
        </AButton>
    </div>
</template>
<script setup lang="ts">
import { defineProps, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { sendGiftMultiple } from '@pet/ones-use.send-gift-multiple';
import { AButton } from '@alive-ui/base';
import { userId, login } from '@alive-ui/actions';
import useGloryStore from '../../stores/index';
import { sendTask } from '@/common/logger';

const gloryStore = useGloryStore();
const { showBtn } = storeToRefs(gloryStore);

const props = defineProps({
    id: {
        type: Number,
        default: 0,
    },
    tabName: {
        type: String,
        default: '',
    },
    value: {
        type: [String, Number],
        default: '',
    },
    text: {
        type: String,
        default: '',
    },
    bottomClass: {
        type: String,
        default: '',
    },
    send: {
        type: Function,
        default() {
            return () => {};
        },
    },
    type: {
        type: Number,
        default: -1,
    },
    token: {
        type: String,
        default: '',
    },
    source: {
        type: String,
        default: '',
    },
});

const SendBtnLog = () => {
    if (props.source === 'home') {
        return {
            action: 'OP_ACTIVITY_MORE_BOOST_CARD',
            params: {
                btn_type: props.text,
                gift_id: props.id,
            },
        };
    }
    return {
        action: 'OP_ACTIVITY_SEND_BUTTON',
        params: {
            count: props.value,
            gift_id: props.id,
            tab_name: props.tabName,
        },
    };
};

const canSend = ref(true);

const emit = defineEmits(['send']);

const sendGift = () => {
    const sendValue = {
        giftId: `${props?.id}`,
        giftName: props?.tabName,
        giftToken: props?.token,
        unitPrice: +props?.value,
    };

    // 如果正在发送中，直接返回
    if (!canSend.value) return;

    // 检查用户是否登录
    if (!userId || userId === '0') {
        login({ checkFromServer: false });

        return;
    }
    // 检查是否有有效的礼物数据
    if (!sendValue) return;

    // 设置为发送中状态
    canSend.value = false;
    sendGiftMultiple(
        sendValue,
        () => {
            sendTask('CLICK', {
                type: 'TASK_EVENT',
                action: 'OP_ACTIVITY_SEND_BUTTON',
                params: {
                    count: props.value,
                    gift_id: props.id,
                    tab_name: props.tabName,
                },
                status: 'SUCCESS',
            });
        },
        () => {
            // 发送成功后才重置为可发送状态
            canSend.value = true;
        },
    ).finally(() => {
        canSend.value = true;
    });
};
</script>
<style lang="less" scoped>
.glory-artifact-bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    .sub-text {
        font-size: 10px;
        font-weight: 400;
        text-align: center;
    }
}
</style>
