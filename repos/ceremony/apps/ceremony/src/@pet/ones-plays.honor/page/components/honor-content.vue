<template>
    <div
        v-if="gloryInfo && gloryInfo.giftTabList.length > 0"
        class="honor-content"
    >
        <!-- 倒计时结束时，停留到他点击的tab，而不是走到了定位逻辑 -->
        <ACard class="honor-acard">
            <ATabs :default-index="defaultIndex" @change="gloryStore.tabChange">
                <ATabList>
                    <ATab
                        v-for="item in gloryInfo.giftTabList"
                        :key="item.id"
                        >{{ item.name }}</ATab
                    >
                </ATabList>
            </ATabs>
            <div v-x-scroll class="scroll-container">
                <div
                    v-for="(item, index) in tabInfo?.effectList"
                    :key="index"
                    class="card a-bg-substrate"
                >
                    <div class="icon-item">
                        <div v-if="item.label" class="icon-tag">
                            {{ item.label }}
                        </div>
                        <img :src="item.pic" class="icon-img mt-8px" alt="" />
                        <div class="icon-title">
                            {{ item.name }}
                        </div>
                        <div class="icon-score">+{{ item.score }}分</div>
                        <div class="icon-coin">价值{{ item.ksCoin }}快币</div>
                    </div>
                </div>
            </div>
            <div class="main-frame">
                <img class="secondary-frame" :src="tabInfo.videoCover" alt="" />
                <div class="layout-frame">
                    <div class="content-frame">
                        <span class="highlight-section"
                            >累送高爆率 还差{{ giftCount }}个</span
                        >
                    </div>
                    <div
                        class="tip-area flex-start-center"
                        @click="gloryStore.showModal"
                    >
                        <img
                            src="./../../assets/tip.png"
                            class="content-group"
                            alt=""
                        />
                    </div>
                </div>
                <div class="container_frame-ellipse_frame-vector">
                    <ProgressCircle
                        :radius="287"
                        :angle-span="49"
                        :progress-width="8"
                        :bottom-margin="32"
                        :nodes="[]"
                        :value="tabInfo?.progress?.currentCount"
                        :max="tabInfo?.progress?.targetCount"
                        :track-gradient-string="'linear-gradient(90deg, rgba(255, 255, 255, 0) 3.37%, rgba(255, 255, 255, 0.4) 22%, rgba(255, 255, 255, 0.4) 77.89%, rgba(255, 255, 255, 0) 96.52%)'"
                    />
                </div>
                <div v-if="tabInfo?.collectInfo?.display" class="nested-frame">
                    <img
                        class="nested-element-gu-li"
                        :src="tabInfo?.collectInfo?.pic"
                        alt=""
                    />
                </div>
            </div>
            <div class="btn">
                <div v-if="showBtn" class="btn-sub">
                    {{ tabInfo?.button?.tip }}
                </div>
                <GloryartifactBtn
                    :id="tabInfo?.id"
                    :source="'second'"
                    :text="tabInfo?.button?.text"
                    :value="tabInfo?.button?.value"
                    :tab-name="tabInfo?.name"
                    :token="tabInfo?.button?.token"
                />
            </div>
            <!-- 规则 -->
            <div class="reward-bg">
                <div
                    v-if="kconfData?.extra?.glory.ruleBgBottom"
                    class="reward-bottom-wrap"
                >
                    <img :src="kconfData?.extra?.glory?.ruleBgBottom" />
                </div>
            </div>
        </ACard>
        <AModal
            v-if="modalVisible"
            :open="modalVisible"
            modal-classes="buff-popup-modal"
            class="honor-tip-model"
            @cancel="gloryStore.close"
        >
            <div class="modal-content">
                <div class="modal-title center mt-12px">爆率翻倍</div>
                <div class="modal-sub center mt-14px formatted-text">
                    {{ formattedText }}
                </div>
                <div class="modal-footer mt-16px">
                    <AButton
                        type="primary"
                        size="sm"
                        class="center"
                        @click="gloryStore.close"
                        >我知道了</AButton
                    >
                </div>
            </div>
        </AModal>
    </div>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import ProgressCircle from '@pet/ones-ui.progress-circle/index.vue';
import { ACard, ATabs, ATabList, ATab, AButton, AModal } from '@alive-ui/base';
import useGloryStore from '../../stores/index';
import GloryartifactBtn from './honor-btn.vue';

const kconfStore = useKconfStore();
const { kconfData } = storeToRefs(kconfStore);

const gloryStore = useGloryStore();
const { gloryInfo, tabInfo, modalVisible, defaultIndex, showBtn } =
    storeToRefs(gloryStore);

const formattedText = computed(() => {
    return tabInfo.value?.progress?.tip?.replace(/\$/g, '\n');
});

const giftCount = computed(() => {
    const targetCount = tabInfo.value?.progress?.targetCount || 0;
    const currentCount = tabInfo.value?.progress?.currentCount || 0;

    // 确保计算结果不会为负数
    return Math.max(0, targetCount - currentCount);
});
</script>
<style lang="less" scoped>
.honor-content {
    .honor-acard {
        background-color: rgba(128, 128, 128, 0.08);
    }
    .scroll-container {
        width: 382px;
        display: flex;
        align-items: flex-start;
        overflow-x: auto;
        padding: 16px 7px 16px 11px;
        .card {
            flex: 0 0 78px;
            margin-right: 4px;
            box-sizing: border-box;
            height: 124px;
            border-radius: 8px;
            text-align: center;
            .icon-item {
                position: relative;
                .icon-tag {
                    position: absolute;
                    right: 0;
                    top: 0;
                    height: 18px;
                    background: linear-gradient(
                        262.85deg,
                        #ff586d 0%,
                        #ff4594 100%
                    );
                    border-radius: 12.03px 8px 0 12.03px;
                    color: #ffffff;
                    font-size: 10px;
                    font-weight: 400;
                    line-height: 18px;
                    padding: 0 4px 0 6px;
                }
                .icon-img {
                    width: 60px;
                    height: 60px;
                }
                .icon-title {
                    color: #ffdfbf;
                    font-size: 12px;
                    font-weight: 500;
                    margin-top: -4px;
                }
                .icon-score {
                    color: #ffdfbf;
                    opacity: 0.6;
                    font-size: 10px;
                    font-weight: 400;
                }
                .icon-coin {
                    color: #ffdfbf;
                    opacity: 0.6;
                    font-size: 9px;
                    font-weight: 400;
                    margin-top: -1px;
                }
            }
        }
    }

    /* 隐藏滚动条但保留滚动功能 */
    .scroll-container::-webkit-scrollbar {
        display: none;
    }

    .scroll-container {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }

    .main-frame {
        box-sizing: border-box;
        height: 194px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .secondary-frame {
            box-sizing: border-box;
            position: absolute;
            width: 382px;
            height: 236px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 3;
            object-fit: cover;
        }
        .layout-frame {
            display: flex;
            align-items: center;
            position: absolute;
            bottom: 25px;
            .content-frame {
                color: #ffdfbf;
                font-size: 14px;
                margin-right: 4px;
                .highlight-section {
                    font-weight: 400;
                    margin-right: 1px;
                }
                .highlight-section-primary {
                    font-weight: 600;
                }
                .highlight-section-primary {
                    font-weight: 600;
                }
            }
            .tip-area {
                width: 24px;
                height: 24px;
                z-index: 100;
                .content-group {
                    width: 12px;
                    height: 12px;
                }
            }
        }
        .container_frame-ellipse_frame-vector {
            box-sizing: border-box;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            flex-direction: column;
            padding-top: 33px;
            align-items: flex-start;
            z-index: 5;
            .ellipse-background {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        .nested-frame {
            box-sizing: border-box;
            position: absolute;
            margin-right: 25px;
            width: 51px;
            height: 63px;
            right: 28px;
            bottom: 48px;
            z-index: 6;
            .nested-element-gu-li {
                box-sizing: border-box;
                position: absolute;
                top: -1px;
                left: 0px;
                width: 51px;
                height: 51px;
                z-index: 1;
                object-fit: contain;
            }
        }
    }

    .btn {
        text-align: center;
        margin-top: 30px;
        .btn-sub {
            color: #ffdfbf;
            opacity: 0.8;
            font-size: 12px;
            font-weight: 400;
            margin-bottom: 8px;
        }
    }
    .reward-bg {
        position: relative;
        width: 100%;
        padding: 16px 12px;
        img {
            width: 100%;
            object-fit: contain;
        }
    }
}
.center {
    text-align: center;
}
.honor-tip-model {
    :deep(.spu-popup__mask) {
        background: rgba(0, 0, 0, 0.5);
    }
    :deep(.buff-popup-modal) {
        width: 278px;
        height: 152px;
        border-radius: 10px;
        background: #191e58;
        color: #ffdfbf;
        .modal-content {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .modal-title {
                font-size: 16px;
                font-weight: 500;
            }
            .modal-sub {
                opacity: 0.6;
                font-size: 12px;
                font-weight: 400;
                line-height: 18px;
            }
            .formatted-text {
                white-space: pre-line;
            }
            .modal-footer {
                display: flex;
                justify-content: center;
                margin-bottom: 20px;
            }
        }
    }
}
</style>
