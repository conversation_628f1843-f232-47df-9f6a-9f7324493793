export interface RandomEffectHomePostResponse {
    serverTime: number; // 服务端时间戳
    explosionTime: ExplosionTime;
    currentTab: number; // 当前用户锚定的tab
    giftTabList: TabItem[];
}
export enum MomentType {
    NotStarted = 1, // 未开始
    InProgress = 2, // 进行中
}
export type ExplosionTime = {
    text: string; // 爆奖时刻文案，如果是进行中这里文案是00:30 开启爆奖时刻
    periodText: string; // 进行中的爆奖时刻文案
    label: string; // 右上角
    type?: MomentType; // 1-未开始 2-进行中 缺失则不展示爆奖时刻
    startTime: number; // 爆奖时刻开始时间
    endTime: number; // 爆奖时刻结束时间
};

export type TabItem = {
    id: number;
    name: string;
    pic: string; // 页面中间的图标
    videoCover?: string;
    homeTabLabel?: string;
    effectList: EffectItem[];
    progress?: Progress;
    button: Button;
    collectInfo?: CollectInfo;
};

export type EffectItem = {
    name: string;
    score: number;
    ksCoin: number;
    pic: string;
    label?: string; // 首次5倍概率、限时5倍概率、送出必得
};

export type Progress = {
    targetCount: number; // 目标送礼进度
    currentCount: number; // 当前送礼进度
    tip: string; // 进度旁边的提示条
};

export type Button = {
    text: string;
    value: number;
    token: string;
    tip: string;
};

export type CollectInfo = {
    display: boolean;
    text: string;
    pic: string;
};

export interface RandomEffectHomePostRequest {
    biz: string;
    authorId: number | string;
}

export interface HemoResponse {
    randomGiftInfo: RandomEffectHomePostResponse;
}

export type GiftItem = {
    giftId: number;
    icon: string;
    tip: string;
    giftDesc: string;
    randomDesc: string;
    buttonText: string;
    value: number;
    token: string;
};

export enum EntrySource {
    HOME = 'home',
    BANNER = 'banner',
    PANDENT = 'pandent',
}
