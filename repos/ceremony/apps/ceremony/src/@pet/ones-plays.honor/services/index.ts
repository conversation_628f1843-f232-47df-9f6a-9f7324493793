import { activityBiz, request, getQuery } from '@alive-ui/actions';
import type {
    RandomEffectHomePostResponse,
    RandomEffectHomePostRequest,
} from '../schemas/index';

const PATH = {
    randomEffectHomeV2:
        '/webapi/live/revenue/operation/activity/randomEffect/home/<USER>',
};

export const randomEffectHomeV2 = async () => {
    const params: RandomEffectHomePostRequest = {
        biz: activityBiz,
        authorId: String(getQuery('authorId')),
    };
    const res = await request.post<RandomEffectHomePostResponse>(
        PATH.randomEffectHomeV2,
        params,
    );
    return res?.data;
};
