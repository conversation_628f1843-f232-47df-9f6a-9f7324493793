import { useRoute } from 'vue-router';
import { ref, computed, watch } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import useAuthorMatch from '@pet/ones-use.bolIsAuthor';
import {
    getPageStatus,
    Report,
    isOutLiveRoom,
    stopMove,
} from '@alive-ui/actions';
import { randomEffectHomeV2 } from '../services/index';
import type { RandomEffectHomePostResponse, TabItem } from '../schemas/index';
import { httpReportValid } from '@/utils/httpReportValid';
import { awaitWrap } from '@/utils/awaitWrap';
import { sendTask } from '@/common/logger';

export default defineStore('glory', () => {
    const route = useRoute();
    const { isAuthorMatch } = storeToRefs(useAuthorMatch());

    const pageStatus = ref(getPageStatus('init')); // 页面状态数据定义
    const gloryInfo = ref<null | RandomEffectHomePostResponse>(null);
    const tabIndex = ref(0);
    // 是否已从默认索引切换到用户选择
    const hasUserTab = ref(false);
    const modalVisible = ref(false);
    const defaultTabItem: TabItem = {
        id: 0,
        name: '',
        pic: '',
        effectList: [],
        progress: { targetCount: 0, currentCount: 0, tip: '' },
        button: {
            text: '',
            value: 0,
            token: '',
            tip: '',
        },
    };

    const getGloryInfo = async () => {
        const [err, res] = await awaitWrap(randomEffectHomeV2());
        if (err) {
            if (httpReportValid(err)) {
                Report.biz.error('荣耀神器初始化失败', {
                    error: err,
                });
            }
            return false;
        }
        gloryInfo.value = res;
        return true;
    };

    const init = async () => {
        pageStatus.value = getPageStatus('loading');
        const res = await getGloryInfo();
        if (res) {
            pageStatus.value = getPageStatus('success');
        } else {
            pageStatus.value = getPageStatus('error');
        }
    };

    const tabInfo = computed<TabItem>(() => {
        // 如果用户已经切换过，使用tabIndex，否则使用defaultIndex
        const index = hasUserTab.value ? tabIndex.value : defaultIndex.value;
        return gloryInfo.value?.giftTabList?.[index] || defaultTabItem;
    });

    const defaultIndex = computed<number>(() => {
        // 优先使用路由参数中的currentTab
        const currentTab =
            route?.query?.currentTab || gloryInfo.value?.currentTab;

        // 如果没有任何tab数据或currentTab不存在，返回0
        if (!gloryInfo.value?.giftTabList?.length || !currentTab) {
            return 0;
        }

        // 查找匹配的tab索引，找不到则返回0
        const index = gloryInfo.value.giftTabList.findIndex(
            (item) => item.id === Number(currentTab),
        );
        return index >= 0 ? index : 0; // 明确处理 -1 情况
    });

    // 当默认索引变化时上报埋点
    watch(
        [() => gloryInfo.value?.giftTabList, () => defaultIndex.value],
        ([giftTabList, newDefaultIndex]) => {
            // 只有用户未交互时才自动更新
            if (!hasUserTab.value && giftTabList?.length) {
                // 首次加载埋点
                sendTask('CLICK', {
                    action: 'OP_ACTIVITY_HONOR_TAB',
                    params: {
                        type: 1, // 首次进到页面默认选中的tab
                        tab_name: giftTabList[newDefaultIndex]?.name,
                    },
                });
            }
        },
        { immediate: true },
    );

    let stopMoveObj: ReturnType<typeof stopMove>;

    watch(
        () => modalVisible.value,
        (newVal, oldVal) => {
            if (newVal) {
                stopMoveObj = stopMove();
            }
        },
        { immediate: true },
    );

    const tabChange = (index: number) => {
        tabIndex.value = index;
        hasUserTab.value = true;
        sendTask('CLICK', {
            action: 'OP_ACTIVITY_HONOR_TAB',
            params: {
                type: 2, // 手动切换tab
                tab_name: tabInfo.value.name,
            },
        });
    };

    const showModal = () => {
        modalVisible.value = true;
    };

    const close = () => {
        modalVisible.value = false;
        stopMoveObj.cancelMove();
        stopMoveObj.destroy();
    };

    const showBtn = computed(() => {
        return !isAuthorMatch.value && !isOutLiveRoom;
    });

    return {
        pageStatus,
        getGloryInfo,
        init,
        gloryInfo,
        tabChange,
        tabInfo,
        showModal,
        close,
        modalVisible,
        defaultIndex,
        showBtn,
        tabIndex,
    };
});
