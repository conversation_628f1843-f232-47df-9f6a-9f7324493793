<script lang="ts" setup>
import { getCurrentInstance, computed } from 'vue';
import RankList from '@pet/ones-rank.common-list/index.vue';
import { goOtherPage } from '@alive-ui/actions';
import { dataFun } from './data';
const emits = defineEmits(['goPage', 'success', 'fail']);
import type {
    RankConfigInfo,
    PropsCommonParams,
} from '@pet/ones-rank.schema/global';
interface RankData extends PropsCommonParams {
    // 榜单相关数据: 列表，关注id，分数标签名称
    data: RankConfigInfo;
    // 榜单单项组件 （类型多，有单头像，双头像，三头像等，分数值分布排列位置不同，可以自定义传入）
    rankType?: string;
    // 分数标签名称
    scoreLabel?: string;
    // 榜单分段类型
    groupType?: string;
}
const props = withDefaults(defineProps<RankData>(), {
    data: () => {
        return {
            list: [],
            // 直播间上下滑id
            liveStreamIdList: [],
            // 榜单关注id
            focusActivityId: '',
        };
    },
    rankType: 'base',
    scoreLabel: '盛典值',
    groupType: '',
    contextName: Symbol.for('ctx'),
});

const commonParams = computed(() => {
    return {
        scheduleId: props.data?.scheduleId,
        liveStreamIdList: props.data?.liveStreamIdList,
        scoreLabel: props?.scoreLabel,
        // 榜单通用埋点
        logParams: props.data?.logParams,
        // 榜单关注id
        focusActivityId: props.data?.focusActivityId,
        // 是否需要分割线
        isNeedDivider: props.data?.isNeedDivider,
    };
});
const contextData = dataFun(props.contextName);
const { proxy } = getCurrentInstance() as any;
// 榜单单项顶部提示跳转链接
const goPage = (type: string, link: string) => {
    if (type === 'jimu') {
        goOtherPage('jimu', link);
    } else {
        proxy.$router.push({
            name: link,
            query: {
                rankId: props.data.currentRankId,
            },
        });
    }
};
const onSuccess = () => {
    emits('success');
};
const onFail = () => {
    emits('fail');
};
</script>

<template>
    <RankList
        :context-name="contextName"
        :data="{ ...data, ...commonParams }"
        :rank-type="rankType"
        need-page
    >
        <template v-if="contextData?.groupList?.length" #self-list>
            <slot name="self-list">
                <!-- 分段直通决赛、 大师赛、 复活赛-->
                <component
                    :is="RankList"
                    v-for="elem in contextData.groupList"
                    :key="elem.title"
                    class="mb-14px"
                    :rank-type="rankType"
                    :context-name="contextName"
                    need-width
                    :data="{
                        needTop: true,
                        logType: elem.logType,
                        topTipsText: elem.title || '',
                        list: elem.list,
                        ...commonParams,
                    }"
                    @go-page="goPage(elem.linkType, elem.link)"
                    @success="onSuccess"
                    @fail="onFail"
                />
            </slot>
            <RankList
                v-if="contextData?.restArray?.length"
                :rank-type="rankType"
                need-width
                need-page
                :context-name="contextName"
                :data="{
                    needTop: true,
                    list: contextData?.restArray,
                    ...commonParams,
                }"
                @success="onSuccess"
                @fail="onFail"
            />
        </template>
    </RankList>
</template>

<style lang="less" scoped></style>
