import { computed, inject } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import type { Ref } from 'vue';
import type {
    ItemRankInfo,
    QueryRankPostResponse,
} from '@pet/ones-rank.schema/query-rank';

export const dataFun = (contextName: symbol) => {
    const rankInfo = contextName
        ? inject<Ref<QueryRankPostResponse>>(contextName)
        : null;
    const { kconfData } = storeToRefs(useKconfStore());
    const data = computed(() => {
        const rankTips = rankInfo?.value?.extraData?.rankTips || [];
        const isRepechageActivity =
            rankInfo?.value?.extraData?.isRepechageActivity;
        console.log('isRepechageActivity', isRepechageActivity);
        const groupList: {
            list: ItemRankInfo[];
            title: string;
            link: string;
            linkType: string;
            logType: string;
        }[] = [];
        let sliceNum = 0;
        rankTips.forEach((rule, index) => {
            const segment = rankInfo?.value?.rankList?.slice(
                rule.startClose - 1,
                rule.endClose,
            ); // 注意索引从0开始，因此需要减1
            groupList.push({
                list: segment || [],
                title: rule.content,
                // 埋点支持
                logType:
                    index === 0 && !isRepechageActivity
                        ? '直通决赛'
                        : '复活玩法',
                link:
                    index === 0 && !isRepechageActivity
                        ? 'through-finals'
                        : kconfData.value?.common?.reviveDirectConnection,
                linkType:
                    index === 0 && !isRepechageActivity ? 'router' : 'jimu',
            });
            sliceNum = rule.endClose === -1 ? rule.startClose : rule.endClose;
        });
        const restArray = rankInfo?.value?.rankList.slice(sliceNum);
        return {
            groupList,
            restArray,
        };
    });
    return data;
};
