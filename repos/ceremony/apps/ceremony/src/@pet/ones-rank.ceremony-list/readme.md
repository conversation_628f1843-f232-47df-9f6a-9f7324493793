# 盛典主赛程榜单组件说明文档

## 组件功能

该组件主要用于展示榜单数据，支持多种榜单类型的展示，包括团队、工会和基础类型。组件提供了以下功能：

- **榜单数据展示**：通过props传入榜单数据，支持自定义榜单项组件。
- **分页能力**：支持对榜单数据进行分页展示。
- **顶部提示**：可以显示榜单的顶部提示信息。
- **自定义插槽**：支持多个插槽用于自定义内容展示。
- **跳转功能**：支持通过点击跳转到其他页面。

## Props 说明

| Prop Name       | Type                  | Default Value | Description                                                                                     |
|------------------|-----------------------|---------------|-------------------------------------------------------------------------------------------------|
| `data`           | `RankConfigInfo`      | `{ list: [] }`| 榜单相关数据，包括列表、直播间ID列表、关注ID等。                                             |
| `contextName`    | `string`              | `''`          | 注入上下文的名称。                                                                             |
| `rankType`       | `string`              | `'base'`      | 榜单单项组件类型，支持多种类型（如单头像、双头像等）。                                         |
| `scoreLabel`     | `string`              | `'盛典值'`     | 榜单分数标签名称。                                                                             |
| `groupType`      | `string`              | `''`          | 榜单分段类型。                                                                                 |
| `needWidth`      | `boolean`             | `false`       | 是否自定义榜单宽度。                                                                           |
| `needPage`       | `boolean`             | `false`       | 是否需要分页展示。                                                                             |

## 使用示例

以下是如何使用该组件的示例代码：

```vue
<template>
    <RankList
        :context-name="contextName"
        :data="rankData"
        :rank-type="rankType"
        need-page
    >
        <template #self-list>
            <div v-for="item in rankData.list" :key="item.id">
                <RankItemBase :data="item" />
            </div>
        </template>
    </RankList>
</template>

<script lang="ts" setup>
import RankList from '@pet/ones-rank.ceremony-list/index.vue';
import { ref } from 'vue';

const contextName = 'exampleContext';
const rankData = ref();
const rankType = 'base';
</script>
组件插槽
该组件支持以下插槽：

self-list：用于自定义榜单项的展示。
rank-header：用于自定义榜单头部信息展示。
rest-container：用于展示休赛内容或未开启的兜底展示。
page-center：用于自定义页面中间区域的内容。
组件代码实现

以下是组件的核心代码实现：

```vue
<script lang="ts" setup>
import { getCurrentInstance, computed } from 'vue';
import RankList from '@pet/ones-rank.common-list/index.vue';
import { goOtherPage } from '@alive-ui/actions';
import { ceremonyFun } from './data';

const emits = defineEmits(['goPage']);
const props = withDefaults(defineProps<RankData>(), {
    data: () => ({ list: [], liveStreamIdList: [], focusActivityId: '' }),
    rankType: 'base',
    scoreLabel: '热度值',
    groupType: '',
    contextName: Symbol.for('ctx'),
});

const commonParams = computed(() => ({
    scheduleId: props.data?.scheduleId,
    liveStreamIdList: props.data?.liveStreamIdList,
    scoreLabel: props?.scoreLabel,
    logParams: props.data?.logParams,
    focusActivityId: props.data?.focusActivityId,
    isNeedDivider: props.data?.isNeedDivider,
}));

const goPage = (type: string, link: string) => {
    if (type === 'jimu') {
        goOtherPage('jimu', link);
    } else {
        const { proxy } = getCurrentInstance() as any;
        proxy.$router.push({ name: link, query: { rankId: props.data.currentRankId } });
    }
};
</script>
