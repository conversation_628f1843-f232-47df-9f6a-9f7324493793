<template>
    <div v-if="isShow" class="anchor-glory">
        <ACard>
            <ACardTitle> {{ title }} </ACardTitle>
            <ACardSubtitle> {{ subTitle }} </ACardSubtitle>

            <ACardContent class="anchor-glory__content">
                <Layout3Col
                    v-slot="{ item }"
                    :list="data"
                    key-name="photoId"
                    class="mt-[12px] px-[12px]"
                >
                    <div
                        v-show-log="{
                            action: 'OP_ACTIVITY_VIDEO_CARD',
                            params: {
                                type: type,
                                video_id: item?.photoId,
                            },
                        }"
                        v-click-log="{
                            action: 'OP_ACTIVITY_VIDEO_CARD',
                            params: {
                                type: type,
                                video_id: item?.photoId,
                            },
                        }"
                        class="video-item"
                        @click="goDetail(item?.photoId, item?.authorId)"
                    >
                        <div
                            class="pic"
                            :style="{ background: getImgStyle(item.cover) }"
                        >
                            <div class="view-count ml-8px">
                                <Play class="mr-2px" />
                                <span>{{ item.viewCount }}</span>
                            </div>
                        </div>

                        <span class="desc a-text-main-o2">
                            <TextLineCut
                                :text="item.title"
                                :max-lines="2"
                            ></TextLineCut>
                        </span>
                    </div>
                </Layout3Col>
            </ACardContent>

            <div
                v-if="isShowRefresh"
                v-click-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: { btn_type: 'CHANGE' },
                }"
                v-show-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: { btn_type: 'CHANGE' },
                }"
                class="refresh-item"
                @click="throttleRefresh"
            >
                <span class="a-text-main-o2 text-12">换一换</span>
                <Refresh class="anchor-glory__icon a-text-main-o2 ml-2px" />
            </div>
        </ACard>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { throttle } from 'lodash-es';
import TextLineCut from '@pet/ones-ui.text-line-cut/index.vue';
import Layout3Col from '@pet/ones-ui.layout-2col/index.vue';
import { Toast } from '@lux/sharp-ui-next';
import { Refresh, Play } from '@alive-ui/icon';
import { ACard, ACardContent, ACardTitle, ACardSubtitle } from '@alive-ui/base';
import { bolIsAuthor, goMoreVideo, isYodaPCContainer } from '@alive-ui/actions';
import type { FeedItem } from './schema/index';

const props = defineProps<{
    data: FeedItem[];
    type: number;
    total: number;
    title: string;
    subTitle: string;
}>();
const emit = defineEmits<{
    (e: 'refresh', params: any): void;
}>();
console.log(props.data);
// 响应式变量，用于缓存所有的 photoId
const allVideoIdList = computed(() => props.data.map((item) => item.photoId));
const getImgStyle = (url: string) => {
    return `#000 url("${url}") center / 100% no-repeat`;
};

// 如果总页数是1，那么不展示换一换
const isShowRefresh = computed(() => {
    return props.total > 1;
});

const goDetail = throttle((photoId: string, userId: string) => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }

    // 获取所有的photoId，从当前的id开始，环形播放
    const reorderedList = getRearrangedArray(photoId);
    goMoreVideo({ photoId, userId }, reorderedList, {
        // 目前这样写是可以用的，后面会更改
        path: '/rest/n/live/revenue/operation/activity/yearendceremony/hotTopicFeed/photoFeed',
        sourceType: '2022yearendceremony',
        hostKey: '2022yearendceremony',
    });
}, 500);

function getRearrangedArray(target: string): string[] {
    const targetIndex = allVideoIdList.value.indexOf(target);
    if (targetIndex === -1) {
        return allVideoIdList.value;
    }
    return [
        ...allVideoIdList.value.slice(targetIndex),
        ...allVideoIdList.value.slice(0, targetIndex),
    ];
}
const isShow = computed(() => {
    return props.data?.length > 0;
});
// 首页加载的页码是0，我们这是从1开始
let page = 1;
const refresh = () => {
    const params = {
        photoArea: props.type,
        page: page++ % props.total,
    };
    emit('refresh', params);
};
const throttleRefresh = throttle(refresh, 1000); // 频控,限制每1000ms执行一次
</script>

<style lang="less" scoped>
.anchor-glory {
    position: relative;
    /deep/ .card-title-icon-attrs {
        display: none;
    }
    &__icon {
        width: 10px;
        height: 10px;
    }
    .refresh-item {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 25px;
        right: 12px;
    }
    &__content {
        position: relative;
        .video-item {
            box-sizing: border-box;
            width: 114px;
            margin-right: 8px;
            flex: 0 0 calc(33.33% - 8px);
            display: flex;
            flex-direction: column;
            position: relative;
            .pic {
                box-sizing: border-box;
                width: 114px;
                height: 152px;
                border-radius: 12px;
            }
            .desc {
                margin-top: 4px;
                width: 114px;
                font-size: 12px;
                line-height: 18px;
            }
            .view-count {
                color: #fff;
                text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.2);
                font-size: 11px;
                font-weight: 500;
                display: flex;
                align-items: center;
                position: absolute;
                top: 130px;
            }
        }
        .video-item:nth-child(n + 4) {
            margin-top: 12px;
        }
        .video-item:nth-child(3n) {
            margin-right: 0px;
        }

        .take-more {
            margin-top: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}
</style>
