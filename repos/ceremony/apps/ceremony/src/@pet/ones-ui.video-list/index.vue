<template>
    <VideoItem
        v-for="(item, index) in feedPhotoList"
        :key="'videoItem' + index"
        :data="item.photoFeeds"
        :type="index + 1"
        :total="item.maxPage"
        :title="item.title"
        :sub-title="item.subTitle"
        @refresh="refresh"
    ></VideoItem>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Report } from '@alive-ui/system';
import VideoItem from './video-item.vue';
import { refreshPhoto } from './service/index';
import type { refreshPhotoPayload } from './service/index';
import type { FeedList } from './schema/index';
const props = defineProps<{
    list: FeedList[];
}>();

const feedPhotoList = ref<FeedList[]>(props.list);
const refresh = async (params: refreshPhotoPayload) => {
    try {
        const res = await refreshPhoto(params);
        // 更新局部数据
        feedPhotoList.value[params.photoArea - 1].photoFeeds = res.photoFeeds;
    } catch (error) {
        Report.biz.error('第四Tab视频卡片刷新失败', {
            error,
        });
    }
};
</script>
