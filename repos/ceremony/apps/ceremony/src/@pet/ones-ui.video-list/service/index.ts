import { request } from '@alive-ui/actions';
export interface refreshPhotoPayload {
    photoArea: number; // 1代表第一块视频区，二代表第二块
    page: number; // 页数，从0开始
}

// 线下晚会&4Tab主接口
export const refreshPhoto = async (payload: refreshPhotoPayload) => {
    const PATH = '/rest/wd/live/plutus/yc24/4tab/refreshPhoto';
    const res = await request.post(PATH, {
        ...payload,
    });

    return res?.data;
};
