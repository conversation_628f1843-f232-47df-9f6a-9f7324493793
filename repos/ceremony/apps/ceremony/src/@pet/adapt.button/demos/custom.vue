<pet-info lang="json">
{
    "title": "自定义样式",
    "description": "使用variable重新定义样式",
    "priority": 1
}
</pet-info>
<script lang="ts" setup>
import Button from '../index.vue';
</script>

<template>
    <div>
        <div class="title">特殊字体</div>
        <section>
            <p>
                <Button class="item-btn" type="primary" :height="72"
                    >按钮文案</Button
                >
            </p>
            <p>
                <Button class="item-btn" type="primary" :height="66"
                    >按钮文案</Button
                >
            </p>
            <p>
                <Button class="item-btn" type="primary" :height="60"
                    >按钮</Button
                >
            </p>
            <p>
                <Button class="item-btn" type="primary" :height="38"
                    >按钮</Button
                >
            </p>
        </section>

        <div class="title">更换背景+自定义</div>
        <i>购物金主按钮为例</i>
        <section>
            <p>
                <Button
                    class="item-btn custom-button custom-background"
                    type="primary"
                    :height="72"
                    >分享再赚62.78元</Button
                >
            </p>
            弱网背景未加载（模拟）：
            <p>
                <Button
                    class="item-btn custom-button"
                    type="primary"
                    :height="72"
                    >分享再赚62.78元</Button
                >
            </p>
        </section>
    </div>
</template>

<style lang="scss" scoped>
@import '@pet/adapt.fonts/style/kuaiyuanhui.css';
@import '@pet/adapt.fonts/style/puhui105.css';

div {
    font-size: 14px;
}
.title {
    margin: 16px 0;
}
section {
    margin-bottom: 40px;
}
.item-btn {
    margin: 0 4px;
    --adapt-button-main-font-family: 'kuaiyuanhui', sans-serif;
}

.custom-button {
    --adapt-button-main-font-family: 'puhui105', sans-serif;
    --adapt-button-l-font-size: 21px;
    --adapt-button-l-width: 228px;
    --adapt-button-l-height: 68px;

    position: relative;
    &::after {
        content: '';
        position: absolute;
        top: 0;
        left: -3px;
        z-index: -1;
        width: 235px;
        height: 76px;

        background-size: 100% 100%;
    }
}

.custom-background {
    --adapt-button-primary-background-image: url('./assets/button-bg.png');
    &::after {
        background-image: url('./assets/button-shadow.png');
    }
}
</style>
