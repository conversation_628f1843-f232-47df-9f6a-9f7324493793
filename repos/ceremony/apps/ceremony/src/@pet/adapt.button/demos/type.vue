<pet-info lang="json">
{ "title": "按钮类型", "description": "支持 primary plain 类型", "priority": 9 }
</pet-info>
<script lang="ts" setup>
import Button from '../index.vue';
</script>

<template>
    <div>
        <div class="title">默认类型primary</div>
        <Button class="item-btn main" :type="'primary'">立即使用</Button>
        <div class="title">primary 文字渐变色</div>
        <Button class="item-btn main linear-text" :type="'primary-linear'"
            >立即使用</Button
        >
        <div class="title">plain类型</div>
        <Button class="item-btn plain" :type="'plain'">立即使用</Button>
        <div class="title">默认类型primary有底部衬底</div>
        <div style="background-color: #000">
            <Button class="item-btn main" :type="'primary'" has-substrate
                >立即使用</Button
            >
            <Button
                class="item-btn main"
                :type="'primary'"
                has-substrate
                disabled
                >立即使用</Button
            >
        </div>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
div {
    font-size: 14px;
}
.title {
    margin: 16px 0;
}
.item-btn {
    margin: 0 4px;
}
.main {
    --adapt-button-primary-background-image: linear-gradient(
        275deg,
        #fe3666 1.91%,
        #fe3666 48.66%,
        #ff7d49 118.64%
    );
}

.linear-text {
    --adapt-button-primary-font-linear: linear-gradient(
        0deg,
        #fff4cf 25%,
        #ffffff 66.07%
    );
}
</style>
