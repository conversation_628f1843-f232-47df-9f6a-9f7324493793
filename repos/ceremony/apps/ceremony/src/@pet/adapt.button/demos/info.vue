<pet-info lang="json">
{ "title": "额外信息", "description": "支持额外信息按钮", "priority": 2 }
</pet-info>
<script lang="ts" setup>
import { ref } from 'vue-demi';
import Button from '../index.vue';

const isClicked = ref(false);

function handleClick() {
    isClicked.value = true;
}
</script>

<template>
    <div>
        <div class="title">loading按钮</div>
        <Button loading></Button>
        <Button type="plain" loading></Button>
        <div class="title">icon按钮</div>
        <Button class="item-btn icon"
            ><template #icon><img src="./assets/star.png" /></template
            >立即使用</Button
        >
        <div class="title">plugin描述</div>
        <Button class="item-btn"
            >立即使用<template #plugin>100金币/次</template></Button
        >
        <div class="title">禁用按钮</div>
        <Button class="item-btn" disabled>立即使用</Button>
        <Button class="item-btn" type="plain" disabled>立即使用</Button>
        <div class="title">禁用状态，但是按钮可以点击</div>
        <div>当前已点击：{{ isClicked ? '是' : '否' }}</div>
        <Button class="item-btn" looks-like-disabled @click="handleClick"
            >立即使用</Button
        >
    </div>
</template>

<style lang="scss" scoped>
div {
    font-size: 14px;
}

.icon {
    --adapt-button-icon-width: 24px;
    --adapt-button-icon-height: 24px;
}
.title {
    margin: 16px 0;
}
.item-btn {
    margin: 0 4px;
}
</style>
