# @pet/adapt.button

基于同一设计规范，提供组件按钮能力

## 属性

| 名称                | 类型                                                                    | 默认值            | 说明                                                                          |
| ------------------- | ----------------------------------------------------------------------- | ----------------- | ----------------------------------------------------------------------------- |
| type                | primary \| plain                                                        | primary           | primary:主按钮<br>plain:线框按钮                                              |
| height              | number                                                                  | 22 38 46 60 66 73 | 按钮高度（根据高度定义尺寸大小）                                              |
| disabled            | boolean                                                                 | false             | 是否禁用                                                                      |
| looks-like-disabled | boolean                                                                 | false             | 禁用状态但样式可以点击                                                        |
| loading             | boolean                                                                 | false             | 加载中                                                                        |
| aniType             | 'no-effect' \| 'sharp' \| 'breath' ['no-effect' \| 'sharp' \| 'breath'] | no-effect         | 按钮动效，支持数组类型同时支持多种动效 no-effect 无动效 sharp 扫光 breath呼吸 |

## 事件

| 名称  | 回调参数 | 说明     |
| ----- | -------- | -------- |
| click | Event    | 按钮点击 |

## slot

| 名称    | 说明                 |
| ------- | -------------------- |
| default | 默认插槽             |
| plugin  | 主内容下的辅助文案区 |
| icon    | 按钮图标             |
