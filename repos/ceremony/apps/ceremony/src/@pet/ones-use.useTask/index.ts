/* eslint-disable max-lines-per-function */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { computed, reactive, ref } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { debounce, throttle } from 'lodash-es';
import useBallance from '@pet/ones-use.useUpdateBallance/index';
import { useShare, ShareMode, Rescode } from '@pet/ones-use.useShare/index';
import { useReservation } from '@pet/ones-use.useReservation/index';
// import { toShare } from '@pet/ones-use.share';
import useKconf from '@pet/ones-use.useKconf';
import {
    linkOtherBizPage,
    SceneType,
} from '@pet/ones-use.linkOtherBizPage/index';
// import { goRedPacketPage } from '@pet/ones-use.goRedPacketPage';
import { Toast } from '@lux/sharp-ui-next';
import {
    useVisibility,
    bolIsAuthor,
    dispatchLiveRouter,
    activityBiz,
    liveStreamId,
    appendParam,
    startNeoAdVideo,
    exitWebView,
    invoke,
    loadUrlOnNewPage,
    authorId,
} from '@alive-ui/actions';
import {
    postLotteryTask,
    queryRecoAuthor,
    dailyLogin,
    getAdInspireScheme,
    receiveTask,
    searchKeyWord,
    TaskType,
    TaskStatus,
    completed,
    pageCompleted,
    // koiCompleted,
} from './services/index';
import { sendClick, taskBtnLog } from './logger/index';
import type { defineEmits } from 'vue';
import type { TaskItem } from './services/index';

const RecieveTasks = ['joinFansGroup', 'sendPopularityGift', 'sendGift'];

// eslint-disable-next-line max-lines-per-function
export default defineStore('lottery-task', () => {
    const pageStatus = reactive({
        loading: true,
        error: false,
    });
    const originTaskList = ref([] as TaskItem[]);
    const isRefetch = ref(false);
    const ballanceStore = useBallance();
    const { kconfData, conf4Tab } = storeToRefs(useKconf());
    const biz = ref(activityBiz);

    const shareParams = computed(() => {
        return conf4Tab.value?.shareParams ?? {};
    });

    const init = (
        needDailyTask: boolean,
        needDataInit: boolean,
        taskList?: TaskItem[],
    ) => {
        if (needDataInit) {
            fetchTask(needDailyTask);
        } else {
            pageStatus.error = false;
            pageStatus.loading = false;
            originTaskList.value = taskList ?? [];
        }
    };

    const fetchTask = async (needDailyTask: boolean) => {
        try {
            if (needDailyTask) {
                await initLogin();
            }
            await queryTaskInfo();
        } catch (err) {
            console.error('lucky machine init', err);
        }
    };

    // 获取任务列表
    const queryTaskInfo = async () => {
        if (!isRefetch.value) {
            pageStatus.loading = true;
        }

        try {
            const res = await postLotteryTask(biz.value);
            pageStatus.error = false;
            const data = res;
            ballanceStore.updateBalance(undefined, biz.value);
            originTaskList.value = data.taskItemViewList || [];
        } catch (error) {
            console.error(' error:', error);
            pageStatus.error = true;
        }
        pageStatus.loading = false;
    };

    // 重新获取任务列表
    const refetchTask = () => {
        isRefetch.value = true;
        queryTaskInfo();
    };

    const curTaskList = computed(() => {
        return originTaskList.value.filter((task) => {
            return liveStreamId
                ? true
                : ![TaskType.PageView, TaskType.H5Follow].includes(
                      task.typeKey,
                  );
        });
    });

    // 获取作者信息
    const queryRecoAuthorInfo = async (typeKey: string) => {
        try {
            const res = await queryRecoAuthor(typeKey, biz.value);

            return res?.recoAuthorList || [];
        } catch (error) {
            console.log('error', error);
        }

        return [];
    };

    // 跳转直播间
    const jump2room = async (item: TaskItem, kwaiLink = '') => {
        const list = await queryRecoAuthorInfo(item.typeKey);
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const lsIds = list?.map((item: any) => item.liveStreamId).join(',');

        if (!lsIds) {
            return;
        }

        location.href = appendParam('kwai://liveaggregatesquare', {
            sourceType: 269,
            liveStreamId: lsIds,
            exp_tag: (window as any).GLOBAL_EXPTAG || activityBiz,
            liveSquareSource: 10012,
            path: '/rest/n/live/feed/common/slide/more',
            internaljump: kwaiLink,
        });
    };

    // 页面visiable时刷新余额
    //  TODO 加 4tab 判断逻辑
    const watchUpdate = () => {
        useVisibility({
            visibleHandler: debounce(() => {
                refetchTask();
            }, 100),
        });
    };

    // 每日登录任务
    const initLogin = async () => {
        try {
            console.log('dailyLogin');
            await dailyLogin(biz.value);
        } catch (err) {
            console.error('lucky machine login-task', err);
        }
    };

    const searchKeyWordCallBack = async (keyWord: string) => {
        try {
            await searchKeyWord(keyWord, biz.value);
        } catch (err) {
            console.error('lucky machine search-task', err);
        }
    };

    const followModal = ref(false);
    const reservationModal = ref(false);

    const closeModal = () => {
        followModal.value = false;
        reservationModal.value = false;
    };

    //  分享按钮，用 throttle
    const shareAction = throttle(
        async (
            item: TaskItem,
            emits: ReturnType<typeof defineEmits>,
            doTask = true,
            activityId?: string,
        ) => {
            if (doTask) {
                Toast.info('正在抽奖中');
                return;
            }
            if (item.status === TaskStatus.Finished) {
                return;
            }
            const { typeKey } = item;
            const videoId = item.expandMap?.photoId || '';

            // 埋点上报
            // status 1-未完成，2-已完成
            sendClick(taskBtnLog(item.buttonText, typeKey, videoId));

            if (bolIsAuthor) {
                Toast.info('开播中，不可参与哦～');

                return;
            }

            // 所有任务均增加领取接口，后端看情况是否使用。
            try {
                await receiveTask(typeKey, biz.value);
            } catch (err: any) {
                const { error_msg } = err?.data ?? {};
                if (error_msg) {
                    Toast.info(error_msg);
                    return;
                }

                if (RecieveTasks.includes(item.typeKey) || item.receiveTask) {
                    Toast.error('网络异常，请稍后再试～');

                    return;
                }
                console.log('receiveTask error');
            }
            const res = await useShare(ShareMode.StaticMode, shareParams.value);
            if (res === Rescode.Suc) {
                await completed(item.typeKey, authorId, biz.value);
                refetchTask();
            }
        },
        800,
        {
            trailing: false,
        },
    );

    // 除了分享的按钮点击逻辑，把影响范围控制到最小吧，只有分享走 throttle
    const buttonAction = debounce(
        async (
            item: TaskItem,
            emits: ReturnType<typeof defineEmits>,
            doTask = true,
            activityId?: string,
        ) => {
            if (doTask) {
                Toast.info('正在抽奖中');
                return;
            }
            if (item.status === TaskStatus.Finished) {
                return;
            }
            const { typeKey, status, kwaiLink } = item;
            const videoId = item.expandMap?.photoId || '';

            // 埋点上报
            // status 1-未完成，2-已完成
            sendClick(taskBtnLog(item.buttonText, typeKey, videoId));

            if (bolIsAuthor) {
                Toast.info('开播中，不可参与哦～');

                return;
            }

            // 所有任务均增加领取接口，后端看情况是否使用。
            try {
                await receiveTask(typeKey, biz.value);

                if (
                    item.typeKey === TaskType.SendGift &&
                    status === 1 &&
                    kwaiLink &&
                    liveStreamId
                ) {
                    // 未做任务
                    dispatchLiveRouter({
                        path: kwaiLink, // kwaiLink,
                        keepDisplayWebView: false,
                    }).catch(console.error);
                }
            } catch (err: any) {
                const { error_msg } = err?.data ?? {};
                if (error_msg) {
                    Toast.info(error_msg);
                    return;
                }

                if (RecieveTasks.includes(item.typeKey) || item.receiveTask) {
                    Toast.error('网络异常，请稍后再试～');

                    return;
                }
                console.log('receiveTask error');
            }

            switch (item.typeKey) {
                case TaskType.SharePoster: //  分享
                    const res = await useShare(
                        ShareMode.StaticMode,
                        shareParams.value,
                    );
                    if (res === Rescode.Suc) {
                        await completed(item.typeKey, authorId, biz.value);
                        refetchTask();
                    }
                    return;
                case TaskType.WatchVideo:
                    if (
                        !item.expandMap.photoId ||
                        !item.expandMap.watchDuration
                    ) {
                        Toast.error('库存暂时不足，请稍后再试');

                        return;
                    }
                    // 以新打开webview形式跳转到视频观看页
                    // 当前页面所有参数加上photoId & watchDuration
                    const search = location.search.startsWith('?')
                        ? location.search
                        : `?${location.search}`;
                    // eslint-disable-next-line max-len

                    const url = `${location.origin}/live/act/annual-ceremony-2024-12/video-play${search}&layoutType=4&photoId=${item.expandMap.photoId}&watchDuration=${item.expandMap.watchDuration}&specBiz=${biz.value}`;
                    loadUrlOnNewPage({
                        url,
                        type: 'back',
                        ignoreHalfScreenDisplay: 1, // 顶部状态栏，仅ios使用
                    });
                    return;
                case TaskType.H5Follow: // 关注任务的逻辑不一样,是打开弹窗
                    followModal.value = true;

                    return;
                case TaskType.WatchLive: // 跳转推荐直播间
                    return jump2room(item);

                case TaskType.SearchKeyWord: // 跳搜索推荐页
                    try {
                        await searchKeyWordCallBack(item.expandMap.keyWord);
                        location.href = item.kwaiLink;
                    } catch (error) {
                        console.error('跳搜索推荐页', error);
                    }

                    return;
                case TaskType.Reservation: // 预约任务
                    console.log('预约任务');
                    reservationModal.value = true;
                    return;
                case TaskType.AdInspire: // 激励任务
                    console.log('激励任务');

                    try {
                        const adInspireRes = await getAdInspireScheme(
                            biz.value,
                        );
                        console.log('AdInspire', adInspireRes);

                        if (adInspireRes?.scheme) {
                            const adRes = await startNeoAdVideo(
                                adInspireRes.scheme,
                            );
                            console.log(adRes);
                            setTimeout(exitWebView, 500);
                        } else {
                            Toast.info('获取激励任务失败');
                        }
                    } catch (error) {
                        console.error('激励任务失败', error);
                    }

                    return;
                case TaskType.SmallBell: // 小铃铛下载
                    invoke('platform.showToast', {
                        type: 'normal',
                        text: '小铃铛在直播间右下角哦～',
                        isAddToWindow: false,
                    });
                    exitWebView();
                    break;
                case TaskType.BatchReservation: // 预约日历任务，24 年度盛典线下新增
                    const { reservation } = useReservation();
                    const reservationRes = await reservation();
                    //  预约成功，更新任务状态
                    refetchTask();
                    emits('task:update', item, {
                        headLiveStreamData: {
                            reservationLiveStream:
                                reservationRes.reservationLiveStream,
                            reservationCount: reservationRes.reservationCount,
                        },
                    });
                    break;
                case TaskType.PageView: // 跳转年度回忆录
                    await pageCompleted(item.typeKey, authorId, biz.value);
                    linkOtherBizPage({
                        scene: SceneType.Memory,
                        link: kconfData.value.memoryEntry?.link,
                        queryData: {
                            entry_src: 'PARTY',
                            msrc: 'PARTY',
                        },
                    });
                    break;
                // case TaskType.CNYJINLIN: // 跳转锦鲤页面
                //     await koiCompleted(item.typeKey, authorId, biz.value);
                //     refetchTask();
                //     goRedPacketPage({ activityId: activityId ?? '' });
                //     break;
                default:
                    break;
            }

            if (
                item.typeKey !== TaskType.SendGift &&
                status === 1 &&
                kwaiLink
            ) {
                // 未做任务
                if (liveStreamId) {
                    dispatchLiveRouter({
                        path: kwaiLink, // kwaiLink,
                        keepDisplayWebView: false,
                    }).catch(console.error);
                } else {
                    if (item.typeKey === TaskType.GrabRedPack) {
                        // 抢红包本身是跳转直播间，不需要特殊处理
                        location.href = kwaiLink;

                        return;
                    }
                    jump2room(item, kwaiLink);
                }
            }
        },
        500,
    );

    const handleTaskClick = async (
        item: TaskItem,
        emits: ReturnType<typeof defineEmits>,
        doTask = true,
        activityId?: string,
    ) => {
        if (item.typeKey === TaskType.SharePoster) {
            await shareAction(item, emits, doTask, activityId);
        } else {
            await buttonAction(item, emits, doTask, activityId);
        }
    };

    return {
        originTaskList,
        taskList: curTaskList,
        pageStatus,
        isRefetch,
        followModal,
        reservationModal,
        queryTaskInfo,
        initLogin,
        refetchTask,
        queryRecoAuthorInfo,
        watchUpdate,
        buttonAction,
        shareAction,
        handleTaskClick,
        closeModal,
        init,
        biz,
        // getAppInfo,
    };
});
