/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    authorId,
    liveStreamId,
    bolIsAuthor,
    activityBiz,
    request,
} from '@alive-ui/actions';

type ApiRes<T> = {
    result: number;
    msg: string;
    data: T;
};

const params = {
    liveStreamId,
    authorId,
    biz: activityBiz,
};

export type PropsCardItem = {
    id: number;
    typeKey: string;
    name: string;
    desc: string;
    icon: string;
    expireTime: number;
    createTime: number;
    sourceName: string;
};

// 道具卡-列表查询
export interface AllPropsCardInfo {
    unusedList: PropsCardItem[]; // 未使用
    alreadyUsedList: PropsCardItem[]; // 已使用
    alreadyExpiredList: PropsCardItem[]; // 已过期
}

export const queryPropsCardList = async () => {
    const res = await request.post<AllPropsCardInfo>(
        '/rest/wd/live/plutus/propsCard/queryPropsCardList',
        {
            ...params,
        },
    );

    return res.data;
};

// 道具卡-最近可用道具卡
export interface RecentPropsCardInfo {
    propsCardView: PropsCardItem;
}

export const queryRecentPropsCard = async () => {
    const res = await request.post<RecentPropsCardInfo>(
        '/rest/wd/live/plutus/propsCard/queryRecentPropsCard',
        {
            ...params,
        },
    );

    return res.data;
};

export type AwardItem = {
    id: number;
    name: string;
    count: number;
    icon: string;
    displayId: number;
    bizId?: any;
};

/** 抽奖活动枚举 */
export enum lotteryTabType {
    marbles = 0,
    scratchOff = 1,
    blindBox = 2,
}

export const lotteryTabRelation = {
    [lotteryTabType.marbles]: 'marbles',
    [lotteryTabType.scratchOff]: 'scratchOff',
    [lotteryTabType.blindBox]: 'blindBox',
} as const;

export const lotteryBtnRelation = {
    0: 'oneLottery',
    1: 'tenLottery',
};

export const lotteryOrder = [
    lotteryTabRelation[lotteryTabType.marbles],
    lotteryTabRelation[lotteryTabType.scratchOff],
    lotteryTabRelation[lotteryTabType.blindBox],
];

export enum TaskType {
    SendGift = 'sendGift',
    /** 关注 */
    H5Follow = 'h5Follow',
    /** 分享 */
    SharePoster = 'sharePoster',
    WatchVideo = 'watchVideo',
    /** 跳转推荐直播间 */
    WatchLive = 'watchLive',
    /** 跳搜索推荐页 */
    SearchKeyWord = 'searchKeyWord',
    /** 预约任务 */
    Reservation = 'reservation',
    /** 激励任务 */
    AdInspire = 'adInspire',
    /** 小铃铛下载 */
    SmallBell = 'smallBell',
    /** 抢红包 */
    GrabRedPack = 'grabRedPack',
    /** 预约日历任务，24 年度盛典线下新增 */
    BatchReservation = 'batchReservationYC24',
    PageView = 'pageView',
    CNYJINLIN = 'koiPageView',
}

export enum TaskStatus {
    /** 未完成 */
    UnFinished = 1,
    /** 已完成 */
    Finished = 2,
}

// 任务+余额聚合接口
export type TaskItem = {
    /** 任务类型 */
    typeKey: TaskType;
    name: string; // 任务名称
    desc: string; // 任务描述
    iconUrl: string; // 任务icon
    finishCount: number; // 完成进度
    needCount: number; // 目标值
    /** 任务状态 1 未完成 2 已完成 */
    status: TaskStatus; //
    buttonText: string;
    kwaiLink: string; // 任务kwai链
    expandMap: Record<string, any>;
    receiveTask: boolean;
    rulePageLink: string;
};

export interface TaskResponse {
    balance: number; // 余额
    taskItemViewList: TaskItem[];
}

export const postLotteryTask = async (biz = activityBiz) => {
    const res = await request.post<TaskResponse>(
        '/rest/wd/live/plutus/aggregate/audienceTask',
        {
            ...params,
            biz,
            // ...taskParams,
        },
    );

    return res.data;
};

// 获取推荐主播列表
export type SmallAuthorItem = {
    orderId: number;
    authorId: number;
    headUrl: string;
    userName: string;
    liveStreamId?: string | null;
    reservationId: string;
    reservationTitle: string;
    reservationLiveTime: number;
    reservationCount: number;
};

export type RecomResponse = {
    recoAuthorList: SmallAuthorItem[];
};

export const queryRecoAuthor = async (typeKey: string, biz = activityBiz) => {
    const res = await request.post<RecomResponse>(
        '/rest/wd/live/plutus/audienceTask/recoAuthor',
        {
            typeKey,
            biz,
        },
    );

    return res.data;
};

// 关注任务回调
export const focusTaskCallBack = async (
    followAuthorId: string | number,
    orderId: number,
    biz = activityBiz,
) => {
    const res = await request.post(
        '/rest/wd/live/plutus/audienceTask/h5Follow',
        {
            followAuthorId,
            biz,
            orderId,
        },
    );

    return res.data;
};

// 每日登录任务
export const dailyLogin = async (biz = activityBiz) => {
    const res = await request.post<ApiRes<boolean>>(
        '/rest/wd/live/plutus/audienceTask/dailyLogin',
        {
            // 服务端对主播id和userId相同的情况有过滤，为了完成任务，主播侧传0
            authorId: bolIsAuthor ? 0 : authorId || 0,
            biz,
        },
    );

    return res.data;
};

// 搜索关键词回调
export const searchKeyWord = async (keyWord: string, biz = activityBiz) => {
    const res = await request.post<boolean>(
        '/rest/wd/live/plutus/audienceTask/searchKeyWord',
        {
            biz,
            keyWord,
        },
    );

    return res.data;
};

// 老虎机配置信息
export type BtnRule = {
    ruleId: number;
    coinCount: number;
    drawCount: number;
};

export type BoxConfigInfo = {
    balance: number;

    marblesPannel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
    scratchOffPanel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
    blindPannel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
    normalPanel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
};

// 预约直播
export const bookReservation = async (reservationId: string) => {
    const res = await request.post(
        '/rest/wd/live/plutus/reservation/user/book',
        { reservationId },
    );

    return res.data;
};

// 获取激励任务参数
export const getAdInspireScheme = async (biz = activityBiz) => {
    const res = await request.post<{ scheme: string }>(
        '/rest/wd/live/plutus/audienceTask/getAdInspireScheme',
        {
            biz,
        },
    );

    return res.data;
};

// 领取任务上报
export const receiveTask = async (typeKey: string, biz = activityBiz) => {
    const res = await request.post<{ scheme: string }>(
        '/rest/wd/live/plutus/audienceTask/receive',
        {
            authorId,
            biz,
            typeKey,
        },
    );

    return res.data;
};

//  分享成功
export const completed = async (
    typeKey: string,
    completeAuthorId: string | number = authorId,
    biz = activityBiz,
) => {
    const res = await request.post<boolean>(
        '/rest/wd/live/plutus/audienceTask/completed',
        {
            authorId: completeAuthorId || authorId,
            typeKey,
            biz, // 需传此字段以区分不同业务
        },
    );
    return res.data;
};

export const pageCompleted = async (
    typeKey: string,
    completeAuthorId: string | number = authorId,
    biz = activityBiz,
) => {
    const res = await request.post<boolean>(
        '/rest/wd/live/plutus/audienceTask/pageViewCompleted',
        {
            authorId: completeAuthorId || authorId,
            typeKey,
            biz, // 需传此字段以区分不同业务
        },
    );
    return res.data;
};

export const koiCompleted = async (
    typeKey: string,
    completeAuthorId: string | number = authorId,
    biz = activityBiz,
) => {
    const res = await request.post<boolean>(
        '/rest/wd/live/plutus/audienceTask/koiPageViewCompleted',
        {
            authorId: completeAuthorId || authorId,
            typeKey,
            biz, // 需传此字段以区分不同业务
        },
    );
    return res.data;
};
