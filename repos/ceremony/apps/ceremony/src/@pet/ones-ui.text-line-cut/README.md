# Text Line Cut 组件文档

## 功能概述

`Text Line Cut` 是一个 Vue 组件，用于限制文本的显示行数，并在超出指定行数时自动截断并显示省略号。该组件支持动态设置最大行数，并且能够移除 iOS 设备上的 Emoji 符号。

## 属性和方法

### Props

- **text** (`String`)
  - **默认值**: `''`
  - **描述**: 需要显示的文本内容。
  
- **maxLines** (`Number`)
  - **默认值**: `2`
  - **描述**: 最大显示行数。

### Emit Events

- 无

## 使用示例

```vue
<template>
  <div>
    <text-line-cut :text=\"longText\" :max-lines=\"3\" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TextLineCut from '@pet/ones-ui.text-line-cut';

const longText = ref('这是一段非常长的文本，将会被截断并显示省略号。这是一段非常长的文本，将会被截断并显示省略号。');
</script>
```

## 注意事项

1. **兼容性问题**: 组件在挂载时会检查 `-webkit-line-clamp` 的兼容性。如果存在兼容性问题，会通过 `Report` 模块记录错误日志。
2. **iOS 设备上的 Emoji**: 该组件会移除 iOS 设备上的 Emoji 符号，以避免显示问题。
