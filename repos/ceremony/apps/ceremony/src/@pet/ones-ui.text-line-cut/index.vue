<template>
    <div ref="textRef" class="text-line-cut" :style="dynamicStyles">
        {{ finalText }}
    </div>
</template>

<script lang="ts" setup>
import { computed, defineProps, onMounted, ref } from 'vue';
import { osName } from '@alive-ui/system';
import { Report } from '@alive-ui/actions';

const textRef = ref();

const props = defineProps({
    text: {
        type: String,
        default: '',
    },
    maxLines: {
        type: Number,
        default: 2,
    },
});
/**
 * 移除文案中的emoji
 * @param content 待处理文案
 * @returns
 */
const removeEmoji = (content: string) => {
    // 非ios，目前不处理
    if (osName !== 'ios') {
        return content;
    }
    return content.replace(
        /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g,
        '',
    );
};
const finalText = computed(() => {
    return removeEmoji(props.text);
});
// 计算样式对象
const dynamicStyles = computed(() => ({
    '--line-clamp-cnt': props.maxLines,
}));
onMounted(() => {
    // 判断是否有兼容性问题
    try {
        const cssStyle = getComputedStyle(textRef.value);
        if (!cssStyle?.webkitLineClamp) {
            Report.biz.error('-webkit-line-clamp 兼容性问题', {
                cssStyle,
            });
        }
    } catch (e) {
        Report.biz.error('text-line-cut挂载异常', {
            e,
        });
    }
});
</script>

<style lang="less" scoped>
.text-line-cut {
    --line-clamp-cnt: 2;
    overflow: hidden; /* 隐藏超出容器的内容 */
    // text-overflow: ellipsis; /* 文本溢出时显示省略号 */
    display: -webkit-box; /* 使用 WebKit 弹性盒子模型 */
    -webkit-line-clamp: var(--line-clamp-cnt); /* 限制行数为 x 行 */
    -webkit-box-orient: vertical; /* 设置弹性盒子的方向为垂直 */
    display: -moz-box; /* 使用 Mozilla 弹性盒子模型 */
    -moz-line-clamp: var(--line-clamp-cnt); /* 限制行数为 x 行 */
    -moz-box-orient: vertical; /* 设置弹性盒子的方向为垂直 */
    display: box; /* 使用标准弹性盒子模型 */
    line-clamp: var(--line-clamp-cnt); /* 限制行数为 x 行 */
    box-orient: vertical; /* 设置弹性盒子的方向为垂直 */
    white-space: normal; /* 允许文本换行 */
    word-break: break-all;
}
</style>
