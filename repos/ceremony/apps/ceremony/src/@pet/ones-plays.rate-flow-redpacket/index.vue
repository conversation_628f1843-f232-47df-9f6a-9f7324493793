<script setup lang="ts">
import { ref, onMounted, computed, onBeforeUnmount } from 'vue';
import { storeToRefs } from 'pinia';
import { sendGiftMultiple } from '@pet/ones-use.send-gift-multiple';
import RuleIcon from '@pet/ones-ui.rule-icon/index.vue';
import { Right } from '@alive-ui/icon';
import {
    ATabs,
    ATabList,
    ATabPanel,
    ATabPanels,
    ATab,
    ACard,
    ACardContent,
    ACardTitle,
    AButton,
    Marquee as AMarquee,
} from '@alive-ui/base';
import { convertSToMinusSecond, isOutLiveRoom } from '@alive-ui/actions';
import { getGiftCarouse } from './service';
import Marquee from './components/marquee.vue';
import { sendTask } from '@/common/logger';

const props = withDefaults(
    defineProps<{
        serviceData: any;
        title?: string;
        extraCardKey?: string;
    }>(),
    {
        title: '送个礼物 流量就来',
        extraCardKey: '',
    },
);
const emits = defineEmits(['refresh-task']);
const enum StatusType {
    NORMAL = 0,
    SELL_OUT = 1,
}
// const countdownFn = (t: number) => convertSToMinusSecond(t / 1000);
const userTips = ref<string[]>([]);

const getGiftCarouseFun = async () => {
    try {
        const res = await getGiftCarouse(props.serviceData.giftId);
        userTips.value = res.carouse;
    } catch (error) {
        // console.error('获取礼物轮播数据失败:', error);
    }
};
const timer = setInterval(() => {
    getGiftCarouseFun();
}, 1000 * 10);
getGiftCarouseFun();

const sendGift = () => {
    const giftParams = {
        giftId: props.serviceData.giftId,
        giftName: props.serviceData.giftName, // 礼物名字
        giftToken: props.serviceData.giftToken, // 礼物token
        unitPrice: props.serviceData.value, // 礼物价格
    };
    sendGiftMultiple(
        giftParams,
        () => {
            sendTask('CLICK', {
                action: 'OP_ACTIVITY_RED_PACKET_GIFT_CARD',
                params: {
                    gift_id: props.serviceData.giftId,
                    btn_type: 'SEND',
                    count: props.serviceData.value,
                },
                status: 'SUCCESS',
                type: 'TASK_EVENT',
            });
        },
        () => {},
    );
};
onBeforeUnmount(() => {
    timer && clearInterval(timer);
});
</script>

<template>
    <div>
        <ACard
            v-if="serviceData"
            v-show-log="{
                action: 'OP_ACTIVITY_RED_PACKET_GIFT_CARD',
                params: {
                    gift_id: serviceData.giftId,
                },
            }"
        >
            <div class="traffic-card-title">
                <RuleIcon
                    :func-btn-log-params="{
                        action: 'OP_ACTIVITY_RED_PACKET_GIFT_CARD',
                        params: {
                            gift_id: serviceData.giftId,
                            btn_type: 'VIEW',
                        },
                    }"
                    class="traffic-rule-icon"
                    rule-key="trafficRedBag"
                ></RuleIcon>
                <ACardTitle class="title">
                    {{ title }}
                </ACardTitle>
            </div>
            <ACardContent class="mt-2">
                <Marquee
                    class="marquee-list"
                    :marquee-list="userTips"
                ></Marquee>
                <!--跑马灯-->
                <!-- <AMarquee
                    v-if="useuserTips?.length"
                    class="marquee-list"
                    :count="useuserTips?.length"
                    :class="`marquee-list-${userTips.length || serviceData.userTips?.length}`"
                    :duration="10000"
                >
                    <div v-for="i in 2" :key="i">
                        <div v-for="item in useuserTips" :key="item">
                            <div class="marquee-item">
                                {{ item }}
                            </div>
                        </div>
                    </div>
                </AMarquee> -->
                <!--礼物介绍-->
                <div class="gift-info">
                    <img :src="serviceData.icon" class="gift-img" />
                    <div class="gift-name">{{ serviceData.giftName }}</div>
                    <div class="gift-desc">{{ serviceData.trafficTip }}</div>
                    <!-- :disabled="serviceData.status === StatusType.SELL_OUT" -->
                    <AButton
                        v-if="!isOutLiveRoom"
                        v-click-log="{
                            action: 'OP_ACTIVITY_RED_PACKET_GIFT_CARD',
                            params: {
                                gift_id: serviceData.giftId,
                                btn_type: 'SEND',
                            },
                        }"
                        class="sned-btn"
                        size="lg"
                        type="primary"
                        @click="sendGift"
                    >
                        立即赠送
                        <span class="gift-price">
                            {{ serviceData.value }}快币
                        </span>
                    </AButton>
                    <!-- <div
                        v-if="serviceData.status === StatusType.SELL_OUT"
                        class="gift-tips a-text-main gift-countdown"
                    >
                        已售罄，
                        <Countdown
                            :to="serviceData.restockingTime"
                            :use-server-time="false"
                            :interval-ms="1000"
                            immediate-emit
                            :transform="countdownFn"
                            @end="refresh"
                        >
                        </Countdown>
                        后补货
                    </div> -->
                    <div class="gift-tips a-text-main">
                        {{ serviceData.giftTip }}
                    </div>
                </div>
            </ACardContent>
        </ACard>
    </div>
</template>

<style lang="less" scoped>
.gift-info {
    display: flex;
    flex-flow: column;
    align-items: center;
    .gift-img {
        width: 114px;
        height: 114px;
        display: block;
        margin-top: 4px;
    }
    .gift-name {
        font-family: HYYakuHei;
        font-size: 16px;
        background: linear-gradient(
            94.51deg,
            #d8ecff 7.58%,
            #ffffff 55.12%,
            #ffdac6 100%
        );
        color: transparent;
        background-clip: text;
        margin-top: 4px;
    }
    .gift-desc {
        font-size: 12px;
        color: #fff;
        margin-top: 2px;
    }
    .sned-btn {
        flex-flow: column;
        margin-top: 14px;
    }
    .gift-price {
        font-size: 10px;
        display: block;
        font-weight: 400;
    }
    .gift-tips {
        font-size: 12px;
        margin-top: 14px;
        opacity: 0.6;
    }
}
.marquee-list {
    position: absolute;
    left: 0;
    top: -10px;
    &.marquee-list-2 {
        height: 63px;
    }
    .marquee-item-line {
        display: flex;
        flex-flow: column;
        &::after {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                180deg,
                rgba(217, 217, 217, 0) -17.14%,
                rgba(217, 217, 217, 0.851092) 39.44%,
                #d9d9d9 120%
            );
        }
    }
    .marquee-item {
        background: rgba(127, 127, 127, 0.15);
        display: inline-block;
        padding: 6px 24px 5px 9px;
        border-radius: 10px;
        font-size: 11px;
        color: #fff;
        margin-top: 4px;
    }
}
.detail-more {
    font-size: 12px;
    width: 100px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.traffic-card-title {
    position: relative;
}
.traffic-rule-icon {
    position: absolute;
    top: 6px;
    right: 12px;
    z-index: 9;
    :deep(.rule-icon) {
        width: 62px;
        height: 18px;
    }
}
</style>
