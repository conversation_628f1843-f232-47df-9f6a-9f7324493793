<template>
    <div v-if="showMarqueen" class="marquee">
        <div v-if="renderList.length" class="marquee-all">
            <div class="list-wrap" :class="`list-wrap-${listLength}`">
                <div class="list" :style="styles">
                    <div v-for="(item, index) in renderList" :key="index">
                        <div class="marquee-item">
                            <slot :index="index" :item="item">
                                {{ item.value }}
                            </slot>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    ref,
    computed,
    onMounted,
    onBeforeUnmount,
    watch,
    nextTick,
} from 'vue';
import { useVisibility } from '@alive-ui/actions';
import type { PropType } from 'vue';

export default defineComponent({
    props: {
        // 自动播放时间（ms）
        autoplay: {
            type: Number,
            default: 3000,
        },
        // 切换速度（ms）
        speed: {
            type: Number,
            default: 1500,
        },
        marqueeList: {
            type: Array as PropType<any[]>,
            default: () => [],
        },
        limitNum: {
            type: Number,
            default: 3,
        },
    },
    // eslint-disable-next-line max-lines-per-function
    setup(props, { emit }) {
        const listText = ['前有人购买', '今日已卖出'];
        let timer: number | null = null;
        let timer2: number | null = null;
        let timer3: number | null = null;
        const showMarqueen = ref(true);
        const indexMaerqueeq = ref(0);
        const running = ref(false);
        const oldFilterData = ref<any[]>([]);
        let newFilterVal: Array<any> = [];
        let oldFilterVal: Array<any> = [];
        const limit = computed(() =>
            Math.min(props.marqueeList.length, props.limitNum || 4),
        );
        const listLength = ref(props.marqueeList.length);
        // 因为同时存在两条，先 concat 下
        // props.marqueeList 的每一项变成个对象，对象中的{value， key}
        const newMarqueeList = props.marqueeList.map((item, index) => {
            return {
                value: item,
                key: new Date().getTime() + index,
            };
        });
        const renderList = ref(
            newMarqueeList
                .slice(0, limit.value)
                .concat(newMarqueeList.slice(0, limit.value)),
        );

        // 计算 styles, 每次滚动一条的位置
        const styles = computed(() => {
            const val = `-0.56rem`;

            if (running.value) {
                return {
                    transform: `translate3d(0, ${val}, 0)`,
                    transitionDuration: `${props.speed}ms`,
                };
            }

            return {};
        });

        const update = () => {
            // 仅1条时无动画
            if (renderList.value.length < 3) {
                return;
            }
            // 计算下一个值的下标值
            const idx =
                (indexMaerqueeq.value + limit.value) % renderList.value.length;
            indexMaerqueeq.value += 1;
            emit('update', idx);
            running.value = true;

            timer2 = window.setTimeout(() => {
                if (indexMaerqueeq.value >= renderList.value.length) {
                    indexMaerqueeq.value = 0;
                    emit('update', indexMaerqueeq.value);
                }
                running.value = false;
                // 如果是有新增的数据，则在队尾插入新增的，否则，插入队头 shift的
                // 抛出 renderList 的第一个
                const newShift = renderList.value.shift();

                if (newShift?.key) {
                    // 如果是老数据，则不往后插入
                    const len = oldFilterVal.filter((item, index) => {
                        if (item.key === newShift.key) {
                            return true;
                        }
                    }).length;
                    if (len === 0) {
                        renderList.value.push(newShift);
                    } else {
                        // 把renderlist中index>2的且key和newShift.key相等的值去掉
                        renderList.value = renderList.value.filter(
                            (item, index) => {
                                return index < 2 || item.key !== newShift.key;
                            },
                        );
                    }

                    // 如果 renderList 为 ['A', 'B']，则补充的数据需要为 ['B', 'A']
                    const renderListHalf = renderList.value.slice(
                        0,
                        Math.round(renderList.value.length / 2),
                    );
                    if (renderListHalf.length) {
                        const isAB = !renderListHalf[
                            renderListHalf.length - 1
                        ]?.value.includes(listText[0]);
                        if (
                            isAB &&
                            newFilterVal[0]?.value?.includes(listText[1])
                        ) {
                            newFilterVal.reverse();
                        } else if (
                            !isAB &&
                            newFilterVal[0]?.value?.includes(listText[0])
                        ) {
                            newFilterVal.reverse();
                        }
                    }
                    if (newFilterVal.length) {
                        // renderList 平分成两组，每组 concat newFilterVal， 然后再组合起来
                        const newRenderList = [
                            ...renderList.value.slice(
                                0,
                                Math.round(renderList.value.length / 2),
                            ),
                            ...newFilterVal,
                        ];
                        newFilterVal = [];
                        renderList.value = newRenderList.concat(newRenderList);
                    }
                }
            }, props.speed);
        };

        const clearTimer = () => {
            indexMaerqueeq.value = 0;
            timer && window.clearInterval(timer);
            timer2 && window.clearTimeout(timer2);
            timer3 && window.clearTimeout(timer3);
            timer = null;
            timer2 = null;
            timer3 = null;
        };

        const start = () => {
            indexMaerqueeq.value = 0;
            timer = window.setInterval(update, props.autoplay);
            update();
        };

        useVisibility({
            hiddenHandler: () => {
                showMarqueen.value = false;
            },
            visibleHandler: () => {
                showMarqueen.value = true;
            },
        });

        onMounted(() => {
            start();
        });

        onBeforeUnmount(() => {
            clearTimer();
        });
        watch(
            () => props.marqueeList,
            (val: any[], oldVal: any[]) => {
                // 第一次有数据
                if ((!oldVal?.length && val.length) || oldVal?.length === 1) {
                    const list = val.map((item, index) => {
                        return {
                            value: item,
                            key: new Date().getTime() + index,
                        };
                    });
                    renderList.value = list.concat(list);
                } else {
                    // 非首次，追加
                    newFilterVal = val.map((item, index) => {
                        return {
                            value: item,
                            key: new Date().getTime() + index,
                        };
                    });

                    oldFilterVal = renderList.value;
                }
                // 新旧数据条数不相等时，要修改列表的长度为新数据的长度
                if (val?.length !== oldVal?.length) {
                    timer3 && window.clearTimeout(timer3);
                    timer3 = window.setTimeout(() => {
                        listLength.value = val.length;
                    }, props.autoplay);
                } else if (!val.length) {
                    timer3 && window.clearTimeout(timer3);
                    timer3 = window.setTimeout(() => {
                        listLength.value = 0;
                    }, props.autoplay);
                }
                // 将新增的不同的数据，塞入到 renderList 的队尾中
                // 查找val中的值不在renderList.value数组中的value的值相等的值
            },
        );
        return {
            styles,
            showMarqueen,
            newMarqueeList,
            renderList,
            listLength,
        };
    },
});
</script>

<style lang="less" scoped>
.marquee {
    &-all,
    .list-wrap {
        width: 100%;
        height: 100%;
    }
    .list-wrap {
        position: relative;
        overflow: hidden;
        height: 28px;
        margin-left: 8px;
        &.list-wrap-2 {
            height: 58px;
        }
    }

    .marquee-item {
        background: rgba(127, 127, 127, 0.15);
        display: inline-block;
        padding: 0 9px 0 9px;
        height: 24px;
        border-radius: 24px;
        font-size: 11px;
        color: #fff;
        margin-top: 4px;
        line-height: 24px;
        font-weight: bold;
    }
}
</style>
