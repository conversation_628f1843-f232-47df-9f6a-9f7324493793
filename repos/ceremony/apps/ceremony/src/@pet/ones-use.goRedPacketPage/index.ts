import { storeToRefs } from 'pinia';
import { throttle } from 'lodash-es';
import { usePartySwitch } from '@pet/ones-use.usePartySwitch';
import useKconfStore from '@pet/ones-use.useKconf';
import { is4Tab } from '@pet/ones-use.is4Tab/index';
import { isStaging } from '@pet/ones-use.env/index';
import { Toast } from '@lux/sharp-ui-next';
import {
    appendParam,
    authorId,
    isOutLiveRoom,
    layoutType,
    liveStreamId,
    userId,
} from '@alive-ui/actions';
import { goPage } from './goPage';

interface PacketPageInfo {
    activityId: string;
}
export const goRedPacketPage = throttle((info: PacketPageInfo) => {
    const { conf4Tab } = storeToRefs(useKconfStore()) || {};
    const downGradeSwitch = usePartySwitch();
    if (downGradeSwitch.value.jinLiJumpDowngradeSwitch) {
        Toast.info(
            conf4Tab.value.jinLiJumpToast ?? '活动太火爆了，请稍后再试～',
        );
        return;
    }
    const { prod, staging } = conf4Tab?.value?.partyKoi?.koiUrl || {};
    const baseUrl = isStaging() ? staging : prod;
    // 剩余场景(-1tab，间外场景)
    let entry_src = 'cny_koi_50';

    if (is4Tab) {
        // 4tab场景
        entry_src = 'cny_koi_51';
    } else if (!isOutLiveRoom) {
        // 间内场景
        entry_src = 'cny_koi_52';
    }

    if (!baseUrl) return;

    const params: Record<string, any> = {
        // 活动id
        activityId: info?.activityId || '',
        // 主播id
        authorId,
        // 用户id
        userId,
        // 活动
        bizId: 'ceremony',
        entry_src,
        entryType: is4Tab ? 3 : 2,
        // 轮次
        round: 1,
        noBackNavi: true,
        liveStreamId,
        layoutType,
    };
    if (!isOutLiveRoom) {
        // 间内跳转添加这两个参数
        params.useRouterPush = true;
        params.exitWebview = 1;
    }
    const url = appendParam(baseUrl, params);
    goPage(url);
}, 1000);
