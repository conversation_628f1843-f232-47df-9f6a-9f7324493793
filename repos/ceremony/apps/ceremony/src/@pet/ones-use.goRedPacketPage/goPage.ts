import { invoke } from '@yoda/bridge';
import { layoutType, liveStreamId } from '@alive-ui/actions';
export const goPage = async (pageUrl: string) => {
    // 直播间
    if (liveStreamId) {
        // 间内跳转
        try {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            await invoke('KwaiLive.openCustomH5', {
                param: {
                    url: pageUrl,
                    portraitHeightRatio: 0.8,
                    layoutType,
                    enablePanDown: true,
                    // 特别重要，保持先前的容器不关闭
                    keeplandscapeifneeded: true,
                    keepdisplaywebview: true,
                },
            });
        } catch (e) {
            // 是否有查询参数
            location.href = pageUrl;
        }
        // 全屏
    } else {
        // 间外跳转
        try {
            await invoke('tool.loadUrlOnNewPage', {
                // 默认为搜索，这种比较多
                url: pageUrl,
                // for ios
                type: 'back',
                // for android
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-expect-error
                leftTopBtnType: 'back',
                ignoreHalfScreenDisplay: 1, // 顶部状态栏，仅ios使用
            });
        } catch (e) {
            location.href = pageUrl;
        }
    }
};
