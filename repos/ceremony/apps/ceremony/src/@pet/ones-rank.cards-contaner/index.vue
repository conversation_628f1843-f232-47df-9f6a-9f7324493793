<script lang="ts" setup>
import { useRoute } from 'vue-router';
import {
    toRef,
    computed,
    ref,
    shallowRef,
    nextTick,
    watch,
    onMounted,
} from 'vue';
import { cloneDeep } from 'lodash-es';
import { useSessionStorage } from '@vueuse/core';
import { useStickyElement } from '@pet/ones-use.useStickyElement';
import { usePageModulsConfig, isDef } from '@pet/ones-use.usePageModulsConfig';
import ErrorBoundary from '@pet/ones-ui.error-boundary/index.vue';
import {
    PlatformEnum,
    UserRoleEnum,
    type DataDetailMap,
    type ShowConfig,
} from '@pet/ones-rank.schema/query-rank';
import { ATabs, ATabList, ATab } from '@alive-ui/base';
import {
    isYodaPCContainer,
    bolIsAuthor,
    isOutLiveRoom,
} from '@alive-ui/actions';
import configs from './moduleConfigs';
import type { TabGroupList } from '@pet/ones-rank.schema/index-home';
// import { countDownBroadcast, EventsEnum } from '@pet/ones-use.useCountDownBroadcast';
// countDownBroadcast.busEvent.on(EventsEnum.countDownBroadcast, (playload: number) => {
//     console.log(playload, 'playload')
// })

type IocDetailMapDataType = {
    [key in keyof DataDetailMap]: undefined | boolean;
};

const props = withDefaults(
    defineProps<{
        cardsTabList: Array<TabGroupList>;
        cardsDetailData: DataDetailMap;
        stageType?: number;
        kconfData?: any;
        rankActivityId?: number;
        showCurrentAnchor?: boolean;
    }>(),
    {
        cardsTabList: () => [] as Array<TabGroupList>,
        cardsDetailData: () => ({}) as DataDetailMap,
        kconfData: () => {},
    },
);

const emits = defineEmits<{
    (
        e: 'refresh-task',
        payload: {
            showLoading?: boolean;
            extraCardKey?: string;
        },
    ): void;
}>();

const router = useRoute();
const tabIndex = ref(0);
const innerCardData = toRef(props, 'cardsDetailData');
const innerKconfData = toRef(props, 'kconfData');

const cardsRef = ref();
// const stickyConfig = {
//     top: 132,
//     tabsHeight: 56,
//     tabsTitleHeight: 58,
// };

const stickyConfig = computed(() => {
    if (props.showCurrentAnchor || isOutLiveRoom) {
        return {
            top: 132,
            tabsHeight: 56,
            tabsTitleHeight: 58,
        };
    }

    return {
        top: 63,
        tabsHeight: 69,
        tabsTitleHeight: 69,
    };
});

const cardsContentRefs = shallowRef<
    { key: string; top: number; toTop: number }[]
>([]);

const lockScroll = ref(false);
const direction = ref<'' | 'down' | 'up'>('');
const clickToTop = ref<number>(0);
function onChange(idx: number) {
    if (idx === tabIndex.value) return;

    direction.value = idx > tabIndex.value ? 'down' : 'up';

    tabIndex.value = idx;
    lockScroll.value = true;
    const { groupType } = renderCardsTabList.value[idx];
    const card = cardsContentRefs.value.find((item) => item.key === groupType);
    clickToTop.value = card!.toTop;

    window.scrollTo({
        top: clickToTop.value,
        behavior: 'smooth',
    });
}
const scrollCb = (pos: number, scrollTop: number) => {
    if (
        (direction.value === 'down' && scrollTop >= clickToTop.value) ||
        (direction.value === 'up' && scrollTop <= clickToTop.value) ||
        toBottom.value
    ) {
        lockScroll.value = false;
        clickToTop.value = 0;
        direction.value = '';
    }
    if (renderCardsTabList.value.length && !lockScroll.value) {
        for (let i = 0; i < renderCardsTabList.value.length; i++) {
            const { groupType } = renderCardsTabList.value[i];
            const card = cardsContentRefs.value.find(
                (item) => item.key === groupType,
            );
            // console.log(card, 'card')
            // console.log(scrollTop, 'scrollTop')
            if (card) {
                if (toBottom.value) {
                    tabIndex.value = renderCardsTabList.value.length - 1;
                    return;
                }
                if (scrollTop < card.toTop) {
                    return;
                }
                tabIndex.value = i;
            }
        }
    }
};
const { isSticky, ratio, outLiveRoomTopHeight, toBottom } = useStickyElement(
    cardsRef,
    stickyConfig,
    scrollCb,
);

//  控制反转，各卡片组件内部抛出是否展示控制
const iocDetailMapData = ref<IocDetailMapDataType>({} as IocDetailMapDataType);
const renderTabs = ref(true);
function getToTop(el: HTMLElement, idx: number, scrollTop: number) {
    const topDistance =
        idx === 0
            ? stickyConfig.value.tabsHeight + stickyConfig.value.top
            : stickyConfig.value.tabsTitleHeight + stickyConfig.value.top;
    const { top } = el.getBoundingClientRect();
    // console.log( el.getBoundingClientRect(), 'toptoptop')
    // console.log(el, 'elelelel')
    const useTop = top + scrollTop;
    return {
        useTop,
        toTop: Math.floor(useTop - topDistance * ratio + outLiveRoomTopHeight),
    };
}

const finishCorrect = ref(false);
/**
 * 可能有些卡片内容数据是卡片组件里单独的接口获取，内容区域的撑开依赖接口数据的返回
 * 如果存在这种情况，卡片组件需要在内容区域撑开后调用这个函数来更新卡片的高度
 * @param cardKey 卡片 key，透传传入组件的 extraCardKey
 */
function correctRefTop(payload: {
    extraCardKey: keyof IocDetailMapDataType;
    show?: boolean;
}) {
    finishCorrect.value = false;
    const { extraCardKey, show } = payload;
    // console.log(extraCardKey, show, 'xxxx')
    renderTabs.value = false;
    const cards = cloneDeep(renderCardsTabList.value);
    iocDetailMapData.value[extraCardKey] = show;

    nextTick(() => {
        // const l = show ? cards.length : cards.length - 1;
        const l = cards.length;
        renderTabs.value = true;
        nextTick(() => {
            const cardContentAreaDoms = Array.from(
                document.querySelectorAll('.arrange-card-content-area'),
            );
            const scrollTop =
                window.pageYOffset ||
                document.documentElement.scrollTop ||
                document.body.scrollTop ||
                0;
            for (let i = 0; i < l; i++) {
                if (cardsContentRefs.value?.[i]) {
                    const { key } = cardsContentRefs.value[i];
                    const el = cardContentAreaDoms.find((c) => {
                        const dataKey =
                            (c as HTMLDivElement).dataset.cardgroup ?? '';
                        return dataKey === key;
                    });
                    if (el) {
                        const { toTop, useTop } = getToTop(
                            el as HTMLElement,
                            i,
                            scrollTop,
                        );
                        cardsContentRefs.value[i].toTop = toTop;
                        cardsContentRefs.value[i].top = useTop;
                    }
                }
            }
            finishCorrect.value = true;
        });
    });
}

const isScrolledToCard = useSessionStorage('scroll-card-flag', false);
const scrollToCardGroup = () => {
    nextTick(() => {
        const { cardGroup } = router.query ?? {};
        if (cardGroup) {
            const scrollToCardContentRef = cardsContentRefs.value.find(
                (item) => item.key === cardGroup,
            );
            if (scrollToCardContentRef && !isScrolledToCard.value) {
                window.scrollTo({
                    top: scrollToCardContentRef.toTop,
                    behavior: 'smooth',
                });
                isScrolledToCard.value = true;
            }
        }
    });
};

const unwatch = watch(
    () => finishCorrect.value,
    (val) => {
        if (val) {
            scrollToCardGroup();
            unwatch();
        }
    },
    {
        deep: false,
    },
);

const groupCombineKeys = computed(() => {
    const res = [] as string[];
    props.cardsTabList?.forEach((item) => {
        item.extraTabDetailList.forEach((ele) => {
            res.push(`${ele.extraTabDataType}::${ele.extraTabDataKey}`);
        });
    });
    return res;
});

const { renderConfigsMap, propsData } = usePageModulsConfig(
    configs,
    innerCardData,
    innerKconfData,
    groupCombineKeys,
);
// console.log(renderConfigsMap, 'renderConfigs');

//  PC 端，主播/观众端，间外
const showInHostHelper = (showConfig: ShowConfig) => {
    /**
     * 没有配置，全部展示
     */
    if (!showConfig) return true;
    /**
     * 卡片在主播/观众端的显隐控制
     */
    const defaultIdentityShow =
        !showConfig.supportIdentity ||
        showConfig.supportIdentity.includes(UserRoleEnum.ALL);
    const showInAuthorRole =
        defaultIdentityShow ||
        (bolIsAuthor &&
            showConfig.supportIdentity.includes(UserRoleEnum.AUTHOR));
    const showInAudienceRole =
        defaultIdentityShow ||
        (!bolIsAuthor &&
            showConfig.supportIdentity.includes(UserRoleEnum.AUDIENCE));
    const showInUser = showInAuthorRole || showInAudienceRole;
    /**
     * 卡片在间外的显隐控制
     */
    if (isOutLiveRoom) {
        return showConfig.supportOutLiveStream;
    }
    /**
     * 卡片在 pc 端显隐控制
     */
    const defaultPlatformShow =
        !showConfig.source || showConfig.source.includes(PlatformEnum.ALL);
    if (isYodaPCContainer) {
        const showInPc =
            defaultPlatformShow || showConfig.source.includes(PlatformEnum.PC);
        return showInPc && showInUser;
    }
    /**
     * 卡片在直播间内的显隐控制
     */
    const showInRoom =
        defaultPlatformShow || showConfig.source.includes(PlatformEnum.CLIENT);
    return showInRoom && showInUser;
};
const renderCardsTabList = computed(() => {
    const { cardsDetailData, cardsTabList } = props;
    if (!cardsTabList.length || !Object.keys(cardsDetailData).length) return [];

    return cardsTabList.filter((cardTab) => {
        return cardTab.extraTabDetailList.some((detail) => {
            const { extraTabDataKey } = detail;
            const detailData =
                cardsDetailData?.[
                    extraTabDataKey as unknown as keyof DataDetailMap
                ];
            const showConfig = detailData?.showConfig;
            return (
                !!detailData &&
                (!isDef(iocDetailMapData.value[extraTabDataKey]) ||
                    iocDetailMapData.value[extraTabDataKey]) &&
                renderConfigsMap[detail.extraTabDataType] &&
                showInHostHelper(showConfig)
            );
        });
    });
});

onMounted(() => {
    nextTick(() => {
        const cardContentAreaDoms = Array.from(
            document.querySelectorAll('.arrange-card-content-area'),
        );
        // const l = cardContentAreaDoms.length
        // const maxScrollTop = document.documentElement.scrollHeight - document.documentElement.clientHeight
        cardContentAreaDoms.forEach((el, index) => {
            const key = (el as HTMLDivElement).dataset.cardgroup ?? '';
            const scrollTop =
                window.pageYOffset ||
                document.documentElement.scrollTop ||
                document.body.scrollTop ||
                0;
            const { toTop, useTop } = getToTop(
                el as HTMLElement,
                index,
                scrollTop,
            );
            // const lastToTop = maxScrollTop - stickyConfig.value.tabsTitleHeight * ratio + outLiveRoomTopHeight
            cardsContentRefs.value.push({
                key,
                top: useTop,
                // toTop: index === l - 1 ? lastToTop : toTop
                toTop,
            });
        });

        const needCorrectComp: string[] = [];
        configs.forEach((c) => {
            if (c.asyncCorrectPos) {
                needCorrectComp.push(c.key);
            }
        });
        //  进入页面需要锚定且不存在卡片异步更新位置的情况，直接滚动锚定，否则等待卡片异步更新位置完成后滚动锚定
        if (
            !props.cardsTabList.some((card) => {
                return card.extraTabDetailList.some((tabDetail) => {
                    return needCorrectComp.includes(tabDetail.extraTabDataType);
                });
            })
        ) {
            finishCorrect.value = true;
        }
    });
});
</script>

<template>
    <div
        v-show="renderCardsTabList.length"
        ref="cardsRef"
        class="cards-container"
        :class="{ 'sticky-tabs': isSticky, 'is-out': isOutLiveRoom }"
    >
        <ATabs
            v-if="renderTabs"
            v-model:selected-index="tabIndex"
            class="tabs-area"
            size="sm"
            :class="{ 'has-anchor': showCurrentAnchor }"
            @change="onChange"
        >
            <ATabList
                class="tabs-list h-42px mb-14px ml-[-4px] leading-42px min-w-358px overflow-x-auto"
                :class="[`tab-count-${renderCardsTabList.length}`]"
            >
                <ATab
                    v-for="(item, index) in renderCardsTabList"
                    :key="index"
                    class="tab-item"
                >
                    <div class="tab-item-title a-text-title">
                        {{ item.title }}
                    </div>
                </ATab>
            </ATabList>
        </ATabs>
        <div v-if="isSticky" class="holder h-56px"></div>
        <!-- 排序按照 server 下发的顺序排序 -->
        <div
            v-for="(item, index) in renderCardsTabList"
            :key="index"
            class="arrange-card-content-area"
            :data-cardgroup="item.groupType"
        >
            <div
                v-if="item.title && index > 0"
                class="card-title-area flex-start-center mt-20px mb-14px"
            >
                <div class="card-title a-text-title text-16px mr-4px">
                    {{ item.title }}
                </div>
                <div
                    v-if="item.subTitle"
                    class="card-sub-tile a-text-main text-12px mt-1px"
                >
                    {{ item.subTitle }}
                </div>
            </div>
            <ErrorBoundary
                v-for="card in item.extraTabDetailList"
                :key="card.extraTabDataKey"
                :module-key="card.extraTabDataKey"
                :module-type="card.extraTabDataType"
            >
                <component
                    :is="renderConfigsMap[card.extraTabDataType].renderComp"
                    v-if="
                        renderConfigsMap[card.extraTabDataType] &&
                        cardsDetailData[card.extraTabDataKey]
                    "
                    :extra-card-key="card.extraTabDataKey"
                    :stage-type="stageType"
                    :rank-activity-id="rankActivityId"
                    v-bind="propsData[card.extraTabDataKey]"
                    @correct-ref-top="correctRefTop"
                    @refresh-task="emits('refresh-task', $event)"
                />
            </ErrorBoundary>
        </div>
    </div>
</template>

<style lang="less" scoped>
.cards-container {
    .card-title-area {
        .card-title {
            font-family: HYYakuHei;
        }
    }
    .arrange-card-content-area {
        .error-boundary {
            :deep(.card-bg-img) {
                margin: 0 0 14px;
            }
            &:last-child {
                :deep(.card-bg-img) {
                    margin: 0;
                }
            }
        }
    }
    .tabs-area {
        z-index: 13;
        position: relative;
        width: 398px;
        overflow: visible;
    }
    .tabs-list {
        font-family: HYYakuHei;
    }
    :deep(.tab-item-title) {
        opacity: 0.5;
        font-size: 14px;
    }
    :deep(.a-text-tab-active) {
        .tab-item-title {
            opacity: 1;
            font-size: 20px;
            // width: 80px;
            transform: skew(-7deg);
        }
    }
    :deep(.a-bg-tab-active) {
        background: linear-gradient(0deg, #ffdfbf, #ffdfbf),
            linear-gradient(
                94.51deg,
                #d8ecff 7.58%,
                #ffffff 55.12%,
                #ffdac6 100%
            );
    }
    // :deep(.tab-item) {
    //     width: 84px;
    // }
}
.sticky-tabs {
    .tabs-area {
        background-color: transparent;
        position: fixed;
        top: 63px;
        &.has-anchor {
            @apply a-bg-page;
            top: 132px;
        }
    }
}
.sticky-tabs.is-out {
    .tabs-area {
        top: 93px;
    }
}
:deep(.tabs-tab-attrs) {
    span {
        font-weight: 400;
    }
}
</style>
