/**
 * 卡片容器配置
 * 在这里补充你在卡片聚合容器，后续也可以放到 kconf 中，也可以通过智能体生成json和代码
 */
import GameCard from '@pet/ones-ui.cur-anchor-game-card/index.vue';
import CountyDistrictChampionCard from '@pet/ones-ui.county-district-champion-card/index.vue';
import CheckIn from '@pet/ones-rank.card-star/index.vue';
import PK from '@pet/ones-rank.card-peak-battle/index.vue';
import StrategyGuideTab from '@pet/ones-plays.video-guide/index.vue';
import ActivityRedPack from '@pet/ones-plays.task-redbag/index.vue';
import TrafficRedPack from '@pet/ones-plays.rate-flow-redpacket/index.vue';
import HotListTab from '@pet/ones-plays.hot-list/index.vue';
import RandomGift from '@pet/ones-plays.honor/index.vue';
import Cohesion from '@pet/ones-plays.cohesion-challenge/index.vue';

export const configs = [
    // {
    //     key: 'propsCard',   //  测试卡片，不要动
    //     path: '/src/@pet/ones-rank.test-card/index.vue',
    //     props2dataKey: {
    //         serviceData: 'propsCard',
    //         // kconfData: '$cBuffCardRules'  // 用 $ 分割的 kconfKey 和值的 key
    //         kconfData: {
    //             aw: '$AWARD_ADDRESS',
    //             pd: '$PREVIEW_DESC',
    //         },
    //     },
    // },
    /**
     * 以下为真实卡片的配置
     */
    {
        //  巅峰对决和道具卡 @wangkun
        key: 'pk',
        path: '/src/@pet/ones-rank.card-peak-battle/index.vue',
        props2dataKey: {
            // propsCard: '$_propsCard',
            pk: '$_pk.pkInfo',
        },
        renderComp: PK,
    },
    {
        //  用户凝聚力挑战 @liuyihao
        key: 'cohesion',
        path: '/src/@pet/ones-plays.cohesion-challenge/index.vue',
        props2dataKey: {
            serviceData: 'cohesion.cohesionTaskHomeInfo',
        },
        renderComp: Cohesion,
    },
    {
        //  送神器开大奖 @renmingming
        key: 'randomGift',
        path: '/src/@pet/ones-plays.honor/index.vue',
        props2dataKey: {
            serviceData: 'randomGift',
        },
        renderComp: RandomGift,
    },
    // {   //  礼物 @zhengchaofan
    //     key: 'trafficRedPack',
    //     path: 'xxx',
    //     props2dataKey: {
    //         serviceData: 'trafficRedPack'
    //     },
    // },
    {
        // 红包 @yangxi
        key: 'activityRedPack',
        path: '/src/@pet/ones-plays.task-redbag/index.vue',
        props2dataKey: {
            serviceData: 'activityRedPack',
            kconfData: {
                title: 'contentPage.titleList.TaskRedbag',
            },
        },
        renderComp: ActivityRedPack,
        asyncCorrectPos: true,
    },
    {
        // 盛典热榜 @yangxi
        key: 'hotListTab',
        path: '/src/@pet/ones-plays.hot-list/index.vue',
        props2dataKey: {
            hotList: '$_hotListTab.hotList',
            kconfData: {
                title: 'contentPage.titleList.HotList',
                hotListSub: 'contentPage.titleList.HotListSub',
            },
        },
        renderComp: HotListTab,
    },
    {
        // 夺冠攻略 @yangxi
        key: 'strategyGuideTab',
        path: '/src/@pet/ones-plays.video-guide/index.vue',
        props2dataKey: {
            strategyGuide: '$_strategyGuideTab.strategyGuide',
            kconfData: {
                title: 'contentPage.titleList.VideoGuide',
            },
        },
        renderComp: StrategyGuideTab,
    },
    {
        //  流量红包 @yangxi
        key: 'trafficRedPack',
        path: '/src/@pet/ones-plays.rate-flow-redpacket/index.vue',
        props2dataKey: {
            serviceData: 'trafficRedPack',
        },
        renderComp: TrafficRedPack,
    },
    {
        //  季度之星 @guozhao
        key: 'checkIn',
        path: '/src/@pet/ones-rank.card-star/index.vue',
        props2dataKey: {
            checkIn: '$_checkIn',
        },
        renderComp: CheckIn,
    },
    {
        key: 'smallBell',
        path: '/src/@pet/ones-ui.cur-anchor-game-card/index.vue',
        props2dataKey: {},
        renderComp: GameCard,
    },
];

export default configs;
