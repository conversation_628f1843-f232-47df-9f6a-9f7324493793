<template>
    <div class="w-[414px] h-[130px] relative translate-z-0">
        <YodaImage
            v-if="imgSrc"
            class="w-[414px] h-[130px] absolute top-0 kv-yoda-image"
            :src="imgSrc"
            @error="(e: Event) => onYodaImageError(e, 'l1')"
        />
        <div class="w-[414px] h-[130px] absolute top-0">
            <VideoPlay
                v-if="!downgrade && videoSrc"
                object-fit="fill"
                not-click
                autoplay
                :video-url="[
                    {
                        url: videoSrc,
                    },
                ]"
            >
            </VideoPlay>
        </div>
    </div>
</template>

<script lang="ts" setup>
import YodaImage from '@pet/yoda.image/img.vue';
import { useDowngrade } from '@pet/ones-use.useDowngrade/index';
import VideoPlay from '@pet/ones-ui.video-play/index.vue';
import { Report } from '@alive-ui/actions';

defineProps<{
    imgSrc: string;
    videoSrc: string;
}>();

const downgrade = useDowngrade();

const onYodaImageError = (e: Event, level: string) => {
    Report.biz.error('kv区yoda-image异常', {
        error: e,
        level,
    });
};
</script>

<style lang="less" scoped>
.kv-yoda-image {
    --y-img-height: 130px;
    --y-img-width: 414px;
}
</style>
