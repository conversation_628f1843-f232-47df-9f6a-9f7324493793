<template>
    <img
        v-if="
            rechargeStore.rechargeJobInfo.hasRecharge &&
            rechargeStore.rechargeJobInfo.jumpUrl &&
            !isOutLiveRoom &&
            !bolIsAuthor
        "
        v-ShowLog="{
            action: 'OP_ACTIVITY_FUNCTION_BUTTON',
            params: { btn_type: '尊享福利' },
        }"
        v-click-log="{
            action: 'OP_ACTIVITY_FUNCTION_BUTTON',
            params: { btn_type: '尊享福利' },
        }"
        class="recharge-icon"
        src="./assets/kv-icon_2x.png"
        @click="gotoRechargeJob"
    />
</template>

<script setup lang="ts">
import {
    isOutLiveRoom,
    bolIsAuthor,
    isYodaPCContainer,
    goOtherPage,
} from '@alive-ui/actions';
import useRechargeStore from './models/recharge';
import { showPCToast } from '@/@pet/ones-ui.recharge-icon/utils/toast';

const rechargeStore = useRechargeStore();

rechargeStore.init();

const gotoRechargeJob = () => {
    if (isYodaPCContainer) {
        showPCToast('请到手机端进行查看');

        return;
    }

    goOtherPage('krn', rechargeStore.rechargeJobInfo.jumpUrl, 1, location.href);
};
</script>

<style scoped lang="less">
.recharge-icon {
    display: block;
    width: 56px;
    height: 56px;
    margin: 0 6px;
}
</style>
