import { request } from '@alive-ui/actions';
import type { HasRechargeJobV2PostResponse } from '../schemas/hasRechargeJobV2';

const PATH = {
    hasRechargeJobV2: '/rest/wd/live/revenueAudienceJob/hasRechargeJobV2',
};

// 有无充返任务v2： https://mock.corp.kuaishou.com/project/941/interface/api/1106367
export const hasRechargeJobV2 = async () => {
    const res = await request.post<HasRechargeJobV2PostResponse>(
        PATH.hasRechargeJobV2,
        {},
    );

    return res?.data;
};
