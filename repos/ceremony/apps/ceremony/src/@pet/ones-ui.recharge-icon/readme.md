# 尊享福利图标组件文档

## 功能概述
尊享福利图标组件用于显示一个尊享福利的图标，当满足特定条件时，点击图标会跳转到充值页面。该组件主要用于直播间的互动场景。

## 属性和方法

### 属性
- **无**

## 使用示例

```vue
<template>
  <div>
    <recharge-icon />
  </div>
</template>

<script setup lang=\"ts\">
import RechargeIcon from '@pet/ones-ui.recharge-icon/index.vue';
</script>

<style scoped lang=\"less\">
/* 自定义样式 */
</style>
```

## 注意事项
1. 该组件在某些条件下不会显示，具体条件如下：
   - `rechargeStore.rechargeJobInfo.hasRecharge` 为 `true`
   - `rechargeStore.rechargeJobInfo.jumpUrl` 存在
   - `isOutLiveRoom` 为 `false`
   - `bolIsAuthor` 为 `false`
2. 如果在 Yoda PC 容器中，点击图标会显示提示信息“请到手机端进行查看”，并阻止跳转操作。
3. 组件使用了 `v-ShowLog` 和 `v-click-log` 自定义指令，用于记录用户行为日志。

## 依赖项
- `@alive-ui/actions`: 提供 `isOutLiveRoom`, `bolIsAuthor`, `isYodaPCContainer`, `goOtherPage` 等方法。
- `@pet/ones-ui.recharge-icon/models/recharge`: 提供 `useRechargeStore` 钩子。
- `@pet/ones-ui.recharge-icon/utils/toast`: 提供 `showPCToast` 方法。

### 使用场景

-   默认是 live-a 工程,如果用在 gundam 工程，请把@live/action 替换成@alive-ui/actions
