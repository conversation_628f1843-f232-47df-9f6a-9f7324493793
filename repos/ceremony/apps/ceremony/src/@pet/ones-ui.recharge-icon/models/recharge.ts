import { ref } from 'vue';
import { defineStore } from 'pinia';
import { hasRechargeJobV2 } from '../services/hasRechargeJobV2';

export default defineStore('recharge-store', () => {
    const rechargeJobInfo = ref({
        hasRecharge: false,
        jumpUrl: '',
    });

    const init = () => {
        // 非核心逻辑，延时请求
        setTimeout(async () => {
            try {
                const res = await hasRechargeJobV2();
                rechargeJobInfo.value = res;
            } catch (e) {
                console.log(e);
            }
        }, 1000);
    };

    return {
        init,
        rechargeJobInfo,
    };
});
