import { invoke, isYodaPCContainer } from '@alive-ui/actions';
/**
 *  @name Toast
 *  @param text toast 内容
 *  @param type toast类型，normal(灰色)，success(绿色)，error(红色)
 */

export const showToast = async (text?: string, type?: 'normal' | 'success') => {
    try {
        if (isYodaPCContainer) {
            showPCToast(text);

            return;
        }
        await invoke('Kwai.showToast', {
            type: type || 'normal',
            text: text || '请求超时',
            isAddToWindow: true,
        });
    } catch (error) {
        console.log(error);
    }
};

/**
 *   Toast
 *  @param text toast 内容
 *  @param type toast类型，normal(灰色)，success(绿色)，error(红色)
 */
export const showPCToast = async (
    text?: string,
    type?: 'normal' | 'success',
) => {
    try {
        await invoke('ui.showToast', {
            type: type || 'normal',
            text: text || '请求超时',
        });
    } catch (error) {
        console.log(error);
    }
};
