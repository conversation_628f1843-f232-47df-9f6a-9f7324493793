import { computed, isRef } from 'vue';
import { get } from 'lodash-es';
import type {
    ComputedRef,
    Ref,
    ComponentPublicInstance,
    defineAsyncComponent,
} from 'vue';
import type { defineStore } from 'pinia';
import type useKConfBatch from '@pet/ones-use.useKconfBatch';

export function isDef<T>(v: T): v is NonNullable<T> {
    return v !== undefined && v !== null;
}

export type ConfigType = {
    [key: string]: any;
    key: string;
    path: string;
    props2dataKey: Record<string, any>;
    show?: boolean | ComputedRef<boolean>;
    eventsBus?: Array<{
        eventKey: string;
        handler: (...args: unknown[]) => void;
    }>;
    eventTriggerNames?: string[];
    asyncCorrectPos?: boolean;
    renderComp:
        | ReturnType<typeof defineAsyncComponent>
        | ComponentPublicInstance;
};

type InputKconfData = ReturnType<typeof useKConfBatch>['kconfData'];
type KeyInKconfData = keyof InputKconfData;

/**
 *
 * @param configs
 * @param pageStore
 * @param kconfData
 * @param groupCombineKeys 是否存在分组，如果存在，其元素为 [${groupKey}::${config.key}]，用 :: 字符链接
 * @returns
 */
export function usePageModulsConfig(
    configs: ConfigType[],
    pageStore: ReturnType<ReturnType<typeof defineStore>> | Ref,
    inputKconfData: InputKconfData | Ref<InputKconfData>,
    groupCombineKeys?: string[] | Ref<string[]> | ComputedRef<string[]>,
) {
    function getPropsData(props2dataKey: Record<string, any>) {
        const dataResource = isRef(pageStore) ? pageStore.value : pageStore;
        const kconfData = isRef(inputKconfData)
            ? inputKconfData.value
            : inputKconfData;
        const propsData = {} as Record<string, any>;
        for (const propKey in props2dataKey) {
            const dataKey = props2dataKey[propKey];
            //  从服务拿数据
            if (
                propKey === 'serviceData' ||
                (typeof dataKey === 'string' && dataKey.startsWith('$_'))
            ) {
                propsData[propKey] = get(
                    dataResource,
                    dataKey.replace('$_', ''),
                );
            }
            //  从 kconf store 拿数据
            else if (propKey === 'kconfData') {
                if (typeof dataKey === 'string') {
                    propsData[propKey] = get(kconfData, dataKey);
                } else if (typeof dataKey === 'object') {
                    for (const key in dataKey) {
                        propsData[key] = get(kconfData, dataKey[key]);
                    }
                } else {
                    console.error(
                        '[module-config:error]：解析 kconfData 报错，请检查 props2dataKey 对应的 kconfData 配置项，支持 string 和 object 类型',
                    );
                }
            } else {
                //  直接取值作为数据
                propsData[propKey] = dataKey;
            }
        }
        return propsData;
    }

    const propsData = computed(() => {
        const datas = {} as unknown as Record<string, any>;
        const unRefCombineKeys = isRef(groupCombineKeys)
            ? groupCombineKeys.value
            : groupCombineKeys;
        if (unRefCombineKeys?.length) {
            for (const combineKey of unRefCombineKeys) {
                const [key, groupKey] = combineKey.split('::');
                const item = configs.find((item) => item.key === groupKey);
                item && (datas[key] = getPropsData(item.props2dataKey));
            }
        } else {
            for (const item of configs) {
                datas[item.key] = getPropsData(item.props2dataKey);
            }
        }
        return datas;
    });

    const renderConfigsMap = {} as ConfigType & {
        renderComp: ReturnType<typeof defineAsyncComponent>;
    };

    configs.forEach((config) => {
        renderConfigsMap[config.key] = {
            ...config,
        };
    });

    return {
        renderConfigsMap,
        propsData,
    };
}
