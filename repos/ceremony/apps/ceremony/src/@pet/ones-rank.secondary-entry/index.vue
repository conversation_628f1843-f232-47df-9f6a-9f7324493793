<template>
    <div
        v-if="entryDataList?.length"
        ref="sentryRef"
        class="secondary-entry"
        :class="{
            'secondary-margin-style': needMargin,
            showNext: showNext,
            'secondary-entry-row': flexDirection === 'row',
            'secondary-entry-col': flexDirection === 'column',
            'six-entry':
                entryDataList.length === 6 && !cutNextShowDataList.length,
        }"
        :style="styles"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
    >
        <div
            v-for="(item, index) in showDataList"
            :key="index"
            v-show-log="{
                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                params: {
                    btn_type: item.logType,
                },
            }"
            v-click-log="{
                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                params: {
                    btn_type: item.logType,
                },
            }"
            class="elem-entry-icon"
            :class="{
                'no-6':
                    entryDataList.length === 6 &&
                    !cutNextShowDataList.length &&
                    index === 5,
            }"
            @click="jumpOtherPage(item)"
        >
            <img :src="item.iconUrl" alt="" />
            <!-- @vue-ignore -->
            <ATag
                v-if="item.name == 'addCard' && Number(item?.displayText) > 0"
                form="number"
                class="count-display-text"
            >
                {{ item.displayText }}
            </ATag>
        </div>
        <div v-show="cutShowDataList.length" class="cut-wrap">
            <div
                v-for="(item, index) in cutShowDataList"
                :key="index"
                v-show-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: {
                        btn_type: item.logType,
                    },
                }"
                v-click-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: {
                        btn_type: item.logType,
                    },
                }"
                class="elem-entry-icon"
                @click="jumpOtherPage(item)"
            >
                <img :src="item.iconUrl" alt="" />
                <!-- @vue-ignore -->
                <ATag
                    v-if="
                        item.name == 'addCard' && Number(item?.displayText) > 0
                    "
                    form="number"
                    class="count-display-text"
                >
                    {{ item.displayText }}
                </ATag>
            </div>
            <template v-if="cutNextShowDataList.length && showNext">
                <div
                    v-for="(item, index) in cutNextShowDataList"
                    :key="index"
                    v-show-log="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: item.logType,
                        },
                    }"
                    v-click-log="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: item.logType,
                        },
                    }"
                    class="elem-entry-icon"
                    @click="jumpOtherPage(item)"
                >
                    <img :src="item.iconUrl" alt="" />
                    <!-- @vue-ignore -->
                    <ATag
                        v-if="
                            item.name == 'addCard' &&
                            Number(item?.displayText) > 0
                        "
                        form="number"
                        class="count-display-text"
                    >
                        {{ item.displayText }}
                    </ATag>
                </div>
            </template>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router';
import { getCurrentInstance, computed, ref, nextTick } from 'vue';
import { debounce } from 'lodash-es';
import { toShare } from '@pet/ones-use.share';
// import { linkOtherBizPage, SceneType } from '@pet/ones-use.linkOtherBizPage';
import { ATag } from '@alive-ui/base';
import {
    activityBiz,
    authorId,
    entry_src,
    layoutType,
    goOtherPage,
    liveStreamId,
    isOutLiveRoom,
    appendParam,
    isYodaPCContainer,
    bolIsAuthor,
    showToast,
    getQuery,
} from '@alive-ui/actions';
import { EntranceType } from './schemas/index';
import type { PropType } from 'vue';
import type { EntranceItem } from './schemas/index';
import { sendShow } from '@/common/logger';
import { showPCToast } from '@/@pet/ones-ui.recharge-icon/utils/toast';
import useRechargeStore from '@/@pet/ones-ui.recharge-icon/models/recharge';
// 需要重新设计下逻辑 11.13日晚来不及了 @liuyihao
const rechargeStore = useRechargeStore();

const jumpTo = (url: string, extraParams: Record<string, any> = {}) => {
    const params = {
        liveStreamId,
        authorId,
        layoutType,
        entry_src,
        showFirstBackBtn: true,
        ...extraParams,
    };
    const jumpUrl = appendParam(url, params);
    window.location.href = jumpUrl;
};
const emits = defineEmits(['extend', 'touch-trigger', 'route-query-change']);
const handleShare = debounce(() => {
    toShare();
}, 500);
const props = defineProps({
    needMargin: {
        type: Boolean,
        default: false,
    },
    entryData: {
        type: Array as PropType<any[]>,
        default: () => {
            return [];
        },
    },
    flexGap: {
        type: String,
        default: '0px',
    },
    flexDirection: {
        type: String,
        default: 'column',
    },
    // 非必填 当前的stageType，在跳转的时候会作为参数告知下一个页面来源赛事
    stageType: {
        type: Number,
        required: false,
    },
    /**
     * 横滑交互形式
     * 默认为 normal，横滑
     * unfold：横滑展开
     */
    slideType: {
        type: String as () => 'normal' | 'unfold',
        default: 'normal',
    },
    /**
     * 截断索引
     */
    cutNumIdx: {
        type: Number,
        default: 6,
    },
    /**
     * 父组件宽度
     */
    secEntryWrapPureWidth: {
        type: Number,
        default: 0,
    },
});

const router = useRouter();
const route = useRoute();
const touchStartX = ref(0);
const offsetX = ref(0);
const initialOffsetX = ref(0);
const isMoving = ref(false);
const direction = ref<'left' | 'right'>();
const threshold = 0.2; // 滑动自动定位的阈值比
const judgeOperate = {
    distanceThreshold: 1, // 移动阈值
    duration: 100, // 时长阈值
};
let startTime = 0;
let duration = 0;
let endDelta = 0;
const sentryRef = ref<HTMLDivElement>();
let sentryRefBoundings: DOMRect | undefined;
const showNext = ref(false);

// 触摸开始时记录起始 x 坐标
const handleTouchStart = (event: TouchEvent) => {
    if (!canUnfold.value) return;
    isMoving.value = true;
    startTime = Date.now();
    duration = 0;
    endDelta = 0;
    touchStartX.value = event?.touches?.[0]?.clientX ?? 0;
    initialOffsetX.value = offsetX.value;
};
// 禁止页面滚动
const handleTouchMove = (event: TouchEvent) => {
    if (!isMoving.value || !canUnfold.value) return;
    event.preventDefault();
    const touchingX = event?.changedTouches?.[0]?.clientX ?? 0;
    const deltaX = touchingX - touchStartX.value;
    direction.value = deltaX > 0 ? 'right' : 'left';
    if (direction.value === 'right') {
        //  向右滑动，收起
        offsetX.value = Math.min(0, initialOffsetX.value + deltaX);
    } else {
        //  向左滑动，展开
        showNext.value = true;
        if (!sentryRefBoundings && sentryRef.value) {
            sentryRefBoundings = sentryRef.value.getBoundingClientRect();
        }
        if (sentryRefBoundings) {
            const maxTanslateX =
                (sentryRefBoundings.width - props.secEntryWrapPureWidth) * -1;
            offsetX.value = Math.max(
                maxTanslateX,
                initialOffsetX.value + deltaX,
            );
        }
    }
};
// 触摸结束时判断滑动方向
const handleTouchEnd = (event: TouchEvent) => {
    if (!canUnfold.value) return;
    const touchEndX = event?.changedTouches?.[0]?.clientX ?? 0;
    const deltaX = touchEndX - touchStartX.value;
    const translateXPos = initialOffsetX.value + deltaX;
    isMoving.value = false;
    const useThreshold = direction.value === 'left' ? threshold : 1 - threshold;
    if (sentryRefBoundings) {
        const maxTanslateX =
            (sentryRefBoundings.width - props.secEntryWrapPureWidth) * -1;
        if (translateXPos < maxTanslateX * useThreshold) {
            offsetX.value = maxTanslateX;
        } else {
            offsetX.value = 0;
            showNext.value = false;
        }

        const shouldUpdateIndicator =
            (direction.value === 'left' &&
                translateXPos < maxTanslateX * useThreshold) ||
            (direction.value === 'right' &&
                translateXPos > maxTanslateX * useThreshold);

        if (initialOffsetX.value !== offsetX.value) {
            emits('touch-trigger', {
                direction: direction.value,
                shouldUpdateIndicator,
            });
        }

        duration = Date.now() - startTime;
        endDelta = Math.abs(touchEndX - touchStartX.value);
    }
};

const canUnfold = computed(() => {
    return (
        props.slideType === 'unfold' &&
        entryDataList.value.length > props.cutNumIdx
    );
});

const showDataList = computed(() => {
    return props.slideType === 'normal'
        ? entryDataList.value
        : entryDataList.value.slice(0, props.cutNumIdx);
});

const cutShowDataList = computed(() => {
    return entryDataList.value.slice(props.cutNumIdx, props.cutNumIdx * 2);
});

const cutNextShowDataList = computed(() => {
    return entryDataList.value.slice(props.cutNumIdx * 2);
});

const lines = computed(() => {
    if (canUnfold.value) {
        return Math.ceil(
            (entryDataList.value.length - props.cutNumIdx) / props.cutNumIdx,
        );
    }
    return 1;
});

//  高度需要有动画效果
const showNextHeight = computed(() => {
    return lines.value > 1 && showNext.value
        ? `${(lines.value * 56 + (lines.value - 1) * 8) / 50}rem`
        : 'unset';
});

const styles = computed(() => {
    const { flexGap, flexDirection, slideType, cutNumIdx } = props;
    const transitionAttrs = isMoving.value
        ? 'height 0.3s'
        : 'height 0.3s, transform 0.3s';
    return {
        transition: transitionAttrs,
        // gap: flexGap,
        flexDirection,
        display: slideType === 'normal' ? 'flex' : 'inline-flex',
        transform:
            entryDataList.value.length > cutNumIdx
                ? `translateX(${offsetX.value}px)`
                : 'unset',
    } as any;
});
const entryDataList = computed(() => {
    const { entryData } = props;
    const filterEntry = entryData.filter((item) => {
        if (item.type === EntranceType.sonus) {
            const showSonues =
                rechargeStore?.rechargeJobInfo?.hasRecharge &&
                rechargeStore?.rechargeJobInfo?.jumpUrl &&
                !isOutLiveRoom &&
                !bolIsAuthor;
            if (showSonues) {
                sendShow({
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: {
                        btn_type: item.logType,
                    },
                });
            }
            return showSonues;
        }
        if (item.type === EntranceType.records) {
            // 年度回忆录在间外不显示
            return !isOutLiveRoom && item.path;
        }
        if (item.type === EntranceType.records) {
            // 年度回忆录在间外不显示
            return !isOutLiveRoom && item.path;
        }
        return true;
    });
    return filterEntry.filter((entry) => {
        return (
            !entry?.dyncParams?.notOutLive ||
            (entry?.dyncParams?.notOutLive && !isOutLiveRoom)
        );
    });
});

const jumpOtherPage = (item: EntranceItem) => {
    if (
        isYodaPCContainer &&
        duration > judgeOperate.duration &&
        endDelta > judgeOperate.distanceThreshold
    ) {
        return;
    }

    const routerName = route.name;
    const bName = 'competition-b';
    const bChampionName = 'champion-b';
    const mainName = 'home';
    // 是否是A赛
    const isMainRouter = routerName === mainName;
    // 是否是B赛
    const isBRouter = routerName === bName;
    let entryStageType;
    if (item?.dyncParams?.yodaPCNot && isYodaPCContainer) {
        showToast('此功能仅支持在APP中使用');
        return;
    }
    if (item?.type === EntranceType.jimu) {
        goOtherPage('jimu', item.path);
    } else if (item?.type === EntranceType.router) {
        if (routerName === item.path) {
            if (item.path === 'home') {
                //  仍然跳转的是首页，但查询参数可以变化
                emits('route-query-change', {
                    ...item.dyncParams,
                });
                return;
            }
            return;
        }
        // 主会场跳转到B赛或B类挂榜需要添加参数
        // if (routerName === mainName) {
        //     if (bName === item.path || bChampionName === item.path) {
        //         entryStageType = props.stageType;
        //     }
        // }
        // // B赛跳到B挂榜
        // if (routerName === bName && bChampionName === item.path) {
        //     entryStageType = getQuery('entryStageType');
        // }
        router.push({
            name: item.path,
            query: {
                rankActivityAlias: item?.extInfo?.rankActivityAlias,
                entryStageType: props.stageType, // 项目内跳转增加来源标记，用于挂榜标记返回住赛程, 值主赛程挂榜使用
                // sourceEntrance:
                //     routerName === bChampionName && bName === item.path
                //         ? ''
                //         : getQuery('sourceEntrance'),
                ...item.dyncParams,
            },
        });
    } else if (item.type === EntranceType.share) {
        handleShare();
    } else if (item.type === EntranceType.extend) {
        emits('extend', item);
    } else if (item.type === EntranceType.sonus) {
        if (isYodaPCContainer) {
            showPCToast('请到手机端进行查看');

            return;
        }
        goOtherPage(
            'krn',
            rechargeStore.rechargeJobInfo.jumpUrl,
            1,
            location.href,
        );
    }
    // else if (item.type === EntranceType.records) {
    //     // 跳转年度回忆录链接
    //     linkOtherBizPage({
    //         scene: SceneType.Memory,
    //         link: item.path,
    //         queryData: {
    //             msrc: 'HOMEPAGE',
    //             entry_src: 'HOMEPAGE',
    //         },
    //     });
    // }
    else if (item.type === EntranceType.giftBattle) {
        // 跳转礼物大作战
        if (
            item.path.includes('/champion') &&
            !item.path.includes('/champion-b')
        ) {
            if (isMainRouter) {
                entryStageType = props.stageType;
            } else if (isBRouter) {
                entryStageType = getQuery('entryStageType');
            }
        }
        if (!entryStageType && item.dyncParams?.entryStageType) {
            entryStageType = item.dyncParams?.entryStageType;
        }
        jumpTo(item.path, {
            ...item.dyncParams,
            entryStageType,
        });
    } else if (item.type === EntranceType.krn) {
        goOtherPage('krn', item.path, 1, location.href);
    } else {
        // A赛、B赛跳转到A挂榜需要携带
        if (
            item.path.includes('/champion') &&
            !item.path.includes('/champion-b')
        ) {
            if (isMainRouter) {
                entryStageType = props.stageType;
            } else if (isBRouter) {
                entryStageType = getQuery('entryStageType');
            }
        }
        if (!entryStageType && item.dyncParams?.entryStageType) {
            entryStageType = item.dyncParams?.entryStageType;
        }
        jumpTo(item.path, {
            activityBiz,
            ...item.dyncParams,
            // entryStageType,
        });
    }
};

defineExpose({
    direction,
    canUnfold,
});
</script>

<style lang="less" scoped>
.secondary-entry {
    display: flex;
    flex-direction: column;
    height: 56px;
}
.secondary-entry-row {
    .elem-entry-icon {
        margin-right: 8px;
    }
    .cut-wrap {
        .elem-entry-icon:not(:nth-child(6n)) {
            margin-right: 8px;
        }
    }
    &.six-entry {
        text-align: center;
        width: 100%;
        justify-content: center;
    }
    .no-6 {
        margin-right: 0;
    }
}
.secondary-entry-col {
    .elem-entry-icon {
        margin-bottom: 8px;
    }
}
.secondary-margin-style {
    .elem-entry-icon {
        position: relative;
        flex-shrink: 0;
    }
}

.elem-entry-icon {
    position: relative;
    width: 56px;
    height: 56px;
    img {
        width: 100%;
    }
    .count-display-text {
        position: absolute;
        top: -2px;
        right: -2px;
        pointer-events: none;
    }
}

.cut-wrap {
    width: calc(414px - 20px);
    flex-wrap: wrap;
    display: inline-flex;
    // gap: 8px;
    // .elem-entry-icon:nth-child(7n) {
    //     margin-top: 14px;
    // }
}
.showNext {
    height: v-bind(showNextHeight);
}
</style>
