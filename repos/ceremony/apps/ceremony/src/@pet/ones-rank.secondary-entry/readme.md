# 二级入口组件文档

## 功能概述

该组件用于展示多个二级入口图标，并根据不同的条件和用户操作跳转到相应的页面或执行特定的动作。每个入口可以通过接口下发配置图标、跳转链接等属性。

## 属性

| 属性名       | 类型     | 默认值 | 描述                                                         |
|--------------|----------|--------|--------------------------------------------------------------|
| `needMargin` | `Boolean`| `false`| 是否需要额外的边距样式。                                     |
| `entryData`  | `Array`  | `[]`   | 入口数据列表，每个元素包含图标、跳转路径等信息。             |
| `flexGap`    | `String` | `0px`  | 入口之间的间距。                                             |
| `flexDirection` | `String` | `column` | 容器的排列方向，可选值为 `row` 或 `column`。              |
| `stageType`  | `Number` | -      | 当前的赛程类型，在跳转时会作为参数告知下一个页面来源赛事。   |

## 方法

| 方法名         | 参数         | 描述                                                         |
|----------------|--------------|--------------------------------------------------------------|
| `extend`       | `item`       | type 为 extend 时触发，传递当前入口项的数据。                         |

## 注意事项

1. **路径拼接**：确保 `path` 中的占位符（如 `${layoutType}`）在实际使用中被正确替换。
2. **环境检测**：部分功能在特定环境下（如 Yoda PC 容器）可能不可用，需注意提示用户。
3. **日志记录**：每个入口点击事件都会记录日志，确保日志类型和参数正确配置。

## 依赖项

- `vue`
- `lodash-es`
- `@pet/ones-use.share`
- `@pet/ones-use.linkOtherBizPage`
- `@alive-ui/base`
- `@alive-ui/actions`
- `@alive-ui/recharge-icon/models/recharge`
- `@alive-ui/recharge-icon/utils/toast`