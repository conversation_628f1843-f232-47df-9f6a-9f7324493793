import { ref } from 'vue';
import { defineStore } from 'pinia';
import { getSecondaryEntry } from '../services/index';
import type { EntranceDataPostResponse } from '../schemas/index';
export default defineStore('secondaryEntry', () => {
    const secondaryEntryInfo = ref<EntranceDataPostResponse>();
    const init = async (rankAliasName: string, subBiz?: string) => {
        try {
            const res = await getSecondaryEntry(rankAliasName, subBiz);
            secondaryEntryInfo.value = res;
        } catch (error) {
            console.log(error);
        }
    };
    return {
        secondaryEntryInfo,
        init,
    };
});
