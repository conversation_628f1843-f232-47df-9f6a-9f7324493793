/**
 * 请求: 首页展示入口Icon接口
 */
export interface EntranceDataPostRequest {
    activityBiz: string;
    liveStreamId: string;
    rankAliasName: string;
}
interface ExtInfo {
    competing: boolean;
    currTeamInfo: null | Record<string, any>; // 假设 currTeamInfo 可能是一个对象
    authorTeamInfo: null | Record<string, any>; // 假设 authorTeamInfo 可能是一个对象
    publicityPeriodDesc: string;
    userTeam: boolean;
    isPublicityPeriod: boolean;
}

interface DyncParams {
    [key: string]: any; // 假设 dyncParams 可以包含任意键值对
}

export interface AuthorCardAreaItem {
    name: string;
    position: string;
    order: number;
    iconUrl: string;
    type: string;
    path: string;
    displayText: string;
    logType: string;
    extInfo: ExtInfo;
    dyncParams: DyncParams;
}
/**
 * 响应: 首页展示入口Icon接口
 */
export interface EntranceDataPostResponse {
    // 顶部左侧入口
    topLeftArea: EntranceItem[];
    // 顶部右侧入口
    topRightArea: EntranceItem[];
    // 横向入口
    verticalArea: EntranceItem[];
    // 底部左下角入口
    bottomLeftArea: EntranceItem[];
    bottomRightArea: EntranceItem[];

    authorCardArea: AuthorCardAreaItem[];

    // 其他：可扩展，视实际情况而定
}

export interface EntranceItem {
    // 链接类型
    type: string;
    // 二级入口埋点名称,根据埋点文档更新
    logType: string;
    // 二级入口icon 图标
    iconUrl: string;
    // 二级入口跳转地址：layoutType ，liveStreamId ，authorId，前端替换，二级巅峰对决所需的rankActivityAlias已经沟通拼接到链接中，无需从homeStore中取
    // 如 https://alive.kuaishou.com/live/act/2024-04-jiugongge/group-rank?activityBiz=liveMultiUserScene&layoutType=${layoutType}&bizId=2024-04-520-gongge&liveStreamId=${liveStreamId}&authorId=${authorId}"
    path: string;
    // 二级入口文案
    displayText: string;
    // 额外信息
    extInfo: { [key: string]: any };
    dyncParams: { [key: string]: any };
    // 用户信息
    userInfo: ItemInfo;
    // 主播信息
    authorInfo: ItemInfo;
}

export interface ItemInfo {
    headUrl: string;
    itemId: number;
    itemName: string;
    liveStreamId: string;
    mysteryMan: boolean;
}

export enum EntranceType {
    // 顶部左侧入口
    share = 'share',
    // 尊享福利
    sonus = 'enjoySonus',
    jimu = 'jimu',
    router = 'router',
    extend = 'extend',
    // 回忆录
    records = 'records',
    // 礼物大作战
    giftBattle = 'giftBattle',
    link = 'link',
    krn = 'krn',
}
