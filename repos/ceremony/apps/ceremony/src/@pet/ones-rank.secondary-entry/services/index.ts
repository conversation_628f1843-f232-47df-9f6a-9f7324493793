import { activityBiz, liveStreamId, request, query } from '@alive-ui/actions';
import type { EntranceDataPostResponse } from '../schemas/index';

// 首页展示入口Icon接口：
// old https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=41001&version=-1
// new https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=494677&version=-1
export const getSecondaryEntry = async (
    rankAliasName: string,
    subBiz?: string,
) => {
    const res = await request.post<EntranceDataPostResponse>(
        '/webapi/live/revenue/operation/activity/complex/entranceList',
        {
            activityBiz,
            liveStreamId,
            rankAliasName, // 依赖于 home 接口下发
            subBiz: subBiz || query.subBiz, // 其他页面需要使用
        },
    );

    return res?.data;
};
