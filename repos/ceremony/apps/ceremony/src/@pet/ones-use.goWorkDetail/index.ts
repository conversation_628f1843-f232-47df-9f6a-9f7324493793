import { Toast } from '@lux/sharp-ui-next';
import {
    isYodaPCContainer,
    bolIsAuthor,
    toWorkVideoPage,
} from '@alive-ui/actions';

const goWorkDetail = (workId: string, userId: string) => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    toWorkVideoPage(workId, userId);
};

export default goWorkDetail;
