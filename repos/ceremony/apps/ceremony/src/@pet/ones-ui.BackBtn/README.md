# BackBtn 组件文档

## 功能概述
`BackBtn` 是一个用于显示返回按钮的组件，根据页面类型（一级页或二级页）和布局类型（间内或间外）决定是否显示以及点击后的行为。对于一级页，如果在间外，则显示返回按钮，点击时退出Web视图；对于二级页，固定显示返回按钮，点击时返回上一页。

## 属性和方法

### Props

- **无**

### Emit Events

- **无**

## 使用示例

```vue
<template>
  <div>
    <BackBtn />
  </div>
</template>

<script lang=\"ts\" setup>
import BackBtn from '@pet/ones-ui.BackBtn';
</script>
```

## 注意事项

- `BackBtn` 组件会根据 URL 参数 `showFirstBackBtn` 和 `layoutType` 自动决定是否显示返回按钮及其点击行为。
- 组件会监听路由变化，确保在路由变化时重新初始化返回按钮的显示逻辑。

## 依赖项

- `vue-router`
- `@alive-ui/icon`
- `@alive-ui/actions`