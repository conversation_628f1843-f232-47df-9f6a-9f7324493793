<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue';
import { Report } from '@alive-ui/system';
import { ElseStatus, ACard } from '@alive-ui/base';

const props = withDefaults(
    defineProps<{
        errorText: string;
        moduleKey?: string;
        moduleType?: string;
    }>(),
    {
        errorText: '模块异常，请刷新重试',
    },
);
const hasError = ref(false);

onErrorCaptured((err, instance, info) => {
    console.error('============卡片渲染异常============');
    console.error(err, instance, info, props.moduleKey, props.moduleType);
    hasError.value = true;
    Report.biz.error('卡片渲染异常', {
        error: err,
        vueInstance: instance,
        info,
        moduleKey: props.moduleKey,
        moduleType: props.moduleType,
    });

    return false;
});
</script>

<template>
    <div class="error-boundary">
        <!-- <ACard v-if="hasError">
            <ElseStatus
                class="error-area"
                :page-status="{ error: true }"
                :no-data="false"
                :is-show-refresh="false"
            >
                <template #text>{{ errorText }}</template>
            </ElseStatus>
        </ACard> -->
        <div v-if="hasError" class="error-area"></div>
        <slot v-else />
    </div>
</template>

<!-- <style scoped lang="less">
.error-boundary {
    .error-area {
        height: 150px;
    }
}
</style> -->
