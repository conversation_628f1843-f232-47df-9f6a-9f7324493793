<template>
    <div class="schedule-author-list">
        <div class="first-line-title flex flex-between">
            <!-- 赛道 -->
            <div
                v-if="hint?.scheduleName || rankInfo?.h5ShowLaneName"
                class="track-name text-bold"
            >
                {{ hint.scheduleName }}
                {{ rankInfo.h5ShowLaneName }}
            </div>
            <JumpRank class="view-more-margin" :stage-type="stageType" />
        </div>
        <slot name="process-bar">
            <div class="mt-14px process-bar">
                <ProcessTab :context-name="contextName" @change="onChange" />
                <MyBoostEntrance
                    v-if="rankInfo && isNotBcompetition && !isOutLiveRoom"
                    :rule-url="ruleUrl"
                    :rank-id="rankInfo?.rankId"
                />
            </div>
        </slot>
        <template v-if="rankStatus.success">
            <DividerTips
                v-if="
                    rankInfo?.extraData?.addCardPrivilegeTip ||
                    rankInfo?.extraData?.rankRoundTip?.directPromotionTip ||
                    rankInfo?.extraData?.rankRoundTip?.homeTrafficAwardText
                "
                class="a-text-main text-12px mt-17px mb-17px mr-16px"
                :add-card-privilege-tip="
                    rankInfo?.extraData?.addCardPrivilegeTip
                "
                :direct-promotion-tip="
                    rankInfo?.extraData?.rankRoundTip?.directPromotionTip
                "
                :home-traffic-award-text="
                    rankInfo?.extraData?.rankRoundTip?.homeTrafficAwardText
                "
            />
            <Clearing
                v-if="rankInfo?.extraData?.isClearing"
                class="mt-35px mb-35px"
                use-props
                :is-absolute="false"
                :data="{
                    endTime: rankInfo?.extraData?.clearingEndTime || 0,
                    text: '已定榜，数据结算中',
                }"
                @end="refreshRank"
            />
            <template v-else>
                <div class="mr-16px">
                    <NationalCompetition
                        v-if="growFansGame"
                        :grow-fans-game="growFansGame"
                        :rank-alias-name="rankAliasName"
                        @refresh-task="emits('refresh-task', $event)"
                    />
                    <CountyDistrictChampionCard
                        v-if="
                            countyChampionAuthor &&
                            countyChampionAuthor.championList.length > 0
                        "
                        :champion-list="countyChampionAuthor.championList"
                    />
                </div>
                <div
                    v-if="displayedList?.length"
                    class="author-list-vertical mt-17px mb-17px"
                >
                    <div v-pcDirectives:scroll class="flex">
                        <AnchorItem
                            v-for="item in displayedList"
                            :key="item.h5RankShowIndex"
                            v-show-log="{
                                action: 'OP_ACTIVITY_BANGDAN_ITEM',
                                params: {
                                    author_id: item?.itemId,
                                    index: item.h5RankShowIndex,
                                },
                            }"
                            :rank-type="rankType"
                            :rank-id="rankInfo?.rankId"
                            :not-show-addtion="
                                rankInfo?.extraData
                                    ?.notSupportAuthorAdditionInfo
                            "
                            :item="{
                                ...item,
                                liveStreamIdList: liveStreamIdList,
                                scoreLabel: scoreLabel,
                            }"
                        />
                    </div>
                </div>
                <ElseStatus
                    v-else
                    class="else-status mt-35px"
                    :page-status="rankStatus"
                    :is-show-refresh="false"
                />
            </template>
        </template>
        <ElseStatus
            v-else
            class="else-status mt-35px"
            :page-status="rankStatus"
            :is-show-refresh="false"
        />
    </div>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, computed, getCurrentInstance, ref } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import CountyDistrictChampionCard from '@pet/ones-ui.county-district-champion-card/index.vue';
import DividerTips from '@pet/ones-rank.new-divider-tips/index.vue';
import NationalCompetition from '@pet/ones-rank.national-competition/components/card.vue';
import JumpRank from '@pet/ones-rank.jump-rank/index.vue';
import Clearing from '@pet/ones-rank.clear/index.vue';
import { ElseStatus } from '@alive-ui/base';
import { isOutLiveRoom } from '@alive-ui/actions';
const ProcessTab = defineAsyncComponent(() => {
    return import('@pet/ones-rank.process-tab/index.vue');
});
const MyBoostEntrance = defineAsyncComponent(() => {
    return import('@pet/ones-ui.my-boost-entrance/index.vue');
});
import AnchorItem from './anchor-item.vue';
import type {
    ItemRankInfo,
    QueryRankPostResponse,
    GrowFansGame,
    CountyChampionAuthor,
} from '@pet/ones-rank.schema/query-rank';
import type { Status } from '@pet/ones-rank.schema/global';
const { kconfData } = storeToRefs(useKconfStore());
interface ComponentSchema {
    notShowAddtion: boolean;
    stageType: number;
    contextName: symbol;
    ruleUrl: string;
    rankInfo: QueryRankPostResponse;
    rankType: string;
    liveStreamIdList: string[];
    growFansGame: GrowFansGame;
    countyChampionAuthor: CountyChampionAuthor;
    extraCardKey?: string;
    rankStatus: Status;
    rankAliasName: string;
    scoreLabel: string;
    hint: {
        scheduleName: string;
        displayName: string;
    };
}
const props = withDefaults(defineProps<ComponentSchema>(), {
    rankType: 'team',
    ruleUrl: '',
    useProps: false,
    // 组件的上下文名称,在页面容器层
    contextName: Symbol.for('ctx'),
    extraCardKey: '',
    rankAliasName: '',
    scoreLabel: '热度值',
});
const emits = defineEmits(['refresh', 'change', 'refresh-task']);
const { proxy } = getCurrentInstance() as any;

// 计算展示列表
interface MainRankItem extends ItemRankInfo {
    content?: string;
    ruleLink?: string;
    logType?: string;
    linkType?: string;
}
const displayedList = computed<MainRankItem[]>(() => {
    const rankTips = props.rankInfo?.extraData?.rankTips || [];
    const isRepechageActivity = props.rankInfo?.extraData?.isRepechageActivity;
    const result: MainRankItem[] = props.rankInfo.rankList || [];
    console.log('rankStatus', props.rankStatus);
    // 遍历 rankTips
    if (!result.length) {
        return result;
    }
    rankTips?.forEach((tip) => {
        for (let i = tip.startClose - 1; i < tip.endClose; i++) {
            if (i >= 0 && i < result.length) {
                result[i].content = tip.content || '';
                result[i].ruleLink =
                    i === 0 && !isRepechageActivity
                        ? 'through-finals'
                        : kconfData.value?.common?.reviveDirectConnection;
                result[i].logType =
                    i === 0 && !isRepechageActivity ? '直通决赛' : '复活玩法';
                result[i].linkType =
                    i === 0 && !isRepechageActivity ? 'router' : 'jimu';
            }
        }
    });

    return result;
});
const refreshRank = () => {
    props.extraCardKey &&
        emits('refresh-task', {
            showLoading: true,
        });
    emits('refresh');
};
const onChange = (item: any, index: number) => {
    props.extraCardKey &&
        emits('refresh-task', {
            payload: {
                item,
            },
            showLoading: true,
            eventTriggerName: 'processChange',
        });
    emits('change', item, index);
};
const isNotBcompetition = computed(() => {
    const bol =
        kconfData.value?.common?.stageTypeList?.find((item) => {
            return item.stageType === props.stageType;
        })?.detailType !== 'B';
    return bol;
});
</script>
<style lang="less" scoped>
.schedule-author-list {
    position: relative;
    min-height: 110px;
    margin-right: -16px;
}
:deep(.else-status) {
    height: auto;
}
.view-more-margin {
    margin-right: 16px;
    margin-left: 0;
}
.author-list-vertical {
    overflow-x: scroll;
    overflow-y: hidden;
    width: 100%;
}
.liuliang-tips {
    border-radius: 8px;
}
.process-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 38px;
    z-index: 1;
}
.track-name {
    background: linear-gradient(90.34deg, #fff8ed 0.17%, #f4bc73 130.85%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 18px;
    font-weight: 400;
    font-family: HYYakuHei;
    line-height: 20px;
    text-align: left;
    vertical-align: middle;
    transform: skew(-12deg);
    white-space: nowrap;
}
</style>
