### 组件demo

## 功能描述

该组件用于展示一个简单的示例，显示传入的数据文本。组件支持通过 props 传入数据或通过上下文名称从数据源中获取数据。

## Props

| 属性名        | 类型                     | 默认值          | 描述                                     |
|---------------|--------------------------|------------------|------------------------------------------|
| `data`        | `{ text: string; }`     | `{ text: '' }`   | 组件显示的数据，包括文本内容            |
| `useProps`    | `boolean`                | `false`          | 是否使用 props 传入的数据               |
| `contextName` | `Symbol`                 | `Symbol.for('ctx')` | 上下文名称，用于数据传递               |

## 使用方式

```vue
<template>
  <ComponentDemo
    :data="{ text: 'Hello, World!' }"
    :useProps="true"
    :contextName="Symbol.for('ctx')"
  />
</template>

<script setup>
import ComponentDemo from './path/to/ComponentDemo.vue';
</script>
markdown
复制
# ComponentDemo 组件

## 功能描述

该组件用于展示一个简单的示例，显示传入的数据文本。组件支持通过 props 传入数据或通过上下文名称从数据源中获取数据。

## Props

| 属性名        | 类型                     | 默认值          | 描述                                     |
|---------------|--------------------------|------------------|------------------------------------------|
| `data`        | `{ text: string; }`     | `{ text: '' }`   | 组件显示的数据，包括文本内容            |
| `useProps`    | `boolean`                | `false`          | 是否使用 props 传入的数据               |
| `contextName` | `Symbol`                 | `Symbol.for('ctx')` | 上下文名称，用于数据传递               |

## 使用方式

```vue
<template>
  <ComponentDemo
    :data="{ text: 'Hello, World!' }"
    :useProps="true"
    :contextName="Symbol.for('ctx')"
  />
</template>

<script setup>
import ComponentDemo from './path/to/ComponentDemo.vue';
</script>
```
## Mock 数据
以下是一个示例的 mock 数据，用于展示组件的使用：

```javascript
const mockData = {
  text: '这是一个组件示例'
};
```
## 计算属性
组件内部使用了计算属性 newData 来决定数据的来源：

如果 useProps 为 true，则使用传入的 data。
如果 useProps 为 false，则通过 contextName 从数据源中获取数据。
