<template>
    <div class="profile-instance-item">
        <div
            v-if="item.rankShowIndex < 4"
            class="bg-top3-icon"
            :class="'bg-icon-' + item.rankShowIndex"
        ></div>
        <!-- 头像组件 -->
        <div class="avatar-container">
            <APAvatar
                :disable-click="rankType == 'guild' || rankType == 'team'"
                size="2xs"
                :sync="true"
                :user-id="item.itemId"
                :head-url="item.headUrl ?? ''"
                :live-stream-id-list="item.liveStreamIdList"
                :live-stream-id="item.liveStreamId"
                :is-mystery-man="item.mysteryMan"
            >
                <template #bottomInner>
                    <slot name="bottomInner" />
                </template>
                <!-- <template #bottomRight>
            <LiveIcon v-if="item.liveStreamId" />
        </template> -->
            </APAvatar>
            <div
                v-if="item?.histParticipatedInRepechageTips"
                class="hist-participated-tips revive-label"
            />
        </div>
        <div
            v-if="item?.extraData?.honorTitle"
            class="hornor-title-label mt-6px"
        >
            {{ item?.extraData?.honorTitle }}
        </div>
        <div
            v-if="item.itemName"
            class="author-name text-12px a-text-main text-bold mt-5px"
        >
            {{
                rankType == 'team' ? item.itemName : nameSlice(item.itemName, 6)
            }}
        </div>
        <div class="score-area a-text-main opacity-60 text-10">
            {{ item?.h5ShowScore }} {{ item?.scoreLabel }}
        </div>
        <!-- 标签区域 -->
        <template v-if="rankType == 'base' && !notShowAddtion">
            <!-- 加成进度 -->
            <AdditionTips
                :value="item?.addition?.progress"
                :max="100"
                class="mt-4px"
            >
                <template #text>
                    <span v-if="item?.addition?.additionStatusDesc">{{
                        item?.addition?.additionStatusDesc
                    }}</span>
                    <span v-else class="opacity-50">暂无加成</span>
                </template>
            </AdditionTips>
        </template>
        <template v-if="rankType == 'team' || rankType == 'guild'">
            <div
                v-if="item?.sponsors?.length"
                class="flex-center-center new-sponsor-list mt-6px"
            >
                <APAvatar
                    v-for="elem in item?.sponsors?.slice(0, 2)"
                    :key="elem?.itemId"
                    :sync="true"
                    class="team-avatar"
                    size="2xs"
                    :head-url="elem.headUrl ?? ''"
                    :user-id="elem?.itemId"
                    :live-stream-id="elem?.liveStreamId"
                    :is-mystery-man="elem?.mysteryMan"
                >
                    <template #bottomInner>
                        <slot name="bottomInner" />
                    </template>
                </APAvatar>
            </div>
        </template>
        <div v-if="item?.extraData?.showCpOrgInfoLabel">
            <div class="info-line-border"></div>
            <div
                v-if="item?.extraData?.cpOrgName"
                class="group-label-section"
                :class="{
                    'org-label': ifSuitStyle(item),
                    'only-one-style': onlyOneData(item),
                }"
            >
                {{ nameSlice(item?.extraData?.cpOrgName, 9) }}
            </div>
            <div
                v-if="
                    item?.extraData?.areaInfo || item?.extraData?.operationsInfo
                "
                class="mt-2px"
                :class="{
                    'flex-center': item?.extraData?.cpOrgName,
                    'need-grid': !item?.extraData?.cpOrgName,
                    'mt-6px': !item?.extraData?.cpOrgName && !onlyOneData(item),
                    'only-one-style': onlyOneData(item),
                }"
            >
                <div
                    v-if="item?.extraData?.areaInfo"
                    class="group-label-section"
                    :class="{
                        'mb-2px': !item?.extraData?.cpOrgName,
                        'margin-auto': !item?.extraData?.operationsInfo,
                    }"
                >
                    {{ item?.extraData?.areaInfo }}
                </div>
                <div
                    v-if="item?.extraData?.operationsInfo"
                    class="group-label-section ml-2px"
                    :class="{
                        'mb-2px': !item?.extraData?.cpOrgName,
                        'margin-auto': !item?.extraData?.areaInfo,
                    }"
                >
                    {{ item?.extraData?.operationsInfo }}
                </div>
            </div>
        </div>
        <div
            v-if="
                item?.extraData?.showCpOrgInfoLabel &&
                !item?.extraData?.cpOrgName &&
                !item?.extraData?.areaInfo &&
                !item?.extraData?.operationsInfo
            "
            class="no-data-info mt-14px mb-8px"
        >
            暂无信息
        </div>
        <div class="direct-connection">
            <div
                v-if="item.rankShowIndex < 4"
                class="ranking-icon"
                :class="'rank-number-' + item.rankShowIndex"
            />
            <div
                v-else
                class="ranking-icon-num text-center a-text-main text-10px text-din text-bold pl-2px"
            >
                {{ item.h5RankShowIndex }}
            </div>

            <span
                v-click-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: {
                        btn_type: item?.logType,
                    },
                }"
                v-show-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: {
                        btn_type: item?.logType,
                    },
                }"
                class="connection-detail"
                @click="
                    jumpToRule(item?.linkType || 'jimu', item?.ruleLink || '')
                "
            >
                {{ item?.content }}
            </span>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, computed } from 'vue';
import AdditionTips from '@pet/ones-rank.addition-tips/index.vue';
import { Avatar as APAvatar } from '@alive-ui/pro';
import { nameSlice, goOtherPage } from '@alive-ui/actions';
import type { PropType } from 'vue';
import type { ItemRankInfo } from '@pet/ones-rank.schema/query-rank';
const props = defineProps({
    notShowAddtion: {
        type: Boolean,
        default: false,
    },
    rankId: {
        type: Number,
        default: 0,
    },
    rankType: {
        type: String,
        default: 'base',
    },
    item: {
        type: Object as PropType<
            ItemRankInfo & {
                disableClick?: boolean;
                liveStreamIdList?: string[]; // 上下滑直播间列表
                activityId?: string;
                scheduleId?: string;
                rankId?: number;
                content?: string;
                ruleLink?: string;
                logType?: string;
                linkType?: string;
                scoreLabel?: string;
            }
        >,
        default: () => {
            return {
                itemName: '昵称',
                itemId: '',
                headUrl: 'xxx',
                h5ShowScore: '134.5w',
                liveStreamId: 'xxx',
                liveStreamIdList: [],
                mysteryMan: false,
                rankShowIndex: '1',
                addition: {
                    progress: 50,
                    showAdditionRate: '1.2',
                },
            };
        },
    },
});
const { proxy } = getCurrentInstance() as any;
// 榜单单项顶部提示跳转链接
const jumpToRule = (type: string, link: string) => {
    if (type === 'jimu') {
        goOtherPage('jimu', link);
    } else {
        proxy.$router.push({
            name: link,
            query: {
                rankId: props.rankId,
            },
        });
    }
};
const ifSuitStyle = (item: any) => {
    const hasOperationsInfo = item?.extraData?.operationsInfo;
    const hasAreaInfo = item?.extraData?.areaInfo;
    const hasCpOrgName = item?.extraData?.cpOrgName;

    const simplifiedCondition =
        (hasCpOrgName && hasOperationsInfo && hasAreaInfo) ||
        (hasOperationsInfo && hasAreaInfo) ||
        (hasCpOrgName && hasOperationsInfo) ||
        (hasCpOrgName && hasAreaInfo);
    return simplifiedCondition;
};
const onlyOneData = (item: any) => {
    const hasOperationsInfo = item?.extraData?.operationsInfo;
    const hasAreaInfo = item?.extraData?.areaInfo;
    const hasCpOrgName = item?.extraData?.cpOrgName;
    const bol =
        [hasOperationsInfo, hasAreaInfo, hasCpOrgName].filter(Boolean)
            .length === 1;
    return bol;
};
</script>

<style lang="less" scoped>
.avatar-container {
    position: relative;
}
.hist-participated-tips {
    position: absolute;
    right: -12px;
    top: -2px;
}
:deep(.addition-tips) {
    width: 68px;
}
.rank-number-1 {
    background: url('./assets/rank-number-1.png') center / cover no-repeat;
}
.rank-number-2 {
    background: url('./assets/rank-number-2.png') center / cover no-repeat;
}
.rank-number-3 {
    background: url('./assets/rank-number-3.png') center / cover no-repeat;
}
.revive-label {
    width: 22px;
    height: 12px;
    background: url('./assets/revive-label_2x.png') center / cover no-repeat;
}
.info-line-border {
    width: 66px;
    opacity: 0.05;
    height: 0.5px;
    background: rgba(255, 255, 255, 1);
    text-align: center;
    margin: 0 auto;
    margin-top: 6px;
}
.score-area {
    white-space: nowrap;
    transform-origin: 50% 50%;
}
.no-data-info {
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 8px;
    line-height: 10px;
    letter-spacing: 0px;
    text-align: center;
    vertical-align: middle;
    color: rgba(255, 255, 255, 1);
    opacity: 0.4;
}
.profile-instance-item {
    background-image: url('./assets/main-rank-card_2x.png');
    background-position: center top;
    background-size: 100%;
    background-repeat: no-repeat;
    background-color: rgba(154, 189, 255, 0.08);
    box-sizing: border-box;
    width: 98px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 23px 3px 11px;
    position: relative;
    margin-right: 8px;
    flex-shrink: 0; // 防止压缩
    .bg-top3-icon {
        position: absolute;
        top: 1px;
        left: 0px;
        width: 98px;
        height: 82px;
        background-position: center top;
        background-size: 100%;
        background-repeat: no-repeat;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
    }
    .bg-icon-1 {
        background-image: url('./assets/bg-num-1_2x.png');
    }
    .bg-icon-2 {
        background-image: url('./assets/bg-num-2_2x.png');
    }
    .bg-icon-3 {
        background-image: url('./assets/bg-num-3_2x.png');
    }
}
.new-sponsor-list {
    width: 90px;
    gap: 6px;
}

.direct-connection {
    box-sizing: border-box;
    flex-basis: 16px;
    min-width: 29px;
    width: fit-content;
    height: 16px;
    position: absolute;
    top: 1px;
    left: 1px;
    border-top-left-radius: 9px;
    border-bottom-right-radius: 7px;
    background: rgba(191, 218, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 2px 6px 2px 4px;
    gap: 2px;
    flex-direction: row;
    white-space: nowrap;
}

.ranking-icon {
    box-sizing: border-box;
    overflow: hidden;
    flex-basis: 16px;
    width: 16px;
    height: 16px;
}
.ranking-icon-num {
    box-sizing: border-box;
    overflow: hidden;
    flex-basis: 17px;
    width: 17px;
    height: 17px;
    line-height: 17px;
    min-width: fit-content;
}
.connection-detail {
    box-sizing: border-box;
    //min-width: 45px;
    height: 13px;
    vertical-align: top;
    font-size: 9px;
    line-height: 12.6px;
    font-family: 'PingFang SC';
    color: #ffdfbf;
}
.group-label-section {
    width: fit-content;
    border-radius: 2px;
    padding: 1px 3px;
    background: rgba(224, 237, 255, 0.1);
    opacity: 0.9;
    color: #fff;
    font-size: 8px;
}
.org-label {
    margin: 0 auto;
    margin-top: 6px;
}
.need-grid {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.only-one-style {
    margin: 0 auto;
    margin-top: 14px;
}
.hornor-title-label {
    background: linear-gradient(264.25deg, #ffe4cb 0%, #fee1ba 93.85%);
    border-radius: 9px;
    color: #310b0f;
    opacity: 0.9;
    font-size: 8px;
    font-weight: 500;
    font-family: PingFang SC;
    letter-spacing: 0px;
    line-height: 10px;
    text-align: center;
    vertical-align: middle;
    padding: 1px 3px;
}
</style>
