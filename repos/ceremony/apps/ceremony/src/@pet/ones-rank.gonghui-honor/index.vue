<template>
    <PageFrame
        :data="{
            image: kconfData.mainPage?.gonghuiHonor.kv,
            rule: 'guildGlory',
            showBack: true,
            homeStatus: homeStatus,
        }"
        class="rank-page-competition-gonghui-honor"
        @refresh="homeStore.refresh()"
    >
        <template #left-top>
            <SceneAllMain
                v-click-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: {
                        btn_type: '赛程全景',
                    },
                }"
                :data="SceneAllMainData"
                @change="changeSceneAllMain"
            ></SceneAllMain>
        </template>
        <template #main-container>
            <div class="content-wrap">
                <div class="mb-14px">
                    <div
                        v-if="stageTableAliasViewList.length === 1"
                        class="single-tab"
                    >
                        {{ stageTableAliasViewList[0].stageName }}
                    </div>
                    <ATabs
                        v-else
                        type="solid"
                        :selected-index="selectedTabsIndex"
                        @change="tabChange"
                    >
                        <ATabList>
                            <ATab
                                v-for="item in stageTableAliasViewList"
                                :key="item.rankAlias"
                                :data-id="item.stageType"
                                >{{ item.stageName }}</ATab
                            >
                        </ATabList>
                    </ATabs>
                </div>
                <template v-if="rankStatus.success">
                    <AwardCollapse
                        class="mb-14px"
                        :data="awardData"
                        use-props
                    />
                    <div v-if="isClearing" class="rank-clear-wrap">
                        <RankClear @end="rankStore.init()" />
                    </div>
                    <template v-else>
                        <template v-if="!isNoStart">
                            <AuthorBase
                                :data="curAuthorData"
                                current-label="所在公会仅自己可见"
                            />
                            <!-- 榜单列表区, 可向ones-rank贡献新的列表组件（基于已有的组件修改） -->
                            <CompetitionList :data="curRankListData" />
                        </template>
                    </template>
                </template>

                <ElseStatus
                    v-else
                    :page-status="rankStatus"
                    :is-show-refresh="true"
                    :no-data="false"
                    @refresh="rankStore.init()"
                />
            </div>
            <img
                v-if="rankStatus.success && !isClearing && isNoStart"
                class="no-star-rule"
                :src="kconfData.mainPage?.gonghuiHonor.ruleImg"
            />
        </template>
    </PageFrame>
</template>

<script lang="ts" setup>
import { onBeforeRouteLeave } from 'vue-router';
import { defineAsyncComponent, ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfBatch from '@pet/ones-use.useKconfBatch';
import PageFrame from '@pet/ones-ui.page-frame/index.vue';
import { ATabs, ATabList, ATab, PageAdaptor, ElseStatus } from '@alive-ui/base';
import { bolIsAuthor } from '@alive-ui/actions';
import useRankStore from './models/query-rank';
import useHomeStore from './models/index-home';
import type { StageLineViewList } from '@pet/ones-rank.schema/index-home';
import { sendClick } from '@/common/logger';
const CompetitionList = defineAsyncComponent(() => {
    return import('./components/competition-list.vue');
});
const SceneAllMain = defineAsyncComponent(() => {
    return import('./components/scene-all-main/index.vue');
});

const AwardCollapse = defineAsyncComponent(() => {
    return import('@pet/ones-rank.award-collapse/index.vue');
});
const AuthorBase = defineAsyncComponent(() => {
    return import('./components/current-author/index.vue');
});
const RankClear = defineAsyncComponent(() => {
    return import('@pet/ones-rank.clear/index.vue');
});

const { kconfData } = storeToRefs(useKconfBatch());
const homeStore = useHomeStore();
const rankStore = useRankStore();
const {
    stageTableAliasViewList,
    status: homeStatus,
    SceneAllMainData,
    selectedTabsIndex,
} = storeToRefs(homeStore);
const {
    status: rankStatus,
    awardData,
    curRankListData,
    curAuthorData,
    curRankAlias,
    isClearing,
} = storeToRefs(rankStore);

homeStore.init().finally(() => {
    rankStore.init();
});

// 路由离开前重置状态
onBeforeRouteLeave(() => {
    curRankAlias.value = '';
});

const tabChange = (index: number) => {
    const curTab = stageTableAliasViewList.value[index];
    curRankAlias.value = curTab.rankAlias || '';
    rankStore.init(true);
    sendClick({
        action: 'OP_ACTIVITY_GAME_TAB',
        params: {
            tab_name: curTab.stageName,
        },
    });
    console.log('tabChange:', curRankAlias.value);
};
const changeSceneAllMain = (item: StageLineViewList) => {
    console.log('changeSceneAllMain:', item.rankAlias);
    curRankAlias.value = item.rankAlias || '';
    homeStore.init(true);
};

const isNoStart = computed(() => {
    return !curRankListData.value.list?.length;
});
</script>

<style lang="less" scoped>
.fixed-bottom-left {
    position: fixed;
    bottom: 100px;
    z-index: 3;
}
.rank-page-competition-gonghui-honor {
    --pagHeadHeight: 390px;
    --marginTop: -63px;
    :deep(.back-track) {
        margin-top: 14px;
    }

    :deep(.gis-scene-all-icon) {
        margin-top: 15px;
    }

    :deep(.rank-current-item-attr) {
        padding-right: 10px;
    }
    .kv-wrap {
        width: 100%;
        height: 390px;
    }

    .content-wrap {
        overflow: auto;
        position: relative;
        margin: -110px auto 0;
        padding-top: 16px;
        width: 382px;
        border-radius: 16px;
        background: url('./assets/honor_bg.png') top / 100% no-repeat;
        background-color: rgba(128, 128, 128, 0.15);
    }

    .single-tab {
        font-family: HYYakuHei;
        font-size: 20px;
        line-height: 30px;
        text-align: center;
        background: linear-gradient(141.56deg, #ffffff 27.32%, #ffc4a3 82.93%);
        background-clip: text;
        color: transparent;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .rank-clear-wrap {
        position: relative;
        height: 250px;
    }

    .no-star-rule {
        margin: 14px 32px 0;
        width: 350px;
    }

    :deep(.yoda-image-box) {
        height: 350px;
        width: 414px;
    }
}
</style>
