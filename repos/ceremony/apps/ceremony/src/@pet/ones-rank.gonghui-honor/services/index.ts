import { request, activityBiz, authorId } from '@alive-ui/actions';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';
import type {
    RankFrameworkPostResponse,
    StageLineViewList,
} from '@pet/ones-rank.schema/index-home';

// 公会荣耀榜首页：https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=781621&version=-1&viewType=repoView
export const rankFramework = async (rankAlias?: string) => {
    const PATH =
        '/webapi/live/revenue/operation/activity/summerCeremony25/cpHonorRank/home';
    const res = await request.post<
        RankFrameworkPostResponse & {
            stageTableAliasViewList?: StageLineViewList[];
        }
    >(PATH, {
        biz: activityBiz,
        authorId,
        rankProxyKey: rankAlias,
    });

    return res?.data;
};

// 公会荣耀榜单：https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=781622&version=-1&viewType=repoView
export const queryRank = async (rankAlias?: string) => {
    const PATH =
        '/webapi/live/revenue/operation/activity/summerCeremony25/cpHonorRank/rank';
    const res = await request.post<QueryRankPostResponse>(PATH, {
        biz: activityBiz,
        authorId,
        rankProxyKey: rankAlias,
    });

    return res?.data;
};
