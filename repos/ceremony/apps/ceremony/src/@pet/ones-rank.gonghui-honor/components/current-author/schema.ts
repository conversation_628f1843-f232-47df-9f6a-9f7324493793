import type { ItemRankInfo } from '@pet/ones-rank.schema/query-rank';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
import type {
    ItemSchema,
    ItemCommonParams,
} from '@pet/ones-rank.base-item/schema';
interface CurrentItemSchema extends ItemCommonParams, PropsCommonParams {
    data?: ItemSchema;
    avatarSizeLeft?: '3xs' | '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
    avatarSizeRight?: '3xs' | '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
    avatarSize?: '3xs' | '2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

    buttonSize: '' | 'xs' | 'sm' | 'md' | 'lg';
    // 自定义右侧区域
    defineRight?: boolean;
    // 双头像情况是否一大一小
    diffSize?: boolean;
    // 送礼文案: 如“助力”
    giftBtnText?: string;
    // 是否吸底在底部导航位
    isFixed?: boolean;
    // 是否是公会赛
    guildRank: boolean;
    itemType: string;
    // 是否是当前主播
    isCurrent?: boolean;
    // 当前主播标签
    currentLabel?: string;
    needTopIndex?: boolean; // 头像顶部是否需要展示排名
    needTopLive?: boolean; // 头像顶部是否需要展示直播icon
}
interface CurrentSchema extends ItemRankInfo {
    // 上下滑直播间列表
    liveStreamIdList?: string[];
    // 主播分数标签描述
    scoreLabel?: string;
    currentLabel?: string;
    // 送礼文案: 如“助力”
    giftBtnText?: string;
    // 头像大小设置
    avatarSize?: 'lg' | '2xs' | 'xs' | 'sm';
    // 埋点
    logParams?: any;
}

export type { ItemSchema, CurrentItemSchema, CurrentSchema, PropsCommonParams };
