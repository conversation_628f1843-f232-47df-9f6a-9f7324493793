<!-- 常规当前主播区域 -->
<template>
    <APBaseRankList
        v-if="data"
        :log-params="data?.otherLogParams"
        class="current-author-wrap"
    >
        <RankItemBase
            :is-current="isCurrent"
            :item="{
                ...data,
                disableClick: disableClick,
            }"
            :diff-size="diffSize"
            :avatar-size="avatarSize"
            :avatar-size-left="avatarSizeLeft"
            :avatar-size-right="avatarSizeRight"
            :need-top-index="needTopIndex"
            :need-top-live="needTopLive"
            :current-label="currentLabel"
            :class="isFixed ? 'rank-fixed-width' : ''"
        >
            <template #extra-label>
                <slot name="extra-label"></slot>
            </template>
            <template #center-info>
                <slot name="center-info" />
            </template>
            <template #info>
                <div class="tags-wrap">
                    <div
                        v-for="tag in data?.extraData?.itemTags"
                        :key="tag"
                        class="tags"
                    >
                        {{ tag }}
                    </div>
                </div>
            </template>
            <template #right>
                <div class="text-12">
                    荣耀值：<span class="score-value a-text-highlight">{{
                        data?.displayScore
                    }}</span>
                </div>
            </template>
        </RankItemBase>
    </APBaseRankList>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import RankItemBase from '@pet/ones-rank.base-item/index.vue';
import { BaseRankList as APBaseRankList } from '@alive-ui/pro';
import { liveStreamId, bolIsAuthor } from '@alive-ui/actions';
import type { CurrentItemSchema } from './schema';
const props = withDefaults(defineProps<CurrentItemSchema>(), {
    avatarSize: 'sm',
    avatarSizeLeft: 'sm',
    avatarSizeRight: 'sm',
    buttonSize: 'xs',
    giftBtnText: '助力主播',
    itemType: 'base',
    guildRank: false,
    isCurrent: true,
    currentLabel: '当前主播',
    defineRight: false,
    diffSize: false,
    isFixed: false,
    needSelfStyle: false,
    useProps: false,
    needTopIndex: false,
    needTopLive: true,
    disableClick: true,
});
</script>
<style lang="less" scoped>
.current-author-wrap {
    .tags-wrap {
        display: flex;
    }

    .tags {
        border-radius: 2px;
        padding: 1px 3px;
        line-height: 10px;
        margin-right: 3px;
        font-size: 8px;
        color: rgba(255, 255, 255, 0.9);
        background: rgba(224, 237, 255, 0.1);
    }

    :deep(.rank-tag-attr) {
        padding: 0;
        color: rgba(49, 11, 15);
        background: var(--main-color, rgba(255, 223, 191));
    }

    .score-value {
        font-family: Alte DIN Mittelschrift;
    }
}
</style>
