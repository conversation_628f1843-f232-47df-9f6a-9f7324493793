<script lang="ts" setup>
import RankList from '../components/rank-item.vue';
import type { RankConfigInfo } from '@pet/ones-rank.schema/global';
interface RankData {
    // 榜单相关数据: 列表，关注id，分数标签名称
    data: RankConfigInfo;
}
withDefaults(defineProps<RankData>(), {
    data: () => {
        return {
            list: [],
            // 榜单分值标签名称，如盛典值
            scoreLabel: '荣耀值',
            // 榜单关注id
            focusActivityId: '',
        };
    },
});
</script>

<template>
    <div class="component">
        <RankList :data="data" :need-width="false" need-page> </RankList>
    </div>
</template>

<style lang="less" scoped></style>
