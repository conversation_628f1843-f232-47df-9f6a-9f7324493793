import { computed, ref } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { Toast } from '@lux/sharp-ui-next';
import { getPageStatus, Report } from '@alive-ui/actions';
import { rankFramework } from '../services';
import useRank from './query-rank';
import type {
    ScheduleViewList,
    StageLineViewList,
    StageTableViewList,
} from '@pet/ones-rank.schema/index-home';

export default defineStore('gonghui-honor-home', () => {
    const kconf = useKconfStore();
    const rank = useRank();
    const { curRankAlias } = storeToRefs(useRank());
    const stageLineViewList = ref<StageLineViewList[]>([]);
    const stageTableAliasViewList = ref<StageLineViewList[]>([]);
    const status = ref(getPageStatus('init')); // 页面状态数据定义
    const clickLoadingFlag = ref(false);
    const selectedTabsIndex = ref(0);

    // 初始化页面，获取接口数据
    const init = async (isTabChange?: boolean) => {
        if (clickLoadingFlag.value) {
            return;
        }
        clickLoadingFlag.value = true;

        if (isTabChange) {
            Toast.loading('正在加载', 0, true);
        } else {
            status.value = getPageStatus('loading');
        }
        try {
            const res = await rankFramework(curRankAlias.value);
            if (!res?.stageTableAliasViewList?.length) {
                status.value = getPageStatus('error');
                return;
            }
            status.value = getPageStatus(
                res?.anchorDisplayScheduleAndLane?.rankAliasName
                    ? 'success'
                    : 'nodata',
            );
            stageLineViewList.value = res.stageLineViewList || [];
            stageTableAliasViewList.value = res.stageTableAliasViewList;
            curRankAlias.value =
                res.anchorDisplayScheduleAndLane?.rankAliasName;
            selectedTabsIndex.value = stageTableAliasViewList.value.findIndex(
                (item) => item.rankAlias === curRankAlias.value,
            );
        } catch (error) {
            status.value = getPageStatus('error');
            Report.biz.error('公会荣誉榜初始化失败', {
                error,
            });
        } finally {
            Toast.hide();
            clickLoadingFlag.value = false;
            rank.init();
        }
    };

    // 刷新整个页面
    const refresh = () => {
        init();
    };

    const SceneAllMainData = computed(() => {
        return {
            // currentAnchorStageType: curRankAlias.value,
            list: stageLineViewList.value,
            lookingBack: false,
            rule: '',
            // curStageType: '',
        };
    });

    return {
        status,
        init,
        refresh,
        stageLineViewList,
        stageTableAliasViewList,
        SceneAllMainData,
        selectedTabsIndex,
    };
});
