import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { Toast } from '@lux/sharp-ui-next';
import { getPageStatus, Report } from '@alive-ui/actions';
import { queryRank } from '../services';
import type {
    BottomInfo,
    QueryRankPostResponse,
} from '@/@pet/ones-rank.schema/query-rank';
import { focusActivityId } from '@/const';

export default defineStore('gonghui-honor-rank', () => {
    const curRankAlias = ref('');
    const curRankData = ref<QueryRankPostResponse>();
    const status = ref(getPageStatus('init')); // 页面状态数据定义
    const clickLoadingFlag = ref(false);

    // 初始化页面，获取接口数据
    const init = async (isTabChange?: boolean) => {
        if (clickLoadingFlag.value) {
            return;
        }
        clickLoadingFlag.value = true;

        if (isTabChange) {
            Toast.loading('正在加载', 0, true);
        } else {
            status.value = getPageStatus('loading');
        }
        try {
            const res = await queryRank(curRankAlias.value);
            if (!res) {
                status.value = getPageStatus('error');
                return;
            }
            status.value = getPageStatus('success');
            curRankData.value = res;
        } catch (error) {
            status.value = getPageStatus('error');
            Report.biz.error('公会荣誉榜榜单加载失败', {
                error,
            });
        } finally {
            Toast.hide();
            clickLoadingFlag.value = false;
        }
    };

    const awardData = computed(() => {
        return {
            randomKey: +new Date(),
            // 奖励提示文案
            marqueeList:
                curRankData.value?.extraData?.privilegeDesc
                    ?.split('$')
                    ?.filter((i) => i) || [],
            //  奖励图片
            awardURL: curRankData.value?.extraData?.privilegeIconUrl || '',
            // 荣耀奖励列表
            awardList:
                curRankData.value?.extraData?.privilegeOverview
                    ?.championAwards || [],
            showAward: true,
            operateText: '奖励',
            // 冠军亚军、十强奖励tab切换
            topAwards:
                curRankData.value?.extraData?.privilegeOverview?.topAwards ||
                [],
        };
    });
    const curRankListData = computed(() => {
        return {
            // ...rankData.value.configData,
            list: curRankData.value?.rankList || [],
            // liveStreamIdList: rankData.value.configData.liveStreamIdList,
            // currentRankId: rankData.value.configData?.currentRankId || 0,
            // 榜单分值标签名称，如盛典值
            // scoreLabel: '热度值',
            // 埋点额外参数
            logParams: {
                scheduleId: 0,
                rankId: 0,
            },
            // 榜单关注id
            focusActivityId, // 全局的constant文件
            isNeedDivider: false, // todo 是否需要分割线需要自行判断
            isNeedTraffic: false, // todo 是否需要分割线需要自行判断
        };
    });
    const isClearing = computed(() => {
        return curRankData.value?.extraData.isClearing;
    });

    const curAuthorData = computed<any>(() => {
        if (!curRankData.value?.bottomInfo) {
            return null;
        }
        return {
            ...curRankData.value?.bottomInfo?.itemRankInfo,
            liveStreamIdList:
                curRankData.value?.rankList?.map((item: any) => {
                    return item?.liveStreamId;
                }) || [],
            // h5ShowScore: curRankData?.bottomInfo?.h5ShowHintScore,
            // rankId: curRankData?.rankId,
            // scoreLabel: hintScoreText?.[0],
            otherLogParams: {
                // rankId: curRankData?.rankId,
            },
        };
    });

    return {
        status,
        init,
        awardData,
        curRankListData,
        curAuthorData,
        curRankData,
        curRankAlias,
        isClearing,
    };
});
