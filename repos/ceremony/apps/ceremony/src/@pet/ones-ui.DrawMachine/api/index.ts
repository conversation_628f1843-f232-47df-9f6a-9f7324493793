/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    authorId,
    liveStreamId,
    bolIsAuthor,
    activityBiz,
    request,
} from '@alive-ui/actions';

const usePreFetchApiService = async <T>(
    apiService: () => Promise<T>,
): Promise<T> => {
    const { preFetchService } = window.usePreFetchApiService?.() || {};

    if (preFetchService) {
        try {
            const res = await preFetchService;
            console.log('preFetchService', res);

            return res[0] || {};
        } catch (error) {
            return await apiService();
        }
    } else {
        return await apiService();
    }
};

type ApiRes<T> = {
    result: number;
    msg: string;
    data: T;
};

const params = {
    liveStreamId,
    authorId,
    biz: activityBiz,
};

export type UserInfo = {
    userId: number;
    headUrl: string;
    userName: string;
    liveStreamId?: string;
};

export type PropsCardItem = {
    id: number;
    typeKey: string;
    name: string;
    desc: string;
    icon: string;
    expireTime: number;
    createTime: number;
    sourceName: string;
};

// 道具卡-列表查询
export interface AllPropsCardInfo {
    unusedList: PropsCardItem[]; // 未使用
    alreadyUsedList: PropsCardItem[]; // 已使用
    alreadyExpiredList: PropsCardItem[]; // 已过期
}

export const queryPropsCardList = async () => {
    const res = await request.post<AllPropsCardInfo>(
        '/rest/wd/live/plutus/propsCard/queryPropsCardList',
        {
            ...params,
        },
    );

    return res.data;
};

// 道具卡-最近可用道具卡
export interface RecentPropsCardInfo {
    propsCardView: PropsCardItem;
}

export const queryRecentPropsCard = async () => {
    const res = await request.post<RecentPropsCardInfo>(
        '/rest/wd/live/plutus/propsCard/queryRecentPropsCard',
        {
            ...params,
        },
    );

    return res.data;
};

// 奖励类型 2:背包礼物 3:荣誉出口 4:代币 5:实物 6:主站万能卡 7:快币 8:道具卡 9:现金 10:暴富卡 11:表情包 12:本地生活商品卡劵
export enum AwardType {
    backpackGift = 2,
    honor = 3,
    token = 4,
    physical = 5,
    kcoin = 7,
    propsCard = 8,
    cash = 9,
    rich = 10,
    emoji = 11,
    localCoupon = 12,
}

export type AwardItem = {
    id: number;
    name: string;
    count: number;
    icon: string;
    displayId: number;
    type: AwardType;
    unit: string;
    amount: string;
    amountTip: string;
    desc: string[];
    jumpUrl: string;
};

/** 抽奖活动枚举 */
export enum lotteryTabType {
    marbles = 0,
    scratchOff = 1,
    blindBox = 2,
}

export const lotteryTabRelation = {
    [lotteryTabType.marbles]: 'marbles',
    [lotteryTabType.scratchOff]: 'scratchOff',
    [lotteryTabType.blindBox]: 'blindBox',
} as const;

export const lotteryBtnRelation = {
    0: 'oneLottery',
    1: 'tenLottery',
};

export const lotteryNameRelation = {
    [lotteryTabType.marbles]: '弹珠机',
    [lotteryTabType.scratchOff]: '刮刮卡',
    [lotteryTabType.blindBox]: '开宝箱',
} as const;

export const lotteryOrder = [
    lotteryTabRelation[lotteryTabType.marbles],
    lotteryTabRelation[lotteryTabType.scratchOff],
    lotteryTabRelation[lotteryTabType.blindBox],
];

export enum btnRuleId {
    marblesOne = 1,
    marblesTen = 2,
    scratchOffOne = 3,
    scratchOffTen = 4,
    blindBoxOne = 5,
    blindBoxTen = 6,
}

export const BtnTextPic = {
    [btnRuleId.marblesOne]: 'draw-btn-marbles-1',
    [btnRuleId.marblesTen]: 'draw-btn-marbles-10',
    [btnRuleId.scratchOffOne]: 'draw-btn-scratch-1',
    [btnRuleId.scratchOffTen]: 'draw-btn-scratch-10',
    [btnRuleId.blindBoxOne]: 'draw-btn-blind-1',
    [btnRuleId.blindBoxTen]: 'draw-btn-blind-10',
};

export enum lotteryChangeEnum {
    tab = 1,
    arrow = 2,
    slide = 3,
}

export interface PosterList {
    poster: string;
    key: number;
}

// 抽奖接口
export interface DrawRewardResponse {
    awards: AwardItem[];
    needFillAddress: boolean; // 是否需要填写地址
    needJumpBackPack: boolean;
    needJumpWallet: boolean;
    toast: string;
    subTitle: string;
    /** 是否需要发送表情包 */
    needSendEmoticon: boolean;
    /** 是否展示表情包 tab */
    enableShowEmoticonTab: boolean;
    needJumpLocalLifeCouponVoucher: boolean;
    localLifeCouponVoucherJumpUrl: string;
}

/**
 * 普通抽奖/使用道具卡
 * https://mock.corp.kuaishou.com/project/941/interface/api/828321
 * @param ruleId
 * @param propsCardId
 * @returns
 */
export const drawReward = async (
    prizeSource: string,
    prizeStyle: number,
    ruleId: number,
    propsCardId?: number,
    biz = activityBiz,
) => {
    const res = await request.post<DrawRewardResponse>(
        '/rest/wd/live/plutus/luckyBag/draw',
        {
            ...params,
            ruleId,
            prizeSource,
            prizeStyle,
            biz,
            ...(propsCardId ? { propsCardId } : {}),
        },
    );

    return res.data;
};

export enum TaskType {
    SendGift = 'sendGift',
    /** 关注 */
    H5Follow = 'h5Follow',
    /** 分享 */
    SharePoster = 'sharePoster',
    WatchVideo = 'watchVideo',
    /** 跳转推荐直播间 */
    WatchLive = 'watchLive',
    /** 跳搜索推荐页 */
    SearchKeyWord = 'searchKeyWord',
    /** 预约任务 */
    Reservation = 'reservation',
    /** 激励任务 */
    AdInspire = 'adInspire',
    /** 小铃铛下载 */
    SmallBell = 'smallBell',
    /** 抢红包 */
    GrabRedPack = 'grabRedPack',
    /** 跳转年度回忆录 */
    PageView = 'pageView',
}

export enum TaskStatus {
    /** 未完成 */
    UnFinished = 1,
    /** 已完成 */
    Finished = 2,
}

// 任务+余额聚合接口
export type TaskItem = {
    /** 任务类型 */
    typeKey: TaskType;
    name: string; // 任务名称
    desc: string; // 任务描述
    iconUrl: string; // 任务icon
    finishCount: number; // 完成进度
    needCount: number; // 目标值
    /** 任务状态 1 未完成 2 已完成 */
    status: TaskStatus; //
    buttonText: string;
    kwaiLink: string; // 任务kwai链
    expandMap: Record<string, any>;
    receiveTask: boolean;
    rulePageLink: string;
};

export interface TaskResponse {
    balance: number; // 余额
    taskItemViewList: TaskItem[];
}

export const postLotteryTask = async () => {
    const res = await request.post<TaskResponse>(
        '/rest/wd/live/plutus/aggregate/audienceTask',
        {
            ...params,
            // ...taskParams,
        },
    );

    return res.data;
};

export type AwardMarqueeInfo = {
    records: string[];
    mustWinToast1: string;
    mustWinToast2: string;
};

/**
 * 扭蛋抽奖-h5跑马灯轮播
 * https://mock.corp.kuaishou.com/project/941/interface/api/828361
 * @returns
 */
export const queryAwardMarquee = async () => {
    const res = await request.post<AwardMarqueeInfo>(
        '/rest/wd/live/plutus/luckyBag/getTopBanner',
        {
            ...params,
        },
    );

    return res.data;
};

export type LotteryRecordItem = {
    id: number;
    name: string;
    count: number;
    icon: string;
};

export type Records = {
    timestamp: number;
    desc: string; // "普通奖池获得" | "至尊奖池获得"
    items: LotteryRecordItem[];
};

export type DrawRewardInfo = {
    records: Records[];
    needFillAddress: boolean; // 是否需要填写地址
};

/**
 * 扭蛋抽奖-抽奖奖励记录
 * https://mock.corp.kuaishou.com/project/941/interface/api/828391
 * @returns
 */
export const queryLotteryRecord = async (biz = activityBiz) => {
    const res = await request.post<DrawRewardInfo>(
        '/rest/wd/live/plutus/luckyBag/getLotteryRecords',
        {
            ...params,
            biz,
        },
    );

    return res.data;
};

//
export type CoinRecordItem = {
    title: string;
    timestamp: number;
    amount: number;
    operationType: number; // 1 正 2 负
};

export type CoinGainResponse = {
    statements: {
        list: CoinRecordItem[];
    };
};

// 福币获取记录:todo:pcursor
export const coinGainRecord = async () => {
    const res = await request.post<CoinGainResponse>(
        '/rest/wd/live/plutus/virtualWallet/queryUserStatement',
        {
            ...params,
            pcursor: '',
            pageSize: 100, // 可不传默认20
        },
    );

    return res.data;
};

// 获取推荐主播列表
export type SmallAuthorItem = {
    orderId: number;
    authorId: number;
    headUrl: string;
    userName: string;
    liveStreamId?: string | null;
    reservationId: string;
    reservationTitle: string;
    reservationLiveTime: number;
    reservationCount: number;
};

export type RecomResponse = {
    recoAuthorList: SmallAuthorItem[];
};

export const queryRecoAuthor = async (typeKey: string, biz = activityBiz) => {
    const res = await request.post<RecomResponse>(
        '/rest/wd/live/plutus/audienceTask/recoAuthor',
        {
            typeKey,
            biz,
        },
    );

    return res.data;
};

// 关注任务回调
export const focusTaskCallBack = async (
    followAuthorId: string | number,
    orderId: number,
    biz = activityBiz,
) => {
    const res = await request.post(
        '/rest/wd/live/plutus/audienceTask/h5Follow',
        {
            followAuthorId,
            biz,
            orderId,
        },
    );

    return res.data;
};

// 每日登录任务
export const dailyLogin = async () => {
    const res = await request.post<ApiRes<boolean>>(
        '/rest/wd/live/plutus/audienceTask/dailyLogin',
        {
            // 服务端对主播id和userId相同的情况有过滤，为了完成任务，主播侧传0
            authorId: bolIsAuthor ? 0 : authorId || 0,
            biz: activityBiz,
        },
    );

    return res.data;
};

// 搜索关键词回调
export const searchKeyWord = async (keyWord: string) => {
    const res = await request.post<boolean>(
        '/rest/wd/live/plutus/audienceTask/searchKeyWord',
        {
            biz: activityBiz,
            keyWord,
        },
    );

    return res.data;
};

export const getBalance = async () => {
    const res = await request.post<{ balance: number }>(
        '/rest/wd/live/plutus/virtualWallet/queryAccountBalance',
        {
            biz: activityBiz,
        },
    );

    return res.data;
};

// 老虎机配置信息
export type BtnRule = {
    ruleId: btnRuleId;
    coinCount: number;
    drawCount: number;
};

export type BoxConfigInfo = {
    balance: number;

    marblesPannel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
    scratchOffPanel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
    blindPannel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
    normalPanel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
};

/**
 * 抽奖页面初始化
 * https://mock.corp.kuaishou.com/project/941/interface/api/897323
 * */
export const getBoxConfig = async (biz = activityBiz) => {
    return await usePreFetchApiService(async () => {
        const res = await request.post<BoxConfigInfo>(
            '/rest/wd/live/plutus/luckyBag/getInitInfo',
            {
                biz,
            },
        );

        return res.data;
    });
};

// 预约直播
export const bookReservation = async (reservationId: string) => {
    const res = await request.post(
        '/rest/wd/live/plutus/reservation/user/book',
        { reservationId },
    );

    return res.data;
};

// 获取激励任务参数
export const getAdInspireScheme = async () => {
    const res = await request.post<{ scheme: string }>(
        '/rest/wd/live/plutus/audienceTask/getAdInspireScheme',
        {
            biz: activityBiz,
        },
    );

    return res.data;
};

// 领取任务上报
export const receiveTask = async (typeKey: string) => {
    const res = await request.post<{ scheme: string }>(
        '/rest/wd/live/plutus/audienceTask/receive',
        {
            authorId,
            biz: activityBiz,
            typeKey,
        },
    );

    return res.data;
};
export const completed = async (
    typeKey: string,
    completeAuthorId: string | number = authorId,
) => {
    const res = await request.post<boolean>(
        '/rest/wd/live/plutus/audienceTask/pageViewCompleted',
        {
            authorId: completeAuthorId || authorId,
            typeKey,
            biz: activityBiz, // 需传此字段以区分不同业务
        },
    );
    return res.data;
};
