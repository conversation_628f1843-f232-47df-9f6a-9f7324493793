<template>
    <!-- 迭代，表情包弹窗 @jiahong -->
    <AnimModal
        :show="show"
        :custom-class="customClass"
        :title="title"
        :sub-title="subtitle"
        :change-style="marginChange"
        @close="handleClose"
    >
        <template #content="action">
            <LoadingAndError
                v-if="pageStatus.loading || pageStatus.error"
                class="loading-and-error"
                page-case="other"
                :page-status="{
                    loading: pageStatus.loading,
                    error: pageStatus.error,
                }"
                :show-refresh-btn="false"
            >
            </LoadingAndError>
            <template v-else-if="awardList && awardList.length > 0">
                <div class="wrapper-gift">
                    <!-- 本地生活优惠券 -->
                    <div v-if="localCouponAward" class="flex-box">
                        <div
                            class="local-coupon-area w-[240px] h-[86px] pr-[16px]"
                        >
                            <div
                                class="amount-area w-[89px] text-center shrink-0"
                            >
                                <div class="flex-center-start mb-[4px]">
                                    <div
                                        class="unit text-center text- text-din text-16px leading-16px mt-8px mr-[3.5px]"
                                    >
                                        {{ localCouponAward.unit }}
                                    </div>
                                    <div
                                        class="int text-din text-24px leading-24px"
                                    >
                                        {{ localCouponAward.amount }}
                                    </div>
                                </div>
                                <div class="tip leading-14px">
                                    {{ localCouponAward.amountTip }}
                                </div>
                            </div>
                            <div class="platform-area ml-14px">
                                <div
                                    class="platform-name leading-18px text-14px mb-4px"
                                >
                                    {{ localCouponAward.name }}
                                </div>
                                <div
                                    v-for="(item, index) in awardList[0].desc"
                                    :key="index"
                                    class="platform-desc leading-14px"
                                >
                                    {{ item }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div
                        v-else
                        class="gift-list-area"
                        :class="{
                            'scroll hide-scroll': awardList.length >= 5,
                            six: awardList.length === 6,
                        }"
                    >
                        <div
                            class="flex-box"
                            :style="{
                                width:
                                    awardList.length >= 9
                                        ? '9rem'
                                        : awardList.length > 6
                                          ? '7.3rem'
                                          : awardList.length >= 5
                                            ? '5.92rem'
                                            : awardList.length === 4
                                              ? '4.2rem'
                                              : 'inherit',
                                'justify-content':
                                    awardList.length < 5 ? 'center' : 'inherit',
                            }"
                            :class="{ 'flex-center': awardList.length <= 3 }"
                        >
                            <div
                                v-for="(item, index) in awardList"
                                :key="index"
                                class="gift-area position-relative"
                                :class="{
                                    noLeft:
                                        awardList.length > 6 &&
                                        (index === 0 ||
                                            index ===
                                                Math.ceil(
                                                    awardList.length / 2,
                                                )),
                                }"
                                :style="{
                                    width:
                                        awardList.length <= 2
                                            ? '2rem'
                                            : '1.6rem',
                                    height:
                                        awardList.length <= 2
                                            ? '2.4rem'
                                            : '1.63rem',
                                }"
                            >
                                <div class="popup-label position-absolute">
                                    {{ item.count }}
                                </div>
                                <div
                                    class="gift-img"
                                    :style="{
                                        width: [1, 2].includes(awardList.length)
                                            ? '1.42rem'
                                            : '.72rem',
                                        height: [1, 2].includes(
                                            awardList.length,
                                        )
                                            ? '1.42rem'
                                            : '.72rem',
                                    }"
                                >
                                    <img
                                        v-lazy="{
                                            src: item.icon,
                                            error: configStore.dataConfig
                                                .giftEmptyIcon,
                                            loading:
                                                configStore.dataConfig
                                                    .giftEmptyIcon,
                                        }"
                                        alt=""
                                    />
                                </div>
                                <div class="sub-name line-cut1 text-12">
                                    <span
                                        :class="{ big: awardList.length <= 2 }"
                                    >
                                        {{ item.name }}
                                        <!-- {{
                                            awardList.length > 4
                                                ? item.name.slice(0, 8)
                                                : item.name
                                        }} -->
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="not-author-end">
                    <div
                        v-show-log="
                            resultPopupBtnLog(buttonName === '开心收下' ? 1 : 2)
                        "
                        v-click-log="
                            resultPopupBtnLog(buttonName === '开心收下' ? 1 : 2)
                        "
                        class="gis-modal-button flex-center text-bold"
                        @click="buttonAction(action.hide)"
                    >
                        {{ buttonName }}
                    </div>
                    <div v-if="tips" class="tips scale-font89">
                        {{ tips }}
                    </div>
                    <div
                        v-if="needJumpWallet"
                        class="cash-tips flex-center"
                        @click="goWallet"
                    >
                        去我的钱包查看现金奖励
                        <span>></span>
                    </div>
                </div>
            </template>
            <div v-else @click="action.hide">
                <div class="gis-thanks-label" />
                <div class="not-author-end">
                    <div class="gis-modal-button flex-center">我知道了</div>
                </div>
            </div>
        </template>
    </AnimModal>
</template>

<script lang="ts">
import { defineComponent, computed, watch } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import {
    loadUrlOnNewPage,
    bolIsAuthor,
    isOutLiveRoom,
    activityBiz,
    appendParam,
    dispatchLiveRouter,
} from '@alive-ui/actions';
import useConfigStore from '../../store/config';
import { resultPopupBtnLog } from '../../logger';
import LoadingAndError from '../../components/loading-and-error/index.vue';
import { AwardType } from '../../api';
import AnimModal from './base/base-anim.vue';
import type { PropType } from 'vue';
import type { AwardItem } from '../../api';
import { goAddress } from '@/utils/go-address';

interface GiftPanelParams {
    sourceType: string;
    tab?: string;
    selectedGiftId?: number;
}

/**
 * @name 打开礼物面板
 * @param params 礼物面板参数 https://docs.corp.kuaishou.com/k/home/<USER>/fcACRlearpMYvruRRuzfYoG3e
 * @param keepDisplayWebView  跳转后是否展示webview，false会关闭webview
 */
const openGiftPanel = (
    params: GiftPanelParams,
    keepDisplayWebView: boolean,
) => {
    const url = appendParam(
        'kwailive://giftpanel',
        params as unknown as Record<string, unknown>,
    );

    dispatchLiveRouter({
        path: url,
        keepDisplayWebView,
    }).catch((err) => {
        console.error('打开礼物面板', err);
    });
};

export default defineComponent({
    components: {
        AnimModal,
        LoadingAndError,
    },
    props: {
        customClass: {
            type: String,
            default: 'draw-lottery',
        },
        subTitle: {
            type: String,
            default: '',
        },
        awardList: {
            type: Array as PropType<AwardItem[]>,
            default: () => [],
        },
        needFillAddress: {
            type: Boolean,
            default: false,
        },
        needJumpBackPack: {
            type: Boolean,
            default: false,
        },
        needJumpWallet: {
            type: Boolean,
            default: false,
        },
        needSendEmoticon: {
            type: Boolean,
            default: false,
        },
        enableShowEmoticonTab: {
            type: Boolean,
            default: false,
        },
        show: {
            type: Boolean,
            default: false,
        },
        tips: {
            type: String,
            default: '',
        },
        pageStatus: {
            type: Object,
            default: () => ({}),
        },
        address: {
            type: String,
            default: '',
            required: false,
        },
        autoCloseTime: {
            type: Number,
            default: 0,
        },
    },
    // eslint-disable-next-line max-lines-per-function
    setup(props, { emit }) {
        const handleClose = () => {
            emit('close');
        };
        const configStore = useConfigStore();
        const title = computed(() => {
            const { awardList } = props;

            if (awardList && awardList.length > 0) {
                return '恭喜你';
            }

            return '很遗憾';
        });
        const subtitle = computed(() => {
            const { subTitle, awardList } = props;

            if (awardList && awardList.length > 0) {
                return subTitle ?? '获得以下奖励';
            }

            return subTitle ?? '未中奖';
        });
        const localCouponAward = computed(() => {
            return (
                props.awardList?.find(
                    (award) => award.type === AwardType.localCoupon,
                ) ?? null
            );
        });
        const buttonName = computed(() => {
            if (props.needFillAddress) {
                return '填写地址';
            }

            if (props.needJumpBackPack) {
                return '去背包查看';
            }

            if (props.needSendEmoticon) {
                return '去发送';
            }

            if (localCouponAward.value && isOutLiveRoom) {
                return '去查看';
            }

            return '开心收下';
        });

        const marginChange = computed(() => {
            return props?.awardList?.length < 7 ? true : false;
        });

        const buttonAction = (fnHide: () => void) => {
            if (props.needFillAddress) {
                if (bolIsAuthor) {
                    Toast.info('开播中，不可填写哦～');

                    return;
                }
                fnHide?.();

                // 跳转地址
                goAddress(true, props.address);
                return;
            }

            if (props.needJumpBackPack) {
                if (isOutLiveRoom) {
                    Toast.info('已存入礼物面板-背包，请于直播间内查看');

                    return;
                }

                if (bolIsAuthor) {
                    Toast.info('开播中，不可跳转哦～');

                    return;
                }

                openGiftPanel(
                    {
                        sourceType: 'h5', // 来源类型
                        tab: 'PacketGift',
                        selectedGiftId: props.awardList.find(
                            (item) => item.bizId,
                        )?.bizId,
                    },
                    false,
                );

                return;
            }

            // 跳转到表情面板
            if (props.needSendEmoticon) {
                // 间外
                if (isOutLiveRoom) {
                    Toast.info('请进入直播间表情面板发送');
                    return;
                }

                //  主播端
                if (bolIsAuthor) {
                    Toast.info('主播开播中，不可发送盛典表情哦');
                    return;
                }

                if (props.enableShowEmoticonTab) {
                    dispatchLiveRouter({
                        path: 'kwailive://showcommenteditor?emoticontabtype=2',
                        keepDisplayWebView: false,
                    }).catch((err) => {
                        console.error('跳转发送表情', err);
                        Toast.info('请进入直播间表情面板发送');
                    });
                } else {
                    Toast.info('请进入其他直播间表情面板发送');
                }
                return;
            }

            // 跳转本地生活商品页
            if (localCouponAward.value && isOutLiveRoom) {
                if (bolIsAuthor) {
                    Toast.info('开播中，不可跳转哦～');

                    return;
                }
                fnHide?.();
                window.location.href = localCouponAward.value.jumpUrl;
                return;
            }
            fnHide?.();
        };

        /** 跳转到钱包页面 */
        const goWallet = () => {
            if (bolIsAuthor) {
                Toast.info('开播中，不可跳转哦～');

                return;
            }

            loadUrlOnNewPage({
                url: /staging|corp/.test(location.origin)
                    ? `https://live-wallet-share.kproxy.corp.kuaishou.com/live/app/wallet-share/index.html?activityBiz=${activityBiz}`
                    : `https://activity.m.kuaishou.com/live/app/wallet-share/index.html?activityBiz=${activityBiz}`,
                type: 'back',
                ignoreHalfScreenDisplay: 1, // 顶部状态栏，仅ios使用
            });
        };

        let timer: ReturnType<typeof setTimeout> | null = null;
        watch(
            () => props.show,
            (val) => {
                if (val && props.autoCloseTime > 0) {
                    timer = setTimeout(() => {
                        handleClose();
                    }, props.autoCloseTime);
                } else {
                    timer && clearTimeout(timer);
                }
            },
        );

        return {
            title,
            subtitle,
            configStore,
            marginChange,
            close,
            buttonName,
            buttonAction,
            goWallet,
            handleClose,
            resultPopupBtnLog,
            AwardType,
            localCouponAward,
        };
    },
});
</script>

<style lang="less" scoped>
.local-coupon-area {
    display: inline-flex;
    justify-content: start;
    align-items: center;
    background: url('./assets/coupon.png') center/100% no-repeat;
    .amount-area {
        color: #ff1b46;
        .unit,
        .int {
            font-weight: bold;
        }
        .tip {
            font-size: 10px;
        }
    }
    .platform-area {
        color: #222;
        .platform-name {
            font-weight: bold;
        }
        .platform-desc {
            font-size: 10px;
            & + .platform-desc {
                margin-top: 4px;
            }
        }
    }
}
.wrapper-gift {
    // width: 235px;
    //   width: 100%;
    padding: 0 20px;
    margin: 0 auto;
    overflow: hidden;

    .scroll {
        overflow-x: scroll;
        overflow-y: hidden;
        &::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
        }
    }
    .hide-scroll {
        padding-bottom: 18px;
        margin-bottom: -20px;
    }
    .flex-box {
        display: flex;
        height: 183px;
        // padding: 0 22px;
        margin: auto;
        flex-wrap: wrap;
        align-items: center;
    }
    .gift-area {
        display: inline-block;
        margin: 5px;
        background: var(--awardItemBg);
        border-radius: 8px;
        &.noLeft {
            margin: 5px 5px 5px 0;
        }
    }
    .six {
        margin-left: 3px;
    }
    .gift-img {
        padding-top: 13px;
        margin: 0 auto;
        margin-bottom: 20px;
        img {
            width: inherit;
            height: inherit;
        }
    }
    .sub-name {
        font-family: PingFangSC, PingFangSC-Regular;
        line-height: 18px;
        text-align: center;
        color: rgba(0, 0, 0, 0.7);
        .big {
            font-size: 12px;
        }
    }

    .popup-label {
        width: 24px;
        height: 16px;
        font-family: 'AlteDIN1451Mittelschrift';
        font-size: 12px;
        line-height: 16px;
        color: var(--awardCountNumColor);
        text-align: center;
        background: linear-gradient(270deg, #ff586d 0%, #ff4594 100%);
        border-radius: 8px 8px 8px 0;
    }
}
.gis-modal-button {
    position: absolute;
    bottom: 95px;
    left: 50%;
    font-size: 16px;
    font-style: normal;
    line-height: 24px;
    color: var(--popupBtnDoColor);
    text-align: center;
    transform: translateX(-50%);
}
.tips {
    display: block;
    margin-top: 6px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-size: 8px;
    line-height: 11px;
    text-align: center;
    white-space: nowrap;
    opacity: 0.3;
}
.cash-tips {
    position: absolute;
    bottom: 13px;
    left: 50%;
    width: 100%;
    font-size: 12px;
    color: rgba(50, 41, 107, 40%);
    text-align: center;
    transform: translateX(-50%);
}
.gis-thanks-label {
    margin: 37px auto;
}
</style>
