<template>
    <SmallModal
        :auto-close-time="autoCloseTime"
        :show="show"
        :title="title || '关注主播领' + configStore.dataConfig.coinName"
        :custom-class="customClass"
        @close="handleClose"
    >
        <template #content="action">
            <div v-if="authorInfo" class="follow-content">
                <div class="user-head-area">
                    <img
                        :src="authorInfo.headUrl || DafaultAvatar"
                        alt=""
                        class="author-img-avatar"
                    />
                    <div class="user-name text-14 text-bold">
                        {{ nameSlice(authorInfo.userName, 8) }}
                    </div>
                </div>
                <div
                    class="ks-button flex-center text-bold text-16"
                    @click="kwaiFocus(action.hide)"
                >
                    <div
                        class="gis-modal-button flex-center"
                        :class="{ 'actived-button': btnPress }"
                    >
                        关注
                    </div>
                </div>
            </div>
        </template>
    </SmallModal>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, ref } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import {
    dispatchLiveRouter,
    appendParam,
    activityBiz,
    nameSlice,
} from '@alive-ui/actions';
import DafaultAvatar from '../../variable/assets/default-avatar-icon_2x.png';
import useConfigStore from '../../store/config';
import { queryRecoAuthor, focusTaskCallBack } from '../../api/index';
import SmallModal from './base/base-small.vue';
import type { SmallAuthorItem } from '../../api/index';

export default defineComponent({
    components: {
        SmallModal,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        biz: {
            type: String,
            default: activityBiz,
        },
        customClass: {
            type: String,
            default: 'draw-lottery',
        },
        autoCloseTime: {
            type: Number,
            default: 0,
        },
    },
    setup(props, { emit }) {
        let hide = () => {};

        const handleClose = () => {
            emit('close');
        };
        const configStore = useConfigStore();
        const authorInfo = ref({} as SmallAuthorItem);

        const queryRecoAuthorInfo = async () => {
            const res = await queryRecoAuthor('h5Follow', props.biz);
            authorInfo.value = res?.recoAuthorList?.[0];

            if (!authorInfo.value) {
                Toast.info('暂无可关注主播');
                emit('refresh');

                setTimeout(() => {
                    hide();
                }, 1000);
            }
            console.log('queryRecoAuthorInfo====', authorInfo.value);
        };
        queryRecoAuthorInfo();

        const focusSuccess = async (res: any) => {
            console.log('focusSuccess===res', res);
            await focusTaskCallBack(
                authorInfo.value.authorId,
                authorInfo.value.orderId,
                props.biz,
            );

            setTimeout(() => {
                emit('refresh');
                hide();
            }, 500);
        };

        const focusError = (res: any) => {
            Toast.info('关注失败');
            console.log('focusError', res);
            setTimeout(hide, 1500);
        };
        const btnPress = ref(false);

        const kwaiFocus = (fnHide: () => void) => {
            if (typeof fnHide === 'function') {
                hide = fnHide;
            }

            if (!authorInfo.value?.authorId) {
                focusError({});
                hide();

                return;
            }
            btnPress.value = true;
            // focusError('关注拦截');
            const path = appendParam('kwailive://followuser', {
                userid: authorInfo.value.authorId, // 面板tab
                isfollow: 'true', // 关注还是取关
                followsource: 187,
                liveFollowExtParams: JSON.stringify({
                    live_activity_name: activityBiz,
                    position: 'LIVE_LOTTERY_FOLLOW_TASK_POPUP',
                }),
            });

            dispatchLiveRouter({
                path,
                keepDisplayWebView: true,
            })
                .then((res: any) => {
                    console.log('关注成功？', res);
                    focusSuccess(res);
                })
                .catch((err: unknown) => {
                    console.error('调用快链关注失败', err);
                    focusError(err);
                });
        };

        return {
            configStore,
            authorInfo,
            focusSuccess,
            focusError,
            btnPress,
            kwaiFocus,
            handleClose,
            nameSlice,
            DafaultAvatar,
        };
    },
});
</script>

<style lang="less" scoped>
.follow-content {
    text-align: center;
    font-size: 0;
    .author-img-avatar {
        width: 88px;
        height: 88px;
        border: 1px solid #fff;
        border-radius: 50%;
    }
    .user-name {
        max-width: 200px;
        margin: 10px auto;
        line-height: 21px;
        color: #000;
        text-align: center;
    }
    .ks-button {
        position: absolute;
        bottom: var(--popBtnBottom, 36px);
        left: 50%;
        margin: 0 auto;
        font-family: 'PingFang SC';

        @apply a-text-button;

        text-align: center;
        transform: translateX(-50%);
        .actived-button {
            opacity: 0.5;
        }
    }
    .user-head-area {
        margin-top: 64px;
    }
}
</style>
