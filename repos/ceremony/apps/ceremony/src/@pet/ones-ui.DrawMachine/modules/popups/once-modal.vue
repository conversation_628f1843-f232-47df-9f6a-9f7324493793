<template>
    <spu-popup
        ref="onceRef"
        v-model="showModel"
        mask-closeable
        popup-class="draw-lottery"
        position="center"
        @before-hide="hide"
    >
        <div class="once-modal-wrapper">
            <div class="once-modal-bg" :style="bgStyle" />
            <div class="once-modal-icon gis-icon-close" @click.stop="hide" />
        </div>
    </spu-popup>
</template>

<script lang="ts">
import {
    defineComponent,
    ref,
    onUnmounted,
    watch,
    computed,
    onMounted,
    nextTick,
} from 'vue';
import { Popup } from '@lux/sharp-ui-next';
import { stopMove } from '@alive-ui/actions';
import useConfigStore from '../../store/config';
import type { StopFunItem } from '@alive-ui/actions';

export default defineComponent({
    components: {
        'spu-popup': Popup,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        showPanelIcon: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        const showModel = ref<boolean>(false);
        let stopMoveObj: StopFunItem;
        const onceRef = ref();
        const configStore = useConfigStore();

        const hide = () => {
            stopMoveObj.cancelMove();

            showModel.value = false;
            document.body.classList.remove('spu-overflow-hidden');

            emit('close');
        };

        const bgStyle = computed(() => {
            return {
                background: `url(${configStore.lotteryOnceImg}) center / 100% no-repeat`,
            };
        });

        watch(
            () => props.show,
            (o) => {
                if (o) {
                    stopMoveObj = stopMove();
                }
                showModel.value = o;
            },
            { immediate: true },
        );

        onMounted(() => {
            nextTick(() => {
                configStore.switchVisit();
            });
        });

        onUnmounted(() => {
            stopMoveObj?.destroy?.();
        });

        return {
            onceRef,
            configStore,
            hide,
            showModel,
            bgStyle,
        };
    },
});
</script>

<style lang="less" scoped>
/deep/.spu-popup__box {
    height: 100vh;
    margin-top: 60px;
}
.once-modal-icon {
    position: relative;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
}

.once-modal-bg {
    width: 414px;
    height: 414px;
}

@media (min-aspect-ratio: 0.6) {
    .once-modal-icon {
        top: 30px;
    }
    .once-modal-wrapper {
        transform: scale(0.8);
    }
}
</style>
