<template>
    <div
        v-show="showModel"
        class="draw-lottery anim-pop buff-popup-mask flex-center-center"
        :class="customClass"
        @click="handleMask"
    >
        <TransparentVideo
            v-if="showModel"
            class="light-video"
            :width="828"
            :height="1284"
            :playing="playTransparent"
            :video-url="lightMp4"
            @video-start="videoPlay"
            @video-ended="videoEnd"
            @video-loaded="playerLoaded"
            @error="playerError"
            @video-renderer-error="playerError"
        />
        <div class="base-anim-modal-wrapper">
            <div ref="refPopup" class="pop-content">
                <transition name="drawOpen">
                    <div
                        v-if="showModel && videoPlayed"
                        class="pop-bg gis-small-modal"
                    >
                        <div
                            v-if="title"
                            class="title-content flex-center-center"
                        >
                            <div class="icon gis-title-extra-icon" />
                            <div
                                class="gift-title title-font mf-font text-text-hyykh"
                            >
                                {{ title }}
                            </div>
                            <div
                                class="icon gis-title-extra-icon rotate-icon"
                            />
                        </div>
                        <div
                            v-if="subTitle"
                            class="gift-sub-title text-12"
                            :class="{
                                'gift-sub-title-margin': changeStyle,
                            }"
                        >
                            {{ subTitle }}
                        </div>
                        <slot name="content" :hide="hide" />
                        <slot name="action-box" :hide="hide" />
                    </div>
                </transition>
            </div>
            <div
                v-click-log="title === '恭喜获得' && resultPopupBtnLog(3)"
                class="gis-icon-close margin-auto"
                @click.prevent="hide"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-explicit-any */
import { onUnmounted, ref, watch, nextTick } from 'vue';
import { usePopAdapter } from '@pet/ones-use.usePopAdapter/index';
import {
    useModelDisableScroll,
    stopMove,
    type StopFunItem,
} from '@alive-ui/actions';
import { useMain } from '../../../store/useMain';
import { resultPopupBtnLog } from '../../../logger';
import TransparentVideo from '../../../components/transparent-video/index.vue';
import lightMp4 from './light.mp4';

const props = defineProps({
    customClass: {
        type: String,
        default: 'draw-lottery',
    },
    show: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: '记录',
    },
    subTitle: {
        type: String,
        default: '',
    },
    changeStyle: {
        type: Boolean,
        default: false,
    },
});
let stopMoveObj: StopFunItem;
const mainStore = useMain();
const emits = defineEmits([
    'close',
    'player-error',
    'video-end',
    'player-loaded',
]);
const showModel = ref<boolean>(false);
const refPopup = ref();
useModelDisableScroll(showModel);
const videoPlayed = ref(false);
const videoClose = ref(false);
let timer: ReturnType<typeof setTimeout>;
const { adapter, recover } = usePopAdapter('left');
const playTransparent = ref(false);

const hide = () => {
    emits('close');
};

watch(
    () => props.show,
    (o) => {
        showModel.value = o;

        if (!o) {
            videoPlayed.value = o;
            stopMoveObj?.cancelMove();
            recover();
        } else {
            stopMoveObj = stopMove();
            nextTick(() => {
                adapter();
            });
            if (mainStore.isLowDev) {
                videoPlayed.value = true;
                videoClose.value = true;

                return;
            }
            videoClose.value = false;

            setTimeout(() => {
                videoPlayed.value = true;
                adapter();

                timer = setTimeout(() => {
                    videoClose.value = true;
                }, 2000);
            }, 500);
        }
    },
    { immediate: true },
);

onUnmounted(() => {
    stopMoveObj?.destroy();
    clearTimeout(timer);
});

const handleMask = (ev: Event) => {
    const dom = refPopup.value;
    playTransparent.value = false;
    if (dom?.contains(ev.target)) {
        return;
    }
    hide();
};

const videoPlay = () => {
    videoPlayed.value = true;
};

const videoEnd = () => {
    console.log('listen====end');
    videoClose.value = true;
    playTransparent.value = false;
    emits('video-end');
};

const playerError = () => {
    console.log('listen====error');
    videoPlayed.value = true;
    videoClose.value = true;
    playTransparent.value = false;
    emits('player-error');
};

const playerLoaded = (bol: boolean) => {
    playTransparent.value = true;
    emits('player-loaded', bol);
};
</script>

<style lang="less" scoped>
.buff-popup-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 70%);
    flex-direction: column;
}
.pop-content {
    min-height: 455px;
    .pop-bg {
        position: relative;
        will-change: transform;
        backface-visibility: visible;
        padding-top: var(--popupTitleSmallTop, var(--popupTitleRecordTop));
        .title-content {
            position: relative;
            z-index: 1;
            text-align: center;
            box-sizing: content-box;
        }
        .gift-title {
            width: fit-content;
            font-family: var(--popupTitleFamily);
            font-size: var(--popupTitleFontSize);
            margin: var(--popupTitleMargin);
            line-height: 28px;
            color: transparent;
            text-align: center;
            background: var(--popupTitleColor);
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .rotate-icon {
            transform: var(--iconRotate, rotateY(180deg));
        }
        .icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            margin-top: 2px;
        }
        .gift-sub-title {
            margin: 4px auto 12px;
            line-height: 18px;
            color: var(--fontSubColor);
            text-align: center;
            opacity: 0.6;
        }
        .gift-sub-title-margin {
            margin-bottom: 10px;
        }
    }
}
.gis-icon-close {
    margin-top: 5px;
}

.light-video {
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    width: 414px;
    margin: 0 auto;
    transform: translateY(-50%) translateZ(-500px);
    will-change: transform;
    /deep/ video {
        will-change: transform;
    }
}
.drawOpen-enter {
    opacity: 0;
}
.drawOpen-enter-to {
    opacity: 1;
}

.drawOpen-enter-active {
    transition: opacity 1s ease;
    animation:
        draw-open-680 680ms 200ms cubic-bezier(0.3, 0, 0.6, 1) both,
        draw-open-680-1000 320ms 880ms cubic-bezier(0.3, 0, 0.39, 1);
}

@keyframes draw-open-680 {
    0% {
        transform: scale(0) rotateY(360deg);
    }
    100% {
        transform: scale(0.96) rotateY(0deg);
    }
}

@keyframes draw-open-680-1000 {
    0% {
        transform: scale(0.96);
    }
    100% {
        transform: scale(1);
    }
}

@media (min-aspect-ratio: 0.6) {
    .base-anim-modal-wrapper {
        transform: scale(0.8);
    }
}
</style>
