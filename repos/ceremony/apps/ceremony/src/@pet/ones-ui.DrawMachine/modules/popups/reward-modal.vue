<template>
    <RecordModal
        :show="show"
        :title="title"
        bg-class="prize-record-pop"
        close-class="prize-record-close"
        :top-text="topText"
        :custom-class="customClass"
        :auto-close-time="autoCloseTime"
        @close="handleClose"
    >
        <template #content>
            <div class="content">
                <LoadingAndError
                    v-if="
                        pageStatus.loading ||
                        pageStatus.nodata ||
                        pageStatus.error
                    "
                    page-case="other"
                    :page-status="pageStatus"
                    @refresh="queryRecord"
                />
                <div v-else class="record">
                    <div
                        v-for="(item, index) in records"
                        :key="index"
                        class="coin-record-item"
                        :class="{ extraTopPad: showPanelIcon }"
                    >
                        <div
                            v-if="showPanelIcon"
                            class="des-icon"
                            :class="{
                                'gis-icon-dzjc':
                                    item.desc &&
                                    item.desc.includes(
                                        lotteryNameRelation[
                                            lotteryTabType.marbles
                                        ],
                                    ),
                                'gis-icon-ggljc':
                                    item.desc &&
                                    item.desc.includes(
                                        lotteryNameRelation[
                                            lotteryTabType.scratchOff
                                        ],
                                    ),
                                'gis-icon-kbxjc':
                                    item.desc &&
                                    item.desc.includes(
                                        lotteryNameRelation[
                                            lotteryTabType.blindBox
                                        ],
                                    ),
                            }"
                        />
                        <div class="flex flex-wrap">
                            <div
                                v-for="(elem, i) in item.items"
                                :key="i"
                                class="gift-item flex align-center"
                                :class="{ wide: elem.count === 10 }"
                            >
                                <img
                                    class="gift-item-icon"
                                    :src="elem.icon"
                                    alt=""
                                />
                                <div class="gift-amount inline-block text-bold">
                                    x{{ elem.count }}
                                </div>
                            </div>
                        </div>
                        <div class="gain-award-time text-10">
                            {{ timeText }}：
                            {{
                                formatTime(
                                    item.timestamp,
                                    'MM月dd日 HH:mm',
                                ).replace(/^0/, '')
                            }}
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template #action-box="action">
            <div
                v-if="!(pageStatus.error || pageStatus.loading)"
                class="action-button-area flex margin-auto text-bold"
                :class="needFillAddress ? 'flex-between' : 'flex-center'"
            >
                <div
                    v-if="needFillAddress"
                    class="btn-address flex-center"
                    @click="inputAddress(action.hide)"
                >
                    填写地址
                </div>
                <div
                    class="btn-continue flex-center"
                    :class="
                        needFillAddress ? 'gis-short-btn' : 'gis-modal-button'
                    "
                    @click="action.hide"
                >
                    继续抽奖
                </div>
            </div>
            <div
                v-if="!(pageStatus.error || pageStatus.loading)"
                class="address-tip"
            >
                仅展示最近100条记录
            </div>
        </template>
    </RecordModal>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, ref, reactive, computed } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import {
    loadUrlOnNewPage,
    formatTime,
    bolIsAuthor,
    activityBiz,
} from '@alive-ui/actions';
import useConfigStore from '../../store/config';
import LoadingAndError from '../../components/loading-and-error/index.vue';
import {
    queryLotteryRecord,
    lotteryNameRelation,
    lotteryTabType,
} from '../../api/index';
import RecordModal from './base/base-record.vue';
import type { Records } from '../../api/index';
import { goAddress } from '@/utils/go-address';

export default defineComponent({
    components: {
        RecordModal,
        LoadingAndError,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        showPanelIcon: {
            type: Boolean,
            default: false,
        },
        customClass: {
            type: String,
            default: 'draw-lottery',
        },
        biz: {
            type: String,
            default: activityBiz,
        },
        autoCloseTime: {
            type: Number,
            default: 0,
        },
        address: {
            type: String,
            default: '',
        },
        title: {
            type: String,
            default: '中奖记录',
        },
        timeText: {
            type: String,
            default: '中奖时间',
        },
    },
    setup(props, { emit }) {
        const configStore = useConfigStore();
        const records = ref<Records[]>([]);
        const pageStatus = reactive({
            loading: false,
            error: false,
            nodata: false,
        });
        const needFillAddress = ref(false);

        const topText = computed(() => {
            return needFillAddress.value
                ? '温馨提示：请于活动结束前填写地址'
                : '';
        });

        const queryRecord = async () => {
            pageStatus.loading = true;
            pageStatus.error = false;

            try {
                const data = await queryLotteryRecord(props.biz);
                records.value = data?.records || [];
                needFillAddress.value = data?.needFillAddress;
                pageStatus.nodata = !records.value?.length;
            } catch (err) {
                pageStatus.error = true;
                console.error('get record error:', err);
            }
            pageStatus.loading = false;
        };

        queryRecord();

        const inputAddress = (fnHide: () => void) => {
            if (bolIsAuthor) {
                Toast.info('开播中，不可填写哦～');

                return;
            }
            fnHide?.();
            goAddress(true, props.address);
        };

        const handleClose = () => {
            emit('close');
        };

        return {
            lotteryNameRelation,
            lotteryTabType,
            pageStatus,
            records,
            topText,
            formatTime,
            queryRecord,
            needFillAddress,
            inputAddress,
            handleClose,
        };
    },
});
</script>

<style lang="less" scoped>
.address-tip {
    margin-top: 8px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-size: 12px;
    line-height: 15px;
    color: var(--fontSubColor);
    text-align: center;
    opacity: 0.4;
}

.content {
    height: var(--recordContentHeight, 220px);
    width: var(--recordContentWidth, 280px);
    margin: 10px auto 0;
    overflow: scroll;

    :deep(.spring-loading-error) {
        bottom: auto;
        margin-top: 18px;
        color: #571818 !important;
        .refresh-button {
            margin-top: 80px;
        }
    }
    .coin-record-item {
        position: relative;
        width: 100%;
        padding: 12px 14px;
        margin: 0 auto;
        margin-bottom: 8px;
        background: var(--recordItemBg);
        border-radius: 14px;
        &.extraTopPad {
            padding-top: 26px;
        }
        .des-icon {
            position: absolute;
            top: 0;
            left: 0;
        }
    }
    .record-title {
        font-family: PingFangSC, PingFangSC-Medium;
        font-size: 14px;

        @apply text-bold;
    }
    .coin-get-time {
        margin-top: 6px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-size: 12px;
        line-height: 17px;
        opacity: 0.7;
    }
    .gift-item {
        width: 48px;
        margin-right: 20px;
        margin-bottom: 12px;
        &.wide {
            width: auto;
        }
        &:nth-child(4n) {
            margin-right: 0;
        }
    }
    .gift-item-icon {
        width: 30px;
        height: 30px;
        // background: #ff517175;
    }
    .gift-amount {
        // margin-right: 21px;
        margin-left: 4px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-size: 12px;
        color: var(--rewardGiftNumColor);
        white-space: nowrap;
    }
    .gain-award-time {
        margin-top: -4px;
        font-family: PingFangSC, PingFangSC-Regular;
        line-height: 15px;
        color: rgba(0, 0, 0, 0.6);
    }
}
.action-button-area {
    width: 234px;
    margin-top: 10px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-size: 16px;
    text-align: center;
    .btn-address {
        width: 114px;
        height: 48px;
        margin-right: 12px;
        color: #571818;
        border: 1px solid;
        border-radius: var(--popBtnAddRadius, 28px);
        box-sizing: border-box;
    }
    .btn-continue {
        color: var(--popupBtnDoColor);
    }
}
</style>
