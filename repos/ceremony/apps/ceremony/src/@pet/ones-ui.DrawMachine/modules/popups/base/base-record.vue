<template>
    <spu-popup
        ref="refPopup"
        v-model="showModel"
        mask-closeable
        transfer-dom
        :popup-class="customClass"
        :class="{ 'out-draw-lottery': is4Tab }"
        position="center"
        @before-hide="hide"
    >
        <div class="base-record-modal-wrapper">
            <div class="pop-content">
                <div class="pop-bg gis-record-modal" :class="bgClass">
                    <div class="title-content flex-center-center">
                        <div class="icon gis-title-extra-icon" />
                        <div class="gift-title title-font mf-font">
                            {{ title }}
                        </div>
                        <div class="icon gis-title-extra-icon rotate-icon" />
                    </div>
                    <div class="gift-sub-title text-12">
                        {{ topText }}
                    </div>
                    <slot name="content" />
                    <slot name="action-box" :hide="hide" />
                </div>
            </div>
            <div
                class="gis-icon-close margin-auto"
                :class="closeClass"
                @click.prevent="hide"
            />
        </div>
    </spu-popup>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, onUnmounted, ref, watch, onMounted } from 'vue';
import { usePopAdapter } from '@pet/ones-use.usePopAdapter/index';
import { is4Tab } from '@pet/ones-use.is4Tab';
import { Popup } from '@lux/sharp-ui-next';
import { stopMove, isOutLiveRoom } from '@alive-ui/actions';
import type { StopFunItem } from '@alive-ui/actions';

export default defineComponent({
    components: {
        'spu-popup': Popup,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '记录',
        },
        bgClass: {
            type: String,
            default: '',
            required: false,
        },
        closeClass: {
            type: String,
            default: '',
            required: false,
        },
        topText: {
            type: String,
            default: '仅展示最近100条记录',
            required: false,
        },
        customClass: {
            type: String,
            default: 'draw-lottery',
        },
        autoCloseTime: {
            type: Number,
            default: 0,
        },
    },
    setup(props, { emit }) {
        const showModel = ref<boolean>(false);
        let stopMoveObj: StopFunItem;
        const refPopup = ref(null);

        const hide = () => {
            stopMoveObj.cancelMove();
            emit('close');
        };

        const { adapter, recover } = usePopAdapter('left');

        watch(
            () => props.show,
            (o) => {
                if (o) {
                    stopMoveObj = stopMove();
                    adapter();
                } else {
                    recover();
                }
                showModel.value = o;
            },
            { immediate: true },
        );

        if (props.autoCloseTime > 0) {
            setTimeout(() => {
                hide();
            }, props.autoCloseTime);
        }

        onUnmounted(() => {
            stopMoveObj?.destroy?.();
        });

        return {
            refPopup,
            hide,
            showModel,
            isOutLiveRoom,
            is4Tab,
        };
    },
});
</script>

<style lang="less" scoped>
/deep/ .spu-popup__box {
    height: 100%;
    max-height: unset;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.pop-content {
    position: relative;
    .pop-bg {
        padding: 0 9px;
        padding-top: 137px;
        .title-content {
            position: relative;
            z-index: 1;
            text-align: center;
            box-sizing: content-box;
        }
        .gift-title {
            width: fit-content;
            font-family: var(--popupTitleFamily);
            font-size: var(--popupTitleFontSize);
            margin: var(--popupTitleMargin);
            line-height: 28px;
            color: transparent;
            text-align: center;
            background: var(--popupTitleColor);
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .rotate-icon {
            transform: var(--iconRotate, rotateY(180deg));
        }
        .icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            margin-top: 2px;
        }
        .gift-sub-title {
            margin: 4px auto 12px;
            line-height: 18px;
            color: var(--fontSubColor);
            text-align: center;
            opacity: 0.6;
        }
    }
}
.gis-icon-close {
    position: absolute;
    right: 50%;
    margin-top: 5px;
    transform: translateX(50%);
}

// .base-record-modal-wrapper {
//     transform: scale(0.8);
// }

@media (min-aspect-ratio: 0.6) {
    .base-record-modal-wrapper {
        transform: scale(0.8);
        transform-origin: top;
    }
    /deep/.spu-popup__box {
        margin-top: 0;
        overflow: hidden;
    }
}
</style>
