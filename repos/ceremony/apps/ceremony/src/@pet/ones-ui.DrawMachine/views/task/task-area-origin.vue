<!-- eslint-disable vue/no-v-html -->
<template>
    <div class="task-area-container">
        <!-- <div class="coin-record-wrap">
            <div
                v-if="needCoin"
                v-show-log="funcLog.coinRecord"
                v-click-log="funcLog.coinRecord"
                class="coin-text flex-center a-text-main text-12 text-pingfang text-regular"
                @click="switchRewardRecord(true)"
            >
                {{ dataConfig.coinName }}记录 <SvgArrow class="icon-jt" />
            </div>
        </div> -->

        <ColorfulCard
            :title="dataConfig.cardTitle"
            car-star-icon=""
            :need-coin="needCoin"
            :coin-name="dataConfig.coinName"
            class="task-container"
            @show-coin="switchRewardRecord(true)"
        >
            <div>
                <div class="task-area">
                    <div
                        v-if="
                            taskStore.taskList.length &&
                            !taskStore.pageStatus.loading &&
                            !taskStore.pageStatus.error
                        "
                        class="task-list-area"
                    >
                        <div
                            v-for="(item, index) in taskStore.taskList"
                            :key="index"
                            class="flex task-item flex-between align-center a-bg-substrate"
                            :class="{
                                'task-item-finished': item.status !== 1,
                            }"
                        >
                            <div
                                v-show-log="
                                    taskBtnLog(item.buttonText, item.typeKey)
                                "
                                class="left-area flex-center"
                            >
                                <div class="icon-img">
                                    <img
                                        :src="item.iconUrl"
                                        :alt="item.typeKey"
                                    />
                                </div>
                                <div class="title-desc">
                                    <div>
                                        <span
                                            class="first-title text-pingfang text-bold text-16 a-text-main"
                                            >{{ item.name }}</span
                                        >
                                        <span
                                            v-show="item.typeKey !== 'h5Follow'"
                                            class="task-value text-regular text-16 task-name-extra-color"
                                        >
                                            {{ item.finishCount }}/{{
                                                item.needCount
                                            }}
                                        </span>
                                    </div>
                                    <!-- eslint-disable-next-line vue/no-v-html -->
                                    <div class="sub-reward-desc-box">
                                        <div
                                            class="sub-reward-desc text-regular text-pingfang text-12 a-text-main"
                                            v-html="descComputed(item)"
                                        />
                                        <div
                                            v-if="item.rulePageLink"
                                            class="rule-page-link"
                                            @click="goRulePageLink(item)"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="right-action">
                                <div
                                    class="flex-center gis-action-btn text-pingfang text-bold text-14 a-text-button"
                                    :class="{
                                        'gis-action-btn-finished a-text-main':
                                            item.status !== 1,
                                    }"
                                    @click="taskStore.buttonAction(item)"
                                >
                                    {{ item.buttonText }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <LoadingAndError
                        v-else
                        page-case="main"
                        :page-status="taskStore.pageStatus"
                        @refresh="taskStore.refetchTask"
                    />
                </div>
            </div>
            <RecordModal
                v-if="rewardRecordVisible"
                :show="rewardRecordVisible"
                @close="switchRewardRecord(false)"
            />
            <FollowModal
                v-if="taskStore.followModal"
                :show="taskStore.followModal"
                @close="taskStore.closeModal"
                @refresh="taskStore.refetchTask"
            />
            <ReservationModal
                v-if="taskStore.reservationModal"
                :show="taskStore.reservationModal"
                @close="taskStore.closeModal"
                @refresh="taskStore.refetchTask"
            />
        </ColorfulCard>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { goOtherPage } from '@alive-ui/actions';
import useTaskStore from '../../store/task';
import useConfigStore from '../../store/config';
import ReservationModal from '../../modules/popups/reservation-modal.vue';
import FollowModal from '../../modules/popups/follow-modal.vue';
import RecordModal from '../../modules/popups/coin-modal.vue';
import { funcLog, taskBtnLog } from '../../logger/index';
import LoadingAndError from '../../components/loading-and-error/index.vue';
import ColorfulCard from '../../components/colorful-card.vue';
// import SvgArrow from '../../components/arrow.vue';

export default defineComponent({
    components: {
        ColorfulCard,
        LoadingAndError,
        RecordModal,
        FollowModal,
        ReservationModal,
        // SvgArrow,
    },
    props: {
        needCoin: {
            type: Boolean,
            default: true,
        },
        needDailyTask: {
            type: Boolean,
            default: true,
        },
    },
    setup(props) {
        const configStore = useConfigStore();
        const { dataConfig } = storeToRefs(configStore);
        const taskStore = useTaskStore();
        taskStore.watchUpdate?.();

        const init = async () => {
            // taskStore.getAppInfo();

            try {
                if (props.needDailyTask) {
                    await taskStore.initLogin();
                }
                await taskStore.queryTaskInfo();
            } catch (err) {
                console.error('lucky machine init', err);
            }
        };
        init();

        const rewardRecordVisible = ref(false);

        const switchRewardRecord = (status: boolean) => {
            rewardRecordVisible.value = status;
        };

        // 搜索描述数据
        const descComputed = (item: any) => {
            let { desc } = item;

            if (item.typeKey === 'searchKeyWord') {
                // 国庆扭蛋机需求
                desc = item.desc.replace(
                    /%k/g,
                    `<span class="strong">${item.expandMap.keyWord}</span>`,
                );
            }

            return desc;
        };

        const goRulePageLink = (item: any) => {
            if (item?.rulePageLink) {
                goOtherPage('jimu', item.rulePageLink);
            }
        };

        return {
            dataConfig,
            taskStore,
            rewardRecordVisible,
            switchRewardRecord,
            descComputed,
            taskBtnLog,
            funcLog,
            goRulePageLink,
        };
    },
});
</script>

<style lang="less" scoped>
.data-status-deal {
    margin-top: 40px;
}
.rotate90 {
    transform: rotate(90deg);
}
.gis-action-btn {
    width: var(--taskBtnWidth, 71px);
    height: var(--taskBtnHeight, 36px);
    line-height: var(--taskBtnHeight, 36px);
    background: url('../../variable/assets/icon-task-1_2x.png') center / 100%
        no-repeat;
    background-size: contain;

    &.gis-action-btn-finished {
        background: none;
        border: 1px solid rgba(255, 223, 191, 60%);
        border-radius: 30px;
    }
}
.gis-icon-currency {
    margin-right: 4px;
}

.task-value {
    margin-left: 10px;
    font-family: 'AlteDIN1451Mittelschrift';
    line-height: 20px;
}
// .coin-text {
//     opacity: 0.6;
//     .icon-jt {
//         width: 10px;
//         height: 10px;
//         margin-left: 4px;
//     }
// }
.task-area {
    // position: relative;
    z-index: 11;
    min-height: var(--taskAreaHeight, 294px);
    border-radius: 20px;
    margin-top: 10px;
    .icon-img {
        width: 60px;
        height: 60px;
        img {
            width: inherit;
            height: inherit;
        }
    }
    .first-title {
        line-height: 24px;
    }
    .sub-reward-desc-box {
        display: flex;
        margin-top: 6px;
        align-items: center;
    }
    .sub-reward-desc {
        display: inline-block;
        line-height: 18px;
        opacity: 0.6;
    }
    .task-list-area {
        padding-bottom: 5px;
        margin: 0 12px;
    }
    .task-item {
        width: 358px;
        padding: 16px 12px;
        margin: 0 auto 10px;
        border-radius: 8px;
        .title-desc {
            margin-left: 8px;
        }
        .strong {
            font-size: 700;
            color: var(--fontRedColor);
        }

        &.task-item-finished {
            .left-area {
                opacity: 60%;
            }

            .gis-action-btn {
                opacity: 50%;
            }
        }
    }
}

.task-area-container {
    // position: relative;
    padding-bottom: 64px;
    margin-top: 77px;
    :deep(.gis-card-star-icon) {
        display: none !important;
    }
}
.coin-record-wrap {
    position: absolute;
    top: 25px;
    right: 0;
    right: 28px;
    z-index: 2;
}
.rule-page-link {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 4px;
    background: url('../../variable/assets/rule-page-link_2x.png') center / 100%
        no-repeat;
}
</style>
