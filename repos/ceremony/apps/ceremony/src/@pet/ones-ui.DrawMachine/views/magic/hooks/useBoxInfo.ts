/* eslint-disable max-lines-per-function */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useRoute } from 'vue-router';
import { ref, watch, reactive, computed } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { Toast } from '@lux/sharp-ui-next';
import { Report } from '@alive-ui/actions';
import { BTN_STATUS, DRAW_EVENT } from '../config';
import { useMain, COMMON_EVENT } from '../../../store/useMain';
import { useConfigStore } from '../../../store/config';
import { drawBtnClick } from '../../../logger';
import {
    getBoxConfig,
    getBalance,
    drawReward,
    lotteryTabRelation,
    lotteryNameRelation,
    lotteryOrder,
    lotteryTabType,
    lotteryBtnRelation,
} from '../../../api';
import { useAudioContext } from './audio';
import type { BoxConfigInfo, BtnRule, AwardItem } from '../../../api';

export const DrawResult = {
    error: 0,
    success: 1,
    empty: 2,
    offseason: 3, // 休赛
} as const;

export const useBoxInfo = defineStore('lottery-box', () => {
    const mainStore = useMain();
    const configStore = useConfigStore();
    const route = useRoute();

    const curPoster = ref(configStore.dataConfig.curPoster);
    // 抽奖机底部tab，0 弹珠机；1 刮刮卡；2 开宝箱
    const lotteryBottomTabType = ref(
        (configStore.dataConfig.lotteryBottomTabType ??
            0) as keyof typeof lotteryTabRelation,
    );

    const pageStatusPop = reactive({
        loading: false,
        error: false,
    });

    const lotteryKey = ref(+new Date());
    const btnStatus = ref(BTN_STATUS.init); // 面板按钮状态
    const pageStatus = reactive({
        loading: true,
        error: false,
    });
    const balance = ref(0);
    const boxConfig = ref({} as BoxConfigInfo);

    const currentId = ref(1);
    const subTitle = ref('');
    const resultList = ref([] as AwardItem[]);
    const needFillAddress = ref(false);
    const needJumpBackPack = ref(false);
    const needJumpWallet = ref(false);
    const randomCount = ref(0);
    const isPlaying = ref(false); // 是否正在播放开盒动画

    const needSendEmoticon = ref(false);
    const enableShowEmoticonTab = ref(false);

    const audioContext = useAudioContext({});

    //  抽1次/10次按钮索引
    const btnIdx = ref<keyof typeof lotteryBtnRelation>(0);
    //  是否播放音频：低电量 && 有配置音频资源
    const canPlayAudio = computed(() => {
        const { lotteryResources } = configStore.dataConfig;
        return (
            !mainStore.isLowDev &&
            !!lotteryResources?.[curDrawType.value]?.[btnType.value]?.audio
        );
    });

    //  当前抽奖类型：marbles | scratchOff | blindBox，对应 kconf 资源的 key
    const curDrawType = computed(() => {
        return lotteryTabRelation[lotteryBottomTabType.value ?? 0];
    });

    //  当前抽奖按钮对应的 kconf 资源的 key: oneLottery | tenLottery，未来加类型可以直接更新 kconf 和 lotteryBtnRelation，默认 oneLottery
    const btnType = computed(() => {
        return lotteryBtnRelation[btnIdx.value] ?? 'oneLottery';
    });

    const curTab = computed(() => {
        return lotteryNameRelation[lotteryBottomTabType.value ?? 0];
    });

    const curResource = computed(() => {
        const { lotteryResources } = configStore?.dataConfig;
        return lotteryResources?.[curDrawType.value] ?? {};
    });

    const checkBalance = () => {
        const needCount =
            btnList.value?.find((btn) => btn.ruleId === currentId.value)
                ?.coinCount || Infinity;
        console.log(balance.value, needCount);

        if (balance.value < needCount) {
            // emit('not-enough');
            mainStore.busEvent.emit(COMMON_EVENT.noEnoughBalance);

            return false;
        }

        return true;
    };

    const getResult = async () => {
        try {
            pageStatusPop.loading = true;
            pageStatusPop.error = false;
            const entry_src = route.query?.entry_src ?? '';
            let prizeSource = '';
            if (Array.isArray(entry_src)) {
                prizeSource = entry_src[0] ?? '';
            } else {
                prizeSource = entry_src;
            }
            const prizeStyle = +lotteryBottomTabType.value + 1;
            const res = await drawReward(
                prizeSource,
                prizeStyle,
                currentId.value,
            );
            subTitle.value = res?.subTitle || '';
            resultList.value = res?.awards;

            if (res?.toast) {
                // eslint-disable-next-line @typescript-eslint/no-throw-literal
                throw { data: { error_msg: res.toast } };
            }

            if (!resultList.value?.length) {
                // 没有返回奖励数据，则未中奖 TODO需要替换文案
                // eslint-disable-next-line @typescript-eslint/no-throw-literal
                throw {
                    data: {
                        error_msg:
                            configStore.dataConfig?.lotteryToast ||
                            '网络出错，请重试',
                    },
                };
            }
            needFillAddress.value = res?.needFillAddress;
            needJumpBackPack.value = res?.needJumpBackPack;
            needJumpWallet.value = res?.needJumpWallet;
            pageStatusPop.loading = false;
            needSendEmoticon.value = res?.needSendEmoticon;
            enableShowEmoticonTab.value = res?.enableShowEmoticonTab;

            return DrawResult.success;
        } catch (err: any) {
            const { error_msg, result } = err?.data || {};
            pageStatusPop.error = true;
            pageStatusPop.loading = false;

            if (result === 81381) {
                // 表示余额不足，需要自动定位到任务
                // emit('not-enough');
                mainStore.busEvent.emit(COMMON_EVENT.noEnoughBalance);
            } else {
                error_msg && Toast.info(error_msg);
                !error_msg && Toast.info('网络出错，请重试');
            }

            isPlaying.value = false;
            btnStatus.value = BTN_STATUS.init;

            return DrawResult.error;
        }
    };

    const drawAward = async (ruleId: number, btnIndex: number) => {
        // 检查抽奖中
        if (
            btnStatus.value !== BTN_STATUS.init ||
            pageStatusPop.loading ||
            isPlaying.value
        ) {
            Toast.info('正在抽奖中');
            return;
        }
        // 检查余额
        currentId.value = ruleId;
        btnIdx.value = btnIndex as keyof typeof lotteryBtnRelation;

        if (!checkBalance()) {
            return;
        }
        btnStatus.value = BTN_STATUS.lucking;
        drawBtnClick(btnIndex, lotteryBottomTabType.value, curTab.value);
        // 开始播放动画
        if (mainStore.isLowDev || !configStore.dataConfig.lotterySwitch) {
            const res = await awardRefresh();
            mainStore.busEvent.emit(DRAW_EVENT.showResultPop, {
                isEmpty: false,
            });
            isPlaying.value = false;
        } else {
            isPlaying.value = true;
        }
        // 1. 如果是透明视频，需要接口请求完打开弹窗
    };

    const awardRefresh = async () => {
        // 请求抽奖接口
        const res = await getResult();
        btnStatus.value = BTN_STATUS.init;
        return res;
    };

    // 获取老虎机配置
    const init = async () => {
        try {
            pageStatus.loading = true;
            const res = await getBoxConfig();
            pageStatus.error = false;
            const data = res;
            boxConfig.value = data ?? {};
            balance.value = data?.balance ?? 0;
        } catch (error: any) {
            if (error?.data?.result === 109) {
                mainStore.busEvent.emit(COMMON_EVENT.noLogin);
            }
            console.error(' error:', error);
            pageStatus.error = true;

            //    自定义埋点，初始化面板失败，基本是接口原因，将接口传入的相关参数作为信息上报
            Report.biz.error('【扭蛋机】：初始化面板失败', {
                error,
            });
        }
        pageStatus.loading = false;
        mainStore.busEvent.emit(COMMON_EVENT.sendFmp);
    };

    const updateConfig = async () => {
        try {
            const res = await getBoxConfig();

            if (res) {
                boxConfig.value = res;
            }
        } catch (error) {
            console.error('update error:', error);
        }
    };

    const dealPrize = (giftObj: any, ind: number) => {
        return {
            ...giftObj,
            ind,
        };
    };
    const giftsList = ref<any[]>([]);
    const btnList = ref<BtnRule[]>([]);

    // TODO: 接口确定后需要修改
    watch(
        () => ({
            lotteryBottomTabType: lotteryBottomTabType.value,
            boxConfig: boxConfig.value,
        }),
        () => {
            const { marblesPannel, scratchOffPanel, blindPannel } =
                boxConfig.value;
            const defaultPanel: BoxConfigInfo['marblesPannel'] = {
                prizeList: [],
                btnList: [],
            };
            let curPanel: BoxConfigInfo['marblesPannel'];
            switch (lotteryBottomTabType.value) {
                case lotteryTabType.marbles:
                    curPanel = marblesPannel ?? defaultPanel;
                    break;
                case lotteryTabType.scratchOff:
                    curPanel = scratchOffPanel ?? defaultPanel;
                    break;
                case lotteryTabType.blindBox:
                    curPanel = blindPannel ?? defaultPanel;
                    break;
                default:
                    curPanel = defaultPanel;
                    break;
            }
            // 修复历史bug
            if (!curPanel.prizeList) {
                curPanel.prizeList = [];
            }
            if (curPanel.prizeList?.length < 5) {
                curPanel.prizeList = [
                    ...curPanel.prizeList,
                    ...new Array(5 - curPanel.prizeList.length).fill({
                        id: -10086,
                        name: '奖品',
                        count: 1,
                        icon: configStore.dataConfig.giftEmptyIcon,
                    }),
                ];
            }
            giftsList.value = curPanel.prizeList?.map(dealPrize);
            // giftsList.value = prizeInfoList.map(dealPrize);
            btnList.value = curPanel.btnList;
        },
        { immediate: true },
    );

    const posterList = computed(() => {
        const posters = lotteryOrder.map((it) => ({
            poster:
                configStore?.dataConfig?.lotteryResources?.[it]?.poster ?? '',
            key: it,
        }));
        return posters;
    });

    watch(
        () => curResource.value,
        (val) => {
            curPoster.value = val?.poster ?? '';
        },
    );

    const curGeneralVideos = computed(() => {
        // 抽一次 1，3 单抽 2，4双抽
        if (isPlaying.value) {
            if (currentId.value % 2 === 1) {
                //  TODO 这里也需要改成 btnType，但本着最少改动最稳定的原则，后面再改吧，改了之后基本就是配置 kconf 就能满足扩展需求了
                return curResource.value?.oneLottery?.general;
                // 抽十次
            }
            // TODO 还有这里
            return curResource.value?.tenLottery?.general;
        }
        return [];
    });

    const curTransparentVideos = computed(() => {
        // 抽一次
        if (isPlaying.value) {
            if (currentId.value % 2 === 1) {
                //  TODO 还有这里
                return curResource.value?.oneLottery?.transparent || [];
                // 抽十次
            }
            //  TODO 还有这里
            return curResource.value?.tenLottery?.transparent || [];
        }
        return [];
    });

    //  播放第几个音频，音频资源的数组索引
    const audioIdx = computed(() => {
        if (curDrawType.value === 'marbles') {
            //  弹珠机抽1次/10次都各有两个音频，交替播放
            return randomCount.value % curGeneralVideos.value?.length;
        } //  其他抽1次/10次各有一个音频
        return 0;
    });

    // 获取随机url
    const randomGeneralLink = computed(() => {
        // 随机逻辑先写死，依赖状态太多
        let videoUrl = '';
        if (!configStore.dataConfig.lotterySwitch) return videoUrl;
        if (curGeneralVideos.value?.length > 1) {
            // %表示当前随机到哪个视频，现在是ababab方案播放
            const curCount = randomCount.value % curGeneralVideos.value?.length;
            videoUrl = curGeneralVideos.value[curCount] || '';
            randomCount.value++;
        } else {
            videoUrl = curGeneralVideos.value?.[0] || '';
        }
        return videoUrl;
    });

    const randomTransparentLink = computed(() => {
        let videoUrl = '';
        if (!configStore.dataConfig.lotterySwitch) return videoUrl;
        if (curTransparentVideos.value?.length > 1) {
            randomCount.value =
                (randomCount.value % curTransparentVideos.value?.length) + 1;
            videoUrl = curTransparentVideos.value[randomCount.value] || '';
        } else if (curTransparentVideos.value?.length === 1) {
            videoUrl = curTransparentVideos.value?.[0] || '';
        }
        return videoUrl;
    });

    const updateBalance = async (val?: number) => {
        try {
            if (typeof val === 'number') {
                balance.value = val;
            } else {
                const res = await getBalance();
                balance.value = res?.balance || 0;
            }
        } catch (error) {
            console.error('刷新余额失败');
        }
    };

    return {
        init,
        pageStatus,
        pageStatusPop,
        lotteryKey,
        btnStatus,
        lotteryBottomTabType,
        curPoster,
        posterList,
        curResource,
        curGeneralVideos,
        curTransparentVideos,
        isPlaying,
        randomGeneralLink,
        randomTransparentLink,
        balance,
        subTitle,
        resultList,
        needFillAddress,
        needJumpBackPack,
        needJumpWallet,
        currentId,
        drawAward,
        awardRefresh,
        getResult,
        checkBalance,
        updateBalance,
        updateConfig,
        boxConfig,
        btnList,
        giftsList,
        needSendEmoticon,
        enableShowEmoticonTab,
        audioContext,
        canPlayAudio,
        audioIdx,
        curTab,
        curDrawType,
        btnType,
    };
});

export default useBoxInfo;
