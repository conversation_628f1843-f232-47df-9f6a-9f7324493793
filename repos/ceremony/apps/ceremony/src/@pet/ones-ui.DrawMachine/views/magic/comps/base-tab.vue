<template>
    <div class="parallel-tab row-center gis-lottery-tab-bg">
        <div
            class="tab-bar gis-lottery-tab-select"
            :class="{
                'slide-l': curTab === lotteryTabType.marbles,
                'slide-m': curTab === lotteryTabType.scratchOff,
                'slide-r': curTab === lotteryTabType.blindBox,
            }"
        />
        <div
            v-click-log="{
                action: 'OP_ACTIVITY_LOTTERY_BOTTOM_TAB',
                params: {
                    tab_name: lotteryNameRelation[lotteryTabType.marbles],
                    cl_type: lotteryChangeEnum.tab,
                },
            }"
            class="row-center item"
            :class="{
                active: curTab === lotteryTabType.marbles,
            }"
            @click="handleChange(lotteryTabType.marbles)"
        >
            <div class="text text-16">
                {{ lotteryNameRelation[lotteryTabType.marbles] }}
            </div>
        </div>
        <div
            v-show="curTab === lotteryTabType.blindBox"
            class="border-pic border-l"
        />
        <div
            v-click-log="{
                action: 'OP_ACTIVITY_LOTTERY_BOTTOM_TAB',
                params: {
                    tab_name: lotteryNameRelation[lotteryTabType.scratchOff],
                    cl_type: lotteryChangeEnum.tab,
                },
            }"
            class="row-center item"
            :class="{
                active: curTab === lotteryTabType.scratchOff,
            }"
            @click="handleChange(lotteryTabType.scratchOff)"
        >
            <div class="text text-16">
                {{ lotteryNameRelation[lotteryTabType.scratchOff] }}
            </div>
        </div>
        <div
            v-show="curTab === lotteryTabType.marbles"
            class="border-pic border-r"
        />
        <div
            v-click-log="{
                action: 'OP_ACTIVITY_LOTTERY_BOTTOM_TAB',
                params: {
                    tab_name: lotteryNameRelation[lotteryTabType.blindBox],
                    cl_type: lotteryChangeEnum.tab,
                },
            }"
            class="row-center item"
            :class="{
                active: curTab === lotteryTabType.blindBox,
            }"
            @click="handleChange(lotteryTabType.blindBox)"
        >
            <div class="text text-16">
                {{ lotteryNameRelation[lotteryTabType.blindBox] }}
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import useBoxInfo from '../hooks/useBoxInfo';
import { DRAW_EVENT, BTN_STATUS } from '../../magic/config';
import { useMain } from '../../../store/useMain';
import {
    lotteryNameRelation,
    lotteryTabType,
    lotteryChangeEnum,
} from '../../../api/index';

export default defineComponent({
    props: {
        isPlaying: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, ctx) {
        const boxStore = useBoxInfo();
        const mainStore = useMain();
        const curTab = ref(lotteryTabType.marbles);

        mainStore.busEvent.on(DRAW_EVENT.tabChange, ({ id }) => {
            curTab.value = id;
            boxStore.lotteryBottomTabType = id;
        });

        const handleChange = (index: number) => {
            if (
                boxStore.btnStatus !== BTN_STATUS.init ||
                boxStore.pageStatusPop.loading ||
                boxStore.pageStatus.loading ||
                props.isPlaying
            ) {
                Toast.info('正在抽奖中');
                return;
            }
            console.log('切换奖池', index);
            curTab.value = index;
            mainStore.busEvent.emit(DRAW_EVENT.videoChange, {
                id: index,
            });
        };

        return {
            handleChange,
            curTab,
            lotteryNameRelation,
            lotteryTabType,
            lotteryChangeEnum,
        };
    },
});
</script>

<style lang="less" scoped>
.parallel-tab {
    box-sizing: border-box;
    position: relative;
    width: 222px;
    height: 34px;
    margin: auto;
    .border-pic {
        position: absolute;
        z-index: 2;
        top: 5.5px;
        width: 1px;
        height: 20px;
        background: url('../assets/tab_seg.png') center / 100% no-repeat;
    }
    .border-l {
        left: 73px;
    }
    .border-r {
        left: 147px;
    }
    .item {
        z-index: 1;
        padding: 4.5px 13px;
        height: 100%;
        color: transparent;
        .text {
            font-family: 'Alibaba-PuHuiTi-Bold';
            line-height: 22px;
            white-space: nowrap;
            // background: var(--tabColor, #fff);
            // background-clip: text;
            // -webkit-text-fill-color: transparent;
            transition: color 0.3s;

            @apply a-text-white;
        }
        &.active {
            .text {
                color: var(--tabActiveColor, #fff);

                @apply a-text-tab-solid-active;
                // background: var(--tabActiveColor, linear-gradient(74.15deg, #193cd6 15.57%, #6349ff 88.59%));
                // background-clip: text;
            }
        }
    }
    .tab-bar {
        position: absolute;
        transition: transform 0.3s;
        top: 1px;
        left: 0;
        &.slide-l {
        }
        &.slide-m {
            transform: translateX(74px);
        }
        &.slide-r {
            transform: translateX(148px);
        }
    }
}
</style>
