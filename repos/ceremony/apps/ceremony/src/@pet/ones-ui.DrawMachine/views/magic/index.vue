<template>
    <!-- 抽奖机 @wenxin -->
    <div class="lottery-main super">
        <div class="pic-title gis-machine-title layer-mid" />
        <!-- 顶部切换tab区域 -->
        <BaseTab
            v-if="dataConfig.isDoublePanel"
            class="panel-tab layer-mid"
            :is-playing="isPlaying"
        />
        <!-- 中奖记录跑马灯区域/奖品陈列区域 @wenxin -->
        <div class="award-wrap layer-mid">
            <AwardMarquee :gifts-list="giftsList" />
            <!-- 奖品陈列区域 @wenxin -->
            <ClientOnly>
                <RewardList :gifts-list="giftsList" />
            </ClientOnly>
        </div>

        <VideoKv :award-list="resultList" />
        <div class="account-wrap flex position-absolute">
            <div
                class="flex account-number layer-mid align-center text-din text-14 text-bold a-text-main"
            >
                <div class="icon-currency gis-icon-currency" />
                {{ balance ? `x${balance}` : 0 }}
            </div>
            <div
                v-show-log="funcLog.drawRecord"
                class="award-record-entry layer-mid flex-center text-12 a-text-main"
                @click="switchLotteryRecord(true)"
            >
                中奖记录 <SvgArrow class="icon-jt" />
            </div>
        </div>
        <BtnList class="btn-box layer-mid" :btn-list="btnList" />
        <ClientOnly>
            <ResultModal
                v-if="resultPopShow"
                :show="resultPopShow"
                :sub-title="subTitle"
                :page-status="pageStatusPop"
                :award-list="resultList"
                :need-fill-address="needFillAddress"
                :need-jump-back-pack="needJumpBackPack"
                :need-jump-wallet="needJumpWallet"
                :need-send-emoticon="needSendEmoticon"
                :enable-show-emoticon-tab="enableShowEmoticonTab"
                @close="switchResultPop(false)"
            />
        </ClientOnly>
        <RecordModal
            v-if="lotteryRecordVisible"
            :show="lotteryRecordVisible"
            :show-panel-icon="dataConfig.isDoublePanel"
            @close="switchLotteryRecord(false)"
        />
        <ClientOnly>
            <OnceModal
                v-if="configStore.showOnceModal"
                :show="configStore.showOnceModal"
                :show-panel-icon="dataConfig.isDoublePanel"
                @close="configStore.dataConfig.onceModal = false"
            ></OnceModal>
        </ClientOnly>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted } from 'vue';
import { storeToRefs } from 'pinia';
import { usePopAdapter } from '@pet/ones-use.usePopAdapter/index';
import { Toast } from '@lux/sharp-ui-next';
import { ClientOnly } from '@live/ssg';
import { ANav, ANavItem } from '@alive-ui/base';
import { Report } from '@alive-ui/actions';
import { useMain, COMMON_EVENT } from '../../store/useMain';
import { useConfigStore } from '../../store/config';
import RecordModal from '../../modules/popups/reward-modal.vue';
import ResultModal from '../../modules/popups/result-modal.vue';
import OnceModal from '../../modules/popups/once-modal.vue';
import { funcLog, sendClick } from '../../logger';
import AwardMarquee from '../../components/award-marquee.vue';
import SvgArrow from '../../components/arrow.vue';
import useBoxInfo from './hooks/useBoxInfo';
import { AudioOffset } from './hooks/audio';
import { DRAW_EVENT, BTN_STATUS } from './config';
import VideoKv from './comps/video-kv.vue';
import RewardList from './comps/reward-list.vue';
import BtnList from './comps/btn-list.vue';
import BaseTab from './comps/base-tab.vue';
import type { PropType } from 'vue';

const { adapter, recover } = usePopAdapter('left');
export default defineComponent({
    components: {
        VideoKv,
        BaseTab,
        AwardMarquee,
        RewardList,
        BtnList,
        ClientOnly,
        ResultModal,
        RecordModal,
        OnceModal,
        SvgArrow,
    },
    props: {
        source: {
            type: String as PropType<'coinModal' | ''>,
            default: '',
        },
        isLowDev: {
            type: Boolean,
            default: false,
        },
        cardSubTitle: {
            type: String,
            default: '',
            required: false,
        },
        cardSubTitleStyle: {
            type: Object,
            default: () => ({}),
            required: false,
        },
    },
    setup(props) {
        const configStore = useConfigStore();
        const { dataConfig } = storeToRefs(configStore);

        const mainStore = useMain();
        const boxStore = useBoxInfo();
        const {
            lotteryKey,
            giftsList,
            isPlaying,
            btnStatus,
            btnList,
            balance,
            subTitle,
            resultList,
            needFillAddress,
            needJumpBackPack,
            needJumpWallet,
            pageStatusPop,
            needSendEmoticon,
            enableShowEmoticonTab,
            canPlayAudio,
        } = storeToRefs(boxStore);

        let audioTimer: ReturnType<typeof setTimeout>;
        const { drawRecord } = funcLog;

        const initBox = () => {
            boxStore.init();
            // boxStore.resetAnim();
        };
        initBox();

        onMounted(() => {
            configStore.setVisitImg();
        });

        onUnmounted(() => {
            audioTimer && clearTimeout(audioTimer);
        });

        if (props.isLowDev) {
            mainStore.isLowDev = true;
        }
        const lotteryRecordVisible = ref(false);

        const switchLotteryRecord = (status: boolean) => {
            sendClick(drawRecord);
            setTimeout(() => {
                adapter();
            }, 0);
            lotteryRecordVisible.value = status;
            if (!status) {
                recover();
            }
        };

        const resultPopShow = ref(false);

        const switchResultPop = (status: boolean) => {
            setTimeout(() => {
                adapter();
            }, 0);
            resultPopShow.value = status;
            if (!status && canPlayAudio.value) {
                try {
                    audioTimer = setTimeout(() => {
                        boxStore.audioContext?.pause();
                    }, AudioOffset * 1000);
                } catch (e) {
                    Report.biz.error('【扭蛋机】：暂停音频报错', { e });
                }
            }
            if (!status) {
                recover();
            }
        };

        mainStore.busEvent.on(COMMON_EVENT.updateBallance, (val: number) => {
            boxStore.updateBalance(val);
        });

        mainStore.busEvent.on(
            DRAW_EVENT.showResultPop,
            (data?: { isEmpty: boolean }) => {
                switchResultPop(true);

                // 更新余额
                boxStore.updateBalance();
                //  只有中奖才更新 updateMarquee
                if (!data?.isEmpty) {
                    mainStore.busEvent.emit(DRAW_EVENT.updateMarquee);
                }
            },
        );

        mainStore.busEvent.on(DRAW_EVENT.showEmpty, () => {
            switchResultPop(true);
        });

        return {
            configStore,
            dataConfig,
            lotteryKey,
            giftsList,
            btnList,
            balance,
            isPlaying,
            resultPopShow,
            lotteryRecordVisible,
            switchLotteryRecord,
            switchResultPop,
            subTitle,
            resultList,
            needFillAddress,
            needJumpBackPack,
            needJumpWallet,
            needSendEmoticon,
            enableShowEmoticonTab,
            funcLog,
            pageStatusPop,
        };
    },
});
</script>

<style lang="less" scoped>
.lottery-main {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 414px;
    height: var(--kvBgHeight);
    margin-bottom: var(--headBgMarginBottom, -48px);
    // .page-header-component {
    //   background: var(--pageHeaderBgColor, linear-gradient(180deg, #121a6a 50%, #d6b4fd 90%));
    // }
}
.layer-mid {
    z-index: 1;
}
.pic-title {
    position: absolute;
    top: 52px;
    left: 50%;
    transform: translateX(-50%);
}
.award-wrap {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
    width: 268px;
    // height: 106px;
    top: 138px;
    background: rgba(137, 145, 227, 0.2);
    border-radius: 20px;
    :deep(.award-marquee) {
        width: 252.92px;
    }
    :deep(.record-list-wrap) {
    }
}
.panel-tab {
    position: absolute;
    top: var(--panelTabTop, 91px);
    left: 50%;
    transform: translateX(-50%);
}
.price-box {
    position: absolute;
    top: var(--priceBoxTop, 227px);
    left: 50%;
    transform: translateX(-50%);
}
.btn-box {
    position: absolute;
    justify-content: center;
    top: 573px;
    // left: var(--btnBoxLeft, 73px);
}
.account-wrap {
    justify-content: space-between;
    padding: 0 29px;
    top: 538px;
    width: 318px;
    height: 26px;
    background: url('./assets/account_record_bg.png') center/100% no-repeat;
}
.account-number {
    bottom: var(--coinBottom, 66px);
    left: var(--coinLeft, 75px);
    line-height: 19px;
    text-align: center;
}
.icon-currency {
    width: 15px;
    height: 15px;
    margin-right: 4px;
}
.award-record-entry {
    right: var(--entryRight, 50px);
    bottom: var(--entryBottom, 67px);
    padding-bottom: 1.5px;
    line-height: 17px;
    text-align: left;
}
.icon-jt {
    width: 10px;
    height: 10px;
}

.pic-sub-title {
    position: absolute;
    top: 88px;
    left: 50%;
    width: 256px;
    height: 26px;
    line-height: 26px;

    @apply a-text-main;

    text-align: center;
    transform: translateX(-50%);
}
.fixed-bottom-wrap {
    position: fixed;
    bottom: -1px;
}
</style>
