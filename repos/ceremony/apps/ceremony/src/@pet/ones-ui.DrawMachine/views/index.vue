<template>
    <div class="luck-bag position-relative draw-lottery">
        <IconWidget
            v-if="dataConfig && dataConfig.ruleLink"
            v-show-log="funcLog.rule"
            v-click-log="funcLog.rule"
            type="rule"
            position="right-top"
            @click="goRule(dataConfig.ruleLink)"
        />
        <Magic />
        <div class="machine-bottom-content">
            <slot name="center-content" />
            <TaskArea id="task-area" :need-daily-task="needDailyTask" />
        </div>
    </div>
</template>

<script lang="ts">
import { useRoute } from 'vue-router';
import { defineComponent, watch, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import IconWidget from '@pet/ones-ui.IconWidget/index.vue';
import { Toast } from '@lux/sharp-ui-next';
import { layoutType, Report } from '@alive-ui/actions';
import config from '../variable/config.json';
import { useMain, COMMON_EVENT } from '../store/useMain';
import { useConfigStore } from '../store/config';
import { funcLog } from '../logger';
import TaskArea from './task/task-area.vue';
import Magic from './magic/index.vue';
import useBoxInfo from './magic/hooks/useBoxInfo';
import { DRAW_EVENT } from './magic/config';

export default defineComponent({
    components: {
        Magic,
        TaskArea,
        IconWidget,
    },
    props: {
        dataConfig: {
            type: Object,
            default: () => {
                return config;
            },
        },
        needDailyTask: {
            type: Boolean,
            default: true,
        },
        forceLogin: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        const mainStore = useMain();
        const configStore = useConfigStore();
        const { dataConfig } = storeToRefs(configStore);
        const route = useRoute();
        const boxInfo = useBoxInfo();

        watch(
            () => props.dataConfig,
            () => {
                configStore.updateConfig(props.dataConfig);
                //  未来增加类型时只需要扩充这里就可以，不需要修改任何其他逻辑
                const audioSources = props.dataConfig?.lotteryResources ?? {
                    marbles: {
                        oneLottery: {
                            audio: [],
                        },
                        tenLottery: {
                            audio: [],
                        },
                    },
                    scratchOff: {
                        oneLottery: {
                            audio: [],
                        },
                        tenLottery: {
                            audio: [],
                        },
                    },
                    blindBox: {
                        oneLottery: {
                            audio: [],
                        },
                        tenLottery: {
                            audio: [],
                        },
                    },
                };
                boxInfo.audioContext.batchPreloadAudio(audioSources);
            },
            {
                immediate: true,
            },
        );

        mainStore.busEvent.on(COMMON_EVENT.noLogin, () => {
            if (props.forceLogin) {
                emit('nologin');
            }

            //    上报未登录，页面不会展示抽奖奖品和任务列表，用户无法抽奖和做任务，符合预期，因此这里只做统计，打业务warning点
            Report.biz.warning('【扭蛋机】：用户未登录', {});
        });

        //  金币不足，定位到任务模块
        onMounted(() => {
            const anchor = route.query?.anchor;
            if (anchor === 'task') {
                toTask();
            }
        });
        const toTask = () => {
            Toast.info(`${dataConfig.value.coinName}不足，快去做任务获取吧~`);
            const taskAreaDis =
                document
                    ?.querySelector?.('#task-area')
                    ?.getBoundingClientRect?.()?.top || 652;

            window.scrollBy({
                top: taskAreaDis,
                behavior: 'smooth',
            });
        };
        mainStore.busEvent.on(COMMON_EVENT.noEnoughBalance, toTask);

        mainStore.busEvent.on(COMMON_EVENT.taskBtnClick, (data) => {
            emit('handleTaskBtn', data.item, data.refresh);
        });

        const goRule = (link: string) => {
            location.href = `${link}&layoutType=${layoutType}`;
        };

        mainStore.busEvent.on(COMMON_EVENT.sendFmp, () => {
            emit('fmp');
        });

        return {
            goRule,
            funcLog,
        };
    },
});
</script>

<style lang="less" scoped>
/deep/ .rotate90 {
    transform: rotate(90deg);
}

.machine-bottom-content {
    // position: relative;
}
.luck-bag {
    min-height: 100vh;
    padding-bottom: 60px;

    .lottery {
        z-index: 999;
    }

    .draw-left {
        top: 309px;
        left: 12px;
    }

    .gis-icon-gz {
        top: 50px;
        right: 14px;
        z-index: 9;
    }
}
</style>
