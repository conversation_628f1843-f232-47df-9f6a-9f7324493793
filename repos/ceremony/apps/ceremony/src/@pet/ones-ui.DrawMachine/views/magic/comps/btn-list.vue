<template>
    <div class="flex draw-button position-absolute">
        <div
            v-for="(item, index) in btnList"
            :key="index"
            class="draw-btn-item"
            :class="[
                `gis-luck-task-${index + 1}`,
                {
                    'press-animation': showPress && item.ruleId === currentId,
                },
            ]"
            @click="boxStore.drawAward(item.ruleId, index)"
        >
            <div class="draw-btn-icon" :class="BtnTextPic[item.ruleId]" />
            <div class="draw-btn-text">消耗{{ item.coinCount }}金币</div>
            <div
                v-show-log="
                    drawBtnLog(index, lotteryBottomTabType, boxStore.curTab)
                "
                class="draw-ten"
            />
            <div class="sub-text">
                <span class="inline-block scale-font83" />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useBoxInfo } from '../hooks/useBoxInfo';
import { BTN_STATUS } from '../config';
import { drawBtnLog } from '../../../logger';
import { BtnTextPic } from '../../../api';
import type { PropType } from 'vue';
import type { BtnRule } from '../../../api';

export default defineComponent({
    props: {
        btnList: {
            type: Array as PropType<BtnRule[]>,
            default: () => [],
        },
    },
    setup() {
        const boxStore = useBoxInfo();
        const { btnStatus, lotteryBottomTabType, currentId } =
            storeToRefs(boxStore);

        const showPress = ref(false);

        watch(
            () => btnStatus.value,
            () => {
                if (btnStatus.value === BTN_STATUS.lucking) {
                    showPress.value = true;

                    setTimeout(() => {
                        showPress.value = false;
                    }, 600);
                }
            },
        );

        return {
            boxStore,
            showPress,
            lotteryBottomTabType,
            drawBtnLog,
            currentId,
            BtnTextPic,
        };
    },
});
</script>

<style lang="less" scoped>
.draw-button {
    .draw-btn-item {
        position: relative;
        display: flex;
        justify-content: center;
        .draw-btn-icon {
            position: absolute;
            top: 14px;
            height: 33px;
        }
        .draw-btn-marbles-1 {
            left: 43px;
            width: 58px;
            background: url(../assets/btn-marbles-1.png) center / 100% no-repeat;
        }
        .draw-btn-marbles-10 {
            left: 50px;
            width: 71px;
            background: url(../assets/btn-marbles-10.png) center / 100%
                no-repeat;
        }
        .draw-btn-scratch-1 {
            left: 43px;
            width: 58px;
            background: url(../assets/btn-scratch-1.png) center / 100% no-repeat;
        }
        .draw-btn-scratch-10 {
            left: 50px;
            width: 71px;
            background: url(../assets/btn-scratch-10.png) center / 100%
                no-repeat;
        }
        .draw-btn-blind-1 {
            left: 43px;
            width: 58px;
            background: url(../assets/btn-blind-1.png) center / 100% no-repeat;
        }
        .draw-btn-blind-10 {
            left: 50px;
            width: 71px;
            background: url(../assets/btn-blind-10.png) center / 100% no-repeat;
        }
        .draw-btn-text {
            position: absolute;
            top: 43px;
            font-size: 10px;
            line-height: 14px;
            color: #6c423b;
        }
    }
    .gis-luck-task-1 {
        .draw-btn-text {
            margin-right: 13px;
        }
    }
    .gis-luck-task-2 {
        .draw-btn-text {
            margin-left: 13px;
        }
    }
    // .luck-btn-1{
    //     width: 127px;
    //     height: 61px;
    //     margin-right: 16px;
    //     background: url('../../../variable/assets/luck-task-1_2x.png') center / 100% no-repeat
    // }
    // .luck-btn-2{
    //     width: 127px;
    //     height: 61px;
    //     background: url('../../../variable/assets/luck-task-2_2x.png') center / 100% no-repeat
    // }
}

@keyframes scale2D {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
    }
}
.press-animation {
    animation: scale2D 500ms ease-in-out forwards;
    // animation: scale2D 500ms ease-in-out forwards;
}
</style>
