/* eslint-disable @typescript-eslint/no-explicit-any */

export enum CircleState {
    'renderBefore' = 'renderBefore',
    'renderStart' = 'renderStart',
    'endFrame' = 'endFrame',
    'rotateEnd' = 'rotateEnd',
    'render' = 'render',
    'downFrame' = 'downFrame',
}

export enum DRAW_EVENT {
    'showResultPop' = 'showResultPop',
    'showEmpty' = 'showEmpty',
    'updateMarquee' = 'updateMarquee',
    'animEndDelay' = 'animEndDelay',
    'tabChange' = 'tabChange',
    'videoChange' = 'videoChange',
}

export interface Rate {
    beginCir: number;
    endCir: number;
    frame: number;
}

export enum BTN_STATUS {
    'init' = 1,
    'lucking' = 2,
    'stop' = 3,
}

export enum BoxSpeedType {
    slow = 1,
    normal = 2,
    fast = 3,
}

export const giftsNum = 9; // 列表中礼品的个数;
