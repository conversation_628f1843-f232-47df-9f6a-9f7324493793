<template>
    <div class="page-header-component">
        <!-- 展示Poster TODO: 透明视频低数据模式会有长时间黑屏思考优化方案-->
        <div
            v-if="posterList"
            v-show="
                !isPlaying || (isPlaying && showPoster && randomTransparentLink)
            "
            class="lottery-pic-swiper-wrap"
        >
            <!-- 箭头-左 -->
            <div
                class="lottery-icon-wrap lottery-arrow-left"
                @click="prevPoster"
            >
                <div class="lottery-icon-arrow" />
            </div>
            <sp-swiper
                ref="lSwiperRef"
                :init-index="lotteryTabType.marbles"
                loop
                is-stop-propagation
                :touchable="!isPlaying"
                :follow-config="{
                    isFollow: true,
                }"
                :indicator-show="false"
                @change="lSwiperChange"
            >
                <div
                    v-for="item in posterList"
                    :key="item.key"
                    class="video-img"
                    :style="{
                        background: `url(${item?.poster}) center/100% no-repeat`,
                    }"
                ></div>
            </sp-swiper>
            <!-- 箭头-右 -->
            <div
                class="lottery-icon-wrap lottery-arrow-right"
                @click="nextPoster"
            >
                <div class="lottery-icon-arrow" />
            </div>
        </div>
        <!-- 播放普通视频 -->
        <div v-if="playGeneral" class="video-img playing">
            <!-- 播放音频 -->
            <VideoPlay
                ref="kvPlayer"
                class="video-play-class"
                not-click
                :poster-url="curPoster"
                :loop="false"
                :video-url="[{ url: randomGeneralLink }]"
                :autoplay="true"
                :show-last-frame="true"
                @video-end="videoEnd"
                @video-play="videoPlay"
                @play-error="playerError"
            />
        </div>
        <!-- 播放透明视频-->
        <div v-show="randomTransparentLink" class="draw-transparent-wrap">
            <div
                v-show="transparentResult && !showPoster"
                class="reward-img-wrap"
            >
                <div
                    class="reward-img"
                    :style="{
                        backgroundImage: `url(${transparentResult})`,
                    }"
                ></div>
            </div>
            <TransparentVideo
                v-if="randomTransparentLink"
                class="draw-transparent-video"
                :width="828"
                :height="1340"
                :poster-url="curPoster"
                :playing="playTransparent"
                :video-url="randomTransparentLink"
                :render-when-loaded="true"
                @video-start="videoPlay"
                @video-ended="videoEnd"
                @video-loaded="playerLoaded"
                @error="playerError"
                @video-renderer-error="playerError"
            />
        </div>
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    ref,
    onUnmounted,
    watch,
    computed,
    nextTick,
} from 'vue';
import { storeToRefs } from 'pinia';
import { Swiper, Toast } from '@lux/sharp-ui-next';
import { Report } from '@alive-ui/actions';
import { UDevice } from '@ad/utils';
import { useBoxInfo, DrawResult } from '../hooks/useBoxInfo';
import { DRAW_EVENT, BTN_STATUS } from '../config';
import { useMain, COMMON_EVENT } from '../../../store/useMain';
import { sendTabLog, sendClick } from '../../../logger/index';
import VideoPlay from '../../../components/video-play.vue';
import TransparentVideo from '../../../components/transparent-video/index.vue';
import {
    lotteryChangeEnum,
    lotteryNameRelation,
    lotteryTabType,
} from '../../../api/index';
import type { PropType } from 'vue';
import type {
    AwardItem,
    lotteryTabType as LotteryTabType,
} from '../../../api/index';

export default defineComponent({
    components: {
        VideoPlay,
        TransparentVideo,
    },
    props: {
        awardList: {
            type: Array as PropType<AwardItem[]>,
            default: () => [],
        },
    },
    // eslint-disable-next-line max-lines-per-function
    setup(props, ctx) {
        const kvPlayer = ref();
        const mainStore = useMain();
        const boxStore = useBoxInfo();

        const {
            isPlaying,
            curPoster,
            randomGeneralLink,
            randomTransparentLink,
            canPlayAudio,
            curDrawType,
            audioIdx,
            btnType,
            posterList,
        } = storeToRefs(boxStore);

        const { isLowDev } = storeToRefs(mainStore);

        let timer: ReturnType<typeof setTimeout>;
        const lSwiperRef = ref();

        const transparentLoaded = ref(false);
        const playTransparent = ref(false);
        const playGeneral = ref(false);
        const showPoster = ref(true);

        const beforeVisible = ref(true);

        const changeType = ref(lotteryChangeEnum.slide);

        const changeVideo = (event: { id: LotteryTabType }) => {
            // 不需要上报change埋点
            changeType.value = lotteryChangeEnum.tab;
            lSwiperRef.value?.to(event.id);
        };

        mainStore.busEvent.on(DRAW_EVENT.videoChange, changeVideo);

        const disableChange = () => {
            if (
                boxStore.btnStatus !== BTN_STATUS.init ||
                boxStore.pageStatusPop.loading ||
                boxStore.pageStatus.loading ||
                isPlaying.value
            ) {
                Toast.info('正在抽奖中');
                return true;
            }
            return false;
        };

        const lSwiperChange = (index: LotteryTabType) => {
            console.log('change video');
            const tab_name = lotteryNameRelation[index];
            if (changeType.value !== lotteryChangeEnum.tab) {
                sendClick(sendTabLog(tab_name, changeType.value));
            }
            mainStore.busEvent.emit(DRAW_EVENT.tabChange, {
                id: index,
            });
            changeType.value = lotteryChangeEnum.slide;
        };

        const prevPoster = () => {
            if (disableChange()) return;
            changeType.value = lotteryChangeEnum.arrow;
            lSwiperRef.value?.prev();
        };

        const nextPoster = () => {
            if (disableChange()) return;
            changeType.value = lotteryChangeEnum.arrow;
            lSwiperRef.value?.next();
        };

        const iosChangeScreen = () => {
            if (
                document.visibilityState === 'visible' &&
                !beforeVisible.value &&
                isPlaying.value
            ) {
                console.log('visible!!!');
                beforeVisible.value = true;
                videoEnd();
            } else if (document.visibilityState === 'hidden') {
                console.log('hidden!!!');
                beforeVisible.value = false;
            }
        };
        // 处理IOS播放过程中切到屏幕外再切回来视频播放停止的场景
        UDevice?.isIOS() &&
            window.addEventListener('visibilitychange', iosChangeScreen);

        const videoPlay = () => {
            if (randomTransparentLink.value) {
                console.log('listen====play transparent');
                nextTick(() => {
                    showPoster.value = false;
                });
            }
            if (canPlayAudio.value) {
                try {
                    boxStore.audioContext?.play(
                        curDrawType.value,
                        btnType.value,
                        audioIdx.value,
                    );
                } catch (e) {
                    Report.biz.error('【扭蛋机】：开始播放音频报错', { e });
                }
            }
        };

        const transparentResult = computed(() => {
            if (props.awardList?.length > 0 && playTransparent.value) {
                return props.awardList[0]?.icon;
            }
            return '';
        });

        const videoEnd = () => {
            // 共用播放结束事件
            if (randomTransparentLink.value) {
                console.log('listen==== transparent end');
                // timer = setTimeout(() => {
                isPlaying.value = false;
                // timer && clearTimeout(timer);
                // }, 500);
                nextTick(() => {
                    openModal();
                });
            } else if (randomGeneralLink.value) {
                console.log('listen==== general end');
                // 处理普通视频 一秒后
                openModal();
                timer = setTimeout(() => {
                    // console.log(
                    //     'kvPlayer.value?.resetVideo(randomGeneralLink.value);',
                    //     kvPlayer,
                    // );
                    try {
                        kvPlayer.value?.destoryVideo();
                    } catch (e) {
                        Report.biz.error(
                            '【扭蛋机】：播放结束销毁普通视频报错',
                            { e },
                        );
                    }
                    timer && clearTimeout(timer);
                    isPlaying.value = false;
                }, 500);
            }
        };

        const openModal = () => {
            mainStore.busEvent.emit(DRAW_EVENT.showResultPop, {
                isEmpty: false,
            });
        };

        const playerError = () => {
            console.log('listen==== transparent error');
            openModal();
            isPlaying.value = false;
            randomGeneralLink.value && cancelGeneralVideo();
            randomTransparentLink.value && cancelTransparentVideo();
        };

        const playerLoaded = (bol: boolean) => {
            console.log('listen==== transparent loaded');
            transparentLoaded.value = true;
        };

        const transparentChange = computed(() => {
            if (
                randomTransparentLink.value &&
                transparentLoaded.value &&
                isPlaying.value
            ) {
                return true;
            }
            return false;
        });

        watch(
            () => [isPlaying.value, randomGeneralLink.value],
            (newchange, oldChange) => {
                if (newchange[0] === oldChange[0]) return;
                console.log('new change=', newchange);
                if (newchange[0] && newchange[1]) {
                    playGeneralVideo();
                } else {
                    cancelGeneralVideo();
                }
            },
        );

        watch(
            () => transparentChange.value,
            (newchange, oldChange) => {
                if (newchange === oldChange) return;
                if (newchange) {
                    playTransparentVideo();
                } else {
                    cancelTransparentVideo();
                }
            },
        );

        const playGeneralVideo = async () => {
            console.log('general video ulr=', randomGeneralLink.value);
            playGeneral.value = true;
            try {
                kvPlayer.value?.resetVideo(randomGeneralLink.value);
            } catch (e) {
                Report.biz.error('【扭蛋机】：开始播放重置普通视频报错', { e });
            }
            // 普通视频与接口调用无依赖关系
            const res = await boxStore.awardRefresh();
            if (res !== DrawResult.success) {
                isPlaying.value = false;
            }
        };

        const playTransparentVideo = async () => {
            console.log('transparent video ulr=', randomTransparentLink.value);
            // 透明视频和接口调用有依赖关系，先调用接口再播透明视频
            // 处理透明视频
            const res = await boxStore.awardRefresh();
            if (res !== DrawResult.success) {
                cancelTransparentVideo();
                isPlaying.value = false;
            } else {
                playTransparent.value = true;
            }
        };

        const cancelGeneralVideo = () => {
            console.log('cancel general');
            playGeneral.value = false;
            if (kvPlayer.value?.videoInstance) {
                kvPlayer.value.videoInstance.currentTime = 0;
            }
        };

        const cancelTransparentVideo = () => {
            console.log('cancel transparent');
            showPoster.value = true;
            playTransparent.value = false;
            transparentLoaded.value = false;
        };

        onUnmounted(() => {
            timer && clearTimeout(timer);
            UDevice?.isIOS() &&
                window.removeEventListener('visibilitychange', iosChangeScreen);
        });

        return {
            kvPlayer,
            isPlaying,
            curPoster,
            prevPoster,
            nextPoster,
            Swiper,
            lSwiperChange,
            disableChange,
            changeVideo,
            lotteryTabType,
            lSwiperRef,
            posterList,
            transparentLoaded,
            transparentChange,
            randomGeneralLink,
            randomTransparentLink,
            playGeneral,
            playTransparent,
            transparentResult,
            isLowDev,
            showPoster,
            changeType,
            lotteryChangeEnum,
            lotteryNameRelation,
            videoPlay,
            videoEnd,
            playerError,
            playerLoaded,
        };
    },
});
</script>

<style lang="less" scoped>
.page-header-component {
    position: relative;
    width: 414px;
    height: var(--kvBgHeight);
    .lottery-pic-swiper-wrap {
        :deep(.spu-swiper__item) {
            will-change: unset;
        }
    }
    .lottery-icon-wrap {
        position: absolute;
        z-index: 1;
        top: 371px;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .lottery-icon-arrow {
        width: 10px;
        height: 18px;
        background: url(../assets/lottery_icon_arrow.png) center/100% no-repeat;
    }
    .lottery-arrow-left {
        left: 0;
    }
    .lottery-arrow-right {
        right: 0;
        transform: rotateY(180deg);
    }
    .video-img {
        // position: absolute;
        width: 100%;
        height: var(--kvBgHeight);
        :deep(.video-play) {
            height: var(--kvBgHeight);
            padding: 0;
        }
    }
    .playing {
        position: absolute;
        top: -0.1px;
        left: 0;
    }
    .reward-img-wrap {
        position: absolute;
        z-index: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #7c6f6e;
        top: 395px;
        left: 50%;
        width: 150px;
        height: 150px;
        transform: translate(-50%, -50%);
    }
    .reward-img {
        background-size: 100% 100%;
        width: 100px;
        height: 100px;
    }
    .draw-transparent-wrap {
        position: relative;
        width: 100%;
        height: var(--kvBgHeight);
    }
    .draw-transparent-video {
        position: absolute;
        // top: 50%;
        top: 0;
        right: 0;
        left: 0;
        width: 414px;
        height: var(--kvBgHeight);
        margin: 0 auto;
        transform: translateZ(-500px);
        will-change: transform;
        :deep(video) {
            will-change: transform;
        }
    }
}
</style>
