// eslint-disable-next-line @typescript-eslint/no-explicit-any
const reduceCallback = (result: any[], item: any) => {
    if (item.count) {
        for (let i = 0; i < item.count; i++) {
            result.push(item);
        }
    } else {
        result.push(item);
    }

    return result;
};

const shuffleCArray = <T>(array: T[]): T[] => {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }

    return array;
};

export const genShuffledResult = <T>(array: T[]): T[] => {
    const flatArr = array.reduce(reduceCallback, []);

    return shuffleCArray(flatArr);
};
