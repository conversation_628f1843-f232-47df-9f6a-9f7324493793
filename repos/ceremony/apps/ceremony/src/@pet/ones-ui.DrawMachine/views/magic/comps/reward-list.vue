<template>
    <div v-if="giftsList && giftsList.length > 0" class="swiper">
        <div class="swiper-wrapper">
            <div
                v-for="item in giftsList"
                :key="item.name + item.ind + changeCount"
                class="swiper-slide"
            >
                <div class="swiper-item-content">
                    <div
                        v-if="item.icon"
                        class="swiper-icon"
                        :style="{
                            background: `url(${item.icon}) center/100% no-repeat`,
                        }"
                    ></div>
                    <div class="swiper-name">{{ item.name }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { Autoplay, FreeMode } from 'swiper/modules';
import Swiper from 'swiper';
import 'swiper/css';
import 'swiper/css/autoplay';
import 'swiper/css/free-mode';
import useBoxInfo from '../hooks/useBoxInfo';
import { useMain } from '../../../store/useMain';
import type { PropType } from 'vue';

export default defineComponent({
    components: {},
    props: {
        giftsList: {
            type: Array as PropType<Record<string, any>[]>,
            default: () => [],
        },
    },
    setup(props) {
        const mySwiper = ref<any>(null);

        const changeCount = ref(0);

        const timer = ref<ReturnType<typeof setTimeout>>();

        const boxStore = useBoxInfo();

        // 顶部tab和视频播放均有动画，会修改lotteryBottomTabType的值；使用事件会重复触发
        watch(
            () => boxStore.lotteryBottomTabType,
            (val, old) => {
                if (val === old) return;
                changeCount.value++;
                // 第一个参数：设为false则不销毁Swiper对象，默认为true
                // 第二个参数：设为true则清除所有swiper设定选项和样式，比如direction等，默认为false
                mySwiper.value?.destroy(true, true);
                mySwiper.value = null;
                timer.value && clearTimeout(timer.value);
                timer.value = setTimeout(() => {
                    createSwiperGenerator();
                }, 300);
            },
        );

        onMounted(() => {
            // 介绍：https://www.swiper.com.cn/api/autoplay/19.html
            createSwiperGenerator();
        });

        const createSwiperGenerator = () => {
            mySwiper.value = new Swiper('.swiper', {
                modules: [Autoplay, FreeMode],
                slidesPerView: 'auto',
                loop: true,
                spaceBetween: 0,
                observer: true,
                autoplay: {
                    delay: 0,
                    // waitForTransition: false,
                },
                // slideToClickedSlide: true,
                speed: 200 * props.giftsList?.length || 10000,
                freeMode: true,
                allowTouchMove: false,
                preventInteractionOnTransition: true,
            });
        };

        onBeforeUnmount(() => {
            clearTimeout(timer.value);
            mySwiper.value?.destroy(true, true);
            mySwiper.value = null;
        });

        return {
            changeCount,
        };
    },
});
</script>
<style lang="less" scoped>
.swiper {
    box-sizing: border-box;
    width: 258px;
    height: 67px;
    padding: 0 8px;
    margin-bottom: 6px;
    .swiper-wrapper {
        -webkit-transition-timing-function: linear; /*之前是ease-out*/
        -moz-transition-timing-function: linear;
        -ms-transition-timing-function: linear;
        -o-transition-timing-function: linear;
        transition-timing-function: linear;
    }
    .swiper-slide {
        width: 56px;
        height: 67px;
        margin-right: 5px;
        background: rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        transform: unset;
        backface-visibility: unset;
        .swiper-item-content {
            position: relative;
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            align-items: center;
            justify-content: center;
            .swiper-icon {
                margin-top: 9px;
                margin-bottom: 4px;
                width: 40px;
                height: 40px;
            }
            .swiper-name {
                min-width: 12px;
                color: #fff;
                font-size: 8px;
                line-height: 11.2px;
                text-align: center;
                white-space: nowrap;
            }
        }
    }
}
</style>
