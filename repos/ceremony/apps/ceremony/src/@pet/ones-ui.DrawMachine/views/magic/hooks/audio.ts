/* eslint-disable max-lines-per-function */
/* eslint-disable @typescript-eslint/no-explicit-any */
// import type { ExtractPropTypes, PropType } from 'vue';
import { ref, onBeforeUnmount, onMounted, computed, watch } from 'vue';
import { Report, useVisibility } from '@alive-ui/actions';
import { useConfigStore } from '../../../store/config';

export const AudioOffset = 0.05;
const buffer = ref(new Map());

const getAudioBufferAsync = async (
    source: string,
    audioContext: AudioContext,
): Promise<AudioBuffer | null> => {
    const request = new XMLHttpRequest();

    try {
        request.open('GET', source, true);
        request.responseType = 'arraybuffer';
    } catch (error) {
        console.log(error);
    }

    return new Promise((resolve) => {
        request.onload = () => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            audioContext.decodeAudioData(request.response, (buffer) => {
                console.log('buffer==value', buffer);
                console.log('buffer===type', typeof buffer);
                resolve(buffer);
            });
        };
        request.send();
    });
};

const preloadAudioBuffer = async (
    bufferKey: string,
    source: string,
    audioContext: AudioContext,
) => {
    return new Promise(async (r) => {
        const bf = await getAudioBufferAsync(source, audioContext);
        buffer.value.set(bufferKey, bf);
        r(1);
    }).catch((error) => {});
};

// 经验值 - 后续看情况是否需要区分设备处理
const audioThreshold = 0.24; // audio标签循环播放超前阙值
const audioContextThreshold = 0.1; // audioContext循环播放超前阙值

export const useAudioContext = ({ autoPlay = false, loop = false }) => {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    const audioFile = new Audio();
    let audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
    const currentBufferSource = ref<AudioBufferSourceNode | null>(null);
    const destroyed = ref(false);
    const configStore = useConfigStore();

    //  未来增加类型时只需要扩充这里就可以，不需要修改任何其他逻辑
    const sources = computed(() => {
        return (
            configStore.dataConfig?.lotteryResources ?? {
                marbles: {
                    oneLottery: {
                        audio: [],
                    },
                    tenLottery: {
                        audio: [],
                    },
                },
                scratchOff: {
                    oneLottery: {
                        audio: [],
                    },
                    tenLottery: {
                        audio: [],
                    },
                },
                blindBox: {
                    oneLottery: {
                        audio: [],
                    },
                    tenLottery: {
                        audio: [],
                    },
                },
            }
        );
    });

    /**
     *  观察 sources，audio 资源有值后预请求下，解决和视频播放不同步的问题
     *  因为 marbles 音频资源较小，可以先不做预请求，减少并发吧，当前并发数 3
     *  后面有时间可以优化下这里，单独做个并发控制，只需要配置 kconf 就行，相应并发逻辑也不用改了，因为本次属于上线后的 sop，就不引入过多的代码更改了
     *  */
    const batchPreloadAudio = async (sources: any) => {
        if (sources?.scratchOff?.oneLottery?.audio?.length) {
            const audioPromise = [];
            for (const key in sources) {
                if (key !== 'marbles') {
                    const one = sources[key].oneLottery.audio;
                    const ten = sources[key].tenLottery.audio;
                    one.length &&
                        audioPromise.push(
                            // eslint-disable-next-line @typescript-eslint/no-loop-func
                            ...one.map((url: string, index: number) => {
                                return preloadAudioBuffer(
                                    `${key}_oneLottery_${index}`,
                                    url,
                                    audioContext,
                                );
                            }),
                        );
                    ten.length &&
                        audioPromise.push(
                            // eslint-disable-next-line @typescript-eslint/no-loop-func
                            ...ten.map((url: string, index: number) => {
                                return preloadAudioBuffer(
                                    `${key}_tenLottery_${index}`,
                                    url,
                                    audioContext,
                                );
                            }),
                        );
                }
            }
            await Promise.allSettled(audioPromise);
        }
    };

    // const hasUserAction = ref((window as any).hasUserAction || navigator.userAgent.includes('Android')); // safari 需要触发一次人机交互，才可以调用系统功能
    const resetContext = () => {
        console.log('resetContext');
        audioContext?.close(); // 关闭旧的，重新创建
        audioContext = new (window.AudioContext ||
            (window as any).webkitAudioContext)();
    };

    const pause = () => {
        if (!audioContext) {
            audioFile?.pause?.();

            return;
        }

        if (currentBufferSource.value) {
            currentBufferSource.value.stop();
            // currentBufferSource.value.disconnect(audioContext?.destination);
            currentBufferSource.value = null;
        }
    };

    const play = async (key: string, subKey: string, audioIdx: number) => {
        const curLotteryConfigs = sources.value[key];
        const source = curLotteryConfigs?.[subKey]?.audio?.[audioIdx] ?? '';
        if (!audioContext) {
            // if (strategy === 'mute') {
            //     return;
            // }
            if (!source) {
                return;
            }

            if (
                audioFile &&
                audioFile.currentTime > audioFile.duration - audioThreshold
            ) {
                audioFile.currentTime = 0;
            }

            audioFile.src = source;
            audioFile?.play().catch((err: any) => {
                console.error(err);
            });

            return;
        }

        try {
            // if (!hasUserAction.value) {
            //     return;
            // }
            const bufferKey = `${key}_${subKey}_${audioIdx}`;
            if (!buffer.value.get(bufferKey) && source) {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                const bf = await getAudioBufferAsync(source, audioContext);
                buffer.value.set(bufferKey, bf);
            }

            if (currentBufferSource.value) {
                currentBufferSource.value?.stop();
                // currentBufferSource.value?.disconnect(audioContext?.destination);
                currentBufferSource.value = null;
            }
            currentBufferSource.value = audioContext?.createBufferSource();
            const bf = buffer.value.get(bufferKey);
            currentBufferSource.value.buffer = bf;
            currentBufferSource.value?.connect(audioContext?.destination);
            // audioContext循环播放无缝衔接， loop设置顺序需要在start后
            // AudioBufferSourceNode.start([when][, offset][, duration]);
            // when: 可选The time声音开始播放的时间，0表示立即被播放
            // offset 可选：表示偏移
            // duration 可选将要播放的声音的持续时间
            // add 50ms latency to work well across systems - tune this if you like
            currentBufferSource.value?.start(
                audioContext?.currentTime,
                AudioOffset,
                (bf?.duration || 0) - (loop ? audioContextThreshold : 0),
            );
            currentBufferSource.value.loop = loop; // 必须放在后面
            audioContext?.resume().catch(console.error);

            // 修复音频被卸载后destroyAudio先执行, bufferSource还未被清除又开始下一轮的connect/start
            if (destroyed.value) {
                pause();
            }
        } catch (error) {
            console.log(error);

            Report.biz.error('【扭蛋机】：初始化音频失败', {
                source,
                audioContext,
                error,
            });
        }
    };

    const destroyAudio = () => {
        // 内核bug https://docs.corp.kuaishou.com/d/home/<USER>
        destroyed.value = true;
        buffer.value?.clear();

        // 清空资源，防止泄露
        if (currentBufferSource.value) {
            audioContext?.close(); // audioContext.suspend();
            currentBufferSource.value?.stop();
            // currentBufferSource.value?.disconnect(audioContext?.destination);
            currentBufferSource.value = null;
        } else if (audioFile) {
            audioFile.pause();
            audioFile.src = '';
            //  TODO 这里 check 下是否是销毁
            audioFile.load();
        }
    };

    //  不需要自动播放
    // onMounted(() => {
    //     if (autoPlay) {
    //         play();
    //     }
    // });

    onBeforeUnmount(() => {
        destroyAudio();
    });

    useVisibility({
        visibleHandler: () => {
            resetContext();
            //  不需要自动不放
            // autoPlay && play();
        },
        hiddenHandler: () => pause(),
    });

    // useEventListener({
    //     elementFactory: () => document,
    //     type: 'touchstart',
    //     listener: () => {
    //         if (autoPlay && !hasUserAction.value) {
    //             hasUserAction.value = true;
    //             play();
    //         } else {
    //             hasUserAction.value = true;
    //         }
    //     },
    //     options: true,
    // });

    // useEventListener({
    //   elementFactory: () => window,
    //   type: 'beforeunload',
    //   listener: () => {
    //     destroyAudio();
    //   },
    //   options: true,
    // });

    return {
        audioContext,
        resetContext,
        play,
        pause,
        batchPreloadAudio,
    };
};
