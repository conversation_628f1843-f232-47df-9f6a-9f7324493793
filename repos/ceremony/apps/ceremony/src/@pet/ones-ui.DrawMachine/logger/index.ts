// 主页pv
import { sendClick, sendShow, sendTask } from '@/common/logger';

// 运营活动页-功能按钮 曝光和点击
export const funcBtnLog = (action_name: string) => {
    return {
        action: 'OP_ACTIVITY_GASHAPON_MACHINE_FUNCTION_BUTTON',
        params: {
            action_name,
        },
    };
};

export const rule = funcBtnLog('规则');

export const goDraw = funcBtnLog('去抽奖');

export const drawTen = funcBtnLog('抽十次');

export const drawOne = funcBtnLog('抽一次');

export const drawAgain = funcBtnLog('继续抽奖');

export const drawRecord = funcBtnLog('抽奖记录');

export const coinRecord = funcBtnLog('代币记录');
// 抽奖按钮

export const drawBtnLog = (btnIndex: number, type: number, tabName: string) => {
    return {
        action: 'OP_ACTIVITY_GASHAPON_MACHINE_FUNCTION_BUTTON',
        params: {
            action_name: btnIndex === 0 ? '抽一次' : '抽十次',
            type,
            tab_name: tabName,
        },
    };
};

export const sendTabLog = (tab_name: string, cl_type: number) => {
    return {
        action: 'OP_ACTIVITY_LOTTERY_BOTTOM_TAB',
        params: {
            tab_name,
            cl_type,
        },
    };
};

export const drawBtnClick = (
    btnIndex: number,
    type: number,
    tabName: string,
) => {
    sendClick(drawBtnLog(btnIndex, type, tabName));
};

// 做任务得金币按钮
export const taskBtnLog = (
    action_name: string,
    task_id: string | number,
    video_id?: string | number,
) => {
    return {
        action: 'OP_ACTIVITY_GASHAPON_MACHINE_TASK_BUTTON',
        params: {
            action_name,
            task_id,
            video_id,
        },
    };
};

export const resultPopupBtnLog = (type: number) => {
    return {
        action: 'OP_ACTIVITY_GASHAPON_MACHINE_DRAW_RESULT_POPUP',
        params: {
            type,
        },
    };
};

export const funcLog = {
    rule,
    goDraw,
    drawTen,
    drawOne,
    drawAgain,
    drawRecord,
    coinRecord,
    taskBtnLog,
    resultPopupBtnLog,
};

export { sendClick, sendShow, sendTask };
