/* eslint-disable @typescript-eslint/no-unused-vars */
import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import { debounce } from 'lodash-es';
import useKconf from '@pet/ones-use.useKconf/index';
import { toShare } from '@pet/ones-use.share';
import {
    linkOtherBizPage,
    SceneType,
} from '@pet/ones-use.linkOtherBizPage/index';
import { Toast } from '@lux/sharp-ui-next';
import {
    useVisibility,
    bolIsAuthor,
    dispatchLiveRouter,
    activityBiz,
    liveStreamId,
    appendParam,
    startNeoAdVideo,
    exitWebView,
    invoke,
    loadUrlOnNewPage,
} from '@alive-ui/actions';
import { useBoxInfo } from '../views/magic/hooks/useBoxInfo';
import { sendClick, taskBtnLog } from '../logger/index';
import {
    postLotteryTask,
    queryRecoAuthor,
    dailyLogin,
    getAdInspireScheme,
    receiveTask,
    searchKeyWord,
    TaskType,
    TaskStatus,
    completed,
} from '../api/index';
import { useMain, COMMON_EVENT } from './useMain';
import type { TaskItem } from '../api/index';

const RecieveTasks = ['joinFansGroup', 'sendPopularityGift', 'sendGift'];

// eslint-disable-next-line max-lines-per-function
export default defineStore('lottery-task', () => {
    const kconfStore = useKconf();
    const mainStore = useMain();
    const pageStatus = reactive({
        loading: true,
        error: false,
    });
    const taskList = ref([] as TaskItem[]);
    const isRefetch = ref(false);
    const boxStore = useBoxInfo();
    //   const appInfo = ref({
    //     appType: '',
    //     appVer: '',
    //     clientId: -1,
    //   });

    // 获取任务列表
    const queryTaskInfo = async () => {
        if (!isRefetch.value) {
            pageStatus.loading = true;
        }

        try {
            const res = await postLotteryTask();
            pageStatus.error = false;
            const data = res;
            mainStore.busEvent.emit(COMMON_EVENT.updateBallance, data.balance);
            taskList.value = data.taskItemViewList || [];
        } catch (error) {
            console.error(' error:', error);
            pageStatus.error = true;
        }
        pageStatus.loading = false;
    };

    // 重新获取任务列表
    const refetchTask = () => {
        isRefetch.value = true;
        queryTaskInfo();
    };

    const curTaskList = computed(() => {
        return taskList.value.filter((task) => {
            return liveStreamId ? true : task.typeKey !== 'h5Follow';
        });
    });

    // 获取作者信息
    const queryRecoAuthorInfo = async (typeKey: string) => {
        try {
            const res = await queryRecoAuthor(typeKey);

            return res?.recoAuthorList || [];
        } catch (error) {
            console.log('error', error);
        }

        return [];
    };

    // 跳转直播间
    const jump2room = async (item: TaskItem, kwaiLink = '') => {
        const list = await queryRecoAuthorInfo(item.typeKey);
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const lsIds = list?.map((item: any) => item.liveStreamId).join(',');

        if (!lsIds) {
            return;
        }

        location.href = appendParam('kwai://liveaggregatesquare', {
            sourceType: 269,
            liveStreamId: lsIds,
            exp_tag: (window as any).GLOBAL_EXPTAG || activityBiz,
            liveSquareSource: 10012,
            path: '/rest/n/live/feed/common/slide/more',
            internaljump: kwaiLink,
        });
    };

    // 页面visiable时刷新余额
    const watchUpdate = () => {
        useVisibility({
            visibleHandler: debounce(() => {
                refetchTask();
            }, 100),
        });
    };

    // 每日登录任务
    const initLogin = async () => {
        try {
            console.log('dailyLogin');
            await dailyLogin();
        } catch (err) {
            console.error('lucky machine login-task', err);
        }
    };

    const searchKeyWordCallBack = async (keyWord: string) => {
        try {
            await searchKeyWord(keyWord);
        } catch (err) {
            console.error('lucky machine search-task', err);
        }
    };

    const followModal = ref(false);
    const reservationModal = ref(false);

    const closeModal = () => {
        followModal.value = false;
        reservationModal.value = false;
    };

    const buttonAction = debounce(async (item: TaskItem) => {
        if (
            boxStore.pageStatusPop.loading ||
            boxStore.pageStatus.loading ||
            boxStore.isPlaying
        ) {
            Toast.info('正在抽奖中');
            return;
        }
        if (item.status === TaskStatus.Finished) {
            return;
        }
        const { typeKey, status, kwaiLink } = item;
        const videoId = item.expandMap?.photoId || '';

        // 埋点上报
        // status 1-未完成，2-已完成
        sendClick(taskBtnLog(item.buttonText, typeKey, videoId));

        if (bolIsAuthor) {
            Toast.info('开播中，不可参与哦～');

            return;
        }
        mainStore.busEvent.emit(COMMON_EVENT.taskBtnClick, {
            item,
            refresh: refetchTask,
        });

        // 所有任务均增加领取接口，后端看情况是否使用。
        try {
            await receiveTask(typeKey);

            if (
                item.typeKey === TaskType.SendGift &&
                status === 1 &&
                kwaiLink &&
                liveStreamId
            ) {
                // 未做任务
                dispatchLiveRouter({
                    path: kwaiLink, // kwaiLink,
                    keepDisplayWebView: false,
                }).catch(console.error);
            }
        } catch (err: any) {
            const { error_msg } = err?.data ?? {};
            if (error_msg) {
                Toast.info(error_msg);
                return;
            }

            if (RecieveTasks.includes(item.typeKey) || item.receiveTask) {
                Toast.error('网络异常，请稍后再试～');

                return;
            }
        }

        switch (item.typeKey) {
            case TaskType.SharePoster: //  分享
                await toShare({ onShareCompleted: refetchTask });
                return;
            case TaskType.WatchVideo:
                if (!item.expandMap.photoId || !item.expandMap.watchDuration) {
                    Toast.error('库存暂时不足，请稍后再试');

                    return;
                }
                // 以新打开webview形式跳转到视频观看页
                // 当前页面所有参数加上photoId & watchDuration
                const search = location.search.startsWith('?')
                    ? location.search
                    : `?${location.search}`;
                // eslint-disable-next-line max-len
                const url = `${location.origin}/live/act/annual-ceremony-2024-12/video-play${search}&layoutType=4&photoId=${item.expandMap.photoId}&watchDuration=${item.expandMap.watchDuration}`;
                loadUrlOnNewPage({
                    url,
                    type: 'back',
                    ignoreHalfScreenDisplay: 1, // 顶部状态栏，仅ios使用
                });
                return;
            case TaskType.H5Follow: // 关注任务的逻辑不一样,是打开弹窗
                followModal.value = true;
                return;
            case TaskType.WatchLive: // 跳转推荐直播间
                return jump2room(item);

            case TaskType.SearchKeyWord: // 跳搜索推荐页
                try {
                    await searchKeyWordCallBack(item.expandMap.keyWord);
                    location.href = item.kwaiLink;
                } catch (error) {
                    console.error('跳搜索推荐页', error);
                }

                return;
            case TaskType.Reservation: // 预约任务
                console.log('预约任务');
                reservationModal.value = true;

                return;
            case TaskType.AdInspire: // 激励任务
                console.log('激励任务');

                try {
                    const adInspireRes = await getAdInspireScheme();

                    if (adInspireRes?.scheme) {
                        await startNeoAdVideo(adInspireRes.scheme);
                        setTimeout(exitWebView, 500);
                    } else {
                        Toast.info('获取激励任务失败');
                    }
                } catch (error) {
                    console.error('激励任务失败', error);
                }

                return;
            case TaskType.SmallBell: // 小铃铛下载
                invoke('platform.showToast', {
                    type: 'normal',
                    text: '小铃铛在直播间右下角哦～',
                    isAddToWindow: false,
                });
                exitWebView();
                break;
            // case TaskType.PageView: // 跳转年度回忆录
            //     completed(item.typeKey);
            //     linkOtherBizPage({
            //         scene: SceneType.Memory,
            //         link: '',
            //         queryData: {
            //             entry_src: 'source_3',
            //             msrc: 'source_3',
            //         },
            //     });
            //     break;
            default:
                break;
        }

        if (item.typeKey !== TaskType.SendGift && status === 1 && kwaiLink) {
            // 未做任务
            if (liveStreamId) {
                dispatchLiveRouter({
                    path: kwaiLink, // kwaiLink,
                    keepDisplayWebView: false,
                }).catch(console.error);
            } else {
                if (item.typeKey === TaskType.GrabRedPack) {
                    // 抢红包本身是跳转直播间，不需要特殊处理
                    location.href = kwaiLink;

                    return;
                }
                jump2room(item, kwaiLink);
            }
        }
    }, 500);

    return {
        taskList: curTaskList,
        pageStatus,
        isRefetch,
        followModal,
        reservationModal,
        queryTaskInfo,
        initLogin,
        refetchTask,
        queryRecoAuthorInfo,
        watchUpdate,
        buttonAction,
        closeModal,
        // getAppInfo,
    };
});
