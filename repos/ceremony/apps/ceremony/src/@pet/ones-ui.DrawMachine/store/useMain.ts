/* eslint-disable @typescript-eslint/no-explicit-any */
import { onUnmounted, ref, watch } from 'vue';
import { defineStore } from 'pinia';
import mitt from 'mitt';
import {
    isICFO,
    isLowDevice,
    countsCT,
    sendRadarEvent,
    useLowPowerMode,
} from '@alive-ui/actions';
import type { DRAW_EVENT } from '../views/magic/config';
import type { TaskItem } from '../api';

export enum COMMON_EVENT {
    updateBallance = 'updateBallance',
    noEnoughBalance = 'noEnoughBalance',
    taskBtnClick = 'taskBtnClick',
    sendFmp = 'sendFmp',
    noLogin = 'noLogin',
    playAudio = 'playAudio',
    pauseAudio = 'pauseAudio',
}

type Events = {
    [COMMON_EVENT.updateBallance]: number;
    [COMMON_EVENT.noEnoughBalance]?: undefined;
    [COMMON_EVENT.taskBtnClick]: {
        item: TaskItem;
        refresh: () => void;
    };
    [COMMON_EVENT.sendFmp]: undefined;
    [COMMON_EVENT.noLogin]: undefined;
    [DRAW_EVENT.updateMarquee]: undefined;
    [DRAW_EVENT.showEmpty]: undefined;
    [DRAW_EVENT.showResultPop]: { isEmpty: boolean };

    [DRAW_EVENT.animEndDelay]: { res: boolean };
    [COMMON_EVENT.playAudio]: { id: number };
    [COMMON_EVENT.pauseAudio]: never;
    [DRAW_EVENT.tabChange]: { id: number };
    [DRAW_EVENT.videoChange]: { id: number };
};

export const busEvent = mitt<Events>();

export const useMain = defineStore('lottery-main', () => {
    onUnmounted(() => {
        busEvent.all.clear();
    });
    const isLowDev = ref(isICFO() || isLowDevice() || countsCT() > 1);
    const { isLowPowerModeStatus } = useLowPowerMode();

    if (countsCT() > 1) {
        sendRadarEvent({
            category: 'lottery-countsCT',
            name: '触发崩溃降级',
            extraInfo: JSON.stringify({
                userAgent: window.navigator.userAgent,
                countsCT: countsCT(),
            }),
        });
    }

    // 兼容低电量模式及充电模式
    watch(
        () => isLowPowerModeStatus.value,
        (o) => {
            if (o) {
                isLowDev.value = true;
            } else {
                isLowDev.value = isICFO() || isLowDevice() || countsCT() > 1;
            }
        },
        { immediate: true },
    );

    return {
        busEvent,
        isLowDev,
    };
});

export default useMain;
