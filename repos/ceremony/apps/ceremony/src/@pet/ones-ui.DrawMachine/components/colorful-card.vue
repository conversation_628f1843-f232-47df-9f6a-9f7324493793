<template>
    <div class="colorful-card a-bg-part">
        <!-- <div class="gis-card-top-img" /> -->
        <slot name="title">
            <div v-if="title" class="title-content">
                <div
                    v-if="carStarIcon && !extra"
                    class="icon"
                    :class="carStarIcon"
                />
                <div class="title-wrap">
                    <p
                        v-if="title"
                        class="title a-text-title text-20 text-hyykh"
                    >
                        {{ title }}
                    </p>
                    <div
                        v-if="needCoin"
                        v-show-log="funcLog.coinRecord"
                        v-click-log="funcLog.coinRecord"
                        class="coin-text flex-center a-text-main text-12 text-pingfang text-regular"
                        @click="emits('show-coin')"
                    >
                        {{ coinName }}记录
                        <SvgArrow class="icon-jt" />
                    </div>

                    <p v-if="extra" class="extra">
                        {{ extra }}
                    </p>
                </div>
                <div
                    v-if="carStarIcon && !extra"
                    class="icon rotate-icon"
                    :class="carStarIcon"
                />
            </div>
            <div v-if="subTitle" class="sub-title">
                {{ subTitle }}
            </div>
        </slot>
        <div>
            <slot name="other" />
        </div>
        <div class="card-content">
            <slot />
        </div>
        <!-- 预留一个插槽 -->
        <slot name="bottom" />
    </div>
</template>

<script lang="ts" setup>
import { funcLog } from '../logger/index';
import SvgArrow from './arrow.vue';

defineProps({
    title: {
        type: String,
        default: '',
    },
    extra: {
        type: String,
        default: '',
    },
    subTitle: {
        type: String,
        default: '',
    },
    carStarIcon: {
        type: String,
        default: 'gis-card-star-icon',
    },
    needCoin: {
        type: Boolean,
        default: true,
    },
    coinName: {
        type: String,
        default: '',
    },
});

const emits = defineEmits(['show-coin']);
</script>

<style lang="less" scoped>
.colorful-card {
    position: relative;
    width: var(--cardWidth, 386px);
    height: auto;
    padding-bottom: var(--cardPaddingBottom, 0);
    margin: 14px auto;
    // background-image: var(--headerBgImg);
    // background-position: center top;
    // background-size: 100%;
    // background-repeat: no-repeat;
    border-radius: var(--cardBorderRadius, 18px);
    // .gis-card-top-img {
    //     position: absolute;
    //     top: var(--cardTopImgTop, 0);
    // }
    .card-content {
        // position: relative;
        padding-top: var(--cardContentPaddingTop, 0);
        overflow: hidden;
    }

    .title-content {
        // position: relative;
        // z-index: 1;
        display: flex;
        height: var(--titleHeight, 28px);
        padding-top: var(--titleContentPaddingTop, 10px);
        line-height: var(--titleHeight, 28px);
        text-align: center;
        box-sizing: content-box;
        align-items: center;
        justify-content: center;
        transform: perspective(1000);
        .title-wrap {
            position: relative;
            display: flex;
            align-items: center;
            width: 100%;
            .title {
                margin-left: 121px;
            }
        }
        .title {
            line-height: 28px;
        }
        .extra {
            position: absolute;
            left: 100%;
            height: 18px;
            padding: 0 4px;
            font-size: 10px;
            font-weight: 600;
            line-height: 18px;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            background: var(
                --backgroundExtra,
                linear-gradient(279.09deg, #ff4242 0%, #ff42b3 100%)
            );
            border-radius: 4px;
        }
        .rotate-icon {
            transform: var(--iconRotate, rotateY(180deg)) translate3d(0, 0, 0);
        }
        .icon {
            display: inline-block;
            width: 24px;
            height: 24px;
        }
    }
    .sub-title {
        margin-top: 2px;
        font-family: var(--fontFamilySubTitle, 'PingFang SC');
        font-size: var(--fontSizeSubTitle, 12px);
        font-style: normal;
        font-weight: var(--fontWeightSubTitle, 400);
        line-height: 18px;
        color: var(--colorSubTitle, #fd3c97);
        text-align: center;
        opacity: 0.6;
    }

    .coin-text {
        opacity: 0.6;
        margin-left: auto;
        margin-right: 12px;
        .icon-jt {
            width: 10px;
            height: 10px;
            margin-left: 4px;
        }
    }
}
</style>
