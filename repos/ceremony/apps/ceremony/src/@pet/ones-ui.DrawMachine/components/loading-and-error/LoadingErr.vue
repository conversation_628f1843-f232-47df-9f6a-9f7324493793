<template>
    <div class="data-status-deal">
        <div v-if="!loading">
            <div
                v-if="errorStatus"
                class="margin-auto-style"
                :class="netErrorIcon"
            />
            <div v-else class="margin-auto-style" :class="netNoDataIcon" />
            <slot name="error-text">
                <div class="error-text">
                    {{ errorText }}
                </div>
            </slot>
            <slot v-if="needRefresh" name="refresh">
                <div
                    class="margin-auto-style"
                    :class="netRefreshIcon"
                    @click="refreshData"
                />
            </slot>
        </div>
        <spu-loading v-else class="spu-loading" :size="loadingSize" />
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { Loading } from '@lux/sharp-ui-next';

export default defineComponent({
    components: {
        'spu-loading': Loading,
    },
    props: {
        needRefresh: {
            type: Boolean,
            default: true,
        },
        errorStatus: {
            type: Boolean,
            default: false,
        },
        netNoDataIcon: {
            type: String,
            default: 'gis-net-no-data-icon',
        },
        netErrorIcon: {
            type: String,
            default: 'gis-net-error-icon',
        },
        netRefreshIcon: {
            type: String,
            default: 'gis-net-refresh-button',
        },
        errorText: {
            type: String,
            default: '',
        },
        loading: {
            type: Boolean,
            default: false,
        },
        loadingSize: {
            type: Number,
            default: 40,
        },
    },
    setup(props, ctx) {
        const refreshData = () => {
            ctx.emit('refresh-data');
        };

        return {
            refreshData,
        };
    },
});
</script>

<style lang="less" scoped>
.data-status-deal {
    .margin-auto-style {
        position: relative;
        z-index: 10;
        margin: 0 auto;
        margin-top: var(--dataNetStatusMgTop, 20px);
    }
    .gis-net-refresh-button {
        margin: 0.3rem auto 0.56rem;
    }
    .spu-loading,
    .sp-loading {
        position: relative;
        z-index: 10;
        display: flex;
        height: 3rem;
        justify-content: center;
        align-items: center;
    }
}
</style>
