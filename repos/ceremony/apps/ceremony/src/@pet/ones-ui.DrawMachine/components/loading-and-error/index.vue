<template>
    <div class="spring-loading-error">
        <LoadingErr
            class="data-load-error"
            :class="`data-load-error-${pageCase}`"
            net-error-icon="gis-draw-net-error"
            net-no-data-icon="gis-draw-no-data"
            :need-refresh="needRefresh"
            :loading="pageStatus.loading"
            :error-status="pageStatus.error"
            @refresh-data="refresh"
        >
            <template #error-text>
                <div class="error-text">
                    {{ errorText }}
                </div>
            </template>
            <template v-if="showRefreshBtn" #refresh>
                <div
                    class="refresh-button margin-auto flex-center gis-short-btn a-text-button"
                    @click="refresh"
                >
                    {{ refreshText }}
                </div>
            </template>
        </LoadingErr>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';
import LoadingErr from './LoadingErr.vue';
import type { PropType } from 'vue';
import type { statusType } from './type';

export default defineComponent({
    components: {
        LoadingErr,
    },
    props: {
        refreshText: {
            type: String,
            default: '刷新',
        },

        pageCase: {
            type: String,
            default: 'main', // main表示主页兜底，other表示其他区块兜底，follow关注弹窗兜底
        },
        pageStatus: {
            type: Object as PropType<statusType>,
            default: () => {
                return {
                    error: false,
                    loading: true,
                    nodata: false,
                };
            },
        },
        loadingSize: {
            type: Number,
            default: 40,
        },
        showRefreshBtn: {
            type: Boolean,
            default: true,
        },
    },
    setup(props, { emit }) {
        const refresh = () => {
            console.log('refresh');
            emit('refresh');
        };
        const netText = {
            main: {
                error: '啊哦，加载失败了，刷新试试吧',
                nodata: '啊哦，暂无数据',
            },
            other: {
                error: '网络异常，请刷新重试',
                nodata: '暂无中奖记录',
            },
            follow: {
                error: '网络异常，请刷新重试',
                nodata: '暂无可认识的新主播',
            },
        };
        const needRefresh = computed(() => {
            return props.pageStatus.error && props.showRefreshBtn;
        });
        const errorText = computed(() => {
            // eslint-disable-next-line
            // @ts-ignore
            return netText[props.pageCase]?.[
                props.pageStatus.error ? 'error' : 'nodata'
            ];
        });

        return {
            errorText,
            refresh,
            needRefresh,
        };
    },
});
</script>

<style lang="less" scoped>
.spring-loading-error {
    position: absolute;
    left: 50%;
    min-width: 100px;
    transform: translateX(-50%);
    .error-text {
        margin-top: 4px;
        margin-bottom: 23px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-size: 12px;
        font-weight: 500;
        color: #000;
        text-align: center;
        opacity: 0.4;
    }
    .refresh-button {
        font-family: PingFangSC, PingFangSC-Semibold;
        font-size: 16px;
        font-weight: 700;
        line-height: 46px;
        text-align: center;
    }
    // .gis-short-btn {
    //   width: 71px;
    //   height: 35px;
    //   font-size: 14px;
    //   font-weight: 500;
    //   line-height: 20px;
    //   letter-spacing: 0;
    //   color: #3a0a0a;
    //   text-align: center;
    //   background: url('../../variable/assets/icon-task-1_2x.png') center / 100% no-repeat;
    // }
}
</style>
