<template>
    <!-- @vue-skip -->
    <div
        class="video-play"
        :style="{
            backgroundImage: posterUrl ? `url(${posterUrl})` : 'unset',
            backgroundSize: objectFit === 'fill' ? '100% 100%' : objectFit,
        }"
        @click="onVideoClick"
    >
        <template v-if="!isLowDev">
            <video
                v-if="loopVideoUrl"
                ref="loopVideoInstance"
                loop
                class="video-play__video"
                crossorigin="anonymous"
                preload="auto"
                playsinline="true"
                webkit-playsinline="true"
                :poster="posterUrl"
                x5-video-player-type="h5"
                x5-playsinline="true"
                x5-video-orientation="portraint"
                x5-video-player-fullscreen="true"
                controlslist="nodownload nofullscreen noremoteplayback"
                disablePictureInPicture
                disableRemotePlayback
                :autoplay="false"
                :muted="muted"
                :style="{
                    objectFit: objectFit,
                    visibility: loopVideoVisible ? 'visible' : 'hidden',
                }"
                @error="handleError"
            >
                <source
                    v-for="(item, index) in loopVideoUrl"
                    :key="index"
                    :src="item.url"
                />
            </video>
            <video
                v-if="!isStartPlayLoop && renderVideo.length > 0"
                ref="videoInstance"
                class="video-play__video"
                crossorigin="anonymous"
                preload="auto"
                playsinline="true"
                webkit-playsinline="true"
                x5-video-player-type="h5"
                poster="https://p4-live.wskwai.com/kos/nlav12706/ceremony2024/lottery/1px.b5c5e658f02a51d4.png"
                x5-playsinline="true"
                x5-video-orientation="portraint"
                x5-video-player-fullscreen="true"
                controlslist="nodownload nofullscreen noremoteplayback"
                disablePictureInPicture
                disableRemotePlayback
                :autoplay="autoplay"
                :muted="muted"
                :style="{
                    objectFit: objectFit,
                    visibility: videoVisible ? 'visible' : 'hidden',
                }"
                :loop="
                    typeof loop === 'boolean'
                        ? loop
                        : range.from === 0 && range.to === -1 && !loopVideoUrl
                "
                @error="handleError"
            >
                <source
                    v-for="(item, index) in renderVideo"
                    :key="index"
                    :src="item.url"
                />
            </video>
        </template>
        <slot name="floatEles" />
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    ref,
    onMounted,
    onBeforeUnmount,
    computed,
    getCurrentInstance,
} from 'vue';
import { isLowDevice } from '@alive-ui/system';
import { Report } from '@alive-ui/actions';
import { UDevice } from '@ad/utils';

export default defineComponent({
    name: 'VideoPlay',
    props: {
        videoUrl: {
            type: Array,
            required: true,
        },
        loopVideoUrl: {
            type: Array,
            required: false,
            default: undefined,
        },
        posterUrl: {
            type: String,
            default: '',
        },
        autoplay: {
            type: Boolean,
            required: false,
            default: true,
        },
        objectFit: {
            type: String,
            required: false,
            default: 'cover',
        },
        loop: {
            type: Boolean,
            require: false,
            default: false,
        },
        muted: {
            type: Boolean,
            default: true,
        },
        range: {
            type: Object,
            required: false,
            default: () => ({
                from: 0,
                to: -1,
            }),
        },
        showLastFrame: {
            type: Boolean,
            default: false,
        },
    },
    // eslint-disable-next-line max-lines-per-function
    setup(props, ctx) {
        const factAutoplay = ref(true); // 记录实际上是否自动播放了
        const playStatus = ref('paused'); // 第一段视频播放状态
        const loopPlayStatus = ref('paused'); // 循环视频播放状态
        const isEnd = ref(false); // 第一段视频是否结束
        const isCloseToEnd = ref(false); // 第一段视频是否接近结束
        const isStartPlayLoop = ref(false); // 是否开始播放循环视频
        const videoInstance = ref(); // 第一段视频的dom
        const loopVideoInstance = ref(); // 循环视频的dom
        const clickVideoInstance = ref(); // 因为中间交替的时候实例会有冲突，故通过中间变量来承载实际上要回传给click的video
        const { proxy } = getCurrentInstance() as any;
        const useTimeupdate = computed(() => {
            return (
                props.range.from !== 0 ||
                props.range.to !== -1 ||
                !!props.loopVideoUrl
            );
        });
        const videoVisible = computed(() => {
            return (
                (playStatus.value === 'play' && !isEnd.value) ||
                (isEnd.value && props.showLastFrame)
            );
        });
        const loopVideoVisible = computed(() => {
            return isCloseToEnd.value && loopPlayStatus.value === 'play';
        });

        const renderVideo = computed(() =>
            props.videoUrl.filter((item: any) => item.url),
        );

        const destoryVideo = () => {
            if (videoInstance.value?.children.length) {
                videoInstance.value.children[0].src = null;
                videoInstance.value.removeChild(
                    videoInstance.value.children[0],
                );
            }
            videoInstance.value.onplay = null;
            videoInstance.value.onpause = null;
            videoInstance.value.onended = null;
            videoInstance.value.oncanplay = null;

            if (useTimeupdate.value) {
                videoInstance.value.ontimeupdate = null;
            }
            videoInstance.value.load();
        };

        const destoryLoopVideo = () => {
            if (loopVideoInstance.value?.children.length) {
                loopVideoInstance.value.children[0].src = null;
                loopVideoInstance.value.removeChild(
                    loopVideoInstance.value.children[0],
                );
            }
            loopVideoInstance.value.onplay = null;
            loopVideoInstance.value.onpause = null;
            loopVideoInstance.value.load();
        };

        const onVideoPlay = () => {
            playStatus.value = 'play';
            ctx.emit('video-play');
        };

        const onLoopVideoPlay = () => {
            loopPlayStatus.value = 'play';
        };

        const onVideoPause = () => {
            playStatus.value = 'paused';
        };

        const onLoopVideoPause = () => {
            console.log('onLoopVideoPause');
            loopPlayStatus.value = 'paused';
        };

        const onVideoCanplay = () => {
            // console.log('onVideoCanplay');
            videoInstance.value.style.width = '100%';
            videoInstance.value.style.height = '100%';

            if (props.autoplay) {
                videoInstance.value?.play()?.catch(console.warn);
            }
            // 在canplay且执行了play方法之后依旧在500ms内没有播放，则认为实际上没有自动播放
            // setTimeout(() => {
            //     console.log('videoInstance.value?.currentTime', videoInstance.value?.currentTime);
            //     if (videoInstance.value?.currentTime === 0) {
            //         factAutoplay.value = false;
            //         console.log('playStatus========update');
            //         playStatus.value = 'paused';
            //     }
            // }, 500);
        };

        const onVideoended = () => {
            playStatus.value = 'paused';
            isEnd.value = true;
            clickVideoInstance.value = loopVideoInstance.value;

            setTimeout(() => {
                if (props.loopVideoUrl) {
                    isStartPlayLoop.value = true;
                }
                // 外部主动destory视频，此处不destory，因为有些动画播完需要接口返回，不然展示首帧
                // destoryVideo();
                ctx.emit('video-end');
            }, 300);
        };

        const onVideoClick = () => {
            ctx.emit('click', clickVideoInstance.value);
        };

        const onVideoTimeupdate = () => {
            // console.log('onVideoTimeupdate', videoInstance.value?.currentTime);
            const to =
                props.range.to === -1
                    ? videoInstance.value?.duration
                    : props.range.to;

            if (videoInstance.value?.currentTime >= to - 0.4) {
                // console.log('onVideoTimeupdateReset', videoInstance.value?.currentTime);
                isCloseToEnd.value = true; // 因为如果直接end之后再去切，会导致video切换的时候闪烁 此时第二段视频开始渲染到dom上
                loopPlayStatus.value = 'play';
                loopVideoInstance.value?.play()?.catch(console.warn);

                if (!props.loopVideoUrl) {
                    videoInstance.value.currentTime = props.range.from;
                }
            }
        };

        const onVisibilityChange = () => {
            const visible = document.visibilityState === 'visible';
            console.log(visible);

            // 因为ios上元素不可见变成可见是会多次触发play,pause事件，故在不可见时，去除事件
            // 在可见之后再去添加事件，避免事件多次触发
            if (!isStartPlayLoop.value && videoInstance.value) {
                if (!visible) {
                    videoInstance.value.onplay = null;
                    videoInstance.value.onpause = null;
                    videoInstance.value?.pause();
                } else {
                    videoInstance.value.onplay = onVideoPlay;
                    videoInstance.value.onpause = onVideoPause;

                    if (videoInstance.value?.paused && props.autoplay) {
                        console.log('visible videoInstance', 'play');
                        videoInstance.value?.play()?.catch(console.warn);
                        // playStatus.value = 'play';
                    }
                }
            }

            if (isStartPlayLoop.value && loopVideoInstance.value) {
                if (!visible) {
                    loopVideoInstance.value.onplay = null;
                    loopVideoInstance.value.onpause = null;
                    loopVideoInstance.value?.pause();
                } else {
                    loopVideoInstance.value.onplay = onLoopVideoPlay;
                    loopVideoInstance.value.onpause = onLoopVideoPause;

                    if (loopVideoInstance.value?.paused && props.autoplay) {
                        loopVideoInstance.value?.play()?.catch(console.warn);
                        // loopPlayStatus.value = 'play';
                    }
                }
            }
        };

        const resetVideo = (url?: string) => {
            const sourceDom = document.createElement('source');
            sourceDom.src = url ? url : (renderVideo.value?.[0] as any).url;

            if (videoInstance.value) {
                (videoInstance.value as HTMLVideoElement).appendChild(
                    sourceDom,
                );
                videoInstance.value.onplay = onVideoPlay;
                videoInstance.value.onpause = onVideoPause;
                videoInstance.value.onended = onVideoended;
                videoInstance.value.oncanplay = onVideoCanplay;

                if (useTimeupdate.value) {
                    videoInstance.value.ontimeupdate = onVideoTimeupdate;
                }
                videoInstance.value.load();
            }

            if (loopVideoInstance.value) {
                (loopVideoInstance.value as HTMLVideoElement).appendChild(
                    sourceDom,
                );
                loopVideoInstance.value.onplay = onVideoPlay;
                loopVideoInstance.value.onpause = onVideoPause;
                loopVideoInstance.value.onended = onVideoended;
                // loopVideoInstance.value.oncanplay = onVideoCanplay;

                loopVideoInstance.value.load();
                loopVideoInstance.value?.play()?.catch(console.warn);
            }
        };

        // 解决ios跳转其它页面、返回后视频不播放问题，bug链接：https://team.corp.kuaishou.com/task/B1305686
        const onPageshow = function (event: PageTransitionEvent) {
            console.log('===onPageshow, event.persisted=', event.persisted);

            if (
                event.persisted &&
                !isStartPlayLoop.value &&
                videoInstance.value
            ) {
                destoryVideo();
                resetVideo();
            }
        };

        const initVideo = () => {
            console.log('initVideo');

            if (!videoInstance.value) {
                return;
            }
            clickVideoInstance.value = videoInstance.value;
            videoInstance.value.onplay = onVideoPlay;
            videoInstance.value.onpause = onVideoPause;
            videoInstance.value.onended = onVideoended;
            videoInstance.value.oncanplay = onVideoCanplay;

            if (useTimeupdate.value) {
                videoInstance.value.ontimeupdate = onVideoTimeupdate;
            }

            if (UDevice.isAndroid()) {
                console.log('UDevice.isAndroid');
                window.addEventListener('visibilitychange', onVisibilityChange);
            } else if (UDevice.isIOS()) {
                console.log('UDevice.isIOS');

                try {
                    window.addEventListener('pageshow', onPageshow);
                } catch (err) {
                    console.error(err);
                    Report.biz.error('【扭蛋机】：监听页面是否可见异常', {
                        error: err,
                    });
                }
            }
        };

        const initLoopVideo = () => {
            console.log('initLoopVideo');
            loopVideoInstance.value = proxy?.$refs?.loopVideoInstance;
            loopVideoInstance.value.style.width = '100%';
            loopVideoInstance.value.style.height = '100%';

            if (!loopVideoInstance.value) {
                return;
            }
            loopVideoInstance.value.onplay = onLoopVideoPlay;
            loopVideoInstance.value.onpause = onLoopVideoPause;
        };

        const handlePause = () => {
            if (
                videoInstance.value &&
                typeof videoInstance.value.pause === 'function'
            ) {
                videoInstance.value.pause();
            }

            if (
                loopVideoInstance.value &&
                typeof loopVideoInstance.value.pause === 'function'
            ) {
                loopVideoInstance.value.pause();
            }
        };

        const handlePlay = () => {
            if (
                videoInstance.value &&
                typeof videoInstance.value.play === 'function'
            ) {
                videoInstance.value.play()?.catch(console.warn);
            }

            if (
                loopVideoInstance.value &&
                typeof loopVideoInstance.value.play === 'function'
            ) {
                loopVideoInstance.value.play()?.catch(console.warn);
            }
        };

        const handleError = (err: any) => {
            ctx.emit('play-error');
            Report.biz.error('【扭蛋机】：视频播放异常', {
                error: err,
            });
        };

        onMounted(() => {
            ctx.emit('mounted');
            initVideo();
            props.loopVideoUrl && initLoopVideo();
        });

        onBeforeUnmount(() => {
            !isStartPlayLoop.value && videoInstance.value && destoryVideo();
            props.loopVideoUrl && loopVideoInstance.value && destoryLoopVideo();
            UDevice.isAndroid() &&
                window.removeEventListener(
                    'visibilitychange',
                    onVisibilityChange,
                );
            UDevice.isIOS() &&
                window.removeEventListener('pageshow', onPageshow);
        });

        return {
            resetVideo,
            destoryVideo,
            destoryLoopVideo,
            loopVideoVisible,
            videoVisible,
            factAutoplay,
            playStatus,
            loopPlayStatus,
            videoInstance,
            loopVideoInstance,
            onVideoClick,
            isCloseToEnd,
            isStartPlayLoop,
            isEnd,
            handlePause,
            handlePlay,
            renderVideo,
            isLowDev: isLowDevice(),
            handleError,
        };
    },
});
</script>

<style lang="less" scoped>
.video-play {
    position: relative;
    width: 100%;
    height: 100%;
    background: no-repeat center center;
    &__video {
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
    }
    video::-webkit-media-controls-enclosure {
        display: none;
    }
}
</style>
