<template>
    <div class="fixed-current-rank gis-fixed-bottom-user-bg">
        <slot>
            <RankBase :item="data">
                <template #info>
                    <div></div>
                </template>
                <template #right>
                    <div class="operate-area text-12 flex">
                        <div class="score-label a-text-main">热力值：</div>
                        <div class="user-score a-text-highlight">
                            {{ data.h5ShowScore }}
                        </div>
                    </div>
                </template>
            </RankBase>
        </slot>
    </div>
</template>
<script setup lang="ts">
import RankBase from '@pet/ones-rank.base-item/index.vue';
import './assets/imgs.less';
const emits = defineEmits(['refresh']);

const props = defineProps({
    data: {
        type: Object as any,
        default: () => {
            return {};
        },
        required: false,
    },
});
</script>
<style lang="less" scoped>
.fixed-current-rank {
    position: fixed;
    bottom: 0;
    left: 50%;
    z-index: 10;
    overflow: hidden;
    transform: translateX(-50%);
    .rank-item-1-bg,
    .rank-item-2-bg,
    .rank-item-3-bg {
        background-image: none;
    }
}
</style>
