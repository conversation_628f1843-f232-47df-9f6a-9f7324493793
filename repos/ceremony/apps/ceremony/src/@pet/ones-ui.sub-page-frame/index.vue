<script lang="ts" setup>
import { useRouter } from 'vue-router';
import YodaImage from '@pet/yoda.image/img.vue';
import RuleIcon from '@pet/ones-ui.rule-icon/index.vue';
import { BackStatic } from '@alive-ui/icon';
import {
    PageHeader,
    PageAdaptor,
    ElseStatus,
    AKvTextIcon,
    RefreshIcon,
    ACard,
    ACardContent,
    ACardTitle,
} from '@alive-ui/base';
import { exitWebView, Report } from '@alive-ui/actions';
import type { PropType } from 'vue';
const router = useRouter();
const emits = defineEmits(['home-refresh', 'rank-refresh']);

type ShowBackAction = 'back' | 'exit';
const props = defineProps({
    cardName: {
        type: String,
        default: '',
    },
    showBack: {
        type: Boolean,
        default: false,
    },
    backAction: {
        type: String as PropType<ShowBackAction>,
        default: 'back',
    },
    ruleKey: {
        type: String,
        default: 'mainSchedulePage',
    },
    kvImage: {
        type: String,
        default: '',
    },
});
const logs = {
    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
    params: {
        btn_type: 'RULE',
    },
};
const handleBack = () => {
    const backFunction = {
        back: () => {
            router.back();
        },
        exit: () => {
            exitWebView();
        },
    };
    const backAction = props.backAction || 'back';
    if (backAction !== 'back' && backAction !== 'exit') {
        throw new Error('Invalid backAction value');
    }
    backFunction[backAction]();
};

const onYodaImageError = (e: Event, level: string) => {
    Report.biz.error('kv区yoda-image异常', {
        error: e,
        level,
    });
};
</script>

<template>
    <PageAdaptor :type="'cut'">
        <PageHeader class="sub-page-header">
            <template #leftTop>
                <!-- 返回按钮 -->
                <slot name="leftTop">
                    <BackStatic
                        v-if="showBack"
                        v-pcDirectives:hide
                        class="back-btn"
                        @click="handleBack"
                    />
                </slot>
            </template>
            <template #rightTop>
                <!-- 规则按钮 -->
                <RuleIcon
                    v-if="ruleKey"
                    :func-btn-log-params="logs"
                    :rule-key="ruleKey"
                ></RuleIcon>
            </template>
            <div class="kv-middle">
                <!-- kv区图片 -->
                <YodaImage
                    v-if="kvImage"
                    class="kv-image"
                    :src="kvImage"
                    @error="(e: Event) => onYodaImageError(e, 'l1')"
                ></YodaImage>
            </div>
        </PageHeader>
        <ACard class="sub-page-card">
            <ACardTitle v-if="cardName">
                {{ cardName }}
            </ACardTitle>
            <ACardContent class="flex-center-center">
                <slot name="default"></slot>
            </ACardContent>
        </ACard>
    </PageAdaptor>
</template>
<style lang="less" scoped>
.kv-middle {
    .kv-image {
        --y-img-height: 250px;
        --y-img-width: 414px;
    }
}
.sub-page-header {
    height: 190px;
}
.sub-page-card {
    margin-bottom: 0;
    padding-bottom: 60px;
}
</style>
