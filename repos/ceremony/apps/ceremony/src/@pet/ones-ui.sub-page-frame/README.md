# SubPageFrame 组件文档

## 功能概述

`SubPageFrame` 是一个用于构建子页面框架的 Vue 组件。该组件提供了一个包含标题栏、返回按钮、规则链接按钮和主要内容区域的布局。它支持通过不同的 `backAction` 来控制返回行为，并且可以显示规则链接和 kv 区域的图片。

## 属性 (Props)

| 属性名      | 类型             | 默认值     | 描述                                                                 |
| ----------- | ---------------- | ---------- | -------------------------------------------------------------------- |
| `cardName`  | `String`         | `''`       | 卡片标题，显示在卡片顶部。                                          |
| `showBack`  | `Boolean`        | `false`    | 是否显示返回按钮。                                                  |
| `backAction`| `String`         | `'back'`   | 返回操作类型，可选值为 `'back'` 或 `'exit'`。                       |
| `ruleLink`  | `String`         | `''`       | 规则链接地址，点击规则按钮时跳转到此链接。                           |
| `kvImage`   | `String`         | `''`       | kv 区域的图片 URL，显示在标题栏下方。                                |

## 事件 (Events)

| 事件名          | 参数             | 描述                                       |
| --------------- | ---------------- | ------------------------------------------ |
| `home-refresh`  | -                | 触发首页刷新事件。                         |
| `rank-refresh`  | -                | 触发榜单刷新事件。                         |

## 注意事项

1. `backAction` 的值必须为 `'back'` 或 `'exit'`，否则会抛出错误。
2. `kvImage` 如果存在，则会在标题栏下方显示 kv 区域的图片。
3. `ruleLink` 如果存在，则会在标题栏右侧显示规则按钮，点击后跳转到指定链接。

## 依赖项

- `vue-router`
- `@pet/yoda.image/img.vue`
- `@alive-ui/icon`
- `@alive-ui/base`
- `@alive-ui/actions`

### 依赖组件

- `PageHeader`
- `PageAdaptor`
- `ElseStatus`
- `AKvTextIcon`
- `RefreshIcon`
- `ACard`
- `ACardContent`
- `ACardTitle`
- `BackStatic`

### 依赖函数

- `useRouter`
- `goOtherPage`
- `exitWebView`
- `Report`