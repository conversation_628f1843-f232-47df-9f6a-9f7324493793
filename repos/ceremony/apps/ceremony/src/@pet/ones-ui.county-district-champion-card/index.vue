<template>
    <ACard
        v-show-log="{
            action: 'OP_ACTIVITY_HOMETOWN_GLORY_CARD',
        }"
        class="county-district-champion-card"
    >
        <ACardContent class="county-district-champion-content">
            <div class="activity-title">
                <!-- 活动名称使用渐变文字 -->
                <span class="activity-name a-text-title">{{
                    kconfData?.mainPage?.countyChampion?.title
                }}</span>

                <!-- 状态标签 -->
                <div class="tips a-text-main-o2">
                    <span>{{ kconfData?.mainPage?.countyChampion?.desc }}</span>
                </div>

                <!-- 查看按钮区域 -->
                <!-- <RuleIcon
                    :func-btn-log-params="{
                        action: 'OP_ACTIVITY_HOMETOWN_GLORY_CARD',
                        params: {
                            btn_type: '规则',
                        },
                    }"
                    class="rule-icon-box"
                    rule-key="countryDistrictChampionCard"
                ></RuleIcon> -->
            </div>
            <div v-pcDirectives:scroll="handleScroll" class="user-management">
                <div
                    v-for="(item, index) in championList"
                    :key="index"
                    class="champion-rank-item"
                >
                    <div
                        class="time-title"
                        :class="item.item ? 'a-text-main' : 'a-text-main-o2'"
                    >
                        {{ formatTime(item.scheduleStartTime, 'MM/dd') }}
                    </div>
                    <div class="user-box">
                        <APAvatar
                            v-click-log="{
                                action: 'OP_ACTIVITY_HOMETOWN_GLORY_CARD',
                                params: {
                                    btn_type: '头像',
                                },
                            }"
                            :user-id="item.item?.itemId"
                            :head-url="item.item?.headUrl"
                            :live-stream-id="item.item?.liveStreamId"
                            :is-mystery-man="item.item?.mysteryMan"
                            size="2xs"
                        />
                        <div
                            class="user-name"
                            :class="
                                item.item?.itemName
                                    ? 'a-text-main'
                                    : 'a-text-main-o2'
                            "
                        >
                            {{
                                item?.status === 1
                                    ? nameSlice(String(item.item?.itemName), 6)
                                    : '虚位以待'
                            }}
                        </div>
                    </div>

                    <!-- 使用 alive-ui 的 Avatar 组件
                        :live-stream-id-list="liveStreamIdList"
					   -->
                </div>
            </div>
        </ACardContent>
    </ACard>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import useKConfBatch from '@pet/ones-use.useKconfBatch';
import RuleIcon from '@pet/ones-ui.rule-icon/index.vue';
import { Avatar as APAvatar } from '@alive-ui/pro';
import { Right, Refresh } from '@alive-ui/icon';
import { ACard, ACardContent, ACardTitle, ACardSubtitle } from '@alive-ui/base';
import { nameSlice, formatTime } from '@alive-ui/actions';
import type { ChampionList } from '@pet/ones-rank.schema/query-rank';
const { kconfData } = storeToRefs(useKConfBatch());
// 定义组件props
const props = withDefaults(
    defineProps<{
        championList: ChampionList[];
    }>(),
    {
        championList: () => {
            return [];
        },
    },
);

// 处理滚动事件
const handleScroll = (e: Event) => {
    // 处理横向滚动逻辑
};
</script>

<style lang="less" scoped>
.county-district-champion-card {
    margin-left: 0;
    margin-right: 0;
    width: 100%;
    padding: 12px 0;
    :deep(.i-alive-avatar) {
        background-image: url('./assets/default-avatar.png');
    }
}
.activity-title {
    box-sizing: border-box;
    width: 382px;
    height: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0 12px;
    .tips {
        font-size: 12px;
    }
}

.activity-name {
    height: 20px;
    margin-right: 4px;
    font-size: 14px;
    line-height: 20px;
    font-family: HYYakuHei;
}

.user-management {
    width: 100%;
    height: 94px;
    border-radius: 8px;
    display: flex;
    align-items: flex-start;
    overflow-x: auto;
    margin-top: 8px;
    justify-content: center;
}
.champion-rank-item {
    width: 96px;
    height: 94px;
    display: flex;
    flex-flow: column;
    align-items: center;
    justify-content: center;
    position: relative;
    .time-title {
        font-size: 12px;
        font-weight: 600;
    }
    .user-box {
        width: 100%;
        height: 68px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgba(154, 189, 255, 0.08);
        border-radius: 8px;
        margin-top: 8px;
        flex-flow: column;
        .user-name {
            font-size: 12px;
            font-weight: 500;
            margin-top: 4px;
        }
    }
    & + .champion-rank-item {
        margin-left: 24px;
        &::before {
            content: '';
            width: 24px;
            height: 100%;
            position: absolute;
            left: -24px;
            top: 0;
            background: url(./assets/left-line.png) no-repeat top / 100%;
        }
    }
}
.rule-icon-box {
    position: absolute;
    right: 12px;
    top: 0;
    :deep(.rule-icon) {
        width: 36px;
        height: 18px;
        background-size: 66px 18px;
        background-position: right 0;
    }
}
</style>
