import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { HomeInfoData } from '@pet/ones-rank.schema/global';
export const sceneAllFun = (contextName: symbol) => {
    const homeData = contextName
        ? inject<Ref<HomeInfoData>>(contextName)
        : null;
    const data = computed(() => {
        return {
            currAuthorHeadUrl: homeData?.value?.currAuthorInfo?.headUrl,
            subView: homeData?.value?.secondLevelStageTableView,
            currentAnchorStageType: homeData?.value?.anchorStageType,
            list: homeData?.value?.stageLineViewList,
            lookingBack: homeData?.value?.lookingBack,
            rule: homeData?.value?.rule || '',
            curStageType:
                homeData?.value?.anchorDisplayScheduleAndLane?.curStageType,
        };
    });
    return data;
};
