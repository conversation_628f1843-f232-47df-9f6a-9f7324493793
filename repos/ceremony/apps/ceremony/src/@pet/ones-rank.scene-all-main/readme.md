# 赛程全景组件 Schedule All Scene Component

## 功能描述
该组件用于展示所有赛程的详细信息，包括当前赛程、赛程状态、时间等。用户可以通过点击不同的赛程项来查看具体信息，并且可以通过弹出窗口了解赛程规则。

## Props 提取
该组件接收以下 props：

| Prop Name     | Type     | Default Value | Description                               |
|---------------|----------|---------------|-------------------------------------------|
| contextName   | String   | ''            | 上下文名称，用于数据注入                |

## Vue 使用代码 Demo
以下是如何在 Vue 组件中使用 `ScheduleAllScene` 组件的示例：

```vue
<script lang="ts" setup>
import { provide, defineAsyncComponent } from 'vue';
import ScheduleAllScene from './ScheduleAllScene.vue';

const contextName = 'yourContextName'; // 替换为实际的上下文名称
provide(contextName, yourData); // 注入数据

</script>

<template>
  <div>
    <ScheduleAllScene :contextName="contextName" />
  </div>
</template>
```
## 数据处理方式
组件通过 contextName 注入上下文数据，使用 dataFun 方法处理数据。确保在使用组件之前，相关的数据已经通过 provide 注入。

```typescript
import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';

export const dataFun = (contextName: string) => {
    const rankInfo = contextName
        ? inject<Ref<QueryRankPostResponse>>(contextName)
        : null;

    const data = computed(() => {
        return {
            // 处理数据逻辑
        };
    });

    return data;
};
```
