<template>
    <div
        v-if="data?.list?.length"
        v-click-log="funcBtnLog('赛程全景')"
        v-show-log="funcBtnLog('赛程全景')"
        class="schedule-all-scene"
    >
        <div class="gis-scene-all-icon" @click="openScenePop" />
        <Popup
            v-if="isShowPop"
            v-model="isShowPop"
            position="bottom"
            popup-class="scene-popup"
            @hide="reset"
        >
            <div class="gis-card-top-bg" />
            <div class="scene-all-pop a-bg-part">
                <div class="header">
                    <div class="a-text-title scene-first-title">全部赛程</div>
                    <div class="rule flex items-center" @click="openRulePage">
                        <span class="a-text-main">了解赛程</span>
                        <div class="arrow-icon"></div>
                    </div>
                </div>
                <div class="wrap">
                    <template
                        v-if="
                            !data.lookingBack &&
                            data.subView?.subStageTableViewList &&
                            data.subView?.subStageTableViewList?.length > 1
                        "
                    >
                        <div class="a-text-main mb-8px">当前赛程:</div>
                        <div class="current-track all-scene-bg-large">
                            <div
                                class="icon-large"
                                :style="{
                                    background: `url('${data.subView?.iconPic}') center / 100% no-repeat`,
                                }"
                            ></div>
                            <div class="top-title-info flex align-center">
                                <div class="stage-name-gradient a-text-title">
                                    {{ data.subView?.stageName }}
                                </div>
                                <div
                                    class="stage-status"
                                    :class="`gis-stage-status-' + ${data.subView?.stageType}`"
                                />
                            </div>
                            <!-- <div
                                class="stage-time-area a-text-main flex align-center"
                            >
                                {{
                                    startTimeToEndTime(
                                        data.subView?.stageStartTime,
                                        data.subView?.stageEndTime,
                                    )
                                }}
                            </div> -->
                            <div
                                v-if="data.subView?.subStageTableViewList"
                                class="sub-track-list flex"
                            >
                                <div
                                    v-for="(item, index) in data.subView
                                        ?.subStageTableViewList"
                                    :key="item.stageType"
                                    class="flex-center"
                                    @click="
                                        onChange(
                                            data.subView?.subStageTableViewList,
                                            index,
                                            true,
                                        )
                                    "
                                >
                                    <div
                                        class="w-full h-full flex-center sub-track-item"
                                        :class="
                                            data.currentAnchorStageType ===
                                            item.stageType
                                                ? 'sub-track-bg-active'
                                                : 'sub-track-bg'
                                        "
                                    >
                                        <div
                                            v-if="
                                                data.curStageType ===
                                                item.stageType
                                            "
                                            class="current-stage"
                                        ></div>
                                        <div class="flex-center">
                                            <span
                                                class="a-text-main text-[12px] font-500 text-center"
                                                >{{ item.stageName }}</span
                                            >
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <div class="all">
                        <div class="a-text-main mb-8px">全部赛程:</div>
                        <div class="track-scroll-wrap flex flex-wrap">
                            <div
                                v-for="(item, index) in data.list"
                                :key="item.stageType"
                                class="track-item all-scene-bg"
                                @click="onChange(data.list, index, false)"
                            >
                                <div
                                    class="icon"
                                    :style="{
                                        background: `url('${item.iconPic}') center / 100% no-repeat`,
                                    }"
                                ></div>
                                <div
                                    :class="{
                                        'gis-active-track-bg':
                                            data.subView?.rootStageType ===
                                            item.stageType,
                                    }"
                                />
                                <div class="top-title-info flex align-center">
                                    <div
                                        class="stage-name-gradient a-text-title"
                                    >
                                        {{ item.stageName }}
                                    </div>
                                    <div
                                        class="stage-status"
                                        :class="
                                            'gis-stage-status-' +
                                            item.stageStatus
                                        "
                                    />
                                </div>
                                <div
                                    class="stage-time-area a-text-main flex align-center"
                                >
                                    {{
                                        startTimeToEndTime(
                                            item.stageStartTime,
                                            item.stageEndTime,
                                        )
                                    }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Popup>
    </div>
</template>

<script lang="ts">
export interface ScheduleAllSceneRef {
    // eslint-disable-next-line @typescript-eslint/ban-types
    openScenePop: Function;
}
</script>

<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { stickLockScroll } from '@pet/ones-use.useStickyElement';
import { usePopAdapter } from '@pet/ones-use.usePopAdapter';
import useKConfBatch from '@pet/ones-use.useKconfBatch';
import { Popup, Toast } from '@lux/sharp-ui-next';
import {
    timestampToTime,
    stopMove,
    goOtherPage,
    activityBiz,
} from '@alive-ui/actions';
import { sceneAllFun } from './data';
import type { StopFunItem } from '@alive-ui/actions';
import './assets/imgs.less';

const props = defineProps({
    // provide name
    contextName: {
        type: Symbol,
        default: Symbol.for('ctx'),
    },
});
const data = sceneAllFun(props.contextName);
const emit = defineEmits(['change']);
const { kconfData } = storeToRefs(useKConfBatch());

const isShowPop = ref(false);
const { adapter, recover, moveDistance } = usePopAdapter('left');
let stopMoveObj: StopFunItem;
watch(
    () => isShowPop.value,
    (o) => {
        if (o) {
            stickLockScroll.value = true;
            stopMoveObj = stopMove();
            nextTick(() => {
                adapter();
                const el = document.querySelector('.scene-popup');
                if (el) {
                    (el as any).style.left = `${moveDistance.value}px`;
                }
            });
        } else {
            stopMoveObj?.cancelMove?.();
            recover();
            const el = document.querySelector('.scene-popup');
            if (el) {
                (el as any).style.left = `0px`;
            }
            stickLockScroll.value = false;
        }
    },
    { immediate: true },
);

const openRulePage = () => {
    const ruleList: Record<string, any> =
        kconfData.value?.common?.ruleMap?.list ?? {};
    const ruleUrl = ruleList?.mainSchedulePage?.url ?? '';
    if (ruleUrl) {
        const url = new URL(ruleUrl);
        // 修改参数
        url.searchParams.set('activityBiz', activityBiz);
        goOtherPage('jimu', url.toString());
    }
};

const openScenePop = () => {
    isShowPop.value = true;
};

const reset = () => {
    isShowPop.value = false;
};

const onChange = (list: any, index: number, current: boolean) => {
    const item = list?.[index];

    const stagetype = current
        ? data.value.currentAnchorStageType
        : data.value.subView?.rootStageType;

    if (stagetype === item.stageType) {
        return;
    }
    if (item.stageStatus === 1) {
        Toast.info('赛程未开始');
        return;
    }
    emit('change', item);
    isShowPop.value = false;
};
const funcBtnLog = (type: string) => {
    return {
        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
        params: {
            btn_type: type,
        },
    };
};

const startTimeToEndTime = (start: number, end: number) => {
    return `${timestampToTime(start).trimEnd()}-${timestampToTime(end).trimEnd()}`;
};

defineExpose({
    openScenePop,
});
</script>

<style lang="less" scoped>
.scene-popup {
    width: 414px;
    :deep(.spu-popup__box) {
        @apply a-bg-page;
        border-radius: 16px 16px 0 0;
    }
}
.scene-all-pop {
    box-sizing: border-box;
    height: 544px;
    overflow: hidden;
    padding: 20px 16px;
    border-top-right-radius: 16px;
    border-top-left-radius: 16px;
}
.header {
    position: relative;
}
.gis-card-top-bg {
    position: absolute;
    top: 0;
    width: 100%;
    height: 168.5px;
    margin: 0 auto;
    pointer-events: none;
    background: url('./assets/card-top-img_2x.png') top / 100% no-repeat;
}
.rule {
    position: absolute;
    top: 4px;
    right: 0;
    z-index: 1;
    font-weight: bold;
}
.wrap {
    position: relative;
    height: 100%;
    margin-bottom: 20px;
    width: 100%;
    overflow: auto;
    &::-webkit-scrollbar {
        display: none;
    }
}
.arrow-icon {
    width: 12px;
    height: 12px;
    background: url('./assets/arrow-right_2x.png') center / cover no-repeat;
}
.stage-name-gradient {
    position: relative;
    z-index: 11;
    font-family: HYYakuHei;
    font-size: 14px;
    line-height: 14px;
    margin-bottom: 2px;
    color: transparent;
    background-clip: text;
}

.track-scroll-wrap {
    position: relative;
    width: 382px;
    height: 100%;
    margin-bottom: 20px;
}
.current-track {
    position: relative;
    display: inline-block;
    width: 382px;
    height: 90px;
    padding: 13.5px 0 0 12px;
    margin-bottom: 7.5px;
}
.icon-large {
    position: absolute;
    top: 18px;
    left: 268px;
    width: 114px;
    height: 72px;
    z-index: 0;
}
.icon {
    position: absolute;
    top: 0;
    left: 91px;
    width: 95px;
    height: 60px;
    z-index: 1;
}
.icon-close {
    position: absolute;
}
.sub-track-list {
    position: relative;
    margin-top: 7.5px;
}
.sub-track-item {
    width: 92px;
    height: 34px;
    position: relative;
    margin-right: 4px;
}
.current-stage {
    position: absolute;
    width: 32px;
    height: 10px;
    top: 0;
    right: 0;
    border-radius: 0 4px 0 2px;
    background: url('./assets/current-stage-text_2x.png') center / cover
        no-repeat;
}
.avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 4px;
    &.avatar-default-border {
        border: none;
    }
}
.track-item {
    position: relative;
    display: inline-block;
    width: 186px;
    height: 60px;
    padding: 13.5px 0 0 12px;
    margin-bottom: 10px;
    &:nth-child(odd) {
        margin-right: 10px;
    }
}

.gis-active-track-bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    background: url('./assets/active-track-bg_2x.png') center / cover no-repeat;
}
.scene-first-title {
    position: relative;
    z-index: 1;
    font-size: 20px;
    font-family: HYYakuHei;
    letter-spacing: 0px;
    line-height: 28px;
    text-align: center;
    vertical-align: middle;
    width: fit-content;
    margin: 0 auto;
}
.schedule-all-scene {
    //position: absolute;
    //left: 12px;
    //z-index: 1;
}
.stage-time-area {
    margin-top: 2px;
    z-index: 11;
    font-family: 'PingFang SC';
    font-size: 12px;
    line-height: 17px;
    text-align: left;
    opacity: 0.7;
}
.stage-status {
    position: relative;
    z-index: 11;
    margin-left: 4px;
}
.gis-honor-btn {
    position: absolute;
    top: 10px;
    right: 10px;
}
</style>
