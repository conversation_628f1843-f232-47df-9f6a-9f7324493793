import { startVibrate } from '@alive-ui/actions';

export interface ModTabContext {
    changeFn: (index: number) => void;
    changeByIdxFn?: (index: number) => void;
}

/**
 * 提供默认 tab 服务
 */
export function ModTabInitialService(modCtx: ModTabContext) {
    if (!modCtx.changeByIdxFn) {
        modCtx.changeByIdxFn = () => {};
    }
}

export function ModTabChangeService(modCtx: ModTabContext) {
    modCtx.changeByIdxFn = (index: number) => {
        startVibrate({
            duration: 30,
            strength: 'high',
        }).catch(() => {
            navigator?.vibrate?.(3);
        });
        modCtx.changeFn(index);
    };
}

export function ModTabService(modCtx: ModTabContext) {
    ModTabInitialService(modCtx);
    ModTabChangeService(modCtx);
}
