<template>
    <div v-if="awardData?.marqueeList?.length" class="mt-12px mb-12px">
        <AInfo type="solid">
            <AMarquee>
                <AInfoHighlight
                    v-for="item in awardData.marqueeList"
                    :text="item"
                />
            </AMarquee>
            <template #extra>
                <AInfoButton
                    v-clickLog="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: '奖励',
                        },
                    }"
                    v-showLog="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: '奖励',
                        },
                    }"
                    class="rank-award-button"
                    @click="foldCollapse"
                >
                    <span class="inline-block mr-4px">
                        {{ awardData.operateText }}</span
                    >
                    <template #suffix>
                        <Up :class="{ 'rotate-180': foldStatus }"
                    /></template>
                </AInfoButton>
            </template>
        </AInfo>
        <div
            v-show="foldStatus"
            class="rank-award-content mx-auto mt-10px pb-10px a-bg-substrate rounded-6px"
        >
            <img
                v-if="foldStatus && awardData.awardURL"
                class="w-auto h-full"
                :src="awardData.awardURL"
                alt="活动奖励说明"
            />
        </div>
        <slot name="extra"> </slot>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Up } from '@alive-ui/icon';
import { Info, Marquee as AMarquee } from '@alive-ui/base';
import type { PropType } from 'vue';
const {
    Info: AInfo,
    InfoHighlight: AInfoHighlight,
    InfoButton: AInfoButton,
} = Info;

const props = defineProps({
    awardData: {
        type: Object as PropType<{
            marqueeList?: string[];
            operateText?: string;
            showAward?: boolean;
            awardURL: string;
        }>,
        default: () => {
            return {
                marqueeList: ['Top1主播将获得流量'],
                operateText: '奖励',
                showAward: false,
                awardURL: '',
            };
        },
    },
});

const foldStatus = ref(false);
watch(
    () => props.awardData.showAward,
    (val, old) => {
        if (val && val !== old) {
            foldStatus.value = val;
        }
    },
    { immediate: true },
);
const foldCollapse = () => {
    foldStatus.value = !foldStatus.value;
};
</script>
<style scoped>
.rank-award-content {
    width: 358px;
    min-height: 100px;
    font-size: 0;
    overflow-y: scroll;
    img {
        width: 100%;
    }
}
.rank-award-button {
    height: 100%;
    white-space: nowrap;
}
</style>
