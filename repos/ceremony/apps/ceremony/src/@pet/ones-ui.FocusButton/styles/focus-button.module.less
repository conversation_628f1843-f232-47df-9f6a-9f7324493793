.mask-shine {
    position: absolute;
    left: 0;
    z-index: 9;
    width: inherit;
    height: inherit;
    overflow: hidden;
    border-radius: 24px;
}

.mask {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    display: flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 0.48rem;
    background-size: cover;
    // 临时瞎写的
    background: linear-gradient(127deg, transparent 32%, rgba(255, 255, 255, 0.3) 46%, transparent 60%);

    background-repeat: no-repeat;
    animation: shine-data 2s linear infinite;
}

@keyframes shine-data {
    0% {
        transform: translate3d(-100%, 0, 0);
    }

    100% {
        transform: translate3d(224px, 0, 0);
    }
}
