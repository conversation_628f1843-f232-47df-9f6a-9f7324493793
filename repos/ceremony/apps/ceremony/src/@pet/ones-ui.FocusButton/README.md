# FocusButton 组件文档

## 功能概述

`FocusButton` 是一个用于关注用户的按钮组件。它可以根据用户的关注状态动态显示不同的文本，并且支持按钮上的扫光效果。该组件主要用于排行榜等场景中，当用户点击按钮时，会触发关注操作，并记录相关的日志信息。

## 属性（Props）

| 属性名       | 类型                    | 默认值     | 描述                                                         |
|------------|------------------------|----------|------------------------------------------------------------|
| `needShine` | `boolean`               | `false`  | 按钮是否需要扫光效果。                                     |
| `followText` | `string`                | `'已关注'` | 用户已关注时按钮显示的文本。                                 |
| `unFollowText` | `string`               | `'关注'`  | 用户未关注时按钮显示的文本。                                 |
| `actionStatus` | `boolean`              | `false`  | 用户的关注状态，`true` 表示已关注，`false` 表示未关注。      |
| `focusParams` | `Omit<FocusUserProps, 'info'>` | -       | 关注功能所需的基本参数，具体类型定义见 `@alive-ui/actions`。 |

## 事件（Emit Events）

| 事件名         | 参数类型  | 描述                                                         |
|--------------|---------|------------------------------------------------------------|
| `focus-success` | `any`   | 关注成功时触发，参数为关注操作的结果。                       |
| `focus-error`   | `Error` | 关注失败时触发，参数为错误对象。                             |

## 使用示例

```vue
<template>
  <FocusButton
    :needShine=\"true\"
    :followText=\"'已关注'\"
    :unFollowText=\"'关注'\"
    :actionStatus=\"isFollowed\"
    :focusParams=\"focusParams\"
    @focus-success=\"handleFocusSuccess\"
    @focus-error=\"handleFocusError\"
  />
</template>

<script setup>
import { ref } from 'vue';
import FocusButton from '@pet/ones-ui.FocusButton';

const isFollowed = ref(false);
const focusParams = {
  userId: '12345',
  scheduleId: '67890',
  laneId: '111213',
};

const handleFocusSuccess = (success) => {
  console.log('关注成功:', success);
  isFollowed.value = true;
};

const handleFocusError = (error) => {
  console.error('关注失败:', error);
};
</script>
```

## 注意事项

- `focusParams` 是必填项，必须包含 `userId`、`scheduleId` 和 `laneId`。
- `actionStatus` 用于控制按钮的状态，必须传递。
- 按钮的点击事件会被节流处理，防止短时间内多次点击。

## 依赖项

- `vue`
- `lodash-es`
- `@alive-ui/base`
- `@alive-ui/actions`