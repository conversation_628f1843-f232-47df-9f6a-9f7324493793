<script lang="ts" setup>
import { computed, ref, watchEffect } from 'vue';
import { throttle } from 'lodash-es';
import { Button as AButton } from '@alive-ui/base';
import { activityBiz, focusUsers, getPageCode } from '@alive-ui/actions';
import { useBaseRankItemContext } from './types';
import styles from './styles/focus-button.module.less';
import type { FocusUserProps } from '@alive-ui/actions';

export interface ActionButtonProps {
    /* 按钮是否需要扫光 */
    needShine?: boolean;
    /* 关注后文案 */
    followText?: string;
    /* 未关注文案 */
    unFollowText?: string;
    /* 关注功能必传，关注状态 */
    actionStatus?: boolean;
    /* 关注功能必传 FocusUserProps 的前三个参数 */
    focusParams: Omit<FocusUserProps, 'info'>;
}

const emit = defineEmits(['focus-error', 'focus-success']);

const { logRankItemClick, getRankListLogParams } =
    useBaseRankItemContext() || {};

const baseLogParams = () => {
    return getRankListLogParams
        ? {
              info: {
                  followSource: 187,
                  bizCustomParams: JSON.stringify({
                      live_activity_name: activityBiz,
                      position: 'RANK_LIST_FOLLOW_OVERT',
                      schedule_id: getRankListLogParams().scheduleId,
                      extraFollow: JSON.stringify({
                          page_code: getPageCode(),
                      }),
                  }),
              },
          }
        : {};
};

const props = withDefaults(defineProps<ActionButtonProps>(), {
    needShine: false,
    followText: '已关注',
    unFollowText: '关注',
    actionStatus: false,
});

const isFollowed = ref<boolean>(false);

watchEffect(() => {
    isFollowed.value = props.actionStatus;
});

const statusText = computed(() => {
    return isFollowed.value ? props.followText : props.unFollowText;
});

const focusUser = async () => {
    if (isFollowed.value) {
        return;
    }
    // 用在榜单内才会有这个方法
    logRankItemClick?.('FOLLOW_BTN');
    try {
        const success = await focusUsers({
            ...props.focusParams,
            ...baseLogParams(),
        });
        isFollowed.value = true;
        emit('focus-success', success);
    } catch (error) {
        emit('focus-error', error);
    }
};

const actionButtonEvent = throttle(focusUser, 800, {
    leading: true,
    trailing: false,
});
</script>

<template>
    <AButton
        class="relative"
        type="primary"
        size="sm"
        :disabled="isFollowed"
        @click="actionButtonEvent"
    >
        <div v-if="needShine && !isFollowed" :class="styles['mask-shine']">
            <div :class="styles['mask']" />
        </div>
        <slot>
            {{ statusText }}
        </slot>
    </AButton>
</template>
