import { inject, type InjectionKey } from 'vue';

/**
 * 埋点数据结构
 */
export interface BaseRankListLogParams {
    rankId: string;
    scheduleId: string;
    laneClassifyName: string;
    laneName: string;
    listType: string;
}

export interface BaseRankItemLogParams {
    item: {
        itemId: number;
    };
    h5RankShowIndex?: string;
    followStatus: boolean; // 是否已经关注
    liveStreamId: string; // 直播间id
    itemId: string;
    sponsors: BaseRankItemLogParams[];
}

// item 点击类型，送礼和助力用 HELP, 关注用 FOCUS_BTN
export type ItemClickType = 'HELP' | 'FOLLOW_BTN' | 'RANK_ITEM' | '';

export interface ItemState {
    getRankListLogParams: () => BaseRankListLogParams;
    logRankItemClick: (clickType?: ItemClickType) => void;
}

export const BaseRankItemContext = Symbol(
    'BaseRankItemContext',
) as InjectionKey<ItemState>;

export function useBaseRankItemContext() {
    return inject(BaseRankItemContext, null);
}
