import { computed, isRef } from 'vue';
import useKconf from '@pet/ones-use.useKconfBatch';

interface Params {
    stageType: number;
    scheduleType: number;
    laneClassifyName: string;
}
export const getKvConfig = (
    params: Params,
    kvConfig: any,
    isSecondaryPage?: boolean,
) => {
    const kconfStore = useKconf();
    // 赛事配置
    const competition = computed(() => {
        const temp: Record<string, any> = {};
        kconfStore.kconfData.common?.stageTypeList?.forEach((item) => {
            temp[`${item.stageType}`] = {
                theme: item.theme,
                // 用stageName
                desc: item.stageName,
            };
        });
        return temp;
    });
    const { stageType, laneClassifyName, scheduleType } = params || {};

    const currentConfig = kvConfig;
    const finalIds = currentConfig?.finalIds || [];
    const commonConfig = kconfStore.kconfData.common ?? {};
    const districtIds = commonConfig?.stageGroup?.cityGroup ?? [];
    const {
        smallKv = {},
        district = {}, // 地区kv库
        bigKvTheme = {}, // kv 主题库
    } = currentConfig;
    if (!stageType || (districtIds.includes(stageType) && !laneClassifyName)) {
        return {};
    }
    let kvOriginConfig = bigKvTheme.default!;
    const competitionConfig = competition.value?.[stageType] || {};
    const defaultBigKv = bigKvTheme?.default?.bigKv;
    if (competitionConfig?.theme) {
        kvOriginConfig = {
            bigKv: defaultBigKv,
            ...bigKvTheme[competitionConfig.theme],
            smallKv: smallKv.default,
        };
    } else {
        kvOriginConfig = {
            bigKv: defaultBigKv,
            ...(competitionConfig?.default || {}),
            smallKv: smallKv.default,
        };
    }

    kvOriginConfig.kvSrc = kvOriginConfig.bigKv;
    kvOriginConfig.kvVideo = kvOriginConfig.videoUrl;
    // 地区赛处理
    if (districtIds.includes(+stageType) && laneClassifyName) {
        // eslint-disable-next-line prefer-destructuring
        const districtKey = laneClassifyName.split('-')?.[0];
        console.log('districtKey', districtKey);
        kvOriginConfig.kvSrc =
            district[districtKey]?.bigKv || kvOriginConfig.bigKv;
        kvOriginConfig.kvVideo = district[districtKey]?.bigKv
            ? ''
            : kvOriginConfig.kvVideo;
    }
    // 决赛日效果
    if (finalIds.includes(scheduleType)) {
        kvOriginConfig.kvSrc =
            kvOriginConfig.final_champion_kv || kvOriginConfig.bigKv;
        kvOriginConfig.smallKv = smallKv.champion;
        kvOriginConfig.kvVideo = kvOriginConfig.final_champion_kv_video;
    }
    // 判断是否是二级页面,只修改吸顶背景
    if (isSecondaryPage) {
        kvOriginConfig.smallKv = smallKv.secondary;
    }

    // 返回当前配置的默认值
    return kvOriginConfig;
};

export function useKvConfig(data: any, isSecondaryPage?: boolean) {
    const kvStore = useKconf();
    return computed(() => {
        const useData = isRef(data) ? data.value : data;
        const kvConfig = kvStore.kconfData.kv || {};
        return getKvConfig(useData, kvConfig, isSecondaryPage);
    });
}
