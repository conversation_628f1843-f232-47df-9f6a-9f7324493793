# KV 组件文档

## 功能概述

KV 组件用于展示赛事的主要视觉信息，包括背景图片、视频播放、赛程信息等。该组件支持大布局模式和小布局模式，并且能够根据屏幕尺寸和滚动位置自动切换显示模式。

## 属性和方法

### Props

-   **contextName** (`string`): 上下文名称，用于传递给子组件。
-   **dataSuccess** (`boolean`): 数据请求是否成功，默认为 `false`。
-   **entryData** (`any`): 入口数据，默认为空对象。
-   **showVideo** (`boolean`): 是否显示视频，默认为 `false`。
-   **type** (`'raise' | 'cut'`): 显示模式，默认为 `'cut'`。
-   **data** (`object`): 包含赛程信息的对象，具体字段如下：
    -   **stageType** (`number`): 赛程类型，默认为 `0`。
    -   **backgroundColor** (`string`): 背景颜色，默认为 `'transparent'`。
    -   **laneClassifyName** (`string`): 赛道分类名称。
    -   **laneName** (`string`): 赛道名称。
    -   **scheduleType** (`number`): 赛段类型，默认为 `0`。
    -   **ruleUrl** (`string`): 规则链接，默认为空字符串。
    -   **offseason** (`boolean`): 是否为休赛期，默认为 `false`。
    -   **isHaveStage** (`boolean`): 是否有赛程，默认为 `false`。

### Emit Events

-   **clickIcon** (`item: EntryInfo`): 点击图标时触发，参数为点击的入口信息。
-   **clickVideo**: 点击视频时触发。
-   **onSceneAllMainChange** (`val: any`): 场景变化时触发，参数为变化后的值。

## 使用示例

```vue
<template>
  <kv :contextName=\"contextName\" :dataSuccess=\"dataSuccess\" :entryData=\"entryData\" :showVideo=\"true\" :type=\"'raise'\" :data=\"data\" @clickIcon=\"handleClickIcon\" @clickVideo=\"handleClickVideo\" @onSceneAllMainChange=\"handleOnSceneAllMainChange\" />
</template>

<script setup>
import { ref } from 'vue';
import Kv from './kv.vue';

const contextName = ref('main');
const dataSuccess = ref(true);
const entryData = ref({});
const data = ref({
  stageType: 1,
  backgroundColor: '#ffffff',
  laneClassifyName: '分类名称',
  laneName: '赛道名称',
  scheduleType: 1,
  ruleUrl: '/rules',
  offseason: false,
  isHaveStage: true,
});

const handleClickIcon = (item) => {
  console.log('Icon clicked:', item);
};

const handleClickVideo = () => {
  console.log('Video clicked');
};

const handleOnSceneAllMainChange = (val) => {
  console.log('Scene changed:', val);
};
</script>
```

## 注意事项

-   组件会根据屏幕尺寸和滚动位置自动切换大布局模式和小布局模式。
-   在休赛期（`offseason` 为 `true`）时，组件会显示特定的休赛期内容。
-   组件内部使用了多个异步加载的子组件，确保在使用时这些子组件已经正确引入。

## 依赖项

-   `@pet/yoda.image/img.vue`
-   `@pet/ones-use.useKconf/index`
-   `@pet/ones-use.kv-kconf`
-   `@pet/ones-ui.video-play/index.vue`
-   `@pet/ones-rank.secondary-entry/index.vue`
-   `@alive-ui/system`
-   `@alive-ui/actions`
-   `@pet/ones-rank.scene-all-main/index.vue`
-   `@pet/ones-ui.recharge-icon/models/recharge`
