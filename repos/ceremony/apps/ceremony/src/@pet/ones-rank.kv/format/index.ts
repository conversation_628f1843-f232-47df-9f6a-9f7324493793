// eslint-disable-next-line @typescript-eslint/default-param-last
import { getQuery } from '@alive-ui/actions';
export const kvFormat = (homeInfo: any = {}) => {
    const { anchorDisplayScheduleAndLane = {}, anchorStageType } = homeInfo;
    const { laneName, scheduleType } = anchorDisplayScheduleAndLane || {};
    const stageType = getQuery('stageType') || anchorStageType;
    return {
        laneName,
        // 转数字
        stageType: +stageType,
        scheduleType: +scheduleType,
        ruleUrl: homeInfo.rule,
        offseason: homeInfo.offseason,
        isHaveStage:
            homeInfo.lookingBack &&
            homeInfo?.secondLevelStageTableView?.subStageTableViewList &&
            homeInfo?.secondLevelStageTableView?.subStageTableViewList?.length >
                1,
    };
};
