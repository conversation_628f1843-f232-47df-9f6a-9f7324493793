<template>
    <div
        :class="{
            'kv-view': true,
            'kv-offseason': data.offseason,
        }"
    >
        <!-- kv 大布局模式 -->
        <div class="big-kv">
            <!-- 背景图 -->
            <div ref="kvRef" class="video-img">
                <YodaImage
                    v-if="bigKv"
                    class="cool-kv-image"
                    :src="bigKv"
                    @error="(e: Event) => onYodaImageError(e, 'l1')"
                />
                <div class="video-play-box">
                    <videoPlay
                        v-if="showVideo && videoUrls?.length && !isLowDev"
                        object-fit="fill"
                        :video-url="videoUrls"
                        not-click
                    />
                    <div class="guo-du"></div>
                </div>
            </div>
            <!-- 左侧区域 -->
            <div
                class="left-area big-kv-icon play-icon-wrap"
                :style="isOutLiveRoomStyle"
            >
                <div class="elem-entry-icon">
                    <SceneAllMain
                        :context-name="contextName"
                        @change="onSceneAllMainChange"
                    />
                </div>
                <SecondaryEntry
                    v-if="entryData?.['topLeftArea']?.length && showTopLeftIcon"
                    class="top-left-icon-area"
                    flex-direction="column"
                    :entry-data="entryData['topLeftArea']"
                    need-margin
                    :stage-type="data.stageType"
                    @route-query-change="onRouteQueryChange"
                />
            </div>
            <!-- 右侧区域 -->
            <div
                class="right-area big-kv-icon play-icon-wrap"
                :style="isOutLiveRoomStyle"
            >
                <div class="elem-entry-icon">
                    <Rule
                        rule-key="mainSchedulePage"
                        :func-btn-log-params="{
                            action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                            params: {
                                btn_type: '规则',
                            },
                        }"
                    ></Rule>
                </div>
            </div>
            <!-- 底部区域1 -->
            <div v-if="bigKv && !data.offseason" class="big-bottom-icon">
                <div class="stage-box">
                    <slot name="stage">
                        <StageName
                            :context-name="contextName"
                            @stage-refresh-task="onStageChange"
                        ></StageName>
                    </slot>
                </div>
                <div
                    ref="secEntryWrapRef"
                    class="big-bottom-icon-box"
                    :class="{ 'no-indicate': !showEntryIndicate }"
                >
                    <SecondaryEntry
                        ref="secondaryEntryRef"
                        v-pcDirectives:scroll
                        :sec-entry-wrap-pure-width="secEntryWrapPureWidth"
                        class="flex"
                        flex-direction="row"
                        :entry-data="entryData['verticalArea']"
                        slide-type="unfold"
                        need-margin
                        :stage-type="data.stageType"
                        @touch-trigger="onTouchTrigger"
                        @route-query-change="onRouteQueryChange"
                    />
                </div>
                <div v-if="showEntryIndicate" class="sec-entry-indicator">
                    <div
                        v-for="(_, index) in 2"
                        :key="index"
                        class="indicator-item"
                        :class="{
                            'active-indicator': index === indicatorActiveIndex,
                        }"
                    ></div>
                </div>
            </div>
        </div>
        <!-- kv 小模式 -->
        <Transition :name="data.offseason ? '' : 'icon'" appear>
            <div
                v-if="showSmallKV"
                class="small-kv-wrap"
                :style="{
                    ...raiseHeaderStyle,
                    // ...isOutLiveRoomStyle,
                }"
            >
                <!-- 背景图 -->
                <YodaImage
                    v-if="smallKv"
                    :src="smallKv"
                    class="small-kv-bg"
                    @error="(e: Event) => onYodaImageError(e, 'l1')"
                />
                <!-- 小kv底部icon -->
                <div class="small-kv-top-icon-wrap">
                    <div class="elem-entry-icon-top">
                        <SceneAllMain
                            :context-name="contextName"
                            @change="onSceneAllMainChange"
                        />
                    </div>
                    <div class="elem-entry-icon-top">
                        <Rule
                            rule-key="mainSchedulePage"
                            :func-btn-log-params="{
                                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                                params: {
                                    btn_type: '规则',
                                },
                            }"
                        ></Rule>
                    </div>
                </div>
                <!-- 吸顶，默认吸顶小 icon -->
                <slot name="anchor">
                    <CurrentAnchor
                        v-if="
                            anchorData &&
                            anchorConfigs?.bottom &&
                            hint.displayName
                        "
                        class="kv-anchor"
                        :class="{ 'anchor-sticky': showSmallKV }"
                        :data="anchorData"
                        :stage-type="stageType"
                        :peak-rank-task="peakRankTask"
                        :use-card-class="false"
                    >
                        <template #ca-hint>
                            <!-- 主播未参赛，不展示主播id下方的描述 -->
                            <div
                                v-if="
                                    !anchorData.isJoinActivity &&
                                    !anchorData.isJoinShadowActivity
                                "
                            ></div>
                            <div
                                v-else-if="!notInGame"
                                class="hint-area text-12px leading-18px"
                            >
                                <div
                                    v-if="hint.scheduleName"
                                    class="hint-group a-text-main mr-4px"
                                >
                                    {{ hint.scheduleName }}
                                </div>
                                <div
                                    v-if="hint.displayName"
                                    class="hint-name mr-4px"
                                >
                                    {{ hint.displayName }}
                                </div>
                                <div v-if="curStageName" class="hint-stage">
                                    {{ curStageName }}
                                </div>
                            </div>
                        </template>
                        <template #ca-right-content>
                            <GiftButton
                                v-if="isNotReachFinal"
                                v-pcDirectives:hide
                                :gift-id="assistConfig.giftId"
                                class="reach-final-btn"
                            >
                                {{ assistConfig.assistBtn }}
                            </GiftButton>
                            <JumpRank v-else :stage-type="stageType" />
                        </template>
                    </CurrentAnchor>
                </slot>
                <!-- 吸底icon -->
                <!-- <div
                    ref="iconRef"
                    :class="{
                        'small-kv-bottom-icon': true,
                        'small-kv-bottom-icon-break': data.offseason,
                    }"
                >
                    <div v-if="!data.offseason" class="play-icon-content">
                        <SecondaryEntry
                            v-pcDirectives:scroll
                            class="flex"
                            flex-direction="row"
                            :entry-data="entryData['verticalArea']"
                            flex-gap="8px"
                            need-margin
                            :stage-type="data.stageType"
                        />
                    </div>
                    <div v-else class="break-content">
                        <div
                            class="text-20 a-text-title text-center rest-title"
                        >
                            {{ kconfData?.mainPage?.restDay?.titie }}
                        </div>
                        <div
                            class="text-14 a-text-title text-center mt-[6px] mb-[22px] rest-title"
                        >
                            {{ kconfData?.mainPage?.restDay?.restText }}
                        </div>
                    </div>
                </div> -->
            </div>
        </Transition>
    </div>
</template>

<script lang="ts" setup>
import { useRouter, type LocationQueryValueRaw } from 'vue-router';
import {
    computed,
    ref,
    watch,
    nextTick,
    onBeforeUnmount,
    defineAsyncComponent,
} from 'vue';
import { storeToRefs } from 'pinia';
import YodaImage from '@pet/yoda.image/img.vue';
import { useStickyElement } from '@pet/ones-use.useStickyElement';
import { isDef } from '@pet/ones-use.usePageModulsConfig';
import useKConfBatch from '@pet/ones-use.useKconfBatch';
import videoPlay from '@pet/ones-ui.video-play/index.vue';
import Rule from '@pet/ones-ui.rule-icon/index.vue';
import useRechargeStore from '@pet/ones-ui.recharge-icon/models/recharge';
import StageName from '@pet/ones-rank.stage-name/index.vue';
import SecondaryEntry from '@pet/ones-rank.secondary-entry/index.vue';
import JumpRank from '@pet/ones-rank.jump-rank/index.vue';
import CurrentAnchor from '@pet/ones-rank.current-anchor/index.vue';
import { useHeaderStyle } from '@alive-ui/system';
import { GiftButton } from '@alive-ui/pro';
import {
    Report,
    isICFO,
    isLowDevice,
    countsCT,
    isOutLiveRoom,
    goOtherPage,
} from '@alive-ui/actions';
import { useKvConfig } from './use-kv-config';
import { kvFormat } from './format/index';
import type { PeakRankTask } from '@pet/ones-rank.schema/query-rank';
import type { EntryInfo } from './schemas';

const kconf = useKConfBatch();
const rechargeStore = useRechargeStore();

const assistConfig = computed(() => {
    return (
        kconf.kconfData?.mainPage?.curAnchorCard ?? {
            giftId: '',
            assistBtn: '助力入场',
        }
    );
});

const showTopLeftIcon = computed(() => {
    return kconf.kconfData?.mainPage?.showTopLeftIcon;
});

rechargeStore.init();
const SceneAllMain = defineAsyncComponent(() => {
    return import('@pet/ones-rank.scene-all-main/index.vue');
});
const emit = defineEmits<{
    (e: 'clickIcon', item: EntryInfo): void;
    (e: 'clickVideo'): void;
    (e: 'onSceneAllMainChange', val: any): void;
    (
        e: 'refresh-task',
        val: {
            showLoading: boolean;
            extraCardKey?: string;
            eventTriggerName?: string;
            afrerRefreshTriggerName?: string;
            payload?: string;
        },
    ): void;
}>();

const props = withDefaults(
    defineProps<{
        contextName: symbol;
        dataSuccess: boolean;
        entryData: any;
        showVideo?: boolean;
        type?: 'raise' | 'cut';
        indexInfoData: {
            anchorDisplayScheduleAndLane: any;
            anchorStageType: any;
            rule: any;
            offseason: any;
            lookingBack: any;
            secondLevelStageTableView: any;
        };
        laneClassifyName: string;
        backgroundColor: string;
        extraCardKey: string;
        anchorData: any;
        /**
         * 吸顶配置
         * top：卡片底部距顶部距离，如果配置则表示有吸顶
         */
        anchorConfigs: {
            bottom: number;
        };
        hint: {
            scheduleName: string;
            displayName: string;
        };
        curStageName: string;
        stageType: number;
        isNotReachFinal: boolean;
        /**
         * 决赛任务未达门槛
         */
        peakRankTask: PeakRankTask;
        /**
         * 是否展示赛程全景
         */
        showSceneAll: boolean;
    }>(),
    {
        dataSuccess: false,
        entryData: {},
        showVideo: false,
        type: 'cut',
        backgroundColor: 'transparent',
        anchorData: () => ({}) as any,
        peakRankTask: () => ({}) as PeakRankTask,
        // eslint-disable-next-line vue/require-valid-default-prop
        // data: () => ({
        //     stageType: 0,
        //     backgroundColor: 'transparent',
        //     displayName: 0,
        //     ruleUrl: '',
        // }),
    },
);
/** 是否低端机 */
const isLowDev = isICFO() || isLowDevice() || countsCT() > 1;

// 吸顶
const kvRef = ref<HTMLDivElement>();
const { isSticky: showSmallKV, outLiveRoomTopHeight } = useStickyElement(
    kvRef,
    props.anchorConfigs,
);

const { headerStyle: cutHeaderStyle, statusH } = useHeaderStyle();
const router = useRouter();
const secondaryEntryRef = ref<{
    direction: 'left' | 'right';
    canUnfold: boolean;
}>();
const secEntryWrapRef = ref<HTMLDivElement>();
//  除了 magin 和 padding 的宽度
const secEntryWrapPureWidth = computed(() => {
    if (!secEntryWrapRef.value) return 0;
    const rect = secEntryWrapRef.value.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(secEntryWrapRef.value);
    // 获取左右边框宽度
    const paddingLeftWidth = parseFloat(computedStyle.paddingLeft);
    const paddingRightWidth = parseFloat(computedStyle.paddingRight);
    return rect.width - paddingLeftWidth - paddingRightWidth;
});

const data = computed(() => {
    return {
        ...kvFormat(props.indexInfoData),
        laneClassifyName: props.laneClassifyName,
    };
});

const bigKv = computed(() => {
    return currentKvConfig.value?.kvSrc;
});
const smallKv = computed(() => {
    return currentKvConfig.value?.smallKv;
});
const raiseHeaderStyle = computed(() => {
    return {
        top:
            props.type === 'cut'
                ? cutHeaderStyle.value.marginTop
                : `${statusH?.value}rem`,
        backgroundColor: props.backgroundColor,
    };
});
const isOutLiveRoomStyle = computed(() => {
    return {
        transform: `translateY(${isOutLiveRoom ? outLiveRoomTopHeight : 0}px)`,
    };
});
//  「已淘汰」主播
const notInGame = computed(() => {
    const { isReplay, isJoinActivity, isDirectPromotionFinal, inGame } =
        props.anchorData;
    return (
        !isReplay &&
        isDef(inGame) &&
        isJoinActivity &&
        !isDirectPromotionFinal &&
        !inGame
    );
});

//  横向 icon 大于 6 个，展示指示器
const showEntryIndicate = computed(() => {
    // return props.entryData?.verticalArea?.length > 6;
    return secondaryEntryRef.value?.canUnfold;
});
//  指示器数量
const indicatorNums = computed(() => {
    const sum = props.entryData?.verticalArea?.length ?? 0;
    return Math.ceil(sum / 6);
});

const indicatorActiveIndex = ref(0);
function onTouchTrigger(payload: {
    direction: 'right' | 'left';
    shouldUpdateIndicator: boolean;
}) {
    if (payload.shouldUpdateIndicator) {
        if (payload.direction === 'left') {
            //  右滑
            // indicatorActiveIndex.value = indicatorActiveIndex.value >= indicatorNums.value - 1 ? indicatorNums.value - 1 : indicatorActiveIndex.value + 1
            indicatorActiveIndex.value = Math.max(
                1,
                indicatorActiveIndex.value + 1,
            );
        } else {
            //  左滑
            indicatorActiveIndex.value = Math.max(
                0,
                indicatorActiveIndex.value - 1,
            );
        }
    }
}

const onYodaImageError = (e: Event, level: string) => {
    Report.biz.error('主kv区yoda-image异常', {
        error: e,
        level,
    });
};
// const goRule = () => {
//     goOtherPage('jimu', data.value.ruleUrl);
// };

/* eslint-disable check-file/folder-naming-convention */
/** 当前路由是否是今日第一次访问 */
// function useIsFirstVisit() {
//     const key = window.location.pathname.slice(1).replace(/\//g, '-');
//     const today = new Date().toLocaleDateString();
//     const lastVisit = localStorage.getItem(key);
//     console.log('lastVisit', lastVisit, 'today', today, key);
//     if (lastVisit === today) {
//         return false;
//     }
//     localStorage.setItem(key, today);

//     return true;
// }

function onStageChange(item: any) {
    emit('refresh-task', {
        showLoading: true,
        // extraCardKey: props.extraCardKey,
        eventTriggerName: 'onChangeStage',
        payload: item,
    });
}

function onRouteQueryChange(item: any) {
    emit('refresh-task', {
        showLoading: true,
        // extraCardKey: props.extraCardKey,
        eventTriggerName: 'onRouteQueryChange',
        afrerRefreshTriggerName: 'afterChangeStage',
        payload: item,
    });
}

// const isFirstVisit = useIsFirstVisit();
// 是否第一次滚动， 进来页面只自动滚动一次
// const isFirstScroll = ref(true);
const timer: any = null;
// watch(
//     () => props.dataSuccess,
//     (val, oldVal) => {
//         if (
//             isFirstScroll.value &&
//             val &&
//             !oldVal &&
//             kvRef.value &&
//             !isFirstVisit
//         ) {
//             const { offseason } = data.value;
//             if (offseason) {
//                 return;
//             }
//             nextTick(() => {
//                 const { bottom } = kvRef.value!.getBoundingClientRect();
//                 const ratio = getRatio();
//                 const offsetTop =
//                     bottom -
//                     getYWhenSmallKvShow() +
//                     (data.value.isHaveStage ? 30 * ratio : 0);
//                 isFirstScroll.value = false;
//                 if (timer) {
//                     clearTimeout(timer);
//                 }
//                 timer = setTimeout(() => {
//                     window.scrollTo({
//                         left: 0,
//                         // 避免因为小数差异而导致小kv展示失效，因此+1
//                         top: offsetTop + 1,
//                         behavior: 'smooth',
//                     });
//                 }, 1000);
//             });
//         }
//     },
//     { immediate: true },
// );

const onSceneAllMainChange = (e: any) => {
    emit('refresh-task', {
        showLoading: true,
        // type: props.extraCardKey,
        eventTriggerName: 'onChangeStage',
        afrerRefreshTriggerName: 'afterChangeStage',
        payload: {
            ...e,
            reloadEntry: true,
        },
    });
};
const currentKvConfig = useKvConfig(data);
// 获取当前赛程，得出要渲染的 kv 头
const videoUrls = computed(() => {
    return currentKvConfig.value.kvVideo
        ? [{ url: currentKvConfig.value.kvVideo }]
        : [];
});

onBeforeUnmount(() => {
    if (timer) {
        clearTimeout(timer);
    }
});
</script>

<style lang="less" scoped>
.top-left-icon-area {
    :deep(.elem-entry-icon) {
        width: 40px;
        height: 40px;
    }
}
:deep(.stage-name) {
    margin-bottom: 64px;
}
:deep(.has-sub) {
    margin-bottom: 20px;
}
.anchor-sticky {
    position: absolute;
    z-index: 12;
    top: 102px;
}
.kv-anchor {
    --top-anchor-area-width: 390px;
    margin: 0;
    box-sizing: border-box;
    background: unset;
    padding: 0 12px;
    :deep(.left-top) {
        height: 20px;
        line-height: 20px;
    }
    :deep(.current-ranking) {
        height: 20px !important;
        line-height: 20px !important;
    }
}
.hint-area {
    display: flex;
    .hint-name,
    .hint-stage {
        color: rgba(255, 223, 191, 0.8);
    }
}
.kv-view {
    --y-img-width: 100%;
    --kvHeight: 390px;
    position: relative;
    // z-index: 0;
    .video-img {
        transform: translateZ(
            0
        ); // 添加kv独立层 解决ios打开弹窗时的kv区视频闪动问题
    }
    .video-img,
    .cool-kv-image {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        overflow: hidden;
        height: 390px;
    }
    .cool-kv-image :deep(div) {
        transition: background ease 0.5s;
    }
}
.big-kv {
    --y-img-height: 390px;
}
.small-kv-wrap {
    --y-img-height: 162px;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 12;
    width: 414px;
    height: 162px;
    // 处理宽屏
    margin-left: calc(50% - 414px / 2);
    background: center / 100% no-repeat;
}

.small-kv-top-icon-wrap {
    position: absolute;
    right: 12px;
    bottom: 76px;
    display: flex;
    height: 40px;

    :deep(.kv-icon) {
        width: 40px;
        height: 40px;
        margin: 0 0 0 8px;
    }
}

.play-icon-wrap {
    position: absolute;
    top: 46px;
    z-index: 12;
}
.small-kv-bottom-icon {
    position: absolute;
    bottom: 13px;
    z-index: 12;
}

.play-icon-fixed {
    position: absolute;
    bottom: 12px;
    left: 0;
    // 处理宽屏
    // max-width: 414px;
    width: 100%;
    // padding-top: 0;
    // margin-left: calc(50% - 414px / 2);
    // overflow-x: scroll;
}

.play-icon-content {
    position: relative;
    display: flex;
    width: 414px;
    overflow: hidden;
    overflow-x: scroll;

    & > :first-child {
        margin-left: 12px;
    }
    &::-webkit-scrollbar {
        width: 0;
        background: transparent;
    }
}

.icon-enter-active,
.icon-leave-active {
    transition: opacity 0.2s cubic-bezier(0, 0, 0.2, 1);
}
.icon-leave-to,
.icon-enter-from {
    opacity: 0;
}
.icon-enter-to,
.icon-leave-from {
    opacity: 1;
}
.small-kv-bg {
    width: 100%;
    height: 10px;
}

.big-kv-icon {
    // position: absolute;
    // top: 16px;
    z-index: 1;
    :deep(.kv-icon) {
        width: 40px;
        height: 40px;
        margin: 0 0 6px;
    }
}
.left-area {
    left: 12px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.right-area {
    right: 12px;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}
.elem-entry-icon {
    position: relative;
    display: flex;
    justify-content: center;
    height: 56px;
}
.elem-entry-icon-top {
    position: relative;
    display: flex;
    justify-content: center;
}
.rule-icon {
    // flex-direction: row-reverse;
    width: 40px;
    height: 40px;
    background-image: url(https://p4-live.wskwai.com/kos/nlav12706/ceremony2024/kv/rules.9bfbd986eca0a1ab.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: 8px;
}
.big-bottom-icon {
    position: relative;
    padding-top: 185px;
    left: 0px;
    // padding-bottom: 20px;
    // z-index: 1;
}
.big-bottom-icon-box {
    overflow-x: hidden;
    overflow-y: hidden;
    width: 414px;
    padding: 0 12px;
}
.no-indicate {
    margin-bottom: 20px;
}
// .stage-box {
//     margin-bottom: 64px;
// }
.video-play-box {
    position: relative;
    height: 100%;
    width: 100%;
}
.guo-du {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 127px;
    width: 100%;
    background: linear-gradient(
        180.64deg,
        rgba(30, 89, 142, 0) 0.55%,
        rgba(5, 20, 78, 0.816992) 66.06%,
        #000540 94.5%
    );
}
.break-content {
    margin: auto;
    width: 100%;
}
.small-kv-bottom-icon-break {
    bottom: 0;
    width: 100%;
}
.rest-title {
    font-family: HYYakuHei;
}

.sec-entry-indicator {
    width: 100%;
    margin: 7px auto 20px;
    display: flex;
    justify-content: center;
    .indicator-item {
        width: 10px;
        height: 2px;
        background:;
        border-radius: 1px;
        background-color: rgba(255, 255, 255, 0.2);
        transition: background-color 0.3s;
        & + .indicator-item {
            margin-left: 2px;
            width: 18px;
        }
        &.active-indicator {
            @apply a-bg-main;
        }
    }
}
</style>
