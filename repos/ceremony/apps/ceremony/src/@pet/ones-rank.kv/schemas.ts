export interface IconItem {
    position?: string;
    show: boolean;
    type: EntryType;
    route?: Route;
    icon: string;
    logFlag: string;
    bgClass: string;
    fn: VoidFunction;
}

export interface Route {
    name: string;
    query: any;
}

export interface ImageItem {
    imageType: string;
    imagePath: any;
}

export const enum EntryType {
    ALL_SCENE = 'scheduleAllScene',
    RULE = 'rule',
    GOLDEN_FANS = 'goldenFans',
    DIAMOND_SIGN = 'diamondSign',
    PEAK_BATTLE = 'peakBattle',
    ADD_CARD = 'addCard',
    FRIEND = 'friend',
    TEAM_PLAY = 'teamPlay',
    GAS_STATION = 'gasStation',
    ARENA = 'arena',
    TEAMPLAYRANK = 'teamPlayRank',
    GUI_ZU = 'nobleEntrance',
    /**
     * 扭蛋机
     */
    DRAW = 'draw',
    /**
     * 游戏场
     */
    GAME = 'gamingVenue',
    GAME_PRE = 'gameWarmup',
    /**
     * 内容场
     */
    CONTENT = 'contentVenue',
    /**
     * 回火力营
     */
    BACK_CONTENT = 'backContentVenue',
    /**
     * 主赛程
     */
    MAIN = 'mainVenue',
    /**
     * 巅峰对决榜
     */
    PEAK_RANK = 'peakBattleRank',
    /**
     * 战队广场
     */
    TEAM = 'team',
    /**
     * 大R贡献榜(新富豪榜)
     */
    RICH_MAN_CONTRIBUTE = 'richManContribute',
    /* 宫格打榜 */
    GONG_GE = 'gongge',
    /* 团播打榜 */
    TUAN_BO = 'tuanbo',
    /* 宫格挂榜 */
    GONE_CHAMPION = 'gonggeHonor',
    /* 团播挂榜 */
    TUAN_CHAMPION = 'tuanboHonor',
    /* 弹幕打榜 */
    DAN_MU = 'danmu',
    /* 弹幕挂榜 */
    DAN_CHAMPION = 'danmuHonor',
    /* 返回 */
    BACK = 'back',
    /** 返回挂榜页 */
    BACK_CHAMPION = 'backChampion',
    /** 主播现金 */
    AUTHOR_CASH = 'authorCash',
    /** 主播现金预热 */
    AUTHOR_CASH_PRE = 'authorCashPre',
}

export interface UserInfo {
    itemId: number;
    itemName: string;
    headUrl: string;
    liveStreamId: string;
    mysteryMan: boolean;
}

export interface EntryConfig {
    type: EntryType;
    /**
     * 入口位置，如果没有设置则自适应左右多余的空间
     */
    position?: 'left' | 'right';
    route?: {
        name?: string;
        query?: Record<string, string>;
    };
    order?: number;
    hidden?: boolean;
    logFlag?: string;
    icon?: string;
    /** 大kv上的按钮是否不展示在小kv上 */
    onlyShowInSmallKv?: boolean;
}

export interface ExtraInfo<T> {
    /**
     * 后端接口字段
     */
    info?: T;
}

export interface FixedEntryInfo {
    display?: boolean;
    displayAnimation?: boolean;
    displayText?: string;
    authorInfo?: UserInfo;
    userInfo?: UserInfo;
    extInfo?: { [key: string]: any };
}

export type EntryInfo = EntryConfig & ExtraInfo<FixedEntryInfo>;
