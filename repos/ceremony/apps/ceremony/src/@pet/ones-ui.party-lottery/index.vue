<template>
    <div>
        <Card class="party-magic a-bg-part pb-12px">
            <Content>
                <div class="lottery-title">
                    <KvTextIcon text="钱包" @click="goWallet"></KvTextIcon>
                    <Title>{{ textConfigs.title }}</Title>
                    <KvTextIcon text="规则" @click="goRule"></KvTextIcon>
                </div>

                <!-- 中奖记录跑马灯区域 -->
                <RecordWrap
                    ref="recordWrapRef"
                    :gifts-list="giftsList"
                    :marquee-data="marqueeData"
                />
                <!-- 奖品轮播区域 -->
                <LotterySwiper :swiper-data="giftsList" />
                <!-- 抽奖按钮 -->
                <div
                    v-show-log="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: 'DRAW',
                        },
                    }"
                    v-click-log="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: 'DRAW',
                        },
                    }"
                    class="lottery-btn"
                    @click="doLottery"
                >
                    <div class="title text-bold">{{ textConfigs.btnText }}</div>
                    <div class="sub-title">
                        你还有{{ magicStore.ballanceStore.balance
                        }}{{ textConfigs.chance }}
                    </div>
                </div>
                <!-- 任务区域 -->
                <Task
                    v-if="audienceTaskData?.taskItemViewList?.length"
                    :need-data-init="false"
                    :auto-close-time="
                        downGradeSwitch.popCloseSwitch
                            ? conf4Tab.popCloseDelay
                            : 0
                    "
                    :can-dotask="magicStore.drawStatus.loading"
                    :task-list="audienceTaskData.taskItemViewList"
                    :activity-id="activityId"
                    custom-class="party-draw-lottery"
                    :show-count="true"
                    @task:update="taskUpdate"
                />
            </Content>
        </Card>
        <ResultModal
            custom-class="party-draw-lottery"
            :show="magicStore.showPop"
            :auto-close-time="
                downGradeSwitch.popCloseSwitch ? conf4Tab.popCloseDelay : 0
            "
            :page-status="magicStore.drawStatus"
            :award-list="magicStore.resultList"
            :need-fill-address="magicStore.needFillAddress"
            :address="address"
            :need-jump-back-pack="magicStore.needJumpBackPack"
            :need-jump-wallet="magicStore.needJumpWallet"
            :need-send-emoticon="magicStore.needSendEmoticon"
            :enable-show-emoticon-tab="magicStore.enableShowEmoticonTab"
            @close="magicStore.showPop = false"
        />
    </div>
</template>

<script lang="ts" setup>
import { watch, computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import useTaskStore from '@pet/ones-use.useTask/index';
import { usePartySwitch } from '@pet/ones-use.usePartySwitch';
import useKconfStore from '@pet/ones-use.useKconf';
import RecordWrap from '@pet/ones-ui.party-lottery/components/record-wrap/index.vue';
import Task from '@pet/ones-ui.lottery-task-list/index.vue';
import ResultModal from '@pet/ones-ui.DrawMachine/modules/popups/result-modal.vue';
import { Card as ACard, KvTextIcon } from '@alive-ui/base';
import { goOtherPage } from '@alive-ui/actions';
import useMagicStore from './store/index';
import LotterySwiper from './components/lottery-swiper/index.vue';
import giftEmptyIcon from './assets/empty_2x.png';
import type { TaskItem } from '@pet/ones-use.useTask/services/index';
import type { LuckyBagData, AudienceTaskData, AwardMarqueeInfo } from './type';
import { goWallet } from '@/utils/tools';
import usePartyPageModel from '@/modules/party/models/page';
import { TAB4_BIZ } from '@/const';
import './assets/imgs.less';

const props = withDefaults(
    defineProps<{
        luckyBagData: LuckyBagData;
        audienceTaskData: AudienceTaskData;
        activityId: string;
    }>(),
    {
        luckyBagData: () => ({}) as LuckyBagData,
        audienceTaskData: () => ({
            taskItemViewList: [],
        }),
        activityId: '',
    },
);

const partyPageModel = usePartyPageModel();
const { updateHomeData } = partyPageModel;
const downGradeSwitch = usePartySwitch();
const recordWrapRef = ref();
const taskStore = useTaskStore();
taskStore.biz = TAB4_BIZ;

const { Card, Content, Title } = ACard;
const magicStore = useMagicStore();
const { conf4Tab } = storeToRefs(useKconfStore());

const address = computed(() => {
    return conf4Tab.value.partyLottery?.acceptRewardAddress ?? '';
});

const textConfigs = computed(() => {
    return conf4Tab.value?.partyLottery ?? {};
});

const giftsList = computed(() => {
    if (!props.luckyBagData?.normalPanel?.prizeList?.length) {
        return new Array(9)
            .fill({
                id: -10086,
                name: '奖品',
                count: 1,
                icon: giftEmptyIcon,
            })
            .map(magicStore.dealPrize);
    }
    return props.luckyBagData.normalPanel.prizeList.map(magicStore.dealPrize);
});
const goRule = () => {
    const url = conf4Tab.value?.partyLottery?.rule;
    if (!url) return;
    goOtherPage('jimu', url);
};
const marqueeData = computed(() => {
    if (props.luckyBagData?.records) {
        return {
            records: props.luckyBagData.records,
            mustWinToast1: props.luckyBagData.mustWinToast1,
            mustWinToast2: props.luckyBagData.mustWinToast2,
        };
    }
    return {} as AwardMarqueeInfo;
});

const btnList = computed(() => {
    return props.luckyBagData?.normalPanel?.btnList ?? [];
});

function taskUpdate(val: TaskItem, extra?: any) {
    updateHomeData(extra);
}

function doLottery() {
    magicStore.drawAward(
        btnList.value[0]?.ruleId,
        recordWrapRef.value?.awardRecordsRef?.refresh,
    );
}

watch(
    () => props.luckyBagData?.balance,
    (val) => {
        magicStore.ballanceStore.balance = val ?? 0;
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.party-magic {
    margin: 20px 16px 12px;

    :deep(.card-title-icon-attrs) {
        display: none;
    }
    .lottery-title {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0 10px;
        margin-bottom: 5px;
    }
    .record-wrap {
        :deep(.msg-item) {
            @apply a-text-main;
        }
    }
    .lottery-btn {
        margin: 0 auto;
        box-sizing: border-box;
        width: 148px;
        min-height: 48px;
        line-height: 48px;
        text-align: center;
        background: url('./assets/big-btn.png') center / 100% no-repeat;
        color: #571818;
        padding: 4.5px 10px;
        .title {
            line-height: 24px;
        }
        .sub-title {
            font-size: 10px;
            word-break: break-all;
            line-height: 1;
            opacity: 0.6;
        }
    }
}
</style>
