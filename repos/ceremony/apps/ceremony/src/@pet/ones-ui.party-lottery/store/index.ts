/* eslint-disable max-lines-per-function */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// TODO 埋点
import { useRoute } from 'vue-router';
import { ref, watch, reactive, computed } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import useBallance from '@pet/ones-use.useUpdateBallance/index';
import { getBalance, drawReward } from '@pet/ones-ui.DrawMachine/api/index';
import { Toast } from '@lux/sharp-ui-next';
import type {
    BoxConfigInfo,
    AwardItem,
} from '@pet/ones-ui.DrawMachine/api/index';
import { TAB4_BIZ } from '@/const';

export const DrawResult = {
    error: 0,
    success: 1,
    empty: 2,
    offseason: 3, // 休赛
} as const;

const ToastInfo = {
    noBalance: '抽奖次数不足，快去做任务获得更多抽奖机会吧～',
    playing: '正在抽奖中',
    netError: '网络出错，请重试',
    needLogin: '请登陆快手 app',
};

export const useBoxInfo = defineStore('lottery-box', () => {
    const route = useRoute();
    const drawStatus = reactive({
        loading: false,
        error: false,
    });
    const ballanceStore = useBallance();
    const showPop = ref(false);

    const resultList = ref([] as AwardItem[]);
    const needFillAddress = ref(false);
    const needJumpBackPack = ref(false);
    const needJumpWallet = ref(false);

    const needSendEmoticon = ref(false);
    const enableShowEmoticonTab = ref(false);

    const enougBalance = computed(() => {
        return ballanceStore.balance > 0;
    });

    const getResult = async (ruleId = 1) => {
        try {
            drawStatus.loading = true;
            drawStatus.error = false;

            const entry_src = route.query?.entry_src ?? '';
            let prizeSource = '';
            if (Array.isArray(entry_src)) {
                prizeSource = entry_src[0] ?? '';
            } else {
                prizeSource = entry_src;
            }
            //  TODO 这个传参暂定 1
            const prizeStyle = 1;
            const res = await drawReward(
                prizeSource,
                prizeStyle,
                ruleId,
                undefined,
                TAB4_BIZ,
            );
            resultList.value = res?.awards;

            if (res?.toast) {
                // eslint-disable-next-line @typescript-eslint/no-throw-literal
                throw { data: { error_msg: res.toast } };
            }

            if (!resultList.value?.length) {
                // eslint-disable-next-line @typescript-eslint/no-throw-literal
                // throw {
                //     data: {
                //         error_msg: ToastInfo.netError,
                //     },
                // };
                showPop.value = true;
                drawStatus.loading = false;
                return DrawResult.empty;
            }
            needFillAddress.value = res?.needFillAddress;
            needJumpBackPack.value = res?.needJumpBackPack;
            needJumpWallet.value = res?.needJumpWallet;
            drawStatus.loading = false;
            needSendEmoticon.value = res?.needSendEmoticon;
            enableShowEmoticonTab.value = res?.enableShowEmoticonTab;
            showPop.value = true;
            return DrawResult.success;
        } catch (err: any) {
            const { error_msg, result } = err?.data || {};
            drawStatus.error = true;
            drawStatus.loading = false;

            if (result === 81381) {
                Toast.info(ToastInfo.noBalance);
            } else {
                error_msg && Toast.info(error_msg);
                !error_msg && Toast.info(ToastInfo.netError);
            }
            return DrawResult.error;
        }
    };

    const drawAward = async (ruleId: number, cb?: () => void) => {
        // 检查抽奖中
        if (drawStatus.loading) {
            Toast.info('正在抽奖中');
            return;
        }
        if (!enougBalance.value) {
            Toast.info(ToastInfo.noBalance);
            return;
        }
        await getResult(ruleId);
        //  更新库存
        ballanceStore.updateBalance(undefined, TAB4_BIZ);
        cb && cb();
    };

    const dealPrize = (giftObj: any, ind: number) => {
        return {
            ...giftObj,
            ind,
        };
    };

    return {
        drawStatus,
        resultList,
        needFillAddress,
        needJumpBackPack,
        needJumpWallet,
        drawAward,
        getResult,
        needSendEmoticon,
        enableShowEmoticonTab,
        dealPrize,
        ballanceStore,
        showPop,
    };
});

export default useBoxInfo;
