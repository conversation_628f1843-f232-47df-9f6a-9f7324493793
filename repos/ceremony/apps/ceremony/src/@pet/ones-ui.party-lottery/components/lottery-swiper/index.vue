<template>
    <div class="lottery-swiper mt-12px mb-20px pl-1px pr-1px">
        <div class="ls-cover-block ls-left" />
        <div class="ls-cover-block ls-right" />
        <div class="swiper-wrapper">
            <div
                v-for="item in swiperData"
                :key="item.id"
                class="swiper-slide a-bg-substrate ml-12px"
            >
                <img :src="item.icon" alt="" />
                <div class="prize-name a-text-main">
                    {{ item.name }}
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
// 组件作者同步目前 sharp-ui-next 的 swiper 并不支持无线轮播的能力，改成 swiper 吧
// import { Swiper } from '@lux/sharp-ui-next';
import { onMounted, ref, onBeforeUnmount } from 'vue';
import { Autoplay, FreeMode } from 'swiper/modules';
import Swiper from 'swiper';
import 'swiper/css';
import 'swiper/css/autoplay';
import 'swiper/css/free-mode';
import type { AwardItem } from '@pet/ones-ui.DrawMachine/api/index';

const props = withDefaults(
    defineProps<{
        swiperData: AwardItem[];
    }>(),
    {
        swiperData: () => [],
    },
);
const swiperRef = ref<Swiper | null>(null);

onMounted(() => {
    createSwiperGenerator();
});

const createSwiperGenerator = () => {
    swiperRef.value = new Swiper('.lottery-swiper', {
        modules: [Autoplay, FreeMode],
        slidesPerView: 'auto',
        loop: true,
        spaceBetween: 0,
        observer: true,
        autoplay: {
            delay: 0,
        },
        speed: 200 * props.swiperData?.length ?? 10000,
        freeMode: true,
        allowTouchMove: false,
        preventInteractionOnTransition: true,
    });
};

onBeforeUnmount(() => {
    swiperRef.value?.destroy(true, true);
    swiperRef.value = null;
});
</script>

<style lang="less" scoped>
.lottery-swiper {
    width: 100%;
    overflow: hidden;
    position: relative;
    .swiper-wrapper {
        transition-timing-function: linear;
    }
    .swiper-slide {
        box-sizing: border-box;
        width: 76px;
        height: 76px;
        border-radius: 12px;
        padding: 5px 0px;
        // 默认每个 swiper 滑动项都会增加一个 layer 层，这里没必要，年度盛典抽奖机在手机端经过测试，去掉合成层不会造成卡顿
        transform: unset;
        backface-visibility: unset;
        img {
            display: block;
            height: 52px;
            width: 52px;
            margin: 0 auto;
            object-fit: contain;
        }
        .prize-name {
            line-height: 14px;
            text-align: center;
            font-size: 10px;
        }
    }
    .ls-cover-block {
        width: 24px;
        height: 100px;
        z-index: 2;
        position: absolute;
    }
    .ls-left {
        left: -6px;
        background: linear-gradient(
            90deg,
            #141522 0%,
            rgba(20, 21, 34, 0) 100%
        );
    }
    .ls-right {
        right: -6px;
        background: linear-gradient(
            270deg,
            #141522 0%,
            rgba(20, 21, 34, 0) 100%
        );
    }
}
</style>
