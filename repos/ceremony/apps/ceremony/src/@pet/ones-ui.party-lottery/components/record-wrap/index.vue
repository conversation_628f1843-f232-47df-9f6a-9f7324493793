<template>
    <div
        class="record-wrap a-bg-substrate ml-12px mr-12px pr-12px flex-between-center"
    >
        <AwardMarquee
            ref="awardRecordsRef"
            :gifts-list="giftsList"
            :biz="TAB4_BIZ"
            :notice-icon="noticeIcon"
            :use-init="false"
            :marquee-data="marqueeData"
        />
        <div
            v-show-log="{
                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                params: {
                    btn_type: 'RECODE',
                },
            }"
            v-click-log="{
                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                params: {
                    btn_type: 'RECODE',
                },
            }"
            class="more-play a-text-main flex-start-center"
            @click="switchLotteryRecord(true)"
        >
            <div class="more-paly-text mr-4px">抽奖记录</div>
            <Right />
        </div>
        <RecordModal
            v-if="lotteryRecordVisible"
            title="抽奖记录"
            :auto-close-time="
                downGradeSwitch.popCloseSwitch ? conf4Tab.popCloseDelay : 0
            "
            :show="lotteryRecordVisible"
            custom-class="party-draw-lottery"
            :biz="TAB4_BIZ"
            :show-panel-icon="true"
            :address="address"
            time-text="抽奖时间"
            @close="switchLotteryRecord(false)"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { usePartySwitch } from '@pet/ones-use.usePartySwitch';
import useKconfStore from '@pet/ones-use.useKconf';
import AwardMarquee from '@pet/ones-ui.lottery-marquee/index.vue';
import RecordModal from '@pet/ones-ui.DrawMachine/modules/popups/reward-modal.vue';
import { Right } from '@alive-ui/icon';
import noticeIcon from '../../assets/icon-laba_2x.png';
import type { PropType } from 'vue';
import type { LotteryMarqueeExpose } from '@pet/ones-ui.lottery-marquee/index.vue';
import type { AwardMarqueeInfo } from '../../type';
import { TAB4_BIZ } from '@/const';

const awardRecordsRef = ref<LotteryMarqueeExpose>();

defineProps({
    giftsList: {
        type: Array as PropType<Record<string, any>[]>,
        default: () => [],
    },
    marqueeData: {
        type: Object as PropType<AwardMarqueeInfo>,
        default: () => ({}),
    },
});

const lotteryRecordVisible = ref(false);
const downGradeSwitch = usePartySwitch();
const { conf4Tab } = storeToRefs(useKconfStore());

const address = computed(() => {
    return conf4Tab.value.partyLottery?.acceptRewardAddress ?? '';
});

const switchLotteryRecord = (status: boolean) => {
    lotteryRecordVisible.value = status;
};

defineExpose({
    awardRecordsRef,
});
</script>

<style lang="less" scoped>
.record-wrap {
    border-radius: 8px;
    height: 32px;
    :deep(.award-marquee-wrap) {
        background: none;
    }
    :deep(.msg-item) {
        @apply a-text-main;
    }
    .more-play {
        font-family: 'PingFang SC';
        font-size: 12px;
        line-height: 18px;
        opacity: 0.6;
    }
}
</style>
