.party-draw-lottery.spu-popup {
    background-color: rgba(0, 0, 0, 60%);
}

.party-draw-lottery {
      // 弹窗
    &.spu-popup,
    &.anim-pop {
        --popBtnAddHeight: 48px;
        --reservationUserNameOpacity: 1;
        --fontSubColor: #000;
        --popupTitleTop: 118px;
        --popupTitleColor: linear-gradient(93.11deg, #743100 8.72%, #391f00 123.1%);
        --popupTitleSmallTop: 118px;
        --popupTitleRecordTop: 84px;
        --popupTitleFontSize: 20px;
        --popupTitleMargin: 0 2px;
        --popupTitleFamily: 'HYYakuHei';
        --popupBtnDoColor: #571818;
        --recordItemBg: #fff;
        --popBtnBottom: 105px;
        // 奖励弹层
        --recordContentHeight: 220px;
        --recordContentWidth: 280px;
        --awardItemBg: #fff;
        --awardCountNumColor: #fff;
        --rewardGiftNumColor: #000;
    }
    // 任务
    --taskAreaItemBgColor: none;
    --taskNavCoinColor: rgba(255, 223, 191, 60%);
    --taskNavCoinMargin: 10px auto;
    --taskBtnWidth: 71px;
    --taskBtnHeight: 34px;
    --fontWeightTitle: 400;
    .gis-action-btn {
        width: 82px;
        height: 36px;
        background: url('./action-btn_2x.png') center / 100% no-repeat;
    }
    .data-load-error-main {
        .gis-draw-net-error {
            width: 96px;
            height: 96px;
            background: url('./draw-net-error_main_2x.png') center / 100% no-repeat;
        }
        .gis-draw-no-data {
            width: 96px;
            height: 96px;
            background: url('./draw-no-data_main_2x.png') center / 100% no-repeat;
        }
        .error-text {
            @apply a-text-main;
        }
    }
    .gis-draw-net-error {
        width: 96px;
        height: 96px;
        background: url('./draw-net-error_2x.png') center / 100% no-repeat;
    }
    .gis-draw-no-data {
        width: 96px;
        height: 96px;
        background: url('./draw-no-data_2x.png') center / 100% no-repeat;
    }
    .gis-empty {
        width: 60px;
        height: 60px;
        background: url('./empty_2x.png') center / 100% no-repeat;
    }
    .gis-icon-close {
        width: 32px;
        height: 32px;
        background: url('./icon-close_2x.png') center / 100% no-repeat;
    }
    .gis-icon-laba {
        width: 14px;
        height: 14px;
        background: url('./icon-laba_2x.png') center / 100% no-repeat;
    }
    .gis-modal-button {
        width: 148px;
        height: 48px;
        font-size: 14px !important;
        background: url('./modal-button_2x.png') center / 100% no-repeat;
    }
    .gis-prize-special {
        width: 90px;
        height: 85px;
        background: url('./prize-special_2x.png') center / 100% no-repeat;
    }
    .gis-record-modal {
        width: 320px;
        height: 574px;
        background: url('./record-modal_2x.png') center / 100% no-repeat;
    }

    .gis-title-extra-icon {
        width: 24px;
        height: 24px;
        background: url('./title-extra-icon_2x.png') center / 100% no-repeat;
    }

    /* 兼容小机型中奖记录样式，如iphoneSE */
    @media (max-height: 812px) {
        .prize-record-pop {
        transform: scale(0.85);
        transform-origin: top;
        }
        .prize-record-close {
        margin-top: -75px !important;
        }
    }
    .gis-short-btn {
        width: 114px;
        height: 48px;
        background: url('./short-btn_2x.png') center / 100% no-repeat;
    }
    .gis-small-modal {
        width: 280px;
        height: 514px;
        background: url('./small-modal_2x.png') center / 100% no-repeat;
    }
    .gis-thanks-label {
        width: 120px;
        height: 120px;
        background: url('./thanks-label_2x.png') center / 100% no-repeat;
    }
    .record-title {
        color: #000;
    }
    .coin-get-time {
        color: #000;
    }
    .btn-continue {
        width: 114px;
        height: 48px;
        background: url('./btn-address.png') center / 100% no-repeat;
    }
}