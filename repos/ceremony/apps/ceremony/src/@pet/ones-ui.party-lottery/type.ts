export interface LuckyBagData {
    balance: number;
    normalPanel: NormalPanel;
    records: string[];
    mustWinToast1: string;
    mustWinToast2: string;
}

export interface AwardMarqueeInfo {
    records: string[];
    mustWinToast1: string;
    mustWinToast2: string;
}

export interface NormalPanel {
    prizeList: PrizeList[];
    btnList: BtnList[];
}

export interface BtnList {
    ruleId: number;
    coinCount: number;
    drawCount: number;
}

export interface PrizeList {
    id: number;
    name: string;
    count: number;
    icon: string;
    authorId: number;
    headUrl: string;
    nickName: string;
    specialRewardText?: string;
}

export interface AudienceTaskData {
    taskItemViewList: TaskItemViewList[];
}

export enum TaskType {
    SendGift = 'sendGift',
    /** 关注 */
    H5Follow = 'h5Follow',
    /** 分享 */
    SharePoster = 'sharePoster',
    WatchVideo = 'watchVideo',
    /** 跳转推荐直播间 */
    WatchLive = 'watchLive',
    /** 跳搜索推荐页 */
    SearchKeyWord = 'searchKeyWord',
    /** 预约任务 */
    Reservation = 'reservation',
    /** 激励任务 */
    AdInspire = 'adInspire',
    /** 小铃铛下载 */
    SmallBell = 'smallBell',
    /** 抢红包 */
    GrabRedPack = 'grabRedPack',
    /** 预约日历任务，24 年度盛典线下新增 */
    BatchReservation = 'batchReservationYC24',
}

export enum TaskStatus {
    /** 未完成 */
    UnFinished = 1,
    /** 已完成 */
    Finished = 2,
}

export interface TaskItemViewList {
    /** 任务类型 */
    typeKey: TaskType;
    name: string; // 任务名称
    desc: string; // 任务描述
    iconUrl: string; // 任务icon
    finishCount: number; // 完成进度
    needCount: number; // 目标值
    /** 任务状态 1 未完成 2 已完成 */
    status: TaskStatus; //
    buttonText: string;
    kwaiLink: string; // 任务kwai链
    expandMap: Record<string, any>;
    receiveTask: boolean;
    rulePageLink: string;
}

export interface ExpandMap {
    giftIds?: number[];
    currFansGroup?: boolean;
    keyWord?: string;
    smallBellH5?: string;
}
