import type { TabItem } from '@pet/ones-rank.schema/index-home';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
import type { ScheduleInfoPostResponse } from '@pet/ones-rank.city-search/schema/index';
export interface TwelveSchema {
    list: TabItem[];
    initHourIndex: number;
    initRotate: number;
    initStartDeg: number;
    subTabData?: {
        list: TabItem[];
        defaultIndex: number;
        anchorScheduleType: number;
        tabType: string;
        btnText: string;
    };
    cityData?: ScheduleInfoPostResponse;
}
export interface TwelveData extends PropsCommonParams {
    data?: TwelveSchema;
}
