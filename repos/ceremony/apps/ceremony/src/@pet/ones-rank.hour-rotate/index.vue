<script lang="ts" setup>
import {
    ref,
    watch,
    computed,
    onMounted,
    onBeforeUnmount,
    onUnmounted,
} from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { startVibrate, invoke, isOutLiveRoom } from '@alive-ui/actions';
// import '@lux/sharp-ui-plus/themes/default/bottom-panel';
// import '@lux/sharp-ui-plus/themes/default/tooltip';
import { dataFun } from './data';
import './assets/img.less';
import type { TwelveData } from './schema';
const emits = defineEmits(['change']);

const props = withDefaults(defineProps<TwelveData>(), {
    data: () => {
        return {
            list: [],
            initHourIndex: 0,
            initRotate: 0,
            initStartDeg: 0,
        };
    },
    useProps: false,
    contextName: Symbol.for('ctx'),
});

const newData = props.useProps
    ? computed(() => props.data)
    : dataFun(props.contextName);

const isDisplayGuide = ref(false);
const LS_TWELVE_HOUR_GUID = '24-04-hour-twelve';
const currentHourIndex = ref(0);
const initRotate = ref(0);
const initStartDeg = ref(0);

const noBack = async (enabled: boolean) => {
    if (isOutLiveRoom) {
        await invoke('webview.setSlideBack', { enabled });
    }
};
noBack(false);

onUnmounted(() => {
    noBack(true);
});
watch(
    () => newData?.value?.initHourIndex,
    () => {
        currentHourIndex.value = newData?.value?.initHourIndex;
        initRotate.value = newData?.value?.initRotate;
        initStartDeg.value = newData?.value?.initStartDeg;
        if (
            newData?.value?.initHourIndex > 0 &&
            localStorage.getItem(LS_TWELVE_HOUR_GUID) !== 'true'
        ) {
            localStorage.setItem(LS_TWELVE_HOUR_GUID, 'true');
            // eslint-disable-next-line vue/no-async-in-computed-properties
            const timer = setTimeout(() => {
                clearTimeout(timer);
                isDisplayGuide.value = false;
            }, 3000);
            const timer2 = setTimeout(() => {
                clearTimeout(timer2);
                isDisplayGuide.value = true;
            }, 1500);
        }

        isDisplayGuide.value = false;
    },
    {
        immediate: true,
    },
);

let box: Element | null;
let deg = 0;
let startX = 0;
let endX = 0;

let moveFlag = false;

// 整体是一个圆，按当前的数据是12时辰，只能是半圆，无法实现首尾相接，故24个拼接而成
const hourLastIndex = 24;

function boxTouchStart(e: any) {
    // eslint-disable-next-line prefer-destructuring
    const touch = e.touches[0]; // 获取触摸对象
    startX = touch.clientX; // 获取触摸坐标
}

function boxTouchMove(e: any) {
    let tempIndex = currentHourIndex.value;
    moveFlag = true;
    endX = e.touches[0].clientX; // 获取触摸坐标
    deg = initRotate.value;
    const subValue = endX - startX; // 控制转动的速度

    // 向右边滑动
    if (subValue > 0) {
        deg = deg + 1;
    } else {
        deg = deg - 1;
    }
    const differ = deg - initStartDeg.value;
    const movingIndex = Math.round(differ / 15);
    tempIndex = tempIndex - movingIndex;

    if (tempIndex >= hourLastIndex) {
        tempIndex = tempIndex - hourLastIndex;
    }

    if (tempIndex < 0) {
        // 23 表示24时的最后一项序号，0开始
        const backLast = newData?.value?.list?.[23]?.startTime || 0;

        if (backLast < +new Date()) {
            tempIndex = hourLastIndex + tempIndex;
        } else {
            Toast.info('下个时辰未开启');
            return;
        }
    }

    const nextTime = newData?.value?.list?.[tempIndex]?.startTime || 0;
    if (nextTime > +new Date()) {
        Toast.info('下个时辰未开启');
        return;
    }

    initRotate.value = deg;
}

function boxTouchEnd() {
    if (!moveFlag) {
        // 没有滑动视作点击处理，不做行为
        moveFlag = false;

        return;
    }
    const differ = initRotate.value - initStartDeg.value;
    const moveIndex = Math.round(differ / 15);

    if (moveIndex === 0) {
        initRotate.value = initStartDeg.value;

        return;
    }

    if (moveIndex > 0) {
        // 顺时针
        console.log('++++++');
        initRotate.value = initStartDeg.value + moveIndex * 15;
        initStartDeg.value = initRotate.value;
    } else {
        // 逆时针
        console.log('-----');
        initRotate.value = initStartDeg.value + moveIndex * 15;
        initStartDeg.value = initRotate.value;
    }

    currentHourIndex.value = currentHourIndex.value - moveIndex;

    if (currentHourIndex.value >= hourLastIndex) {
        currentHourIndex.value = currentHourIndex.value - hourLastIndex;
    }

    if (currentHourIndex.value < 0) {
        currentHourIndex.value = hourLastIndex + currentHourIndex.value;
    }
    const item = newData?.value?.list?.[currentHourIndex.value];
    console.log('item', item);
    emits('change', item);

    startVibrate({
        duration: 30,
        strength: 'middle',
    }).catch(() => {
        navigator?.vibrate?.(30);
    });
}

const initTouchListen = () => {
    box = document.querySelector('.hour-list');

    if (!box) {
        return;
    }

    box.addEventListener('touchstart', boxTouchStart, false);
    box.addEventListener('touchmove', boxTouchMove, false);
    box.addEventListener('touchend', boxTouchEnd, false);
};
onMounted(() => {
    initTouchListen();
});
onBeforeUnmount(() => {
    box?.removeEventListener('touchstart', boxTouchStart, false);
    box?.removeEventListener('touchmove', boxTouchMove, false);
    box?.removeEventListener('touchend', boxTouchEnd, false);
});
</script>

<template>
    <div class="gis-twelve-half-circle">
        <template v-if="isDisplayGuide">
            <div class="hour-first-info">滑动可查看其他时辰榜单哦～</div>
            <div class="gis-cursor-guide" />
        </template>
        <div class="gis-twelve-front-flower" />
        <div class="area-limit">
            <div
                class="hour-list"
                :style="{ transform: `rotate(${initRotate}deg)` }"
            >
                <div
                    v-for="(item, index) in newData.list"
                    :key="item.classifyId"
                    class="twelve-elem-hour"
                    :class="{
                        'active-hour-lane': currentHourIndex === index,
                    }"
                    :style="{
                        transform: `rotate(${15 * index}deg)`,
                    }"
                >
                    <span class="lane-name"> {{ item.displayName }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.twelve-elem-hour {
    position: absolute;
    right: 0;
    left: 0;
    display: inline-block;
    height: 476px;
    font-family: FZDWTJW;
    font-size: 14px;
    font-weight: 500;
    line-height: 21px;
    letter-spacing: 0;
    color: #c18b40;
}
.hour-first-info {
    position: relative;
    top: -55px;
    right: 0;
    left: 0;
    z-index: 101;
    width: fit-content;
    height: 48px;
    padding: 0 20px;
    margin: 0 auto;
    font-size: 16px;
    font-weight: 400;
    line-height: 48px;
    letter-spacing: 0;
    color: #fff;
    text-align: center;
    background: rgba(0, 0, 0, 70%);
    border-radius: 24px;
}
.gis-close {
    position: absolute;
    top: 18px;
    right: 12px;
}
.gis-cursor-guide {
    position: absolute;
    top: 30px;
    right: 54px;
    z-index: 999;
}
.gis-hour-score-rule-main {
    border-radius: 17px;
}

.active-hour-lane {
    font-size: 18px;
    font-style: normal;
    line-height: 22px; /* 100% */
    color: #a25d04;
    text-align: center;
    opacity: 0.88;
}
.gis-twelve-half-circle {
    position: relative;
    z-index: 10;
}
.gis-twelve-front-flower {
    position: absolute;
    top: -9px;
    z-index: 99;
    pointer-events: none;
}
.area-limit {
    position: absolute;
    top: 26px;
    right: 0;
    left: 0;
    width: 235px;
    height: 46px;
    margin: 0 auto;
    overflow: hidden;
    text-align: center;
}
.hour-list {
    position: relative;
    // width: 496px;
    height: 476px;
    margin: 0 auto;
    border-radius: 100%;
}
</style>
