import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { RankFrameworkPostResponse } from '@pet/ones-rank.schema/index-home';
import type { TwelveSchema } from './schema';
export const dataFun = (contextName: symbol) => {
    const homeData = contextName
        ? inject<Ref<RankFrameworkPostResponse>>(contextName)
        : null;
    const data = computed(() => {
        const displayLaneViewList = homeData?.value?.displayLaneViewList || [];
        const classifyId =
            homeData?.value?.anchorDisplayScheduleAndLane?.classifyId;
        const joinActivity =
            homeData?.value?.anchorDisplayScheduleAndLane?.joinActivity;
        const list = displayLaneViewList.concat(displayLaneViewList);
        const anchorIdx = list.findIndex((i) => i.classifyId === classifyId);
        const initHourIndex = anchorIdx > -1 ? anchorIdx : 0;

        const init = -(15 * initHourIndex);
        const initRotate = init;
        const initStartDeg = init;

        // 二级默认tab 选中序号
        const subIndex =
            list?.[initHourIndex]?.subLaneViews?.findIndex((elem: any) => {
                return (
                    homeData?.value?.anchorDisplayScheduleAndLane?.rankId ===
                    elem.rankId
                );
            }) || 0;
        const defaultSubIndex = subIndex < 0 ? 0 : subIndex;
        return {
            list,
            initHourIndex,
            initRotate,
            initStartDeg,
            subTabData: {
                list: list?.[initHourIndex]?.subLaneViews?.slice(0, 3) || [],
                defaultIndex: defaultSubIndex,
                anchorScheduleType:
                    homeData?.value?.anchorDisplayScheduleAndLane
                        ?.scheduleType || 0,
                tabType: 'city',
                btnText: '地区',
            },
            cityData: {
                anchorSchedule: joinActivity
                    ? homeData?.value?.anchorDisplayScheduleAndLane
                    : undefined,
                schedules: list?.[initHourIndex]?.subLaneViews,
            },
        };
    });

    return data;
};
