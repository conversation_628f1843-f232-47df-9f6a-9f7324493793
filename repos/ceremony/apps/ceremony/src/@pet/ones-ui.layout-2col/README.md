# 两列布局组件文档

## 功能概述
`@pet/ones-ui.layout-2col` 是一个用于展示列表数据的两列布局组件。该组件通过 `v-for` 循环渲染列表中的每个项目，并支持自定义插槽内容。

## 属性和方法

### Props
- **list**: `T[]`  
  - **描述**: 需要渲染的列表数据。
  - **类型**: 泛型数组，其中 `T` 是一个对象类型。
  - **必填**: 是

- **keyName**: `string`  
  - **描述**: 用于唯一标识列表中每个项目的键名。如果未提供，则默认使用 `col-${idx}` 作为键名。
  - **类型**: 字符串
  - **必填**: 否

### 插槽
- **default**  
  - **描述**: 渲染列表中每个项目的自定义内容。
  - **参数**: 
    - `item`: 当前项目的数据。
    - `idx`: 当前项目的索引。

## 使用示例

```vue
<template>
  <TwoColLayout :list=\"items\" keyName=\"id\">
    <template #default=\"{ item, idx }\">
      <div class=\"item\">
        <span>{{ item.name }}</span>
        <span>{{ item.value }}</span>
      </div>
    </template>
  </TwoColLayout>
</template>

<script lang=\"ts\" setup>
import TwoColLayout from '@pet/ones-ui.layout-2col/index.vue';

const items = [
  { id: 1, name: 'Item 1', value: 'Value 1' },
  { id: 2, name: 'Item 2', value: 'Value 2' },
  // 更多项目...
];
</script>
```

## 注意事项
- 确保 `list` 属性是一个有效的数组。
- 如果 `keyName` 属性未提供，组件将使用 `col-${idx}` 作为键名，这可能会导致性能问题或错误，特别是在列表动态变化时。

## 依赖项
- Vue 3.x
- TypeScript
