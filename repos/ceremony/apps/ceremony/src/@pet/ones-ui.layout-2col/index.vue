<template>
    <div class="flex flex-wrap">
        <template
            v-for="(item, idx) in list"
            :key="item?.[keyName] ?? `col-${idx}`"
        >
            <slot name="default" :item="item" :idx="idx"></slot>
        </template>
    </div>
</template>

<script lang="ts" setup generic="T extends { [key: string]: any }">
defineProps<{
    list: T[];
    keyName: string;
}>();
</script>
