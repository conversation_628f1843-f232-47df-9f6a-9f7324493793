export interface LiveStreamProps {
    cover: string;
    endTime: number;
    /**
     * 未加密的直播ID
     */
    liveStreamId: number;
    /**
     * 加密的直播ID
     */
    liveStreamIdStr: string;
    /**
     * 晚会预约人数
     */
    reservationCount: string;
    reservationId: string;
    /**
     * 是否预约了晚会直播
     */
    reservationLiveStream: boolean;
    startTime: number;
    /**
     * 左下角的提示文案
     */
    tip: string;
    authorId: number;
    title: string;
    headTip: string;
}

export type ManifestProps = {
    liveAdaptiveConfig: string;
    liveStream: {
        [x: string]: unknown;
        liveStreamId: string;
        coverUrl: string;
        hlsPlayUrl: string;
    };
};

export type LiveStreamData = {
    [x: string]: unknown;
    liveStreamId: string;
    coverUrl: string;
    hlsPlayUrl: string;
    androidHWDecode?: boolean;
    liveAdaptiveConfig: string;
};

export interface TipType {
    tip: string;
    headTip: string;
}
