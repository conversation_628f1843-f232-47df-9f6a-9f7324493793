<template>
    <div class="video-live">
        <ACard class="video-live-card">
            <ACardTitle>{{ liveData.title }} </ACardTitle>
            <ACardContent class="live-content" @click="goLiveRoom">
                <div
                    v-if="playStatus === 'normal'"
                    id="party-live-dom"
                    ref="playerRef"
                    v-click-log="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: { btn_type: 'ENTER_LIVE' },
                    }"
                    v-show-log="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: { btn_type: 'ENTER_LIVE' },
                    }"
                    class="live-box"
                ></div>
                <div
                    v-else
                    class="live-box else-status flex-center"
                    :style="defaultBg"
                >
                    <div class="play-icon"></div>
                </div>
                <div
                    v-if="
                        conf4Tab.progamShow && isOutLiveRoom && programTip.tip
                    "
                    class="program"
                >
                    <div class="ongoing-text a-text-main">
                        {{ programTip.headTip }}
                    </div>
                    <div class="program-name a-text-main">
                        {{ programTip.tip }}
                    </div>
                </div>
                <!-- <div
                    v-if="isOutLiveRoom && playStatus !== 'ended'"
                    class="sound"
                    @click="clickSound"
                >
                    <div v-if="soundState" class="sound-open"></div>
                    <div v-else class="sound-close"></div>
                </div> -->
            </ACardContent>
        </ACard>
    </div>
</template>

<script lang="ts" setup>
// # if (target === '4tab')
import { Container } from 'typedi';
// # endif
// eslint-disable-next-line import/order
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { usePartySwitch } from '@pet/ones-use.usePartySwitch';
import useKconfStore from '@pet/ones-use.useKconf';
import { useDowngrade } from '@pet/ones-use.useDowngrade';
import useYodaLivePlayer from '@pet/ones-use.use-yoda-video-play/live';
import { goToLiveRoom } from '@pet/ones-use.goLiveRoom';
import { ACard, ACardContent, ACardTitle } from '@alive-ui/base';
import { getKwaiVersion, isOutLiveRoom, Report } from '@alive-ui/actions';
import { getLiveStream, getTip } from './service';
import type {
    ManifestProps,
    LiveStreamData,
    LiveStreamProps,
    TipType,
} from './schema/index';
const playerLiveParam = ref<LiveStreamData | undefined>();
const { conf4Tab } = storeToRefs(useKconfStore());

const props = defineProps<{
    liveData: LiveStreamProps;
    showLiveStream: boolean;
}>();

const programTip = ref<TipType>({
    headTip: props.liveData.headTip,
    tip: props.liveData.tip,
});

const emit = defineEmits(['refresh']);

const { destroy, create, showFallbackError, updateLock } = useYodaLivePlayer();
const { playStatus, player, lock } = storeToRefs(useYodaLivePlayer());
const downGradeSwitch = usePartySwitch();
const resolveFn = ref();
const p = new Promise((r) => {
    resolveFn.value = r;
});
const playerRef = ref();

const defaultBg = computed(() => {
    return {
        background: `url("${conf4Tab.value?.liveBg?.stage1 ?? ''}") center / 100% no-repeat`,
    };
});

watch(
    () => playerRef.value,
    (val) => {
        if (val) {
            resolveFn.value?.();
        }
    },
);

// 是否低端机
const isDowngrade = useDowngrade();

// 跳转直播间
const goLiveRoom = () => {
    // 优先使用拉流过来的直播间id，兜底使用后端穿过来的
    const liveStreamId =
        playerLiveParam.value?.liveStreamId ?? props.liveData.liveStreamIdStr;
    goToLiveRoom([liveStreamId], liveStreamId);
    sessionStorage.setItem('fromVideoLive', 'true');
};
const getLiveInfo = async () => {
    try {
        if (lock.value) {
            return;
        }
        // 加锁
        updateLock(true);
        // 获取直播流信息
        const data = (await getLiveStream(
            props.liveData.authorId,
        )) as ManifestProps;
        playerLiveParam.value = {
            ...data.liveStream,
            androidHWDecode: true,
            liveAdaptiveConfig: data.liveAdaptiveConfig,
        };
        if (playStatus.value !== 'normal') {
            await player?.value?.destroy();
            playStatus.value = 'normal';
            await p;
        }
        // 拉起直播流
        // await nextTick();
        await create({
            src: playerLiveParam.value,
            fallbackSrc: playerLiveParam.value.hlsPlayUrl,
            onEnded: () => {
                if (conf4Tab.value?.closeRefreshSwitch) {
                    // 兜底开关，设置 true 则不刷新，避免影响体验、打挂接口
                    return;
                }
                emit('refresh');
            },
        });
        updateLock(false);
    } catch (error) {
        updateLock(false);
        // 如果获取直播流信息的时候返回了错误信息，直接展示兜底图片

        showFallbackError();
        Report.biz.error('【直播流】获取直播流信息接口请求失败', {
            type: 'getLiveStream',
            error,
        });
    }
};

function onMountedLiveHanlder() {
    if (
        isOutLiveRoom &&
        !props.showLiveStream &&
        !isDowngrade.value &&
        !downGradeSwitch.value.liveDowngradeSwitch
    ) {
        // 只有在间外,并且没有出发降级方案的时候才能拉起直播流
        getLiveInfo();
    } else {
        // 展示兜底图片
        showFallbackError();

        Report.biz.warning('【直播流】命中降级-展示静态兜底图', {
            kwaiVersion: getKwaiVersion(),
            showLiveStream: props.showLiveStream,
            isDowngrade: isDowngrade.value,
            downGradeSwitch: downGradeSwitch.value.liveDowngradeSwitch,
        });
    }
}

// # if (target === '4tab')
Container.set({
    id: 'on_native_visible_show',
    multiple: true,
    value: () => {
        onMountedLiveHanlder();
    },
});
// # endif
onMounted(() => {
    onMountedLiveHanlder();
    document.addEventListener('visibilitychange', visibilityHandler);
});
const isFromVideoLive = () =>
    sessionStorage.getItem('fromVideoLive') === 'true';

const updateTip = async () => {
    const data = await getTip();
    programTip.value = data;
};
const visibilityHandler = () => {
    if (!document.hidden && isFromVideoLive() && isOutLiveRoom) {
        // 更新节目单信息
        updateTip();
        sessionStorage.removeItem('fromVideoLive');
    }
};

onUnmounted(() => {
    destroy();
    document.removeEventListener('visibilitychange', visibilityHandler);
});
</script>

<style lang="less" scoped>
.video-live-card {
    margin-top: 16px;
}
.live-content {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: start;
    flex-direction: column;
    padding-bottom: 95px;
}
.live-box {
    width: 358px;
    height: 214px;
    border-radius: 16px;
    overflow: hidden;
}
.program {
    position: absolute;
    left: 0;
    top: 155px;
    margin-left: 12px;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 36px;
    align-items: center;
    padding: 2px 10px 2px 5px;
    border-radius: 0px 18px 18px 0px;
    background: rgba(0, 0, 0, 0.35);
}
.ongoing-text {
    font-size: 10px;
    opacity: 0.7;
}
.program-name {
    font-family: 'PingFang SC';
    font-size: 12px;
    font-weight: 500;
}
// .sound {
//     position: absolute;
//     right: 11px;
//     bottom: 11px;
//     margin-right: 12px;
//     &-open {
//         width: 24px;
//         height: 24px;
//         background-color: #666;
//         background: url(./assets/sound-open.png) center / 100% no-repeat;
//     }
//     &-close {
//         width: 24px;
//         height: 24px;
//         background: url(./assets/sound-close.png) center / 100% no-repeat;
//     }
// }
.else-status {
    background: url(./assets/default.jpg) center / 100% no-repeat;
}
.play-icon {
    width: 55px;
    height: 55px;
    background: url(./assets/play-live-icon.png) center / 100% no-repeat;
}
</style>
