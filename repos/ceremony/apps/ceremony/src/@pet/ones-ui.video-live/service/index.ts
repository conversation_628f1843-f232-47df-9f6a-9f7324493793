import { request } from '@alive-ui/actions';
import type { ManifestProps, TipType } from '../schema/index';

const LIVE_STREAM = '/rest/k/live/byUser';
const TIP = '/rest/wd/live/plutus/yc24/4tab/getTip';

/**
 * 直播拉流接口文档
 * https://docs.corp.kuaishou.com/d/home/<USER>
 * @param userId
 */
export const getLiveStream = async (userId: number) => {
    const res = await request.post<ManifestProps>(LIVE_STREAM, {
        userId,
        count: 6,
    });
    return res as unknown;
};
// 获取节目单信息
// https://mock.corp.kuaishou.com/project/941/interface/api/1227782
export const getTip = async () => {
    const res = await request.post<TipType>(TIP);
    return res.data;
};
