<template>
    <div class="team-rank">
        <RankItemBase
            :item="{
                ...item,
                disableClick: true,
            }"
        >
            <template #user-name>
                <Username>
                    {{ item.itemName }}
                </Username>
            </template>

            <template #extra><slot name="extra" /></template>
            <template #right>
                <!-- 定义右侧内容 -->
                <div
                    v-if="item?.sponsors?.length"
                    class="flex sponsor-list-style"
                    @click.capture="opeBattleList"
                >
                    <div class="gis-small-back-icon battle-sm-icon" />
                    <AAvatar
                        v-for="elem in item?.sponsors?.slice(0, 4)?.reverse()"
                        :key="elem?.itemId"
                        class="team-avatar"
                        size="xs"
                        :src="elem?.headUrl"
                    >
                        <template #bottomInner>
                            <slot name="bottomInner" />
                        </template>
                        <template #bottomRight> <span></span> </template>
                    </AAvatar>
                </div>
                <div v-else></div>
            </template>
        </RankItemBase>
        <Popup
            ref="refPopup"
            v-model="isShowPop"
            position="bottom"
            @hide="reset"
        >
            <div class="sponsor-dialog-rank a-bg-page">
                <RankHeader class="mb-0 card-width-100">
                    <BackStatic class="back-btn gis-back-icon" @click="reset" />
                    <!-- <div class="gis-sponsor-top-bg" /> -->
                    <div class="group-info-area text-center mb-18px">
                        <AAvatar
                            class="card-team-avatar"
                            size="md"
                            :src="item.headUrl"
                        />
                        <div class="team-name a-text-main text-18 text-bold">
                            {{ item.itemName }}
                        </div>
                        <div class="gis-sponsor-line" />
                    </div>

                    <div v-if="item?.sponsors?.length">
                        <APBaseRankList
                            :has-top3-bg="false"
                            :log-params="item.logParams"
                        >
                            <RankItemBase
                                v-for="elem in item.sponsors"
                                :key="elem.itemId"
                                :item="{
                                    ...elem,
                                    activityId: item.activityId,
                                    h5RankShowIndex: '0',
                                }"
                            >
                                <template v-if="elem.teamLeader" #extra-label>
                                    <div class="team-leader-icon ml-3px"></div>
                                </template>
                            </RankItemBase>
                        </APBaseRankList>
                    </div>
                </RankHeader>
            </div>
        </Popup>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import RankHeader from '@pet/ones-rank.list-head/index.vue';
import RankItemBase from '@pet/ones-rank.base-item/index.vue';
import { Popup } from '@lux/sharp-ui-next';
import { BaseRankList as APBaseRankList } from '@alive-ui/pro';
import { BackStatic } from '@alive-ui/icon';
import { Avatar, RankList } from '@alive-ui/base';
import type { PropType } from 'vue';
import type { ItemRankInfo } from '@pet/ones-rank.base-item/schema';
const { Username } = RankList;

const { Avatar: AAvatar } = Avatar;
const props = defineProps({
    item: {
        type: Object as PropType<
            ItemRankInfo & {
                activityId?: string;
                scheduleId?: string;
                rankId?: number;
                logParams?: Record<string, any>;
            }
        >,
        default: () => {
            return {};
        },
    },
});
const currentBattleSponsor = ref<ItemRankInfo>();
const isShowPop = ref(false);
const opeBattleList = () => {
    currentBattleSponsor.value = props.item;
    isShowPop.value = true;
};
const reset = () => {
    isShowPop.value = false;
};
</script>

<style lang="less" scoped>
:deep(.rank-user-name) {
    display: flex;
    align-items: center;
}
:deep(.rank-username-mb) {
    margin-bottom: 0;
}
.sponsor-list-style {
    width: 130px;
    //padding-right: 14px;
    flex-direction: row-reverse;
    align-items: center;
}
.sponsor-dialog-rank {
    position: relative;
    border-radius: 16px 16px 0 0;
}
.card-width-100 {
    width: 100%;
}
.card-team-avatar {
    margin: 0 auto;
}
.team-avatar {
    margin-left: -10px;
}
.gis-back-icon {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 1;
}
.gis-small-back-icon {
    margin-left: 7px;
}
.battle-sm-icon {
    width: 10px;
    height: 10px;
    background: url('./battle-sm-icon.png') center / 100% no-repeat;
}
.team-leader-icon {
    flex: none;
    width: 44px;
    height: 14px;
    background: url('./team-leader-icon.png') center / 100% no-repeat;
}
</style>
