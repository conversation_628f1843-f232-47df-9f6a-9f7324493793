<template>
    <div v-if="swiperListData" class="gift-track-scroll-wrap">
        <div class="track hideMask">
            <Swiper
                ref="swiper"
                :loop="true"
                :init-index="swiperIndex"
                indicator-space
                :indicator-show="false"
                :is-overflow-hidden="false"
                :follow-config="{
                    isFollow: true,
                    scale: {
                        minScale: 0.75,
                    },
                }"
                @change="onChange"
            >
                <div
                    v-for="item in swiperListData"
                    :key="item.stageType"
                    class="track-item"
                >
                    <img :src="item.iconPic" />
                </div>
            </Swiper>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Swiper } from '@lux/sharp-ui-next';
const props = defineProps({
    swiperListData: {
        type: Object,
        default: () => {},
    },
    anchorStageType: {
        type: Number,
        default: 0,
    },
});
const swiperIndex = ref();
const emit = defineEmits(['swiperChange']);
// 找到首次加载的index
watch(
    () => [props.swiperListData, props.anchorStageType],
    () => {
        swiperIndex.value = props.swiperListData.findIndex(
            (item: any) => item.stageType === props.anchorStageType,
        );
    },
    {
        immediate: true,
    },
);
// 礼物赛切换礼物加载榜单逻辑处理
const onChange = (index: number) => {
    swiperIndex.value = index;
    emit('swiperChange', swiperIndex.value);
};
</script>

<style lang="less" scoped>
.gift-track-scroll-wrap {
    position: relative;
    width: 100%;
    overflow: hidden;
    flex-wrap: wrap;
    margin-top: 2px;
    // background: #38266b;
    border-radius: 16px;
    .track {
        width: 42%;
        // height: 196px;
        // padding-top: 30px;
        margin: 0px auto;

        &::before,
        &::after {
            position: absolute;
            top: 0;
            z-index: 2;
            display: none;
            width: 50px;
            height: 100px;
            content: ' ';
        }

        &::before {
            left: 0;
            pointer-events: none;
            background: url('./assets/mask-big-l.png') no-repeat;
            background-size: cover;
        }
        &::after {
            right: 0;
            pointer-events: none;
            background: url('./assets/mask-big-r.png') no-repeat;
            background-size: cover;
        }
        &.hideMask::before,
        &.hideMask::after {
            display: inline-block;
        }
        img {
            width: 100px;
            height: 100px;
        }
    }
}
</style>
