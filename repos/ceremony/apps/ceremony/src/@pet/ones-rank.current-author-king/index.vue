<!-- 普通榜单头像当前主播区域 -->
<template>
    <APBaseRankList
        v-if="
            data.isJoinCurrRank || (!data.isJoinCurrRank && data?.nextLaneHint)
        "
        :log-params="data.logParams"
    >
        <RankItemBase
            is-current-author
            :item="data"
            :need-show-right="!!liveStreamId && !bolIsAuthor"
            :current-label="data.currentLabel"
        >
            <template v-if="data?.nextLaneHint" #info>
                <div class="a-text-main opacity-60 text-12 lane-next-style">
                    {{ data.nextLaneHint }}
                </div>
            </template>
            <template #right>
                <div v-if="data?.nextLaneHint" />
                <GiftButton v-else>
                    {{ data.giftBtnText }}
                </GiftButton>
            </template>
        </RankItemBase>
    </APBaseRankList>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import RankItemBase from '@pet/ones-rank.base-item/index.vue';
import { GiftB<PERSON>on, BaseRankList as APBaseRankList } from '@alive-ui/pro';
import { liveStreamId, bolIsAuthor } from '@alive-ui/actions';
import { dataFun } from './data';
import type { CurrentKingSchema, PropsCommonParams } from './schema';
interface CurrentData extends PropsCommonParams {
    data?: CurrentKingSchema;
}
const props = withDefaults(defineProps<CurrentData>(), {
    data: () => {
        return {
            h5ShowScore: '',
            scoreLabel: '',
            currentLabel: '当前主播',
            giftBtnText: '去助力',
            avatarSize: 'sm',
            nextLaneHint: '',
            itemId: 0,
            rankShowIndex: 0,
            isJoinCurrRank: false,
        };
    },
    useProps: false,
    contextName: Symbol.for('ctx'),
});
const data = props.useProps ? ref(props.data) : dataFun(props.contextName);
</script>
<style class="less" scoped>
.lane-next-style {
    white-space: nowrap;
}
</style>
