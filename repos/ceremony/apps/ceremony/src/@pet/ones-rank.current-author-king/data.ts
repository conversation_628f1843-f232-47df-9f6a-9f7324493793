import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';
import type { CurrentKingSchema } from './schema';
export const dataFun = (contextName: symbol) => {
    const rankInfo = contextName
        ? inject<Ref<QueryRankPostResponse>>(contextName)
        : null;
    const data = computed<CurrentKingSchema>(() => {
        const hintScoreText =
            rankInfo?.value?.bottomInfo?.displayHint?.split('${hintScore}');
        const nextLane = rankInfo?.value?.bottomInfo?.nextLane || '';
        const nextTimeHint = rankInfo?.value?.bottomInfo?.nextTimeHint || '';
        const hintScore = rankInfo?.value?.bottomInfo?.h5ShowHintScore;
        return {
            ...rankInfo?.value?.bottomInfo?.itemRankInfo,
            rankShowIndex:
                rankInfo?.value?.bottomInfo.itemRankInfo?.rankShowIndex || 0,
            itemId: rankInfo?.value?.bottomInfo?.itemRankInfo?.itemId || 0,
            liveStreamIdList:
                rankInfo?.value?.rankList?.map((item: any) => {
                    return item?.liveStreamId;
                }) || [],
            h5ShowScore: hintScoreText?.[0] && hintScore ? hintScore : '',
            scoreLabel:
                hintScore && hintScoreText?.[0] ? hintScoreText?.[0] : '',
            currentLabel: '当前主播',
            giftBtnText: '去助力',
            avatarSize: 'sm',
            isJoinCurrRank: rankInfo?.value?.extraData?.isJoinCurrRank || false,
            nextLaneHint: nextTimeHint?.replace('${nextLane}', nextLane),
            logParams: {},
        };
    });
    return data;
};
