<template>
    <SubPageFrame
        class="cohesion-challenge-page"
        :card-name="
            cohesionData.levelId ? `延续今日${lastTask?.additionRateText}` : ''
        "
        :show-back="true"
        :kv-image="pageKconf.kv"
        rule-key="cohesiveness"
    >
        <template #leftTop>
            <img
                v-if="showGoHome"
                src="../assets/leftTopIcon.png"
                alt=""
                class="w-40px h-40px mb-14px"
                @click="goMain"
            />
            <BackStatic
                v-else
                v-pcDirectives:hide
                class="back-btn"
                @click="handleBack"
            />
        </template>
        <template #default>
            <div v-if="pageStatus.success">
                <!-- 加成状态 -->
                <template v-if="status === StatusType.InAddition">
                    <div class="text-center flex-col items-center">
                        <img
                            v-if="cohesionData?.additionIcon"
                            class="w-165px h-167px block m-l-0px m-r-0px mt-32px"
                            :src="cohesionData?.additionIcon"
                            alt="奖励图片"
                        />
                        <div class="text-16px m-t-44px addition-content-ttitle">
                            {{ currentTask?.additionRateText }}时刻开启
                        </div>
                        <div class="text-14px a-text-main m-t-4px">
                            加成倒计时<span class="a-text-highlight">{{
                                useCountdownText?.text
                            }}</span>
                        </div>
                    </div>
                </template>

                <!-- 其他状态 -->
                <template v-else>
                    <!-- 倒计时显示 -->
                    <div
                        v-if="
                            status === StatusType.InProgress ||
                            status === StatusType.Unlock
                        "
                        class="text-12px a-text-main op-60 line-height-18px text-center"
                    >
                        挑战倒计时{{ useCountdownText?.text }}
                    </div>

                    <!-- 图片/视频区域 -->
                    <div class="text-center flex-col items-center mt-32px">
                        <div
                            class="w-165px h-167px block m-l-0px m-r-0px relative"
                        >
                            <img
                                v-if="!videoUrl && imgUrl"
                                class="w-165px h-167px block m-l-0px m-r-0px"
                                :src="imgUrl"
                                alt="奖励图片"
                            />
                            <div
                                v-if="videoUrl"
                                class="absolute top-0 left-0 w-full h-full"
                            >
                                <!-- <VideoPlay
                                    object-fit="fill"
                                    :video-url="videoUrls"
                                    not-click
                                    :loop="false"
                                /> -->
                                <TransparentVideo
                                    class="draw-transparent-video"
                                    :width="165 * 4"
                                    :height="167 * 4"
                                    :video-url="videoUrl"
                                    :poster-url="imgUrl"
                                />
                            </div>
                        </div>

                        <!-- 状态标签 -->
                        <div
                            v-if="
                                status === StatusType.Unlock ||
                                status === StatusType.InProgress
                            "
                            class="status-tag inline-block text-12px line-height-none"
                        >
                            解锁中
                        </div>
                        <!-- <div
                            v-if="status === StatusType.Unlock || status === StatusType.InProgress"
                            class="status-tag-unlock inline-block text-12px line-height-none"
                        >
                            已解锁
                        </div> -->

                        <!-- 进度圈 -->
                        <ProgressCircleNode
                            v-if="
                                [
                                    StatusType.InProgress,
                                    StatusType.Unlock,
                                    StatusType.Failed,
                                    StatusType.Success,
                                ].includes(status)
                            "
                            :value="cohesionData.currentScore"
                            :max="50"
                            :nodes="nodes"
                        />
                    </div>

                    <!-- 状态信息区域 -->
                    <div class="text-center flex-col items-center m-t-21px">
                        <!-- 已结束状态 -->
                        <div
                            v-if="
                                status === StatusType.Ended ||
                                status === StatusType.InAdditionEnd
                            "
                            class="flex"
                        >
                            <div class="a-text-main text-14px">
                                <div
                                    class="text-24px a-text-highlight f-w-bold"
                                >
                                    {{ cohesionData?.assistNumStr || 0 }}
                                </div>
                                <div>助力人数</div>
                            </div>
                            <div class="m-l-42px a-text-main text-14px">
                                <div
                                    class="text-24px a-text-highlight f-w-bold"
                                >
                                    {{ cohesionData?.additionScoreStr || 0 }}
                                </div>
                                <div>额外加分</div>
                            </div>
                        </div>

                        <!-- 其他状态信息 -->
                        <div v-else>
                            <!-- 进行中或成功状态的分数显示 -->
                            <template
                                v-if="
                                    [
                                        StatusType.InProgress,
                                        StatusType.Success,
                                        StatusType.Failed,
                                        StatusType.Unlock,
                                    ].includes(status)
                                "
                            >
                                <span class="a-text-highlight">{{
                                    cohesionData?.currentScoreStr
                                }}</span>
                                <span class="op-60 a-text-main"
                                    >/{{
                                        status === StatusType.Unlock
                                            ? unlockTask.targetScoreStr
                                            : currentTask.targetScoreStr
                                    }}分</span
                                >
                            </template>

                            <!-- 状态文本信息 -->
                            <div
                                v-if="status === StatusType.Unlock"
                                class="text-14px a-text-main"
                            >
                                已解锁{{
                                    unlockTask.preAdditionDurations
                                }}秒，继续解锁<span
                                    class="a-text-highlight m-l-4px m-r-4px"
                                    >{{ unlockTask.additionDurations }}秒</span
                                >{{ unlockTask?.additionRateText }}
                            </div>
                            <div
                                v-if="
                                    [
                                        StatusType.NotStarted,
                                        StatusType.InProgress,
                                    ].includes(status)
                                "
                                class="text-14px a-text-main"
                            >
                                送"{{ cohesionData.sendGiftName }}", 可解锁最高
                                <span class="a-text-highlight m-l-2px m-r-2px"
                                    >{{ lastTask.additionDurations }}秒 </span
                                >{{ lastTask?.additionRateText }}
                            </div>

                            <div
                                v-if="status === StatusType.Failed"
                                class="text-14px a-text-main"
                            >
                                挑战失败，未获得加成
                            </div>
                            <div
                                v-if="status === StatusType.Success"
                                class="text-14px a-text-main"
                            >
                                挑战成功，已解锁<span
                                    class="a-text-highlight m-l-4px m-r-4px"
                                    >{{ currentTask.additionDurations }}秒</span
                                >{{ currentTask?.additionRateText }}
                            </div>
                        </div>
                    </div>
                </template>

                <!-- 按钮区域 - 使用计算属性简化 -->
                <div class="flex justify-center">
                    <!-- 交互按钮 - 可点击状态 -->

                    <AButton
                        v-if="showInteractiveButton"
                        v-show-log="buttonLogParams"
                        v-click-log="buttonLogParams"
                        size="lg"
                        type="primary"
                        class="flex-col m-t-24px w-192px h-52px send-gift-btn"
                        @click="onSendBtnClick"
                    >
                        <!-- @click="" -->
                        <template v-if="bolIsAuthor"> 号召助力 </template>
                        <template v-else-if="status === StatusType.InAddition">
                            立刻送礼
                        </template>
                        <template v-else>
                            <div>送{{ cohesionData.sendGiftName }}</div>
                            <div class="text-10px op-80 m-t-2px">
                                {{ cohesionData?.giftUnitPrice }}快币
                            </div>
                        </template>
                    </AButton>

                    <!-- 禁用状态按钮 -->
                    <AButton
                        v-else
                        v-show-log="buttonLogParams"
                        v-click-log="buttonLogParams"
                        :disabled="true"
                        size="lg"
                        type="primary"
                        class="flex-col m-t-24px w-192px h-52px send-gift-btn"
                    >
                        {{ disabledButtonText }}
                    </AButton>
                </div>
                <div
                    v-if="showHintDesc"
                    class="a-text-main text-12px op-70 text-center m-t-8px"
                >
                    {{ pageKconf?.hintDesc || '每人每轮挑战最多可记50000票' }}
                </div>
            </div>

            <!-- 错误状态 -->
            <ElseStatus
                v-else-if="pageStatus.nodata"
                :page-status="pageStatus"
                :is-show-refresh="true"
                class="h-465px"
                @refresh="cohesionHomeStore.init"
            >
                <template #icon>
                    <img
                        src="https://p4-live.wskwai.com/kos/nlav12706/ceremony-summer-25/frame_2x.e88e5890cc612ce2.png"
                        class="w-96px h-90px op-60"
                    />
                </template>
                <template #text> 凝聚力挑战加载中 </template>
            </ElseStatus>
            <ElseStatus v-else-if="showCountdownDelay">
                <template #icon>
                    <img
                        src="https://p4-live.wskwai.com/kos/nlav12706/ceremony-summer-25/frame_2x.e88e5890cc612ce2.png"
                        class="w-96px h-90px op-60"
                    />
                </template>
                <template #text>
                    {{
                        pageKconf?.countdownDelayDesc ||
                        '本轮挑战结束，结果处理中'
                    }}
                </template>
            </ElseStatus>
            <ElseStatus
                v-else
                :page-status="pageStatus"
                :is-show-refresh="true"
                @refresh="cohesionHomeStore.init"
            />
            <!-- 刷新按钮 -->

            <RefreshIcon
                :refresh-time="1500"
                @refresh="cohesionHomeStore.init"
            />
        </template>
    </SubPageFrame>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { ref, onMounted, computed, onBeforeUnmount } from 'vue';
import { storeToRefs } from 'pinia';
import SubPageFrame from '@pet/ones-ui.sub-page-frame/index.vue';
import TransparentVideo from '@pet/ones-ui.DrawMachine/components/transparent-video/index.vue';
import { sendFmp } from '@gundam/weblogger';
import { BackStatic } from '@alive-ui/icon';
import { AButton, ElseStatus, RefreshIcon } from '@alive-ui/base';
import {
    // nameSlice,
    bolIsAuthor,
    getPageStatus,
} from '@alive-ui/actions';
import { StatusType, EntrySource } from '../schemas/index';
import useCohesionHomeStore from '../models/index';
import { useCohesion } from '../hooks/useCohesion';
import ProgressCircleNode from '../components/ProgressCircleNode.vue'; // 添加新组件导入

// const kconfStore = useKconf();
const cohesionHomeStore = useCohesionHomeStore();
const { cohesionData, pageStatus } = storeToRefs(useCohesionHomeStore());
const emits = defineEmits(['refresh-task']);
const router = useRouter();
const route = useRoute();
const showCountdownDelay = ref(false);
const countdownDelayTime = 3000;
cohesionHomeStore.init().finally(() => {
    sendFmp();
});
// const entrySource =
const handleBack = () => {
    router.back();
};
const goMain = () => {
    router.push({
        name: 'home',
        query: {
            // 跳转的时候去处链接中的sourceEntrance参数
            sourceEntrance: '',
        },
    });
};

const showGoHome = computed(() => {
    console.log('route.query.entrySource------', route.query.entrySource);
    return EntrySource.pandent === route?.query?.entrySource;
});
// const backAction = props.backAction || 'back';
// if (backAction !== 'back' && backAction !== 'exit') {
//     throw new Error('Invalid backAction value');
// }
// backFunction[backAction]();
// };
const timeOutBar = ref<ReturnType<typeof setTimeout> | null>(null);

const {
    pageKconf,
    currentTask,
    nextTask,
    nodes,
    status,
    useCountdownText,
    videoUrl,
    getMNowTime,
    lastTask,
    buttonLogParams,
    unlockTask,
    onSendBtnClick,
    imgUrl,
} = useCohesion(cohesionData, {
    onCountdownEnd: () => {
        // 随机打散刷新
        const { loadingSwitch } = pageKconf.value;
        const randomNumber = Math.random() * 3000;
        const timeoutTime =
            (pageKconf.value.countdownDelayTime || countdownDelayTime) +
            randomNumber;
        const openDelayStatus =
            loadingSwitch && status.value === StatusType.InAddition;
        if (openDelayStatus) {
            showCountdownDelay.value = true;
            cohesionHomeStore.pageStatus = getPageStatus('loading');
        }
        timeOutBar.value = setTimeout(() => {
            if (openDelayStatus) {
                showCountdownDelay.value = false;
                cohesionHomeStore.pageStatus = getPageStatus('success');
            }
            cohesionHomeStore.init();
            getMNowTime();
        }, timeoutTime);
    },
});
// 注销
onBeforeUnmount(() => {
    timeOutBar.value && clearTimeout(timeOutBar.value);
});

// 添加计算属性来简化模板逻辑
const showInteractiveButton = computed(() => {
    return [
        StatusType.Unlock,
        StatusType.InProgress,
        StatusType.InAddition,
    ].includes(status.value);
});
// 添加计算属性来简化模板逻辑
const showHintDesc = computed(() => {
    return [
        StatusType.NotStarted,
        StatusType.InProgress,
        StatusType.Unlock,
    ].includes(status.value);
});
const disabledButtonText = computed(() => {
    if (
        [
            StatusType.Failed,
            StatusType.InAdditionEnd,
            StatusType.Ended,
        ].includes(status.value) &&
        cohesionData.value.nextStartTime
    ) {
        return `下一场 ${nextTask?.value?.startTimeText} 开始`;
    }
    if (status.value === StatusType.Success) {
        return `${useCountdownText?.value?.text}后 加成生效`;
    }
    if (status.value === StatusType.NotStarted) {
        return `${nextTask?.value?.startTimeText} 即将开始`;
    }
    return '已结束';
});

//     switch (status.value) {
//         case StatusType.Failed && cohesionData.value.nextStartTime:
//             return `下一场 ${nextTask?.value?.startTimeText} 开始`;
//         case StatusType.Success:
//             return `${useCountdownText?.value?.text}后 加成生效`;
//         case StatusType.Ended:
//             return cohesionData.value.nextStartTime
//                 ? `下一场 ${nextTask?.value?.startTimeText} 开始`
//                 : '已结束';
//         case StatusType.NotStarted:
//             return `${nextTask?.value?.startTimeText} 即将开始`;
//         case StatusType.InAdditionEnd && cohesionData?.value?.nextStartTime:
//             return `下一场 ${nextTask?.value?.startTimeText} 开始`;
//         default:
//             return '已结束';
//     }
// });
</script>

<style scoped lang="less">
.cohesion-challenge-page {
    /deep/ .card-title-icon {
        background-image: url('https://p4-live.wskwai.com/kos/nlav12706/ceremony-summer-25/frame_2x.669519c25ffdff4a.png');
    }
    /deep/ .card-title-container {
        margin-bottom: 2px;
    }
    /deep/ .sub-page-card {
        padding-bottom: 20px;
    }
    .status-tag {
        background: linear-gradient(105.76deg, #b6b6b6 0%, #565656 100%);
        border-radius: 16px;
        padding: 4px 6px;
        color: #fff;
    }
    .status-tag-unlock {
        background: linear-gradient(105.76deg, #ff5f8f 0%, #ff4646 100%);
        border-radius: 16px;
        padding: 4px 6px;
        color: #fff;
    }
    .addition-content-ttitle {
        color: #fff;
        font-family: MF YuanHei;
    }
    .send-gift-btn {
        background-size: 100% 52px;
    }
}
</style>
