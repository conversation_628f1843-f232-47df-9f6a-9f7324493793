import { request, getQuery } from '@alive-ui/actions';
import type { HemoRequest, HemoResponse } from '../schemas/index';

const PATH = {
    home: '/webapi/live/revenue/operation/activity/cohensionv2/home',
    authorCall:
        '/webapi/live/revenue/operation/activity/cohensionv2/authorCall',
};
/**
 * 首页接口
 */
export const getHome = async (): Promise<HemoResponse | undefined> => {
    const params: HemoRequest = {
        activityBiz: getQuery('activityBiz') ?? '',
        liveStreamId: getQuery('liveStreamId') ?? '',
    };
    const res = await request.post<HemoResponse>(PATH.home, params);
    return res?.data;
};

// 号召接口
/**
 * 首页接口
 */
export const postAuthorCall = async (): Promise<HemoResponse | undefined> => {
    const params: HemoRequest = {
        activityBiz: getQuery('activityBiz') ?? '',
        liveStreamId: getQuery('liveStreamId') ?? '',
    };
    const res = await request.post<HemoResponse>(PATH.authorCall, params);
    return res?.data;
};
