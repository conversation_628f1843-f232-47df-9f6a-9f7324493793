/* eslint-disable max-lines-per-function */
import { ref } from 'vue';
import { defineStore } from 'pinia';
import { Toast } from '@lux/sharp-ui-next';
import { getPageStatus, Report, query } from '@alive-ui/actions';
import { getHome } from '../services/index';
import { StatusType } from '../schemas/index';
import type { HemoResponse } from '../schemas/index';
export default defineStore('cohesion-challenge-home', () => {
    const currentAnchorStageType = ref(Number(query.stageType) || 0);
    const pageStatus = ref(getPageStatus('init')); // 页面状态数据定义
    const clickLoadingFlag = ref(false);
    const cohesionData = ref<HemoResponse>({} as HemoResponse);
    const cohesionOriginData = ref({});

    // 初始化页面，获取接口数据
    const init = async (isTabChange?: boolean) => {
        if (clickLoadingFlag.value) {
            return;
        }
        clickLoadingFlag.value = true;
        // Toast.loading('正在加载', 0, true);
        pageStatus.value = getPageStatus('loading');
        try {
            const res = await getHome();
            cohesionData.value = res || {
                value: null,
                isAuthor: false,
                levelId: 0,
                status: StatusType.NotStarted,
                startTime: 0,
                additionRate: 0,
                additionStartTime: 0,
                additionEndTime: 0,
                endTime: 0,
                finishTime: 0,
                targetScore: 0,
                targetScoreStr: '',
                currentScore: 0,
                currentScoreStr: '',
                gradientItemViews: [],
                imageUrl: '',
                nextStartTime: 0,
                sendGiftId: 0,
                giftUnitPrice: 0,
                addGiftScore: 0,
                giftToken: '',
                assistNum: 0,
                additionScore: 0,
                additionScoreStr: '',
                sendGiftName: '',
                additionIcon: '',
                endIcon: '',
                assistNumStr: '',
            };
            if (cohesionData.value.gradientItemViews?.length) {
                pageStatus.value = getPageStatus('success');
            } else {
                pageStatus.value = getPageStatus('nodata');
            }
        } catch (error) {
            pageStatus.value = getPageStatus('error');
            Report.biz.error('home init error', {
                error,
            });
        } finally {
            Toast.hide();
            clickLoadingFlag.value = false;
        }
    };

    return {
        cohesionData,
        currentAnchorStageType,
        pageStatus,
        init,
    };
});
