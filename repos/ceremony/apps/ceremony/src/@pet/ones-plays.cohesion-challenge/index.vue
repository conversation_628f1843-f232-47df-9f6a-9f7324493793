<!-- eslint-disable vue/no-v-html -->
<template>
    <ACard v-if="cohesionInfo" class="cohesion-card p-l-12px p-r-12px">
        <ACardContent>
            <div class="card-bar m-b-12px">
                <!-- {{ status }} -->
                <div class="card-bar-title text-16px">
                    {{ cardKconf?.title }}
                </div>
                <div
                    class="text-12px a-text-main flex items-center op-60"
                    @click="goPage"
                >
                    <div
                        v-if="
                            status === StatusType.NotStarted &&
                            cohesionInfo?.taskId
                        "
                    >
                        最高解锁{{ lastTask?.additionDurations }}秒
                    </div>
                    <!--兜底-->
                    <div
                        v-if="
                            status === StatusType.NotStarted &&
                            !cohesionInfo?.taskId
                        "
                    >
                        查看更多
                    </div>
                    <div
                        v-else-if="
                            status === StatusType.InProgress ||
                            status === StatusType.Unlock
                        "
                    >
                        {{ useCountdownText?.text }}后挑战结束
                    </div>
                    <div
                        v-else-if="
                            status === StatusType.Failed ||
                            status === StatusType.Success
                        "
                    >
                        下一场{{ nextTask?.startTimeText }}开始
                    </div>
                    <div
                        v-else-if="
                            status === StatusType.InAddition ||
                            isLastTask ||
                            status === StatusType.InAdditionEnd
                        "
                    >
                        查看更多
                    </div>
                    <div v-else-if="status === StatusType.AnchorNotOnList">
                        查看更多
                    </div>
                    <Right class="title-arrow ml-4px text-14px" />
                </div>
            </div>
            <div class="card a-bg-substrate">
                <!-- 主播未上榜 -->
                <img
                    v-if="status === StatusType.AnchorNotOnList"
                    class="schedule-img"
                    :src="cardKconf?.maxImg || maxImg"
                />

                <img
                    v-else-if="cohesionInfo?.taskId"
                    class="schedule-img"
                    :src="imgUrl"
                />
                <img v-else class="schedule-img" :src="cardKconf.defaultImg" />

                <div class="cohesion-info">
                    <template v-if="status === StatusType.NotStarted">
                        <div
                            v-if="cohesionInfo?.taskId"
                            class="text-14px a-text-main font-bold"
                        >
                            <template v-if="lastTask?.additionDurations">
                                解锁<span
                                    class="a-text-highlight m-l-2px m-r-2px"
                                    >{{ lastTask?.additionDurations }}秒</span
                                ></template
                            >{{ lastTask?.additionRateText }}
                        </div>
                        <div v-else class="text-14px a-text-main">
                            凝聚力挑战加载中
                        </div>
                        <div
                            v-if="cohesionInfo?.taskId"
                            class="cohesion-info-desc a-text-main text-12px m-t-4px op-60"
                        >
                            未开始，开始时间 {{ nextTask?.startTimeText }}
                        </div>
                        <div
                            v-else
                            class="cohesion-info-desc a-text-main text-12px m-t-4px op-60"
                        >
                            请耐心等待...
                        </div>
                    </template>
                    <!-- 任务进行中 -->
                    <template
                        v-else-if="
                            status === StatusType.InProgress ||
                            status === StatusType.Unlock
                        "
                    >
                        <div class="text-14px a-text-main font-bold">
                            解锁<span class="a-text-highlight m-l-2px m-r-2px"
                                >{{
                                    status === StatusType.Unlock
                                        ? unlockTask.additionDurations
                                        : currentTask?.additionDurations
                                }}秒</span
                            >{{ currentTask?.additionRateText }}
                        </div>
                        <div
                            class="cohesion-info-desc a-text-main text-12px m-t-4px"
                        >
                            <span class="a-text-highlight">{{
                                cohesionInfo?.currentScoreStr
                            }}</span>
                            <span class="op-60"
                                >/{{
                                    status === StatusType.Unlock
                                        ? unlockTask.targetScoreStr
                                        : currentTask.targetScoreStr
                                }}分</span
                            >
                        </div>
                    </template>
                    <!-- 主播未上榜 -->
                    <template v-else-if="status === StatusType.AnchorNotOnList">
                        <div class="text-14px a-text-main font-bold">
                            {{ cardKconf?.maxDesc || '最高可解锁180秒加成' }}
                        </div>
                        <div
                            class="cohesion-info-desc a-text-main text-12px m-t-4px op-60"
                        >
                            {{
                                cardKconf?.anchorNotOnListDesc ||
                                '入围top99名可以参与该玩法'
                            }}
                        </div>
                    </template>
                    <template v-else-if="status === StatusType.Failed">
                        <div class="text-14px a-text-main text-12px font-bold">
                            {{ currentTask?.additionRateText }}，挑战失败
                        </div>
                        <div
                            class="cohesion-info-desc a-text-main op-60 text-12px m-t-4px"
                        >
                            未获得加成时长
                        </div>
                    </template>
                    <template v-else-if="status === StatusType.Success">
                        <div class="text-14px a-text-main text-12px font-bold">
                            {{ currentTask?.additionRateText }}，挑战成功
                        </div>
                        <div
                            class="cohesion-info-desc a-text-main text-12px m-t-4px"
                        >
                            <span class="op-60">获得</span
                            ><span class="a-text-highlight m-l-2px m-r-2px"
                                >{{ currentTask?.additionDurations }}秒</span
                            ><span class="op-60">加成生效时间</span>
                        </div>
                    </template>
                    <template v-else-if="status === StatusType.InAddition">
                        <div class="cohesion-info-title a-text-main font-bold">
                            {{ currentTask?.additionRateText }}时刻开启！
                        </div>
                        <div
                            class="cohesion-info-desc a-text-main text-12px m-t-4px"
                        >
                            <span class="op-60">倒计时</span
                            ><span
                                class="a-text-highlight m-l-2px m-r-2px font-bold"
                                >{{ useCountdownText?.text }}</span
                            >
                        </div>
                    </template>
                    <template
                        v-else-if="
                            status === StatusType.Ended ||
                            status === StatusType.InAdditionEnd
                        "
                    >
                        <div class="cohesion-info-title a-text-main font-bold">
                            {{ currentTask?.additionRateText }}时刻开启！
                        </div>
                        <div
                            class="cohesion-info-desc a-text-main text-12px m-t-4px"
                        >
                            <span class="op-60">获得额外加成</span
                            ><span class="a-text-highlight m-l-2px m-r-2px">{{
                                cohesionInfo?.additionScoreStr || 0
                            }}</span>
                        </div>
                    </template>
                </div>
                <AButton
                    v-if="
                        status === StatusType.NotStarted && cohesionInfo?.taskId
                    "
                    v-show-log="buttonLogParams"
                    v-click-log="buttonLogParams"
                    size="sm"
                    type="primary"
                    :disabled="true"
                    class="flex-col line-height-none"
                >
                    即将开始
                </AButton>
                <!-- 兜底 -->
                <AButton
                    v-if="
                        status === StatusType.NotStarted &&
                        !cohesionInfo?.taskId
                    "
                    v-show-log="buttonLogParams"
                    v-click-log="buttonLogParams"
                    size="sm"
                    type="primary"
                    class="flex-col line-height-none"
                    @click="refreshHandle"
                >
                    刷新
                </AButton>
                <AButton
                    v-else-if="
                        status === StatusType.InProgress ||
                        status === StatusType.Unlock
                    "
                    v-show-log="buttonLogParams"
                    v-click-log="buttonLogParams"
                    size="sm"
                    type="primary"
                    class="flex-col line-height-none"
                    @click="onSendBtnClick"
                >
                    <template v-if="!bolIsAuthor">
                        <div>去助力</div>
                        <div class="text-9px op-80 m-t-2px">
                            {{ cohesionInfo?.giftUnitPrice }}快币
                        </div>
                    </template>
                    <template v-else>号召助力</template>
                </AButton>
                <AButton
                    v-else-if="status === StatusType.Failed"
                    v-show-log="buttonLogParams"
                    v-click-log="buttonLogParams"
                    class="button-primary-sm-bg"
                    size="sm"
                    type="primary"
                    :disabled="true"
                >
                    <!-- {{ bolIsAuthor ? '号召助力' : '立刻送礼' }} -->
                    {{ cohesionInfo.nextStartTime ? '等待下场' : '已结束' }}
                </AButton>
                <AButton
                    v-else-if="status === StatusType.Success"
                    v-show-log="buttonLogParams"
                    v-click-log="buttonLogParams"
                    size="sm"
                    type="primary"
                    :disabled="true"
                >
                    即将生效
                </AButton>
                <AButton
                    v-else-if="status === StatusType.InAddition"
                    v-show-log="buttonLogParams"
                    v-click-log="buttonLogParams"
                    size="sm"
                    type="primary"
                    @click="onSendBtnClick"
                >
                    {{ bolIsAuthor ? '号召助力' : '立刻送礼' }}
                </AButton>
                <AButton
                    v-else-if="
                        status === StatusType.Ended ||
                        status === StatusType.InAdditionEnd
                    "
                    v-show-log="buttonLogParams"
                    v-click-log="buttonLogParams"
                    size="sm"
                    type="primary"
                    :disabled="true"
                >
                    {{ cohesionInfo.nextStartTime ? '等待下场' : '已结束' }}
                </AButton>
                <div
                    v-if="status === StatusType.AnchorNotOnList"
                    class="font-12px w-82px"
                >
                    <!-- 暂不可参与 -->
                </div>
            </div>
        </ACardContent>
    </ACard>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { computed, ref, watch, onBeforeUnmount } from 'vue';
import { Right } from '@alive-ui/icon';
import { AButton, ACard, ACardContent } from '@alive-ui/base';
import {
    // nameSlice,
    bolIsAuthor,
    toWorkVideoPage,
    isYodaPCContainer,
    timestampToTime,
    stopMove,
    goOtherPage,
    activityBiz,
} from '@alive-ui/actions';
import { StatusType } from './schemas/index';
import { useCohesion } from './hooks/useCohesion';
import type { HemoResponse } from './schemas/index';
// const serviceDataInfo = ref(null);
const props = defineProps<{
    serviceData: HemoResponse;
    stageType: number;
    extraCardKey?: string;
}>();
const router = useRouter();
const timeOutBar = ref();
const goPage = () => {
    if (status.value === StatusType.AnchorNotOnList) {
        openRulePage();
        return;
    }
    router.push({
        name: props.extraCardKey,
        // query: {
        //     peakType: 0,
        //     // stageType: props.stageType,
        // },
    });
};

const cohesionInfo = ref<HemoResponse>({
    isAuthor: false,
    levelId: 0,
    status: StatusType.NotStarted,
    startTime: 0,
    endTime: 0,
    finishTime: 0,
    targetScore: 0,
    currentScore: 0,
    gradientItemViews: [],
    additionRate: 0,
    additionStartTime: 0,
    additionEndTime: 0,
    nextStartTime: 0,
    sendGiftId: 0,
    giftUnitPrice: 0,
    addGiftScore: 0,
    giftToken: '',
    imageUrl: '',
    assistNum: 0,
    assistNumStr: '',
    additionScore: 0,
    sendGiftName: '',
    value: undefined,
    targetScoreStr: '',
    currentScoreStr: '',
    taskId: undefined,
    additionScoreStr: '',
    additionIcon: '',
    endIcon: '',
});
const emits = defineEmits(['refresh-task']);

watch(
    () => props.serviceData,
    (newVal) => {
        console.log('props.serviceData', newVal);
        cohesionInfo.value = newVal;
    },
    {
        deep: true,
        immediate: true,
    },
);
const {
    cardKconf,
    currentTask,
    isLastTask,
    onAssist,
    onCallUpon,
    nextTask,
    useCountdownText,
    logCommonParams,
    lastTask,
    status,
    buttonLogParams,
    onSendBtnClick,
    unlockTask,
    kconfData,
    maxImg,
    imgUrl,
} = useCohesion(cohesionInfo, {
    onCountdownEnd: () => {
        timeOutBar.value = setTimeout(() => {
            emits('refresh-task', {
                extraCardKey: props.extraCardKey,
                showLoading: false,
            });
        }, 3000);
    },
    isHome: true,
});
const refreshHandle = () => {
    emits('refresh-task', {
        extraCardKey: props.extraCardKey,
        showLoading: true,
    });
};
const openRulePage = () => {
    const ruleList: Record<string, any> =
        kconfData.value?.common?.ruleMap?.list ?? {};
    const ruleUrl = ruleList?.cohesiveness?.url ?? '';
    if (ruleUrl) {
        const url = new URL(ruleUrl);
        // 修改参数
        url.searchParams.set('activityBiz', activityBiz);
        goOtherPage('jimu', url.toString());
    }
};
// 注销
onBeforeUnmount(() => {
    timeOutBar.value && clearTimeout(timeOutBar.value);
});
</script>

<style lang="less" scoped>
.cohesion-card {
}
.card-bar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-content: center;
}
.card-bar-title {
    font-family: HYYakuHei;
    background: linear-gradient(89.98deg, #ffffff 0.03%, #ffc4a3 95.69%),
        linear-gradient(94.51deg, #d8ecff 7.58%, #ffffff 55.12%, #ffdac6 100%);
    background-clip: text;
    color: transparent;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    width: 100%;
    height: 84px;
    padding: 0 20px;
}

.schedule-img {
    z-index: 1;
    width: 56px;
    height: 56px;
}

.cohesion-info {
    width: 170px;
    &-title {
        font-size: 14px;
    }
    &-desc {
        font-family: 'PingFang SC';
    }
}
</style>
