<template>
    <ProgressCircle
        type="even-nodes"
        :progress-width="4"
        :value="value"
        :max="max"
        :angle-span="24"
        :radius="900"
        :nodes="nodes"
        :width="384"
        :height="52"
        :animate="false"
        :bottom-margin="20"
        progress-gradient-string="linear-gradient(90deg, rgba(255, 223, 191, 0) 21.95%, #FFDFBF 98.4%)"
        track-gradient-string="linear-gradient(90deg, rgba(255, 255, 255, 0) 3.37%, rgba(255, 255, 255, 0.1) 22%, rgba(255, 255, 255, 0.1) 77.89%, rgba(255, 255, 255, 0) 96.52%)"
    >
        <template #node="{ node }">
            <!-- 小点 -->
            <image
                :href="
                    node.unlockStatus === UnlockStatus.ComingSoon
                        ? 'https://p4-live.wskwai.com/kos/nlav12706/dot.png'
                        : 'https://p4-live.wskwai.com/kos/nlav12706/dot2.png'
                "
                x="-8"
                y="-8"
                width="16"
                height="16"
            />
            <!-- 文案描述 -->
            <text
                y="18"
                text-anchor="end"
                :x="((node.label?.length || 0) * 11) / 2"
                :class="
                    node.unlockStatus === UnlockStatus.NotUnlocked ||
                    node.unlockStatus === UnlockStatus.Success
                        ? 'op-60'
                        : ''
                "
                fill="#FFDFBF"
                font-size="14"
                font-family="HYYakuHei"
            >
                {{ node.label }}
            </text>
            <!-- 锁图 -->
            <image
                v-if="node.unlockStatus !== UnlockStatus.Success"
                :class="
                    node.unlockStatus === UnlockStatus.NotUnlocked
                        ? 'op-60'
                        : ''
                "
                href="https://p4-live.wskwai.com/kos/nlav12706/ceremony-summer-25/suo-quan-xian_2x.f03b612d79a9525a.png"
                :x="((node.label?.length || 0) * 11) / 2"
                y="7"
                width="12"
                height="12"
            />
        </template>
    </ProgressCircle>
</template>

<script setup lang="ts">
import ProgressCircle from '@pet/ones-ui.progress-circle/index.vue';
import { UnlockStatus } from '../schemas/index';
import type { ProgressNode } from '@pet/ones-ui.progress-circle/types.ts';

defineProps<{
    value: number;
    max: number;
    nodes: ProgressNode[];
}>();
</script>
