export interface HemoRequest {
    activityBiz: string;
    liveStreamId: string;
}

export interface GradientItemView {
    successVideo: string;
    levelId: number; // 任务id
    targetScore: number;
    additionRate: number;
    additionDurationMs: number; // 任务开始时间
    previewIcon: string;
    successIcon: string;
    runningIcon: string;
    failIcon: string;
    targetScoreStr: string;
}

export enum StatusType {
    NotStarted = 1, // 预告
    Failed = 2, // 失败
    Success = 3, // 成功
    InProgress = 4, // 进行中
    Ended = 5, // 结束
    InAdditionEnd = 6, // 加成结束
    AnchorNotOnList = 7, // 主播未上榜
    InAddition = 100, // 加成中 （未完成、进行中、完成）非后端状态
    Unlock = 101, // 解锁
}

// 解锁状态: 解锁成功、未解锁、即将解锁
export enum UnlockStatus {
    Success = 1, // 解锁成功
    NotUnlocked = 2, // 未解锁
    ComingSoon = 3, // 即将解锁
}
export interface HemoResponse {
    value: any;
    isAuthor: boolean; // 是否主播端
    levelId: number; // 任务id
    status: StatusType; // 待定:未开始、失败、成功、进行中、加成中（未完成、进行中、完成）
    startTime: number; // 任务开始时间
    endTime: number; // 任务结束时间
    finishTime: number; // 任务完成时间
    targetScore: number; // 任务最高的目标值
    targetScoreStr: string;
    currentScore: number; // 任务当前值
    currentScoreStr: string;
    gradientItemViews: Array<GradientItemView>;
    additionRate: number;
    additionStartTime: number;
    additionEndTime: number;
    nextStartTime: number;
    sendGiftId: number;
    giftUnitPrice: number;
    addGiftScore: number;
    giftToken: string;
    imageUrl: string; // 图片URL
    assistNum: number; // 助力人数
    additionScore: number; // 额外加分
    additionScoreStr: string; // 额外加分字符串
    sendGiftName: string; // 发送的礼物名称
    taskId?: number; // 任务id
    additionIcon: string; // 加成图标
    endIcon: string; // 结束图标
    assistNumStr: string; // 助力人数字符串
}

// 入口来源
export enum EntrySource {
    pandent = 'pandent', // 挂件
    live = 'live', // 直播间
    home = 'home', // 首页
    other = 'other', // 其他
}
