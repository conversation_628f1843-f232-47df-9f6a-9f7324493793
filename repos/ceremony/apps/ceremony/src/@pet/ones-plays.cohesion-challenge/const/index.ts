import { StatusType } from '../schemas/index';
// tips 模板
const tipsReward = '最高解锁60秒';
const tipsInProgress = '4分30秒后挑战结束';
const tipsNext = '下一场 23:30开始';
const tipsDefault = '更多';
// 中间部分模板
const contentReward = '解锁30秒32%加成';
const contentFailed = '32%加成，挑战失败';
const contentSuccess = '32%加成，挑战成功';
const contentInAddition = '32%加成时刻！';
// tips 模板
export const tipsTeml = {
    [StatusType.NotStarted]: tipsReward,
    [StatusType.InProgress]: tipsInProgress,
    [StatusType.Failed]: tipsNext,
    [StatusType.Success]: tipsNext,
    [StatusType.InAddition]: tipsDefault,
};
// 内容标题
export const contentTeml = {
    [StatusType.NotStarted]: contentReward,
    [StatusType.InProgress]: contentReward,
    [StatusType.Failed]: contentFailed,
    [StatusType.Success]: contentSuccess,
    [StatusType.InAddition]: contentInAddition,
    [StatusType.Ended]: contentInAddition,
};

export const contentDescTeml = {};

export const showPageTopCountdown = [StatusType.InProgress];
