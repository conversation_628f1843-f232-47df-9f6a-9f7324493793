/* eslint-disable max-lines-per-function */
import { ref, computed, watch, type Ref } from 'vue';
import { storeToRefs } from 'pinia';
import { throttle } from 'lodash-es';
import dayjs from 'dayjs';
import useKConfBatch from '@pet/ones-use.useKconfBatch';
import { sendGiftMultiple } from '@pet/ones-use.send-gift-multiple';
import { Toast } from '@lux/sharp-ui-next';
import {
    dispatchLiveRouter,
    isYodaPCContainer,
    showToast,
} from '@alive-ui/system';
import { useBaseRankItemContext } from '@alive-ui/pro';
import { Button as AButton } from '@alive-ui/base';
import {
    useCountdownTo,
    convertSToMinusSecond,
    activityBiz,
    authorId,
    liveStreamId,
    entry_src,
    bolIsAuthor,
    Report,
    getServerTime,
    appendParam,
    isOutLiveRoom,
} from '@alive-ui/actions';
import { postAuthorCall } from '../services/index';
import {
    type HemoResponse,
    type GradientItemView,
    UnlockStatus,
    StatusType,
} from '../schemas/index';
import { sendTask } from '@/common/logger';

const homeImgKeys: Record<StatusType, keyof GradientItemView> = {
    [StatusType.NotStarted]: 'previewIcon',
    [StatusType.Failed]: 'failIcon',
    [StatusType.Ended]: 'previewIcon',
    [StatusType.Success]: 'successIcon',
    [StatusType.InAddition]: 'previewIcon',
    [StatusType.InProgress]: 'runningIcon',
    [StatusType.Unlock]: 'runningIcon',
    [StatusType.InAdditionEnd]: 'runningIcon',
    [StatusType.AnchorNotOnList]: 'previewIcon',
};
const pageImgKeys: Record<StatusType, keyof GradientItemView> = {
    [StatusType.NotStarted]: 'previewIcon',
    [StatusType.Failed]: 'failIcon',
    [StatusType.Ended]: 'previewIcon',
    [StatusType.Success]: 'successIcon',
    [StatusType.InAddition]: 'previewIcon',
    [StatusType.InProgress]: 'runningIcon',
    [StatusType.Unlock]: 'runningIcon',
    [StatusType.InAdditionEnd]: 'runningIcon',
    [StatusType.AnchorNotOnList]: 'previewIcon',
};
// 取整
const numCeil = (num: number) => Math.floor(num);
const maxImg =
    'https://p4-live.wskwai.com/kos/nlav12706/ceremony-summer-25/s-off_2x.e2d5d8f7e09a05f2.png';
export function useCohesion(
    cohesionInfo: Ref<HemoResponse>,
    options: {
        onCountdownEnd: () => void;
        isHome?: boolean;
    },
) {
    const { kconfData } = storeToRefs(useKConfBatch());
    const cardKconf = computed(() => {
        return kconfData.value.cohesion.card;
    });
    const commonKconf = computed(() => {
        return kconfData.value?.cohesion?.common || {};
    });
    const sendGifting = ref(false);
    const nowTime = ref(new Date().getTime());
    const useCountdownText = ref<{
        text: Ref<string>;
        reset: (_t: number) => void;
        leftMs: Ref<number>;
    }>();
    const pageKconf = computed(() => {
        return kconfData.value.cohesion?.page;
    });
    const getCurrentIndex = (val: number) => {
        const gradientItemViews = JSON.parse(
            JSON.stringify(cohesionInfo.value?.gradientItemViews || []),
        );
        for (let i = 0; i < gradientItemViews.length; i++) {
            if (gradientItemViews[i].targetScore > val) {
                return i;
            }
        }
        return gradientItemViews.length - 1;
    };
    // 最后一个任务
    const lastTask = computed(() => {
        const gradientItemViews = cohesionInfo.value?.gradientItemViews || [];
        const lastItem = gradientItemViews[gradientItemViews.length - 1];
        return {
            ...lastItem,
            additionDurations: lastItem?.additionDurationMs / 1000,
            viewPageImg:
                lastItem?.[pageImgKeys[status.value] as keyof GradientItemView],
            viewHomeImg:
                lastItem?.[homeImgKeys[status.value] as keyof GradientItemView],
            additionRateText: `${numCeil((cohesionInfo.value?.additionRate ?? 0) * 100)}%加成`,
            additionRatePageText: `${numCeil((cohesionInfo.value?.additionRate ?? 0) * 100)}%加成时刻`,
        };
    });

    // 当前任务项
    const currentTask = computed(() => {
        const gradientItemViews = JSON.parse(
            JSON.stringify(cohesionInfo.value?.gradientItemViews || []),
        );
        const currentIndex = getCurrentIndex(cohesionInfo.value.currentScore);
        // const currentIndex = gradientItemViews.findIndex(
        //     (item: GradientItemView) => item.levelId === currentLevelId,
        // );
        const startTime = cohesionInfo.value?.startTime;
        const endTime = cohesionInfo.value?.endTime;
        const levelId = cohesionInfo.value?.levelId;
        const createTask = gradientItemViews[currentIndex];
        const maxTask = gradientItemViews?.[gradientItemViews.length - 1] || {};
        const targetScore = createTask?.targetScore || 0;
        const currentScore = cohesionInfo.value?.currentScore || 0;
        const perTask = gradientItemViews[currentIndex - 1] || createTask;
        let additionDurations = createTask?.additionDurationMs / 1000;
        const getViewImg = (
            imgKeys: Record<StatusType, keyof GradientItemView>,
        ) => {
            let nowTask = createTask;
            if (
                status.value === StatusType.Success &&
                currentIndex !== 0 &&
                currentScore < maxTask.targetScore
            ) {
                nowTask = perTask;
            }
            return status.value === StatusType.NotStarted
                ? lastTask.value?.viewPageImg
                : nowTask?.[
                      imgKeys[
                          cohesionInfo.value?.status as keyof typeof pageImgKeys
                      ] as keyof GradientItemView
                  ];
        };
        if (
            status.value === StatusType.Success &&
            currentIndex !== 0 &&
            currentScore < maxTask.targetScore
        ) {
            additionDurations = perTask.additionDurationMs / 1000;
        }
        return {
            ...createTask,
            additionDurations,
            startTime,
            endTime,
            currentIndex,
            additionRateText: `${numCeil((cohesionInfo.value?.additionRate ?? 0) * 100)}%加成`,
            additionRatePageText: `${numCeil((cohesionInfo.value?.additionRate ?? 0) * 100)}%加成时刻`,
            viewPageImg: getViewImg(pageImgKeys),
            viewHomeImg: getViewImg(homeImgKeys),
            successVideo: gradientItemViews[currentIndex]?.successVideo,
        };
    });

    const unlockTask = computed(() => {
        const gradientItemViews = JSON.parse(
            JSON.stringify(cohesionInfo.value?.gradientItemViews || []),
        );
        const nowTask = currentTask.value;
        const { currentIndex } = currentTask.value;
        const preTask = gradientItemViews[currentIndex - 1] || nowTask;
        return {
            ...currentTask.value,
            targetScoreStr: nowTask?.targetScoreStr || '',
            preAdditionDurations: preTask?.additionDurationMs / 1000,
            additionDurations: nowTask?.additionDurationMs / 1000,
            additionRateText: `${numCeil((cohesionInfo.value?.additionRate ?? 0) * 100)}%加成`,
            viewPageImg:
                nowTask?.[
                    pageImgKeys[StatusType.Unlock] as keyof GradientItemView
                ],
            viewHomeImg:
                nowTask?.[
                    homeImgKeys[StatusType.Unlock] as keyof GradientItemView
                ],
        };
    });
    // 下一个任务信息
    const nextTask = computed(() => {
        const gradientItemViews = JSON.parse(
            JSON.stringify(cohesionInfo.value.gradientItemViews || []),
        );
        const currentLevelId = cohesionInfo.value?.levelId;
        const currentIndex = gradientItemViews.findIndex(
            (item: GradientItemView) => item.levelId === currentLevelId,
        );
        if (currentIndex === -1) {
            return null;
        }
        const nextStartTime = cohesionInfo.value?.nextStartTime;

        const nextTaskItem = gradientItemViews[currentIndex + 1];
        return {
            ...nextTaskItem,
            startTimeText: dayjs(nextStartTime).format('HH:mm'),
        };
    });

    const isLastTask = computed(() => {
        return cohesionInfo.value?.status === StatusType.Ended;
    });

    const nodes = computed(() => {
        // const gradientItemViews = cohesionInfo.value?.gradientItemViews || [];
        const gradientItemViews = JSON.parse(
            JSON.stringify(cohesionInfo.value.gradientItemViews || []),
        );
        let targetIndex = 0;
        let preTargetScore = 0;
        return [
            ...gradientItemViews.map(
                (
                    item: {
                        targetScore: any;
                        additionDurationMs: any;
                        levelId: number;
                    },
                    index: number,
                ) => {
                    // if (item.le)
                    const { currentScore, levelId } = cohesionInfo.value;

                    if (item.levelId === levelId) {
                        targetIndex = index;
                    }
                    let unlockStatus = UnlockStatus.NotUnlocked;
                    if (
                        currentScore < item.targetScore &&
                        currentScore >= preTargetScore
                    ) {
                        unlockStatus = UnlockStatus.ComingSoon;
                    }
                    if (currentScore >= item.targetScore) {
                        unlockStatus = UnlockStatus.Success;
                    }
                    // if (item.targetScore <= currentScore) {
                    //     unlockStatus = UnlockStatus.Success;
                    //     targetIndex = index;
                    // } else if (targetIndex + 1 === index) {
                    //     unlockStatus = UnlockStatus.ComingSoon;
                    // }
                    preTargetScore = item.targetScore;
                    return {
                        value: item.targetScore,
                        unlockStatus,
                        label: `${item.additionDurationMs / 1000}秒`,
                    };
                },
            ),
        ];
    });
    const status = computed(() => {
        const {
            additionStartTime,
            additionEndTime,
            currentScore,
            gradientItemViews,
        } = cohesionInfo.value || {};
        let taskStatus = cohesionInfo.value?.status || StatusType.NotStarted;
        if (
            nowTime.value >= additionStartTime &&
            nowTime.value < additionEndTime &&
            taskStatus === StatusType.Success
        ) {
            taskStatus = StatusType.InAddition;
        } else if (
            taskStatus === StatusType.InProgress &&
            gradientItemViews?.find((item) => item.targetScore === currentScore)
        ) {
            taskStatus = StatusType.Unlock;
        }

        return taskStatus;
    });
    // 升级动画视频
    // const getSuccess = () => {

    // };
    const videoUrl = computed(() => {
        if (!cohesionInfo.value?.startTime) {
            return '';
        }
        const playNum = +(localStorage.getItem(nowStorageKey.value) || 0);
        const { gradientItemViews } = cohesionInfo.value || {};
        const { currentIndex } = currentTask.value;
        const preTask = gradientItemViews?.[currentIndex - 1] || {};
        const successVideo = preTask?.successVideo || '';
        return [StatusType.InProgress, StatusType.Unlock].includes(
            status.value,
        ) &&
            successVideo &&
            currentIndex > 0 &&
            playNum < 1
            ? successVideo
            : '';
    });
    const getMNowTime = async () => {
        nowTime.value = await getServerTime()
            .then((ms) => {
                return ms.serverTimeStamp || +new Date();
            })
            .catch(() => +new Date());
    };
    const nowStorageKey = computed(() => {
        return `cohesion-challenge${cohesionInfo.value.startTime}-${cohesionInfo.value?.levelId || ''}`;
    });
    watch(
        () => cohesionInfo.value?.additionStartTime,
        (newVal, oldVal) => {
            if (newVal && newVal !== oldVal) {
                getMNowTime();
            }
        },
    );
    watch(
        () => status.value,
        (newVal, oldVal) => {
            if (newVal) {
                let endTime = 0;
                if (
                    newVal === StatusType.InProgress ||
                    (newVal === StatusType.Unlock &&
                        cohesionInfo.value?.endTime)
                ) {
                    endTime = cohesionInfo.value.endTime;
                } else if (
                    newVal === StatusType.InAddition &&
                    cohesionInfo.value?.additionEndTime
                ) {
                    endTime = cohesionInfo.value.additionEndTime;
                } else if (
                    newVal === StatusType.Success &&
                    cohesionInfo.value?.additionStartTime
                ) {
                    endTime = cohesionInfo.value.additionStartTime;
                }
                if (!endTime) return;
                useCountdownText.value = useCountdownTo(endTime, {
                    immediateEmit: true,
                    useServerTime: true,
                    transformFn: (t: number) =>
                        [StatusType.Success].includes(newVal)
                            ? `${Math.floor(t / 1000)}秒`
                            : convertSToMinusSecond(t / 1000),
                    onEnd: () => {
                        console.log('倒计时结束');
                        options?.onCountdownEnd();
                    },
                });
            }
        },
        {
            immediate: true,
            deep: true,
        },
    );

    watch(
        () => videoUrl,
        (newVal) => {
            if (newVal && cohesionInfo.value?.startTime) {
                let playNum = +(localStorage.getItem(nowStorageKey.value) || 0);
                playNum++;
                localStorage.setItem(nowStorageKey.value, String(playNum));
            }
        },
        {
            immediate: true,
            deep: true,
        },
    );
    // 埋点通用参数
    const logCommonParams = {
        play_type: 2,
        // activity_name: activityBiz,
        // anchor_user_id: authorId,
        // live_stream_id: liveStreamId,
        // entry_src,
        // user_type: bolIsAuthor ? 'AUTHOR' : 'USER',
        // gift_id: cohesionInfo.value?.sendGiftId || 0,
    };
    // 去助力
    const onAssist = () => {
        const { sendGiftId, sendGiftName, giftToken, giftUnitPrice } =
            cohesionInfo.value;
        if (sendGifting.value) return;
        sendGifting.value = true;
        const giftParams = {
            giftId: `${sendGiftId}`,
            giftName: sendGiftName, // 礼物名字
            giftToken, // 礼物token
            unitPrice: giftUnitPrice, // 礼物价格
        };

        sendGiftMultiple(
            giftParams,
            () => {
                sendTask('CLICK', {
                    type: 'TASK_EVENT',
                    action: 'OP_ACTIVITY_SEND_BUTTON',
                    params: {
                        btn_type: '去助力',
                        gift_id: sendGiftId,
                        play_type: 2,
                    },
                    status: 'SUCCESS',
                });
                showToast('感谢助力，分值即将刷新');
            },
            () => {
                // showToast('送礼失败，请稍后重试');
            },
        ).finally(() => {
            sendGifting.value = false;
        });
    };
    // 拉起礼物面板
    /**
     * @name 打开礼物面板
     * @param params 礼物面板参数 https://docs.corp.kuaishou.com/k/home/<USER>/fcACRlearpMYvruRRuzfYoG3e
     * @param keepDisplayWebView  跳转后是否展示webview，false会关闭webview
     */
    const openGiftPanel = () => {
        const { sendGiftId, sendGiftName, giftToken, giftUnitPrice } =
            cohesionInfo.value;
        if (sendGifting.value) return;
        sendGifting.value = true;
        const url = appendParam('kwailive://giftpanel', {
            sourceType: 'h5', // 来源类型
            tab: 'tab-604',
            selectedGiftId: sendGiftId,
        } as unknown as Record<string, unknown>);

        dispatchLiveRouter({
            path: url,
            keepDisplayWebView: false,
        })
            .catch((err) => {
                console.error('打开礼物面板', err);
            })
            .finally(() => {
                sendGifting.value = false;
            });
        sendTask('CLICK', {
            type: 'TASK_EVENT',
            action: 'OP_ACTIVITY_SEND_GIFT_BUTTON',
            params: {
                btn_type: btnMainText.value,
                play_type: 2,
            },
            status: 'SUCCESS',
        });
    };
    // 去号召
    const onCallUpon = async () => {
        if (sendGifting.value) return;
        sendGifting.value = true;
        try {
            const res = await postAuthorCall();

            if (res) {
                sendTask('CLICK', {
                    type: 'TASK_EVENT',
                    action: 'OP_ACTIVITY_ONE_BUTTON',
                    params: {
                        btn_type: '号召助力',
                        play_type: 2,
                    },
                    status: 'SUCCESS',
                });
                showToast('已在直播间发出助力号召');
            } else {
                showToast('系统繁忙，请稍后重试');
            }
            sendGifting.value = false;
        } catch (error) {
            sendGifting.value = false;
            Report.biz.error('主会场召集失败', {
                error,
            });
            showToast('系统繁忙，请稍后重试');
        }
    };
    const btnMainText = computed(() => {
        if (status.value === StatusType.InAddition) {
            return bolIsAuthor ? '去号召' : '立刻送礼';
        }
        if (status.value === StatusType.NotStarted) {
            return '即将开始';
        }
        if (
            status.value === StatusType.Ended ||
            status.value === StatusType.InAdditionEnd
        ) {
            return '已结束';
        }
        return bolIsAuthor ? '去号召' : '去助力';
    });
    const buttonLogParams = computed(() => {
        let actionLogName = bolIsAuthor
            ? 'OP_ACTIVITY_ONE_BUTTON'
            : 'OP_ACTIVITY_SEND_GIFT_BUTTON';
        if (options.isHome) {
            actionLogName = 'OP_ACTIVITY_PK_SEND_GIFT_BUTTON';
        }
        return {
            action: actionLogName,
            params: {
                btn_type: btnMainText.value,
                gift_id: cohesionInfo.value?.sendGiftId,
                ...logCommonParams,
            },
        };
    });
    const onSendBtnClick = () => {
        if (isOutLiveRoom) {
            Toast.info(
                commonKconf.value?.outLiveRoomDesc || '请进入直播间赠送礼物',
            );
            return;
        }
        if (bolIsAuthor) {
            onCallUpon();
        } else if (status.value === StatusType.InAddition) {
            openGiftPanel();
        } else {
            openGiftPanel();
        }
    };
    const imgUrl = computed(() => {
        if (
            status.value === StatusType.InAddition ||
            status.value === StatusType.InAdditionEnd
        ) {
            return cohesionInfo.value.additionIcon;
        }
        return status.value === StatusType.Unlock
            ? unlockTask.value.viewPageImg
            : currentTask.value.viewPageImg;
    });
    return {
        cardKconf,
        kconfData,
        pageKconf,
        currentTask,
        nextTask,
        isLastTask,
        status,
        nodes,
        onAssist,
        onCallUpon,
        useCountdownText,
        logCommonParams,
        videoUrl,
        getMNowTime,
        openGiftPanel,
        lastTask,
        buttonLogParams,
        unlockTask,
        maxImg,
        onSendBtnClick,
        imgUrl,
    };
}
