// import useKconf from '@pet/ones-use.useKconf';
import { Toast } from '@lux/sharp-ui-next';
import {
    query,
    appendParam,
    bolIsAuthor,
    loadUrlOnNewPage,
    isYodaPCContainer,
    Report,
    compareAppVersion,
    getDeviceInfo,
} from '@alive-ui/actions';

//  其他场景在这里添加枚举
export enum SceneType {
    Memory = 'memory',
}

//  这里配置通用的跳转参数
export interface CommonLinkData {
    link: string;
}

//  回忆录配置
export interface LinkMemoryQueryData {
    msrc: string;
    entry_src: string;
}

//  可以定义某些场景必须的传参，用 ts 做参数校验
export type LinkData = CommonLinkData & { scene: SceneType.Memory } & {
    queryData: LinkMemoryQueryData;
};

/**
 * 配置 link 时，注意需要添加 bizId
 * @param linkData
 */
const baseVersion = '10.7.20'; //  先写固定，后面有时间再抽配置吧
export async function linkOtherBizPage(linkData: LinkData, isStopLive = false) {
    try {
        const appInfo = !isYodaPCContainer ? await getDeviceInfo() : {};
        const curVersion = appInfo?.data?.appVersion ?? '';

        if (
            (curVersion && compareAppVersion(curVersion, baseVersion) >= 0) ||
            isYodaPCContainer
        ) {
            if (linkData.link) {
                let params = {} as Record<string, any>;
                const curQuery = { ...query };
                //  删除当前 biz
                curQuery.bizId && delete curQuery.bizId;

                //  跳转回忆录
                if (linkData.scene === SceneType.Memory) {
                    // eslint-disable-next-line max-depth
                    if (bolIsAuthor || isYodaPCContainer) {
                        //  主播端 toast
                        Toast.info(
                            '为了避免直播卡顿，请用非开播手机开启年度回忆哦～',
                        );
                        return;
                    }

                    curQuery.entry_src && delete curQuery.entry_src;
                    const { queryData } = linkData;
                    params = {
                        ...curQuery,
                        msrc: queryData.msrc,
                        entry_src: queryData.entry_src,
                        tmpEntrySrc: query.entry_src,
                        tmpBizId: query.bizId,
                        layoutType: 4,
                    };
                }
                const jumpUrl = appendParam(linkData.link, params);
                await loadUrlOnNewPage({
                    url: jumpUrl,
                    type: 'close',
                });

                // if (isStopLive) {
                //     // eslint-disable-next-line max-depth
                //     try {
                //         await invoke('live.stopLivePlay');
                //     } catch (error) {
                //         Report.biz.error('【停流异常】live.stopLivePlay', {
                //             error,
                //         });
                //     }
                // }
            }
        } else {
            Toast.info('请升级快手APP后开启年度回忆哦~');
        }
    } catch (error) {
        Report.biz.error('【获取设备信息异常】webview.getDeviceInfo', {
            error,
        });
    }
}
