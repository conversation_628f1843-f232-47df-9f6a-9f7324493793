/* eslint-disable @typescript-eslint/ban-types */
import { invoke } from '@yoda/bridge';
import { Toast } from '@lux/sharp-ui-next';
import { isYodaPCContainer, Report } from '@alive-ui/actions';

//  分享模式，后续可能会有 TK 等自定义模版，在这里添加
export enum ShareMode {
    StaticMode = 'staticMode', // 头像、大图、长图和短图模式
}

export interface shareParams {
    subBiz: string;
    shareObjectId: string;
    placeholder: Record<string, unknown>;
}

export enum Rescode {
    Suc = 1,
    Err = 0,
    ForBidden = -1,
}

export async function useShare(
    mode: ShareMode,
    shareParams: shareParams,
    toastConfig = {
        pc: '去手机端分享给更多朋友吧~',
        error: '分享失败，请稍后重试~',
    },
) {
    try {
        if (isYodaPCContainer) {
            Toast.info(toastConfig.pc);
            return Rescode.ForBidden;
        }
        if (mode === ShareMode.StaticMode && shareParams.subBiz) {
            await invoke('social.share', {
                param: {
                    ...shareParams,
                    showSharePanel: true,
                },
            });
            return Rescode.Suc;
        }
    } catch (error: any) {
        if (error?.code === 0 || error.code === -1) {
            //  用户主动取消分享或者未授权相册权限
            Report.biz.warning('【分享取消】social.share', {
                error,
                shareParams,
            });
        } else {
            toastConfig.error && Toast.info(toastConfig.error);
            Report.biz.error('【分享失败】social.share', {
                error,
                shareParams,
            });
        }
        return Rescode.Err;
    }
}
