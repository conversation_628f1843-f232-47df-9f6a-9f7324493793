# IconWidget 组件文档

## 功能概述

`IconWidget` 是一个用于显示不同类型的图标小部件的组件，支持多种类型（如刷新、返回等），并可以自定义位置、样式等属性。此组件适用于页面中的导航和操作按钮。

## 属性和方法

### Props

| 名称              | 类型                 | 默认值       | 描述                                                                                      |
|-------------------|----------------------|--------------|-------------------------------------------------------------------------------------------|
| `type`            | `'refresh' \\| 'rule' \\| 'back' \\| 'wallet' \\| 'record'` | `'rule'`     | 图标类型，可选值有 `refresh`（刷新）、`rule`（规则）、`back`（返回）、`wallet`（钱包）、`record`（记录）。 |
| `text`            | `string`             | `''`         | 自定义文本，当 `type` 不为 `refresh` 或 `back` 时有效。                                   |
| `refreshing`      | `boolean \\| null`    | `null`       | 刷新状态，用于控制刷新图标是否旋转。                                                      |
| `refreshTime`     | `number \\| null`     | `null`       | 刷新间隔时间，单位为毫秒。                                                                |
| `position`        | `'left-top' \\| 'right-top' \\| 'right-middle' \\| 'right-bottom'` | `undefined` | 图标的位置，可选值有 `left-top`、`right-top`、`right-middle`、`right-bottom`。           |
| `isFixed`         | `boolean \\| null`    | `false`      | 是否固定定位。                                                                            |
| `isRenderToBody`  | `boolean \\| null`    | `true`       | 是否将图标渲染到 `body` 中。                                                              |
| `outLivingRoom`   | `boolean \\| null`    | `+query.layoutType !== 3` | 是否在直播间外。                                                                          |
| `textStyle`       | `StyleObject \\| null`| `null`       | 文本样式对象。                                                                            |
| `showThrottleToast` | `boolean \\| null`   | `false`      | 是否显示频率限制提示。                                                                    |
| `changeTop`       | `boolean`            | `true`       | 是否根据状态栏高度调整顶部距离。                                                          |

### Events

| 名称  | 参数        | 描述                       |
|-------|-------------|----------------------------|
| `click` | `params?: string` | 点击图标时触发，可传递参数。 |

## 使用示例

### 基本用法

```vue
<template>
  <IconWidget type=\"refresh\" @click=\"handleRefresh\" />
</template>

<script setup>
import { IconWidget } from '@pet/ones-ui';

const handleRefresh = () => {
  console.log('刷新');
};
</script>
```

### 自定义文本

```vue
<template>
  <IconWidget type=\"rule\" text=\"查看规则\" @click=\"handleRuleClick\" />
</template>

<script setup>
import { IconWidget } from '@pet/ones-ui';

const handleRuleClick = () => {
  console.log('查看规则');
};
</script>
```

### 固定定位

```vue
<template>
  <IconWidget type=\"back\" position=\"left-top\" isFixed @click=\"handleBackClick\" />
</template>

<script setup>
import { IconWidget } from '@pet/ones-ui';

const handleBackClick = () => {
  console.log('返回');
};
</script>
```

## 注意事项

- `refreshTime` 和 `refreshing` 只在 `type` 为 `refresh` 时生效。
- `showThrottleToast` 为 `true` 时，如果用户点击频率过快，会显示提示信息。
- `isRenderToBody` 为 `true` 时，图标会被渲染到 `body` 中，适合用于固定定位的场景。

## 依赖项

- `vue`
- `@lux/sharp-ui`
- `@alive-ui/system`
- `@alive-ui/actions`