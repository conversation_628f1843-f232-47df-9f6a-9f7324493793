<script lang="ts" setup>
import { ref, computed, onUnmounted } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { useHeaderStyle } from '@alive-ui/system';
import { query, isOutLiveRoom } from '@alive-ui/actions';
import RefreshIcon from './refresh.vue';
import BackIcon from './back.vue';

type StyleObject = {
    [key: string]: string | number;
};

export interface Props {
    type?: 'refresh' | 'rule' | 'back' | 'wallet' | 'record';
    text?: string;
    refreshing?: boolean | null;
    refreshTime?: number | null;
    position?: 'left-top' | 'right-top' | 'right-middle' | 'right-bottom';
    isFixed?: boolean | null;
    isRenderToBody?: boolean | null;
    outLivingRoom?: boolean | null;
    textStyle?: StyleObject | null;
    // 是否展示限频提示
    showThrottleToast?: boolean | null;
    changeTop: boolean;
}

const textMap = {
    rule: '规则',
    wallet: '钱包',
    record: '记录',
    refresh: '',
    back: '',
};

const emit = defineEmits<{
    (e: 'click', params?: string): void;
}>();

const props = withDefaults(defineProps<Props>(), {
    type: 'rule',
    text: '',
    refreshTime: null,
    refreshing: null,
    position: undefined,
    isFixed: false,
    isRenderToBody: true,
    outLivingRoom: +query.layoutType !== 3, // 此刻是间外
    textStyle: null,
    showThrottleToast: false,
    changeTop: true,
});

const isRefreshing = ref(false);

let refreshTimer = 0 as any;
const renderText = computed(() =>
    props.type && textMap[props.type] ? textMap[props.type] : props.text,
);

const handleClick = () => {
    if (
        props.type === 'refresh' &&
        props.refreshTime &&
        props.refreshTime > 0
    ) {
        if (isRefreshing.value) {
            props.showThrottleToast && Toast.info('操作过快，请5s后再尝试哦～');

            return;
        }

        isRefreshing.value = true;
        emit('click');

        refreshTimer = setTimeout(() => {
            isRefreshing.value = false;
        }, props.refreshTime);

        return;
    }

    emit('click');
};

onUnmounted(() => {
    refreshTimer && clearTimeout(refreshTimer);
    refreshTimer = 0;
});

const refreshAnimation = computed(() => props.refreshing ?? isRefreshing.value);

const positionClass = computed(() => {
    return props.position
        ? [props.isFixed ? 'fixed' : 'absolute', ...props.position.split('-')]
        : [];
});

const { statusH } = useHeaderStyle();
</script>

<template>
    <div>
        <Teleport v-if="props.isRenderToBody" to="body">
            <div
                :class="['icon-widget', ...positionClass]"
                v-bind="$attrs"
                :style="{
                    marginTop:
                        outLivingRoom && props.position && props.changeTop
                            ? (Math.min(statusH, 0.6) || 0) + 'rem'
                            : 0,
                }"
                @click="handleClick"
            >
                <div v-if="type === 'back'" class="back-icon">
                    <BackIcon />
                </div>
                <div
                    v-else-if="type === 'refresh'"
                    :class="{
                        'refresh-icon': true,
                        animation: refreshAnimation,
                    }"
                >
                    <RefreshIcon />
                </div>
                <div
                    v-else
                    :class="{
                        'render-text': true,
                        'four-word': renderText.length === 4,
                    }"
                    :style="textStyle ? textStyle : {}"
                >
                    {{ renderText }}
                    <slot />
                </div>
            </div>
        </Teleport>
        <div
            v-else
            :class="['icon-widget', ...positionClass]"
            v-bind="$attrs"
            :style="{
                marginTop:
                    outLivingRoom && props.position && props.changeTop
                        ? (Math.min(statusH, 0.6) || 0) + 'rem'
                        : 0,
            }"
            @click="handleClick"
        >
            <div v-if="type === 'back'" class="back-icon">
                <BackIcon />
            </div>
            <div
                v-else-if="type === 'refresh'"
                :class="{ 'refresh-icon': true, animation: refreshAnimation }"
            >
                <RefreshIcon />
            </div>
            <div
                v-else
                :class="{
                    'render-text': true,
                    'four-word': renderText.length === 4,
                }"
                :style="textStyle ? textStyle : {}"
            >
                {{ renderText }}
                <slot />
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
@widget-color: var(--icon-widget-color, #fff);
@widget-background-color: var(
    --icon-widget-background-color,
    rgba(0, 0, 0, 0.2)
);
@widget-size: 32px;

svg {
    display: block;
}

.icon-widget {
    display: inline-flex;
    width: @widget-size;
    height: @widget-size;
    font-weight: 700;
    color: @widget-color;
    vertical-align: middle;
    cursor: pointer;
    background-color: @widget-background-color;
    border: 1px solid rgba(255, 255, 255, 15.9%);
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;

    .render-text {
        font-size: 12px;
        // 如果是2个字，就是10px
        transform: scale(0.83);
    }

    .four-word {
        text-align: center;
        // 如果是4个字，就是9px
        transform: scale(0.75);
    }

    &-icon {
        width: 100%;
        height: 100%;
    }

    &.absolute {
        position: absolute;
        z-index: 10;

        &.left {
            left: var(--HorizontalPadding, 12px);
        }

        &.right {
            right: var(--HorizontalPadding, 12px);
        }

        &.middle {
            top: 50%;
        }

        &.bottom {
            bottom: var(--BottomPadding, 120px);
        }

        &.top {
            top: var(--VerticalPadding, 18px);
            // &.out-living-room {
            //   margin-top: 30px;
            // }
        }
    }
    &.fixed {
        position: fixed;
        z-index: 10;

        &.left {
            left: var(--HorizontalPadding, 12px);
        }

        &.right {
            right: var(--HorizontalPadding, 12px);
        }

        &.middle {
            top: 50%;
        }
        &.bottom {
            bottom: var(--BottomPadding, 120px);
        }

        &.top {
            top: var(--VerticalPadding, 18px);
            // &.out-living-room {
            //   margin-top: 30px;
            // }
        }
    }
    .refresh-icon.animation {
        animation: spin 600ms linear;
    }

    @keyframes spin {
        0% {
            transform: rotateZ(0deg);
        }

        100% {
            transform: rotateZ(-720deg);
        }
    }
}
</style>
