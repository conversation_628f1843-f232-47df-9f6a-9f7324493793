<template>
    <PageAdaptor class="ceremony-star-page">
        <template v-if="status.success || inSSG()">
            <div class="ceremony-star-content a-bg-page">
                <Top></Top>
                <div class="content-wrap">
                    <!-- 涨粉组件 -->
                    <Fans></Fans>
                    <ClientOnly>
                        <template v-if="canPreShowIncrFansTask">
                            <!-- 限时任务 -->
                            <LimitedTimeTask
                                ref="itemRefs"
                                @end="handleEnd"
                            ></LimitedTimeTask>
                            <!-- 任务组件 -->
                            <StarTask />
                        </template>
                        <template v-else>
                            <!-- 任务组件 -->
                            <StarTask />
                            <!-- 限时任务 -->
                            <LimitedTimeTask
                                ref="itemRefs"
                                @end="handleEnd"
                            ></LimitedTimeTask>
                        </template>
                    </ClientOnly>
                </div>
            </div>
            <ClientOnly>
                <LeftButton @handle-task-btn="handleTaskBtn"></LeftButton>
            </ClientOnly>
        </template>
        <AsyncErrorPage v-else />
    </PageAdaptor>
</template>

<script lang="ts" setup>
import { defineAsyncComponent, nextTick, ref, watch, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import useDefendTaskStore from '@pet/ones-plays.star/stores/limited-task';
import { ClientOnly, inSSG } from '@live/ssg';
import { sendFmp } from '@gundam/weblogger';
import { PageAdaptor } from '@alive-ui/base';
import { Report } from '@alive-ui/actions';
import useStarStore from './stores/star';
import { Top, Fans } from './components';

const AsyncErrorPage = defineAsyncComponent(
    () => import('./components/error-page/error-page.vue'),
);

const StarTask = defineAsyncComponent(
    () => import('./components/task/index.vue'),
);

const LimitedTimeTask = defineAsyncComponent(
    () => import('./components/limited-time-task/index.vue'),
);

const LeftButton = defineAsyncComponent(
    () => import('./components/left-button/index.vue'),
);

const indexStore = useStarStore();
const defendTaskStore = useDefendTaskStore();

const { status, canPreShowIncrFansTask, incrFansTask } =
    storeToRefs(indexStore);
const { needLodeTask } = storeToRefs(defendTaskStore);

if (inSSG()) {
    indexStore.initSSGData();
} else {
    indexStore.initData().finally(() => {
        try {
            nextTick(() => {
                sendFmp();
            });
        } catch {
            console.log('sendFmp 出错');
        }
    });
}

defendTaskStore.initDefendTask();

const handleEnd = () => {
    // 后端确认2-3s打散
    const randomNumber = (Math.random() + 2) * 1000;

    setTimeout(() => {
        defendTaskStore.initDefendTask();
    }, randomNumber);
};

const timer = ref<any>(null);
watch(
    () => needLodeTask.value,
    (val) => {
        if (val) {
            Report.biz.error('限时任务接口返回数据未更新', {});

            clearTimeout(timer.value);

            timer.value = setTimeout(() => {
                defendTaskStore.initDefendTask();
            }, 1000);
        }
    },
    {
        immediate: true,
        deep: true,
    },
);

const itemRefs = ref<any>(null);

const handleTaskBtn = () => {
    // 滚动到目标元素的位置
    itemRefs.value?.rootElement?.scrollIntoView({
        block: 'center',
        behavior: 'smooth',
    });
};

// 注销
onMounted(() => {
    timer.value && clearTimeout(timer.value);
});
</script>

<style scoped lang="less">
.ceremony-star-page {
    position: relative;
    overflow-x: hidden;
    .ceremony-star-content {
        position: absolute;
        width: 100%;
        background-color: #070722;
        // top: -12px;
    }

    .content-wrap {
        position: relative;
        margin-top: -114px;
        margin-bottom: 88px;
        z-index: 9;
    }

    :deep(.card-bg-img) {
        background-color: #0c0d31;
    }

    :deep(.card-m) {
        margin: 16px auto;
    }

    :deep(.card-py) {
        padding-top: 14px;
        padding-bottom: 16px;
    }
}
</style>
