import { activityBiz, authorId, request } from '@alive-ui/actions';
import type {
    RepairResponseRoot,
    RequestBody,
    ResponseRoot,
} from '../schemas/home';

const biz = activityBiz;

const PATH = {
    home: '/rest/wd/live/plutus/attendance/home',
    repair: '/rest/wd/live/plutus/attendance/repairLatestDay',
};

export const home = async () => {
    const params: RequestBody = {
        authorId,
        biz,
    };
    const res = await request.post<ResponseRoot>(PATH.home, params);
    return res?.data;
};

export const repair = async () => {
    const res = await request.post<RepairResponseRoot>(PATH.repair, {
        biz,
    });
    return res?.data;
};
