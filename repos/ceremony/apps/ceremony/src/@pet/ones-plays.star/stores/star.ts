/* eslint-disable max-lines-per-function */
import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { Toast } from '@lux/sharp-ui-next';
import { getPageStatus, exitWebView, Report } from '@alive-ui/actions';
import { home, repair } from '../services/star';
import {
    type IncrFansTask,
    type AttendanceTask,
    type DateInfo,
    IncrFansTaskStatus,
    RepairStatus,
} from '../schemas/home';
import awaitWrap from '@/utils/awaitWrap';
import { RESULT_CODE_ENUM } from '@/common/http/consts';

export default defineStore('star', () => {
    const status = ref(getPageStatus('init'));
    const serverTime = ref(0);
    const isForbidden = ref(false);
    /** 涨粉任务 */
    const incrFansTask = ref<IncrFansTask | null>();
    /** 打卡任务 */
    const attendanceTask = ref<AttendanceTask | null>();
    /** 日历 */
    const dateInfo = ref<DateInfo | null>();

    const kconfStore = useKconfStore();

    const pageStatus = computed(() => {
        if (kconfStore.pageStatus.success) {
            return status.value;
        }

        return kconfStore.pageStatus;
    });

    // 限时任务是否可前置展示
    const canPreShowIncrFansTask = computed(() => {
        // 今日已经打卡，主播涨粉挑战未开启，且不能补签
        return (
            incrFansTask.value?.status === IncrFansTaskStatus.NOT_STARTED &&
            attendanceTask.value?.checkInTaskInfo?.complete &&
            dateInfo.value?.repairInfo?.status === RepairStatus.CANNOT_REPAIR
        );
    });

    // 静态渲染
    function initSSGData() {
        serverTime.value = window.ssgInjectData.serverTimestamp || +new Date();
        isForbidden.value = false;
        incrFansTask.value = window.ssgInjectData.star.incrFansTask;
        attendanceTask.value = window.ssgInjectData.star.attendanceTask;
        dateInfo.value = window.ssgInjectData.star.dateInfo;
        status.value = getPageStatus('success');
    }

    async function toRepair() {
        Toast.loading();
        try {
            const [err, res] = await awaitWrap(repair());
            if (!res || err || res.status === 0) {
                Toast.info('网络异常，请稍后重试');
                Report.biz.error('季度之星开启补签失败', {
                    error: {
                        message: err?.message,
                        stack: err?.stack,
                    },
                });
                return;
            }
            Toast.info('成功开启补签，快去做任务打卡吧');
        } catch (error: any) {
            Toast.info('网络异常，请稍后重试');
            Report.biz.error('季度之星补签失败', {
                error: {
                    message: error?.message,
                    stack: error?.stack,
                },
            });
            return;
        } finally {
            initData();
        }
    }

    async function initData() {
        status.value = getPageStatus('loading');
        const [err, res] = await awaitWrap(home());

        if (err) {
            const errorCode = err?.data?.result;
            // result 非 1 会通过拦截器进 error
            if (errorCode === RESULT_CODE_ENUM.NOT_LIVING) {
                // 关播
                exitWebView();
                return;
            }
            if (errorCode === RESULT_CODE_ENUM.IN_BLACK_LIST) {
                // 拉黑
                isForbidden.value = true;
                status.value = getPageStatus('nodata');
            } else {
                status.value = getPageStatus('error');
            }

            return;
        }

        if (!res) {
            status.value = getPageStatus('error');
            return;
        }

        try {
            serverTime.value = res.serverTime || +new Date();
            isForbidden.value = false;
            incrFansTask.value = res.incrFansTask;
            attendanceTask.value = res.attendanceTask;
            dateInfo.value = res.attendanceTask?.dateInfo;

            status.value = getPageStatus('success');
        } catch (error: any) {
            status.value = getPageStatus('error');
            Report.biz.error('季度之星初始化失败', {
                error: {
                    message: error?.message,
                    stack: error?.stack,
                },
            });
        }
    }

    return {
        initData,
        initSSGData,
        status: pageStatus,
        serverTime,
        isForbidden,
        incrFansTask,
        attendanceTask,
        dateInfo,
        toRepair,
        canPreShowIncrFansTask,
    };
});
