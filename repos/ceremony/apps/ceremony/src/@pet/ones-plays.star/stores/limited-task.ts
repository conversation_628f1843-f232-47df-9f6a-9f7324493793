/* eslint-disable max-lines-per-function */
import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { Report } from '@alive-ui/actions';
import { getDefendTask } from '../services/limited-task';
import { TaskStatusEnum } from '../schemas/limited-task';

export default defineStore('star-defend-task', () => {
    const levelInfoList = ref<any>([]);
    const currentLevel = ref<number>(0);

    const currentLevelList = computed(() => {
        if (!levelInfoList.value?.length) {
            return [];
        }

        return levelInfoList.value.find((item: any) => {
            return item.level === currentLevel.value;
        });
    });

    const needLodeTask = computed(() => {
        if (currentLevelList.value?.status === TaskStatusEnum.ING) {
            return (
                currentLevelList.value?.serviceTimestamp >
                currentLevelList.value?.taskEndTimestamp
            );
        }
        return (
            currentLevelList.value?.serviceTimestamp >
            currentLevelList.value?.nextTaskStartTimestamp
        );
    });

    async function initDefendTask() {
        try {
            const res: any = await getDefendTask();
            levelInfoList.value = res.levelInfo;
            currentLevel.value = res.currentLevel;
        } catch (error) {
            Report.biz.error('限时任务初始化失败', {
                error,
            });
        }
    }

    return {
        initDefendTask,
        levelInfoList,
        currentLevel,
        currentLevelList,
        needLodeTask,
    };
});
