<script lang="ts" setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { ElseStatus, Card as ACard, PageHeader } from '@alive-ui/base';
import useStarStore from '../../stores/star';

const kconfStore = useKconfStore();
const indexStore = useStarStore();
const { status, isForbidden } = storeToRefs(indexStore);
const { Card, Content } = ACard;

const errorConfig = computed(() => {
    return kconfStore?.kconfData?.star?.error || {};
});

const errorText = computed(() => {
    return errorConfig.value.errorText || '网络异常请重试';
});

const forbiddenText = computed(() => {
    return errorConfig.value.forbiddenText || '暂未参与活动';
});

const emptyText = computed(() => {
    return errorConfig.value.emptyText || '活动数据加载中';
});

const kvImgStyle = computed(() => {
    if (!errorConfig.value.kv) {
        return '';
    }
    return `background: url("${errorConfig.value.kv}") center / 100% no-repeat`;
});

const statusText = computed(() => {
    if (isForbidden.value) {
        return forbiddenText.value;
    }
    return status.value.nodata ? emptyText.value : errorText.value;
});
</script>

<template>
    <div class="relative star-error-page">
        <PageHeader type="auto">
            <div class="star-error-kv-img" :style="kvImgStyle"></div>
        </PageHeader>
        <Card class="star-error-card">
            <Content>
                <ElseStatus
                    class="error-page-status"
                    :page-status="status"
                    :is-show-refresh="!status.nodata"
                    @refresh="indexStore.initData"
                >
                    <template v-if="!status.loading" #text>
                        {{ statusText }}
                    </template>
                </ElseStatus>
            </Content>
        </Card>
    </div>
</template>

<style scoped lang="less">
.star-error-page {
    .star-error-kv-img {
        width: 414px;
        height: 480px;
    }

    .star-error-card {
        margin-top: -100px;
        height: calc(100vh + 30px + 12px - 88px - 380px);
        min-height: 284px;
        margin-bottom: 0;
    }
    .error-page-status {
        margin-top: -11px;
    }
}
</style>
