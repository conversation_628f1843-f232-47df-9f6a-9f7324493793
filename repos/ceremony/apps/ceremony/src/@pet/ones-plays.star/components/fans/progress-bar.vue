<template>
    <div class="fans-progress-bar-wrapper">
        <!-- 全部解锁 -->
        <div
            v-if="status === IncrFansTaskStatus.EVENT_LIMIT_REACHED"
            class="progress-bar all-unlock"
        ></div>
        <div v-else class="progress-bar-not-all-lock-wrapper">
            <div
                class="progress-bar"
                :style="{ width: `${progressWidth}%` }"
            ></div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { SegmentStatus, IncrFansTaskStatus } from '../../schemas/home';
import type { PropType } from 'vue';
import type { Segment } from '../../schemas/home';

const props = defineProps({
    list: {
        type: Array as PropType<Segment[]>,
        default: () => [],
    },
    currentLevel: {
        type: Number,
        default: 0,
    },
    curScore: {
        type: Number,
        default: 0,
    },
    totalScore: {
        type: Number,
        default: 0,
    },
    status: {
        type: Number,
        default: 0, // 4 全部解锁
    },
});

const progressWidth = computed(() => {
    if (!props.list.length) {
        return 0;
    }

    // 每个档位占的进度
    const segmentTotalProgress = 100 / props.list.length;

    // 找到已解锁的档位
    const lockList: Segment[] = props.list?.filter((item: Segment) => {
        return item?.status === SegmentStatus.UNLOCK;
    });

    // 找到当前档位数据
    const currentSegmentData: any = props.list?.find((item: Segment) => {
        return item?.level === props.currentLevel;
    });

    let currentSegmentProgress = 0; // 当前档位占总进度的比例
    let currentSegmentRange = 0; // 当前档位进度条的长度
    let currentSegmentAverageRatio = 0; // 当前档位进度条每个分值的占比
    let prevSegmentTargetAcore = 0; // 前一个档位的目标分数
    let realNember = 0; // 每个档位进度条的实际应该占的长度（因为圆点占了宽度，所以在每个档位超过22.5的时候会和圆点重叠）

    if (currentSegmentData.level === 1) {
        currentSegmentRange =
            currentSegmentData.targetScore - prevSegmentTargetAcore;
    } else {
        prevSegmentTargetAcore = props.list?.find((item: Segment) => {
            return item?.level === props.currentLevel - 1;
        })!.targetScore;

        currentSegmentRange =
            currentSegmentData.targetScore - prevSegmentTargetAcore;
    }

    currentSegmentAverageRatio = segmentTotalProgress / currentSegmentRange;
    currentSegmentProgress =
        (props.curScore - prevSegmentTargetAcore) * currentSegmentAverageRatio;

    const totalProgress = lockList.length * segmentTotalProgress;

    const totalNumber = totalProgress + currentSegmentProgress;

    realNember = segmentTotalProgress - 2.5;

    if (totalNumber === 0) {
        return 0;
    }
    if (
        totalNumber !== segmentTotalProgress * props.currentLevel &&
        totalNumber >
            segmentTotalProgress * (props.currentLevel - 1) + realNember
    ) {
        return segmentTotalProgress * (props.currentLevel - 1) + realNember;
    }
    return Math.max(totalNumber, 3);
});
</script>

<style lang="less" scoped>
// 进度条
.fans-progress-bar-wrapper {
    position: absolute;
    top: 22px;
    z-index: 1;
    width: 296px;
    height: 6px;
    border-radius: 16px;
    background: rgba(154, 189, 255, 0.078);

    .progress-bar {
        position: absolute;
        left: 0;
        top: 0;
        height: 6px;
        width: 40px;
        border-radius: 16px;
        background: linear-gradient(
            91deg,
            #e89888 -20.55%,
            #fbc9a1 32.96%,
            #ffdfbf 97.86%
        );
    }

    .all-unlock {
        width: 100%;
    }

    .progress-bar-not-all-lock-wrapper {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        width: 272px;
        height: 6px;
        border-radius: 16px;
    }
}
</style>
