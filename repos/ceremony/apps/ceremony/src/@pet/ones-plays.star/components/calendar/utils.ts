import type { DateTaskInfo } from '../../schemas/home';

export function generateCalendarData(
    dateTaskInfos?: DateTaskInfo[],
): Partial<CalendarDay>[] {
    if (!dateTaskInfos?.length) {
        return [];
    }
    const days = dateTaskInfos.map((item) => {
        const { desc, date, status } = item;
        const dateObj = new Date(`${date}T00:00:00`);
        const day = dateObj.getDate(); // 一个月中某一天
        const month = dateObj.getMonth();
        const year = dateObj.getFullYear();
        const nowDate = new Date();
        const nowDay = nowDate.getDate();
        const nowMonth = nowDate.getMonth();
        const nowYear = nowDate.getFullYear();

        return {
            value: dateObj.valueOf(),
            day,
            desc,
            status,
            date,
            isToday: month === nowMonth && day === nowDay && year === nowYear,
            dayOfWeek: dateObj.getDay(), // 一周中某一天
        } as unknown as Partial<CalendarDay>;
    });

    const firstDay = days[0];

    if (firstDay) {
        const { dayOfWeek = 0, value = new Date() } = firstDay;
        const emptyDayLenth = firstDay ? dayOfWeek % 7 : 0;

        for (let i = 1; i <= emptyDayLenth; i++) {
            const emptyDay = new Date(
                value.valueOf() - 60 * 60 * 24 * 1000 * i,
            );

            days.unshift({
                value: emptyDay.valueOf(),
                isEmptyDay: true,
                day: emptyDay.getDate(),
            });
        }
    }

    return days;
}

export interface CalendarDay {
    date?: string;
    value?: number;
    day?: number;
    isToday?: boolean;
    dayOfWeek?: number;
    isEmptyDay?: boolean;
    desc?: string;
    status?: number;
}
