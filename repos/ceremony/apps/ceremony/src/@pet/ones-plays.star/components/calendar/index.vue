<template>
    <div v-if="dateInfo" class="star-task-calendar a-text-main">
        <div class="card-task-top">
            <div class="title">
                <div class="title-content">打卡日历</div>
            </div>
            <div
                v-if="
                    rightTxt &&
                    (rightTxt.before || rightTxt.param || rightTxt.after)
                "
                class="right-txt"
            >
                <span>{{ rightTxt.before }}</span
                ><span v-if="rightTxt.param" class="a-text-highlight">{{
                    rightTxt.param
                }}</span
                ><span>{{ rightTxt.after }}</span>
                <div
                    v-if="showRepair"
                    v-show-log="starBtnLog('去补签')"
                    v-click-log="starBtnLog('去补签')"
                    class="re-sign"
                    @click="repairTask"
                >
                    补签
                </div>
                <div v-else class="wenhao" @click="showTips = !showTips"></div>
                <div v-if="showTips" class="tips-wrap">
                    {{ kconfData.star?.calendar.tips }}
                </div>
                <div v-if="showRepair" class="tips-wrap tips-wrap-repair">
                    {{ kconfData.star?.calendar.repair }}
                </div>
            </div>
        </div>
        <Calendar />
    </div>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { debounce } from 'lodash-es';
import useKconf from '@pet/ones-use.useKconfBatch';
import { ACard, ACardContent } from '@alive-ui/base';
import { bolIsAuthor } from '@alive-ui/actions';
import useStar from '../../stores/star';
import { RepairStatus } from '../../schemas/home';
import Calendar from './Calendar.vue';

const { dateInfo } = storeToRefs(useStar());
const { kconfData } = storeToRefs(useKconf());
const showTips = ref(false);

const showRepair = computed(() => {
    return (
        bolIsAuthor &&
        dateInfo.value?.repairInfo.status === RepairStatus.CAN_REPAIR
    );
});
const rightTxt = computed(() => {
    const repairInfo = dateInfo.value?.repairInfo;
    if (repairInfo?.desc.indexOf('${param}') === -1) {
        return { before: repairInfo.desc, param: '', after: '' };
    }
    if (repairInfo?.desc && typeof repairInfo.param !== 'undefined') {
        const splitArr = repairInfo.desc.split('${param}');
        return {
            before: splitArr[0] || '',
            param: repairInfo.param || '',
            after: splitArr.length > 1 ? splitArr[1] : '',
        };
    }
    return { before: '', param: '', after: '' };
});

// 防抖
const repairTask = debounce(() => {
    useStar().toRepair();
}, 300);

const starBtnLog = (btnType: string) => {
    return {
        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
        params: {
            btn_type: btnType,
        },
    };
};
</script>
<style lang="less" scoped>
.star-task-calendar {
    border-radius: 8px;
    margin: 12px 12px 0;
    padding: 12px;
    background: #9abdff0f;
    .card-task {
        padding: 0 12px;
    }
    .card-task-top {
        display: flex;
        justify-content: space-between;

        .title {
            display: flex;
        }

        .right-txt {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
        }

        .title-content {
            font-family: HYYakuHei;
            background: linear-gradient(
                78.49deg,
                #f6fdff 15.2%,
                #ffc4a3 87.91%
            );
            background-clip: text;
            color: transparent;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .card-task-content {
        display: flex;
        overflow-x: scroll;
        margin-top: 16px;

        .task-item {
            margin-right: 10px;
            flex-shrink: 0;
        }
    }

    .re-sign {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 4px;
        width: 42;
        height: 20;
        border-radius: 20px;
        color: #571818;
        font-weight: 600;
        padding: 3px 11px;
        font-size: 10px;
        background: linear-gradient(88.24deg, #ffe8be 1.42%, #fecfa4 100.84%);
    }

    .wenhao {
        width: 16px;
        height: 16px;
        margin-left: 4px;
        background: url('./imgs/wenhao.png') center / 100% no-repeat;
    }
    .tips-wrap {
        position: absolute;
        top: -8px;
        right: -14px;
        width: max-content;
        max-width: 257px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500px;
        transform: translateY(-100%);
        padding: 8px 16px;
        color: #fff;
        background: rgba(#000, 0.7);

        &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            right: 17px;
            width: 10px;
            height: 5px;
            background: url('../../assets/jiantou.png') center / 100% no-repeat;
        }
    }

    .tips-wrap-repair {
        &::after {
            right: 30px;
        }
    }
}
</style>
