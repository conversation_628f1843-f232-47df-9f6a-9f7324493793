<template>
    <div v-if="incrFansTask" class="fans-wrapper">
        <ACard>
            <ACardContent class="fans-card-content">
                <!-- tag标签 -->
                <div
                    :class="{
                        'fans-tag': true,
                        'a-text-main':
                            incrFansTask?.status !==
                            IncrFansTaskStatus.IN_PROGRESS,
                        'not-start':
                            incrFansTask?.status !==
                            IncrFansTaskStatus.IN_PROGRESS,
                        start:
                            incrFansTask?.status ===
                            IncrFansTaskStatus.IN_PROGRESS,
                    }"
                >
                    {{ incrFansTask?.statusDesc }}
                </div>
                <!-- 主标题 -->
                <div class="fans-title a-text-main">
                    <span class="a-text-title">{{ incrFansTask?.title }}</span>
                </div>
                <!-- 副标题 -->
                <div class="fans-sub-title">
                    <span class="sub-title1 a-text-main-o2">
                        {{ incrFansTask?.subTitle }}
                    </span>
                    <span
                        v-if="
                            incrFansTask?.status !==
                            IncrFansTaskStatus.EVENT_LIMIT_REACHED
                        "
                        class="sub-title2 a-text-main"
                    >
                        {{ incrFansTask?.subTitleScore }}
                    </span>
                </div>
                <!-- 涨粉进度 -->
                <div class="fans-progress-wrapper">
                    <img class="fans-icon" src="../../assets/fans-icon.png" />
                    <div
                        :class="{
                            'fans-progress': true,
                            'op-40':
                                incrFansTask?.status ===
                                IncrFansTaskStatus.NOT_STARTED,
                        }"
                    >
                        <!-- 进度条 -->
                        <ProgressBar
                            :current-level="incrFansTask?.currentLevel"
                            :list="incrFansTask?.segmentList"
                            :cur-score="incrFansTask?.curScore"
                            :total-score="incrFansTask?.totalScore"
                            :status="incrFansTask?.status"
                        />
                        <!-- 进度模块 -->
                        <div class="moduel-wrapper">
                            <ProgressModel
                                :current-level="incrFansTask?.currentLevel"
                                :list="incrFansTask?.segmentList"
                                :status="incrFansTask?.status"
                            />
                        </div>
                    </div>
                </div>
                <!-- 底部信息 -->
                <img
                    v-if="incrFansTask?.bottomIcon"
                    :src="incrFansTask?.bottomIcon"
                    class="fans-bottom-info-icon"
                />
            </ACardContent>
        </ACard>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { ACard, ACardContent } from '@alive-ui/base';
import useStarStore from '../../stores/star';
import { IncrFansTaskStatus } from '../../schemas/home';
import ProgressModel from './progress-model.vue';
import ProgressBar from './progress-bar.vue';

const indexStore = useStarStore();
const kconfStore = useKconfStore();

const { incrFansTask } = storeToRefs(indexStore);
const { kconfData } = storeToRefs(kconfStore);
</script>

<style lang="less" scoped>
.fans-wrapper {
    position: relative;
    width: 382px;
    margin: 0 auto;

    .fans-card-content {
        padding: 0 12px;
    }

    .fans-tag {
        position: absolute;
        top: -15px;
        right: 0;
        height: 23px;
        padding: 10px;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-weight: 400;
        border-radius: 0px 16px 0 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .not-start {
        // background: rgba(191, 219, 255, 0.1);
        background: #1f2447;
    }

    .start {
        font-weight: 500;
        color: #571818;
        background: linear-gradient(
                263deg,
                #ffe4cb 0%,
                #fff1db 0.01%,
                #fee1ba 100%
            ),
            linear-gradient(257deg, #44445a 0%, #242528 100%);
    }

    .fans-title {
        height: 20px;
        font-family: HYYakuHei;
        font-size: 16px;
        font-weight: 400;
        line-height: 20px;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .fans-sub-title {
        margin-top: 2px;
        font-family: 'PingFang SC';
        font-size: 12px;
        line-height: 18px;
        .sub-title1 {
            font-weight: 400;
        }

        .sub-title2 {
            margin-left: 2px;
            font-weight: 600;
        }
    }

    .fans-progress-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 12px 0;
        width: 358px;
        height: 54px;

        .fans-icon {
            width: 54px;
            height: 54px;
        }

        .fans-progress {
            width: 296px;
            height: 47px;
            position: relative;

            .moduel-wrapper {
                margin-left: 39px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
        }
    }

    .fans-bottom-info-icon {
        width: 358px;
        height: 32px;
        display: block;
    }
}
</style>
