<template>
    <div class="left-btn-wrap">
        <!-- 荣誉殿堂 -->
        <div
            v-show-log="starBtnLog('荣耀殿堂')"
            v-click-log="starBtnLog('荣耀殿堂')"
            class="icon-honor"
            @click="goHonorWall"
        ></div>
        <RuleIcon rule-key="summerStarTask" />
        <div
            v-if="kconfData.star?.isShowRankIcon"
            v-show-log="starBtnLog('公会打卡榜')"
            v-click-log="starBtnLog('公会打卡榜')"
            class="icon-gonghui"
            @click="goToGonghui"
        ></div>
        <LimitedTaskButton
            v-show-log="starBtnLog('流量红包')"
            v-click-log="starBtnLog('流量红包')"
            @click="handleTaskBtn"
        ></LimitedTaskButton>
    </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { defineAsyncComponent } from 'vue';
import { storeToRefs } from 'pinia';
import useKconf from '@pet/ones-use.useKconfBatch';
import { LimitedTaskButton } from '@pet/ones-plays.star/components';
import { goHonorWall } from '@/utils/tools';

const { kconfData } = storeToRefs(useKconf());

const RuleIcon = defineAsyncComponent(
    () => import('@pet/ones-ui.rule-icon/index.vue'),
);

const router = useRouter();
const goToGonghui = () => {
    router.push({
        name: 'guildRank',
    });
};

const starBtnLog = (btnType: string) => {
    return {
        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
        params: {
            btn_type: btnType,
        },
    };
};

const emits = defineEmits(['handleTaskBtn']);
const handleTaskBtn = () => {
    emits('handleTaskBtn');
};
</script>

<style lang="less" scoped>
.left-btn-wrap {
    position: absolute;
    left: 16px;
    top: 0;
    z-index: 3;
    margin-top: 46px;
    height: max-content;
    .icon-honor {
        width: 56px;
        height: 56px;
        background: url('../../assets/rongyu.png') center / 100% no-repeat;
    }
    .icon-gonghui {
        width: 56px;
        height: 56px;
        margin-top: 8px;
        background: url('../../assets/gonghuidaka.png') center / 100% no-repeat;
    }

    :deep(.rule-icon) {
        width: 56px;
        height: 56px;
        margin-top: 8px;
        margin-left: 0;
    }
}
</style>
