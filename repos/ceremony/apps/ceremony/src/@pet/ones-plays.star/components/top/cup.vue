<template>
    <div class="ceremony-star-award-cup">
        <div class="ceremony-star-award-cup-poster">
            <template v-if="!isLowDev && assets.video">
                <VideoPlay
                    object-fit="cover"
                    not-click
                    autoplay
                    :video-url="[
                        {
                            url: assets.video,
                        },
                    ]"
                >
                    <template #floatEles>
                        <div
                            class="ceremony-star-award-cup-poster-backup"
                            :style="{ backgroundImage: `url('${assets.img}')` }"
                        ></div>
                    </template>
                </VideoPlay>
            </template>
            <div
                v-else
                class="ceremony-star-award-cup-poster-backup"
                :style="{ backgroundImage: `url('${assets.img}')` }"
            ></div>
            <div class="cup-mask"></div>
            <ClientOnly>
                <img class="sub-title" :src="kconfData.star.cup.subTitle" />
                <div v-if="statisticsInfo.challengeSuccess" class="success">
                    <div class="txt-wrap">
                        <div class="txt">{{ kconfData.star?.award.cup }}</div>
                    </div>
                    <div
                        v-if="bolIsAuthor"
                        v-show-log="starBtnLog('填写地址')"
                        v-click-log="starBtnLog('填写地址')"
                        class="address"
                        @click="emit('address')"
                    ></div>
                </div>
                <div v-else class="star-num-wrap a-text-main">
                    已打卡<span class="a-text-highlight">{{
                        statisticsInfo.taskCompleteNum
                    }}</span
                    >/{{ statisticsInfo.taskTotalNum }}天
                </div>
            </ClientOnly>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import useKconf from '@pet/ones-use.useKconfBatch';
import VideoPlay from '@pet/ones-ui.video-play/index.vue';
import { ClientOnly } from '@live/ssg';
import {
    bolIsAuthor,
    countsCT,
    isICFO,
    isLowDevice,
    useLowPowerMode,
} from '@alive-ui/actions';
import type { StatisticsInfo } from '../../schemas/home';
defineProps<{
    assets: {
        video: string;
        img: string;
    };
    statisticsInfo: StatisticsInfo;
}>();

const emit = defineEmits(['address']);

const { isLowPowerModeStatus } = useLowPowerMode();
const { kconfData } = storeToRefs(useKconf());

const isLowDev = computed(() => {
    return (
        isLowPowerModeStatus.value ||
        isICFO() ||
        isLowDevice() ||
        countsCT() > 1 ||
        kconfData.value?.star?.videoDowngrade // 手动降级开关
    );
});

const starBtnLog = (btnType: string) => {
    return {
        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
        params: {
            btn_type: btnType,
        },
    };
};
</script>

<style scoped lang="less">
.ceremony-star-award-cup {
    position: absolute;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    color: #ffffff;
    font-size: 10px;
    transform: translateZ(0);
    &-poster {
        width: 100%;
        height: 100%;
        transform: translateZ(0);
        overflow: hidden;

        &-backup {
            width: 100%;
            height: 100%;
            background-repeat: no-repeat;
            background-size: cover;
            &.cup-poster-backup-top {
                position: absolute;
                top: 0;
                left: 0;
                z-index: -1;
            }
        }
    }
    &-name {
        position: absolute;
        top: 166px;
        left: 0;
        text-align: center;
        width: 100%;
        z-index: 2;
    }
    &-section {
        z-index: 2;
        position: absolute;
        bottom: 104px;
        left: 0;
        right: 0;
        margin: auto;
        width: 200px;
        height: 31px;
        border-radius: 100px;
        background: linear-gradient(257.5deg, #515164 0%, #242528 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        transform-origin: center bottom;
        color: rgba(255, 255, 255, 0.8);
        @apply text-bold;
        line-height: 1;
        .un-exchange-content {
            display: flex;
            align-items: center;
            color: #ffffff;
            .special-content {
                @apply a-text-main;
            }
        }
        .edit-address-action {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
        }
        .ceremony-star-award-star {
            display: inline-block;
            margin: 0 0 0 1px;
            width: 19px;
            height: 19px;
            background-repeat: no-repeat;
            background-size: cover;
        }
        .ceremony-star-award-star-yellow {
            background-image: url('../assets/yellow-star_2x.png');
        }
        .ceremony-star-award-star-purple {
            background-image: url('../assets/purple-star_2x.png');
        }
    }
    :deep(.video-play__video) {
        // 这里的特殊top偏移量是因为，兜底图与特效视频的尺寸不一致
        // 特效视频用了object-fit: cover，所以需要调整一下位置
        // 如果两个资源都是同一尺寸的话这里未来就不需要进行偏移了
        top: 28px;
    }

    .sub-title {
        position: absolute;
        top: 84px;
        left: 50%;
        height: 36px;
        transform: translateX(-50%);
    }
}
.cup-mask {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 213px;
    background: linear-gradient(
        180.99deg,
        rgba(7, 7, 34, 0) 35.38%,
        #070722 62.54%
    );
}

.star-num-wrap {
    position: absolute;
    bottom: 106px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    border-radius: 100px;
    padding: 6px 10px;
    background: #070722b2;
}

.success {
    position: absolute;
    bottom: 152px;
    left: 50%;
    transform: translateX(-50%);
    .txt-wrap {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 292px;
        height: 36px;
        text-align: center;
        background: url('../../assets/award_bg.png') center / 100% no-repeat;
    }

    .txt {
        font-family: MF YuanHei;
        font-size: 16px;
        line-height: 28px;
        background: linear-gradient(89.98deg, #ffffff 0.03%, #ffc4a3 95.69%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .address {
        position: absolute;
        top: 55px;
        left: 50%;
        transform: translate(-50%);
        width: 89px;
        height: 26px;
        background: url('../../assets/address.png') center / 100% no-repeat;
    }
}
</style>
