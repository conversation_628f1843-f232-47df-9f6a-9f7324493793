<template>
    <div
        v-if="
            attendanceTask?.checkInTaskInfo &&
            !attendanceTask.statisticsInfo.challengeSuccess
        "
        class="star-task a-text-main"
    >
        <div v-if="showTips" class="tips-wrap">
            {{ kconfData.star?.calendar?.taskCardTips }}
        </div>
        <ACard>
            <ACardContent class="card-task">
                <div class="card-task-top">
                    <div class="title">
                        <div class="title-content a-text-title mr-4px">
                            {{ attendanceTask?.checkInTaskInfo.title }}
                        </div>
                        (<span class="a-text-highlight">{{
                            attendanceTask.checkInTaskInfo.ruleFinishNum
                        }}</span
                        >/{{ attendanceTask.checkInTaskInfo.ruleTotalNum }})
                    </div>
                    <div class="title-right text-12px">
                        {{ kconfData.star?.calendar?.rightTxt }}
                    </div>
                </div>
                <div v-pcDirectives:scroll class="card-task-content">
                    <TaskItem
                        v-for="(item, index) in attendanceTask.checkInTaskInfo
                            .ruleViews"
                        :key="index"
                        class="task-item"
                        :task="item"
                        :is-repair="attendanceTask.checkInTaskInfo.repairing"
                    />
                </div>
                <Calendar />
            </ACardContent>
        </ACard>
    </div>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, ref } from 'vue';
import { storeToRefs } from 'pinia';
import useKconf from '@pet/ones-use.useKconfBatch';
import { ACard, ACardContent } from '@alive-ui/base';
import { userId } from '@alive-ui/actions';
import useStar from '../../stores/star';
import TaskItem from './task-item.vue';

const { attendanceTask } = storeToRefs(useStar());
const { kconfData } = storeToRefs(useKconf());
const Calendar = defineAsyncComponent(() => import('../calendar/index.vue'));

const showTips = ref(true);
// isTodayFirstVisit();

// function isTodayFirstVisit() {
//     const key = `2025-SUMMER-STAR-${userId}`;
//     const today = new Date().toISOString().split('T')[0]; // 获取今天的日期 (格式: YYYY-MM-DD)

//     const lastShownDate = localStorage.getItem(key);

//     // 如果今天还没有显示过
//     if (
//         lastShownDate !== today &&
//         kconfData.value.star?.calendar?.taskCardTips
//     ) {
//         showTips.value = true;
//         setTimeout(() => {
//             showTips.value = false;
//         }, 5000);
//         localStorage.setItem(key, today);
//     }
// }
setTimeout(() => {
    showTips.value = false;
}, 5000);
</script>

<style lang="less" scoped>
.star-task {
    position: relative;
    .card-task-top {
        display: flex;
        justify-content: space-between;
        padding: 0 12px;

        .title {
            display: flex;
            font-size: 12px;
            line-height: 20px;
        }
        .title-right {
            line-height: 20px;
        }

        .title-content {
            font-family: HYYakuHei;
            font-size: 16px;
        }
    }

    .card-task-content {
        display: flex;
        overflow-x: scroll;
        margin-top: 16px;
        padding-left: 12px;

        .task-item {
            margin-right: 10px;
            flex-shrink: 0;

            &:only-child {
                width: 358px;
                background-image: url(../../assets/task_bg_one.png);
            }
        }
    }
    .tips-wrap {
        position: absolute;
        top: -8px;
        left: 10px;
        width: max-content;
        max-width: 300px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        transform: translateY(-100%);
        padding: 8px 16px;
        color: #fff;
        background: rgba(#000, 0.7);

        &::after {
            content: '';
            position: absolute;
            bottom: -5px;
            right: 17px;
            width: 10px;
            height: 5px;
            background: url('../../assets/jiantou.png') center / 100% no-repeat;
        }
    }
}
</style>
