<template>
    <div class="ceremony-star-award">
        <div class="ceremony-star-award-content">
            <Cup
                :assets="
                    cupAssets || {
                        img: 'https://p1-live.wskwai.com/kos/nlav12706/2025-summer/star-0528-1/cup-img-0.png',
                    }
                "
                :statistics-info="statisticsInfo"
                @address="useCup().goEditAddress()"
            />
        </div>
        <ClientOnly>
            <AsyncRedpack
                v-if="!isYodaPCContainer"
                class="star-kv-redpack"
                :data-config="{ liveSquareSource: 10044 }"
                :recommend-source="RecommendSource.STAR"
                self-refresh
                :pack-type="1"
            />
        </ClientOnly>
    </div>
</template>
<script setup>
import { defineAsyncComponent } from 'vue';
import { storeToRefs } from 'pinia';
import { RecommendSource } from '@pet/ones-ui.kv-redpack/interface/type';
import { ClientOnly } from '@live/ssg';
import { isYodaPCContainer } from '@alive-ui/actions';
import useCup from './use-cup';
import Cup from './cup.vue';

const { cupAssets, statisticsInfo } = storeToRefs(useCup());
const AsyncRedpack = defineAsyncComponent(
    () => import('@pet/ones-ui.kv-redpack/index.vue'),
);
</script>

<style scoped lang="less">
.ceremony-star-award {
    overflow: hidden;
    margin-bottom: 6px;
    height: 422px;
    width: 100%;

    &-content {
        position: relative;
        z-index: 2;
        display: flex;
        height: 100%;
        &-other-list {
            width: 100%;
            padding: 0 12px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            display: flex;
            flex: 1;
            flex-wrap: nowrap;
            justify-content: space-between;
            overflow-x: scroll;
            overflow-y: hidden;
        }
    }
    .narrow-award-card {
        width: 86px;
    }
    .star-kv-redpack {
        position: absolute;
        right: 8px;
        top: 40px;
        z-index: 10;
    }
}
</style>
