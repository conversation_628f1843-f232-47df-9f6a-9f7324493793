<template>
    <div
        v-if="!isEmptyObject(currentLevelList)"
        class="star-task-button-wrapper"
    >
        <div class="star-task-button">
            <div class="img"></div>
            <div class="star-task-text a-text-main">{{ iconText }}</div>
        </div>
        <Countdown
            class="star-task-tag"
            :to="time"
            :transform="transformTime"
            :immediate-emit="false"
        />
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import Countdown from '@pet/ones-ui.countdown/index.vue';
import { transformTime } from '../../utils/transformTime';
import { isEmptyObject } from '../../utils/empty';
import useDefendTaskStore from '../../stores/limited-task';
import { TaskStatusEnum } from '../../schemas/limited-task';
const defendTaskStore = useDefendTaskStore();

const { currentLevelList } = storeToRefs(defendTaskStore);

const iconText = computed(() => {
    if (currentLevelList.value?.status === TaskStatusEnum.ING) {
        return `再集${currentLevelList.value?.gapScoreH5}值`;
    }
    if (currentLevelList.value?.status === TaskStatusEnum.FINISHED) {
        return currentLevelList.value?.finishDesc;
    }
    if (currentLevelList.value?.status === TaskStatusEnum.FAILED) {
        return currentLevelList.value?.notFinishDesc;
    }

    return '';
});

const time = computed(() => {
    if (currentLevelList.value?.status === TaskStatusEnum.ING) {
        return currentLevelList.value?.taskEndTimestamp;
    }
    return currentLevelList.value?.nextTaskStartTimestamp;
});
</script>

<style lang="less" scoped>
.star-task-button-wrapper {
    position: relative;
    width: 73px;
    height: 75px;
    margin-top: 12px;
    margin-left: 4px;
    background: url('../../assets/fans-task-icon.png') no-repeat left top;
    background-size: 100% 100%;

    .star-task-button {
        position: absolute;
        left: 0;
        top: 0;
        width: 73px;
        height: 75px;

        .star-task-text {
            font-weight: bold;
            font-size: 10px;
            width: 73px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 51px;
        }
    }

    .star-task-tag {
        background: linear-gradient(262.85deg, #ff586d 0%, #ff4594 100%);
        border-radius: 8px;
        width: 33px;
        height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 4px;
        right: 0;
        color: #ffffff;
        font-size: 10px;
        font-weight: 500;
        font-family: PingFang SC;
    }
}
</style>
