<template>
    <div class="ceremony-card">
        <div class="ceremony-value">
            {{ task?.targetScoreStr
            }}{{
                task?.simpleRuleName +
                (isRepair ? kconfData.star?.taskRepair.repair : '')
            }}
        </div>
        <div
            v-if="task.finished === RuleViewFinishedStatus.NOT_FINISHED"
            class="direct-qualification"
        >
            <span class="status">待完成</span>
        </div>
        <div class="ceremony-progress">
            <span class="progress-label">{{ task?.unit }}</span>
            <span class="progress-value">{{ task?.currentScoreStr }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { toRefs, defineProps } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { RuleViewFinishedStatus } from '../../schemas/home';
import type { RuleView } from '../../schemas/home';
const kconfStore = useKconfStore();
const { kconfData } = storeToRefs(kconfStore);

const props = defineProps<{
    task: RuleView;
    isRepair: boolean;
}>();
</script>

<style scoped>
.ceremony-card {
    box-sizing: border-box;
    position: relative;
    width: 148px;
    height: 68px;
    border-radius: 8px;
    padding: 12px 11px 11px;
    background: url(../../assets/task_bg.png) center / 100% no-repeat;
    background-color: rgba(154, 189, 255, 0.06);
}

.ceremony-value {
    font-size: 14px;
    line-height: 24px;
    font-weight: 600;
}

.direct-qualification {
    position: absolute;
    top: 0;
    right: 0;
    width: 42px;
    height: 16px;
    border-top-right-radius: 8px;
    border-bottom-left-radius: 8px;
    background: rgba(191, 218, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2px 6px;
}

.status {
    height: 14px;
    font-size: 10px;
    line-height: 14px;
    font-weight: 500;
}

.ceremony-progress {
    position: relative;
    width: 124px;
    height: 18px;
    display: flex;
    align-items: center;
}

.progress-label {
    opacity: 0.6;
    height: 18px;
    font-size: 12px;
    line-height: 18px;
}

.progress-value {
    margin-left: 2px;
    height: 18px;
    font-size: 12px;
    line-height: 18px;
    font-weight: 500;
}
</style>
