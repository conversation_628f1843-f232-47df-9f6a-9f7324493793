<template>
    <div id="calendar" class="calendar-wrapper">
        <div class="calendar-main">
            <div v-if="showMonth" class="calendar-header">
                <div
                    v-for="item in header"
                    :key="item"
                    class="calendar-header-item"
                >
                    {{ item }}
                </div>
            </div>
            <div id="calendar" class="calendar-content">
                <div
                    v-for="day in calendarDays"
                    :key="day?.value"
                    class="calendar-day"
                >
                    <div
                        class="day-num a-text-main"
                        :class="{
                            'a-text-main-o2':
                                day?.status === DateTaskStatus.CHECKED_IN,
                        }"
                    >
                        {{ day?.day }}
                    </div>
                    <div v-if="showMonth" class="a-text-main-o2 day-desc">
                        {{ day?.desc }}
                    </div>
                    <img
                        v-if="day?.status === DateTaskStatus.CHECKED_IN"
                        class="day-accomplished-flag"
                        src="./imgs/<EMAIL>"
                    />
                </div>
            </div>
            <div class="calendar-toggle a-text-main-o2" @click="toggleView">
                <span>{{ showMonth ? '收起' : '查看完整日历' }}</span>
                <div class="arrow" :class="{ 'arrow-open': showMonth }"></div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import useStar from '../../stores/star';
import { DateTaskStatus } from '../../schemas/home';
import { generateCalendarData } from './utils';
import type { CalendarDay } from './utils';
import { sendTask } from '@/common/logger';

const { attendanceTask, dateInfo } = storeToRefs(useStar());
const dateTaskInfos = computed(() => dateInfo.value?.dateTaskInfos);

const showMonth = ref(false);

function getCurrentWeek(days: CalendarDay[] = []) {
    const today = new Date();
    const todayTimestamp = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
    ).getTime();
    const todayIndex = days.findIndex(
        (day) => day && day.value === todayTimestamp,
    );
    if (todayIndex === -1) {
        return days.slice(0, 7);
    }
    const weekStart = todayIndex - (todayIndex % 7);
    return days.slice(weekStart, weekStart + 7);
}

const calendarDays = computed(() => {
    const allDays = generateCalendarData(dateTaskInfos.value);
    return showMonth.value ? allDays : getCurrentWeek(allDays);
});

const header = ['日', '一', '二', '三', '四', '五', '六'];

function toggleView() {
    !showMonth.value &&
        sendTask('CLICK', {
            action: 'OP_ACTIVITY_FUNCTION_BUTTON',
            params: {
                btn_type: '查看完整日历',
            },
        });
    showMonth.value = !showMonth.value;
}
</script>

<style lang="less" scoped>
.calendar-wrapper {
    width: 100%;
    .calendar-title-wrapper {
        display: flex;
        margin-top: 32px;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .calendar-title {
            font-family: HYYakuHei;
            font-size: 20px;
            font-weight: 400;
            line-height: 28px;
            background: linear-gradient(89.98deg, #fff 0.03%, #ffc4a3 95.69%);
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .calendar-sub-title {
            margin-top: 2px;
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            color: #ffdfbf;
            opacity: 0.6;
        }
    }

    .calendar-main {
        width: 334px;
        margin: 14px auto 0;

        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            height: 33px;
            border-radius: 4px;
            margin-bottom: 12px;
            background-color: #d9ddff1a;

            .calendar-header-item {
                width: 40px;
                height: 17px;
                font-size: 12px;
                font-weight: bold;
                color: #ffdfbf;
                text-align: center;
            }
        }

        .calendar-content {
            display: grid;
            width: 334px;
            border-radius: 8px;
            gap: 20px 8px;
            grid-template-columns: repeat(7, 1fr);

            .calendar-day {
                position: relative;
                width: 40px;
                height: 39px;

                .day-num {
                    font-size: 14px;
                    font-weight: bold;
                    line-height: 21px;
                    text-align: center;
                }

                .day-desc {
                    margin-top: 2px;
                    font-size: 10px;
                    line-height: 15px;
                    text-align: center;
                }

                .day-accomplished-flag {
                    position: absolute;
                    top: 0px;
                    left: 3px;
                    width: 35px;
                    height: 30px;
                }

                .day-flag {
                    position: absolute;
                    bottom: 1px;
                    left: 3.5px;
                    width: 33px;
                    height: 13px;
                }
            }
        }
        .calendar-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px auto 0;
            text-align: center;
            font-size: 12px;
        }

        .arrow {
            margin-left: 4px;
            width: 10px;
            height: 10px;
            background: url('./imgs/arrow-top-icon.png') no-repeat center / 100%;
            transform: rotate(0deg);
            transition: transform 0.3s ease;
        }

        .arrow-open {
            transform: rotate(180deg);
        }
    }
}
</style>
