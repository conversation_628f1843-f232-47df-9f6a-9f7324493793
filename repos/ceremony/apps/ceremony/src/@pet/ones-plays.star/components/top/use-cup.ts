import { computed } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import useStarStore from '../../stores/star';
import { goAddress } from '@/utils/go-address';
import useKConfStore from '@/@pet/ones-use.useKconfBatch/index';

export default defineStore('use-cup', () => {
    const starStore = useStarStore();
    const { kconfData } = storeToRefs(useKConfStore());
    const kconf = computed(() => {
        return kconfData.value?.star?.award || {};
    });
    const statisticsInfo = computed(() => {
        return starStore.attendanceTask?.statisticsInfo;
    });

    const getProgressTag = (progress: number) => {
        const progressList = [0, 33, 66, 99, 100];
        let index = progressList.findIndex((i) => progress <= i) || 0;
        if (progress > 100) {
            index = 4;
        }
        // 不足100的都算99
        if (progress < 100 && index === 4) {
            index--;
        }
        return `${progressList[index]}`;
    };
    // 奖杯视频/图片
    const cupAssets = computed(() => {
        const rewardAssets: any = kconf.value.cupMap;
        if (
            !starStore.attendanceTask ||
            starStore.attendanceTask.statisticsInfo.taskTotalNum === 0
        ) {
            return rewardAssets[0];
        }
        const { taskCompleteNum, taskTotalNum } =
            starStore.attendanceTask.statisticsInfo;
        const progress = ((taskCompleteNum || 0) / taskTotalNum) * 100;
        return rewardAssets?.[getProgressTag(progress)];
    });
    // 填写地址
    const goEditAddress = () => {
        goAddress();
    };

    return {
        cupAssets,
        kconf,
        goEditAddress,
        statisticsInfo,
    };
});

export const useAwardLog = () => {
    /** 填写地址埋点 */
    const getEditAddressLogParams = () => {
        return {
            action: 'OP_ACTIVITY_FUNCTION_BUTTON',
            params: {
                btn_type: '填写地址',
            },
        };
    };

    return {
        getEditAddressLogParams,
    };
};
