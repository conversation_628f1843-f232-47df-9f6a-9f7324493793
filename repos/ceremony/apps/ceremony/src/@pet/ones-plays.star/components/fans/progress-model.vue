<template>
    <div
        v-for="(item, index) in list"
        :key="index"
        class="fans-progress-moduel"
    >
        <div class="fans-jiangli-wrapper">
            <span
                :class="{
                    'fans-jiangli a-text-main': true,
                    'op-50': !isHeightLight(item),
                }"
            >
                {{ item?.incrFansAward }}
            </span>
            <img
                v-if="item?.status === SegmentStatus.LOCK"
                :class="{
                    'lock-icon': true,
                    'op-50': !isHeightLight(item),
                }"
                src="../../assets/lock-icon.png"
            />
        </div>
        <span
            :class="{
                circle: true,
                'circle-unlock a-bg-white':
                    item?.status === SegmentStatus.UNLOCK,
                'circle-target a-bg-main':
                    isHeightLight(item) &&
                    status !== IncrFansTaskStatus.EVENT_LIMIT_REACHED,
                'a-bg-main op-40':
                    item?.status === SegmentStatus.LOCK && !isHeightLight(item),
            }"
        />
        <span
            :class="{
                'fans-score a-text-main': true,
                'op-50': !isHeightLight(item),
            }"
        >
            {{
                item?.status === SegmentStatus.LOCK
                    ? `${item?.targetScoreDesc}${item?.unit}`
                    : '已解锁'
            }}
        </span>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { SegmentStatus, IncrFansTaskStatus } from '../../schemas/home';
import type { PropType } from 'vue';
import type { Segment } from '../../schemas/home';

const props = defineProps({
    list: {
        type: Array as PropType<Segment[]>,
        default: () => [],
    },
    currentLevel: {
        type: Number,
        default: 0,
    },
    status: {
        type: Number,
        default: 0, // 4 全部解锁
    },
});

// 是否是目标档位
const isTargetLevel = (item: Segment) => {
    return (
        item?.status !== SegmentStatus.UNLOCK &&
        item?.level === props.currentLevel
    );
};

// 是目标档位或者全部解锁
const isHeightLight = (item: Segment) => {
    return (
        isTargetLevel(item) ||
        props.status === IncrFansTaskStatus.EVENT_LIMIT_REACHED
    );
};
</script>

<style lang="less" scoped>
.fans-progress-moduel {
    position: relative;
    z-index: 4;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 54px;
    height: 47px;

    .fans-jiangli-wrapper {
        height: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        .fans-jiangli {
            font-family: MF YuanHei;
            font-size: 14px;
            font-weight: 400;
            line-height: 14px;
        }

        .lock-icon {
            width: 12px;
            height: 12px;
        }
    }

    .circle {
        width: 4px;
        height: 4px;
        border-radius: 4px;
    }

    .circle-unlock {
        box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    }

    .circle-target {
        height: 8px;
        width: 8px;
        border-radius: 8px;
        box-shadow: 0px 0px 4px rgba(255, 223, 191, 0.5);
    }

    .fans-score {
        font-family: 'PingFang SC';
        font-size: 10px;
        font-weight: 500;
        line-height: 11px;
    }
}
</style>
