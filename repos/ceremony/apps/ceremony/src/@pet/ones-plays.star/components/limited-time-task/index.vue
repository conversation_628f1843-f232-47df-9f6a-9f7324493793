<template>
    <div ref="rootElement" class="limit-time-task-wrapper">
        <ACard>
            <ACardContent class="card-content">
                <div class="limit-time-task-head">
                    <span class="limit-time-task-title a-text-title">
                        {{ kconfData.star?.limitedTimeTask.title }}
                    </span>
                    <div
                        v-if="currentLevelList?.status === TaskStatusEnum.ING"
                        class="time"
                    >
                        <span class="a-text-main">挑战倒计时：</span>
                        <Countdown
                            class="a-text-main time-box1"
                            :to="currentLevelList?.taskEndTimestamp"
                            :transform="transformTime"
                            :immediate-emit="false"
                            @end="handleCountDownEnd"
                        />
                    </div>
                    <div v-else class="time">
                        <Countdown
                            class="a-text-main"
                            :to="currentLevelList?.nextTaskStartTimestamp"
                            :transform="transformTimeOther"
                            :immediate-emit="false"
                            @end="handleCountDownEnd"
                        />
                        <span class="a-text-main">s后开启下一轮</span>
                    </div>
                </div>
                <div class="limit-time-task-content a-bg-substrate">
                    <img
                        src="../../assets/task.png"
                        class="limit-time-task-img"
                    />
                    <div class="limit-time-task-desc">
                        <!-- 进行中 -->
                        <template
                            v-if="
                                currentLevelList?.status === TaskStatusEnum.ING
                            "
                        >
                            <div class="limit-time-task-desc-title a-text-main">
                                累计<span class="a-text-highlight">{{
                                    currentLevelList?.currentScoreH5
                                }}</span
                                >/{{ currentLevelList?.targetScoreH5 }}热度值
                            </div>
                            <div class="limit-time-task-desc-subtitle">
                                <span class="a-text-main-o2">可获得</span
                                ><span class="a-text-main">{{
                                    currentLevelList?.incentiveTrafficWorth +
                                    currentLevelList?.incentiveTrafficUnit
                                }}</span>
                            </div>
                        </template>
                        <!-- 成功 -->
                        <template
                            v-if="
                                currentLevelList?.status ===
                                TaskStatusEnum.FINISHED
                            "
                        >
                            <div class="limit-time-task-desc-title a-text-main">
                                {{
                                    kconfData.star?.limitedTimeTask.finishTitle
                                }}
                            </div>
                            <div class="limit-time-task-desc-subtitle">
                                <span class="a-text-main-o2">已获得</span
                                ><span class="a-text-main">{{
                                    currentLevelList?.incentiveTrafficWorth +
                                    currentLevelList?.incentiveTrafficUnit
                                }}</span>
                            </div>
                        </template>
                        <!-- 失败 -->
                        <template
                            v-if="
                                currentLevelList?.status ===
                                TaskStatusEnum.FAILED
                            "
                        >
                            <div class="limit-time-task-desc-title a-text-main">
                                {{
                                    kconfData.star?.limitedTimeTask
                                        .notFinishTitle
                                }}
                            </div>
                            <div
                                class="limit-time-task-desc-subtitle a-text-main-o2"
                            >
                                {{
                                    kconfData.star?.limitedTimeTask
                                        .notFinishSubTitle
                                }}
                            </div>
                        </template>
                    </div>
                </div>
            </ACardContent>
        </ACard>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import Countdown from '@pet/ones-ui.countdown/index.vue';
import { ACard, ACardContent } from '@alive-ui/base';
import { transformTime, transformTimeOther } from '../../utils/transformTime';
import useDefendTaskStore from '../../stores/limited-task';
import { TaskStatusEnum } from '../../schemas/limited-task';

const defendTaskStore = useDefendTaskStore();
const kconfStore = useKconfStore();

const { currentLevelList } = storeToRefs(defendTaskStore);
const { kconfData } = storeToRefs(kconfStore);

const emits = defineEmits(['end']);

const handleCountDownEnd = () => {
    emits('end');
};

const rootElement = ref<any>(null);

defineExpose({ rootElement });
</script>

<style lang="less" scoped>
.limit-time-task-wrapper {
    width: 382px;
    height: 148px;
    margin: 0 auto;

    .card-content {
        padding: 0 12px;

        .limit-time-task-head {
            display: flex;
            justify-content: space-between;

            .limit-time-task-title {
                font-size: 16px;
                font-weight: 400;
                font-family: HYYakuHei;
                line-height: 20px;
            }

            .time {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 12px;

                .time-box1 {
                    display: inline-block;
                    width: 30px;
                }

                .time-box2 {
                    display: inline-block;
                    width: 15px;
                }
            }
        }

        .limit-time-task-content {
            width: 358px;
            height: 84px;
            border-radius: 8px;
            margin-top: 12px;
            display: flex;
            align-items: center;

            .limit-time-task-img {
                width: 60px;
                height: 60px;
                margin-left: 12px;
            }

            .limit-time-task-desc {
                margin-left: 12px;

                .limit-time-task-desc-title {
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 24px;
                    margin-bottom: 6px;
                }

                .limit-time-task-desc-subtitle {
                    font-family: PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 18px;
                }
            }
        }
    }
}
</style>
