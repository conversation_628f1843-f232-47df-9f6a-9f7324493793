import { msToString } from '@alive-ui/actions';

export const transformTime = (t: number) => {
    if (t >= 3600000) {
        return msToString(t, 'HH:mm:ss');
    }

    return msToString(t, 'mm:ss');
};

export const transformTimeOther = (t: number) => {
    if (t >= 3600000) {
        return msToString(t, 'HH:mm:ss');
    }

    if (t <= 60000) {
        return msToString(t, 'ss');
    }

    return msToString(t, 'mm:ss');
};
