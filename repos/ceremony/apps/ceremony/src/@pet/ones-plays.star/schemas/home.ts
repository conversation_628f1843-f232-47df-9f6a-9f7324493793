export interface Segment {
    /** 涨粉奖励 */
    incrFansAward: string;
    /** 目标分数 */
    targetScore: number;
    /** 单位 */
    unit: string;
    /** 当前分数 */
    curScore: number;
    /** 0 未满, 1已满 */
    status: number;
}

export enum IncrFansTaskStatus {
    /** 未开始 */
    NOT_STARTED = 1,
    /** 进行中 */
    IN_PROGRESS = 2,
    /** 当日已达上限 */
    DAILY_LIMIT_REACHED = 3,
    /** 活动周期内已达上限 */
    EVENT_LIMIT_REACHED = 4,
}

export enum SegmentStatus {
    LOCK = 0, // 未解锁
    UNLOCK = 1, // 已解锁
}

export interface IncrFansTask {
    status: IncrFansTaskStatus;
    title: string;
    subTitle: string;
    subTitleScore: string;
    totalScore: number;
    bottomIcon: string;
    segmentList: Segment[];
    statusDesc: string;
    currentLevel: number;
    curScore: number;
}

export interface Segment {
    level: number;
    incrFansAward: string;
    targetScore: number;
    targetScoreDesc: string;
    unit: string;
    status: number;
}

export enum RuleViewFinishedStatus {
    /** 未完成 */
    NOT_FINISHED = 0,
    /** 完成 */
    FINISHED = 1,
}

export interface RuleView {
    ruleId: number;
    /** 任务描述 */
    desc: string;
    /** 简化规则名称 */
    simpleRuleName: string;
    /** 当前子任务的进度值 */
    currentScore: number;
    currentScoreStr: string;
    /** 当前子任务的目标值 */
    targetScore: number;
    targetScoreStr: string;
    gapScore: number;
    gapScoreStr: string;
    /** 单位 */
    unit: string;
    /** 0 未完成， 1 完成 */
    finished: RuleViewFinishedStatus;
}

export interface CheckInTaskInfo {
    date: string;
    /** 当日的打卡任务是否完成 */
    complete: boolean;
    title: string;
    ruleViews: RuleView[];
    desc: string;
    /** 子任务完成的个数 */
    ruleFinishNum: number;
    /** 子任务总个数 */
    ruleTotalNum: number;
    repairing: boolean;
}

export enum DateTaskStatus {
    /** 未开始 */
    NOT_STARTED = 0,
    /** 进行中 */
    IN_PROGRESS = 1,
    /** 已打卡 */
    CHECKED_IN = 2,
    /** 去补签 */
    NEED_REPAIR = 3,
    /** 补签中 */
    REPAIRING = 4,
    /** 休赛期 */
    OFF_SEASON = 5,
}

export interface DateTaskInfo {
    date: string;
    desc: string;
    status: DateTaskStatus; // 打卡任务状态
}

export enum RepairStatus {
    /** 不可开启补签 */
    CANNOT_REPAIR = 0,
    /** 可以补签 */
    CAN_REPAIR = 1,
}

export interface RepairInfo {
    desc: string;
    param: number;
    ruleDesc: string;
    status: RepairStatus;
}

export interface DateInfo {
    repairInfo: RepairInfo;
    dateTaskInfos: DateTaskInfo[];
}

export interface StatisticsInfo {
    /** 整个活动是否打卡挑战成功 */
    challengeSuccess: boolean;
    /** 主播第completedRank完成挑战 */
    completedRank: number;
    /** 当前打卡的天数 */
    taskCompleteNum: number;
    /** 距离活动打卡挑战成功剩余打卡天数 */
    taskRemainingNum: number;
    /** 总打卡天数 */
    taskTotalNum: number;
    /** 当天剩余可补签的次数 */
    todayRepairRemainingNum: number;
    /** 当天总共可补签的次数 */
    todayTotalRepairNum: number;
}

export interface AttendanceTask {
    allCheckInTaskEnd: boolean;
    statisticsInfo: StatisticsInfo;
    checkInTaskInfo?: CheckInTaskInfo;
    dateInfo?: DateInfo;
}

export interface ResponseRoot {
    /** 涨粉任务 */
    incrFansTask: IncrFansTask;
    /** 打卡任务 */
    attendanceTask: AttendanceTask;
    serverTime: number;
}

export interface RepairResponseRoot {
    status: number;
    msg: string;
}

export interface RequestBody {
    biz: string;
    authorId: number | string;
}
