<template>
    <div class="small-bell-wrapper">
        <CardTemplate :hidden-right-icon="true" @click="goToBell">
            <template #topLeftTtle>
                点击小铃铛下载
                <span class="left-desc-red">{{
                    kconfData.mainPage?.gameRewardsText
                }}</span>
            </template>
            <template #topRightText>
                <div @click="goGameDetail">查看详情</div>
            </template>
            <template #cardContainerUnBg>
                <div v-if="bolIsAuthor" class="card-content-author" />
                <div v-else class="card-content-visitor">
                    <div class="download-btn button-primary-sm-bg">去下载</div>
                </div>
            </template>
        </CardTemplate>
    </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance } from 'vue';
import { storeToRefs } from 'pinia';
import { debounce } from 'lodash-es';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { Toast } from '@lux/sharp-ui-next';
import {
    bolIsAuthor,
    exitWebView,
    invoke,
    isYodaPCContainer,
    isGteVersion,
    liveStreamId,
} from '@alive-ui/actions';
import CardTemplate from './card-template.vue';
import { showPCToast } from '@/@pet/ones-ui.recharge-icon/utils/toast';

const proxy = getCurrentInstance()?.proxy;
const { kconfData } = storeToRefs(useKconfStore());

const goToBell = debounce(async () => {
    if (isYodaPCContainer) {
        showPCToast('请到手机端进行查看');

        return;
    }

    // 旧实现
    // invoke('platform.showToast', {
    //   type: 'normal',
    //   text: '小铃铛在直播间右下角哦～',
    //   isAddToWindow: false,
    // });
    // exitWebView();
    if (isGteVersion('12.3.10')) {
        try {
            // 查询是否有
            const res = await invoke('advertise.bellCommonBridge', {
                action: 1,
                liveStreamId,
            });

            // 是否可见
            if (res?.data?.isVisible) {
                await invoke(
                    'advertise.bellCommonBridge',
                    {
                        action: 2,
                        liveStreamId,
                    },
                    () => {},
                );
            } else {
                Toast.info('暂无下载');
            }
        } catch {
            invoke('platform.showToast', {
                type: 'normal',
                text: '小铃铛在直播间右下角哦～',
                isAddToWindow: false,
            });

            setTimeout(() => {
                exitWebView();
            }, 200);
        }
    } else {
        invoke('platform.showToast', {
            type: 'normal',
            text: '小铃铛在直播间右下角哦～',
            isAddToWindow: false,
        });

        setTimeout(() => {
            exitWebView();
        }, 200);
    }
}, 500);
const goGameDetail = () => {
    // TODO:
    proxy?.$router.push({
        name: 'author-addition',
    });
};
</script>

<style lang="less" scoped>
.small-bell-wrapper {
    border-radius: 8px;
    padding: 12px;
    background: var(--substrate-color, rgba(154, 189, 255, 0.06));
}
.left-desc-red {
    @apply a-text-highlight;
}
.card-content-author {
    width: 100%;
    height: 48px;
    margin-top: 8px;
    background: url('./assets/game-card-zhubo_2x.png') center / 100% no-repeat;
}
.card-content-visitor {
    display: flex;
    width: 100%;
    height: 40px;
    margin-top: 8px;
    background: url('./assets/game-card-guanzhong_2x.png') left / 100% no-repeat;
    background-size: 258px 40px;
    align-items: center;
    justify-content: flex-end;
    .download-btn {
        display: flex;
        width: 64px;
        height: 32px;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-weight: 500;

        @apply a-text-tab-solid-active;

        text-align: center;
        background-size: 100% 100%;
        align-items: center;
        justify-content: center;
        &.has-down {
            opacity: 0.5;
        }
    }
}
</style>
