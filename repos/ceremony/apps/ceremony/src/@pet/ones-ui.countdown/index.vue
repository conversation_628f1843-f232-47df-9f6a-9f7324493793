<template>
    <span class="countdown-value">
        <slot :text="text" :left="leftMs">
            {{ text }}
        </slot>
    </span>
</template>

<script setup lang="ts">
import { watch } from 'vue';
import {
    msToString,
    isYodaPCContainer,
    useCountdownTo,
} from '@alive-ui/actions';
import type { PropType } from 'vue';
const emits = defineEmits(['end']);
const props = defineProps({
    // 倒计时终点时间戳
    to: {
        type: Number,
        default: 0,
    },
    // 将距离重点的毫秒数格式化成string
    transform: {
        type: Function as PropType<(t: number) => string>,
        default: () => ({}),
        required: false,
    },
    // 轮询时间
    intervalMs: {
        type: Number,
        default: 200,
    },
    timeFormat: {
        type: String,
        default: '',
        required: false,
    },
    // 当to改变时是否重置
    notReset: {
        type: Boolean,
        default: false,
    },
    immediateEmit: Boolean,
    // 是否使用服务器时间
    useServerTime: {
        type: Boolean,
        default: () => {
            if (isYodaPCContainer) {
                return false;
            }
            return true;
        },
    },
});

// eslint-disable-next-line vue/no-setup-props-destructure
let transformFn = props.transform;

if (!transformFn && props.timeFormat) {
    transformFn = (t: number) => msToString(t, props.timeFormat);
}
console.log('transformFn', props.to);
const { text, reset, leftMs } = useCountdownTo(props.to, {
    useServerTime: props.useServerTime,
    transformFn,
    intervalMs: props.intervalMs,
    immediateEmit: props.immediateEmit,
    onEnd: (isInitEnd) => {
        emits('end', isInitEnd);
    },
});

watch(
    () => props.to,
    (t) => {
        if (!props.notReset) {
            reset(t);
        }
    },
);
</script>
