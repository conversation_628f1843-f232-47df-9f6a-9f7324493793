# 倒计时组件 (`@pet/ones-ui.countdown`)

## 功能概述

倒计时组件用于显示从当前时间到指定时间的倒计时。支持自定义时间格式化函数、轮询间隔、是否重置倒计时等配置。

## 属性 (Props)

| 属性名          | 类型       | 默认值       | 描述                                                                        |
| --------------- | ---------- | ------------ | --------------------------------------------------------------------------- |
| `to`            | `Number`   | `0`          | 倒计时终点的时间戳（毫秒）。                                                |
| `transform`     | `Function` | `() => ({})` | 将距离终点的毫秒数格式化成字符串的函数。                                    |
| `intervalMs`    | `Number`   | `200`        | 倒计时的轮询间隔（毫秒）。                                                  |
| `timeFormat`    | `String`   | `''`         | 时间格式化字符串，当 `transform` 未提供时使用此格式。                       |
| `notReset`      | `Boolean`  | `false`      | 当 `to` 改变时是否重置倒计时。                                              |
| `immediateEmit` | `Boolean`  | `false`      | 是否立即触发 `end` 事件。                                                   |
| `useServerTime` | `Boolean`  | `true`       | 是否使用服务器时间。默认情况下，如果在 Yoda PC 容器中，则不使用服务器时间。 |

## 方法 (Events)

| 事件名 | 参数        | 描述                     |
| ------ | ----------- | ------------------------ |
| `end`  | `isInitEnd` | 倒计时结束时触发的事件。 |

## 使用示例

### 基本用法

```vue
<template>
  <countdown to=\"1677436800000\" />
</template>

<script setup>
import { Countdown } from '@pet/ones-ui.countdown';
</script>
```

### 自定义时间格式

```vue
<template>
  <countdown to=\"1677436800000\" timeFormat=\"HH:mm:ss\" />
</template>

<script setup>
import { Countdown } from '@pet/ones-ui.countdown';
</script>
```

### 自定义格式化函数

```vue
<template>
  <countdown to=\"1677436800000\" :transform=\"customTransform\" />
</template>

<script setup>
import { Countdown } from '@pet/ones-ui.countdown';

const customTransform = (ms) => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  return `${hours}:${minutes % 60}:${seconds % 60}`;
};
</script>
```

### 监听倒计时结束事件

```vue
<template>
  <countdown to=\"1677436800000\" @end=\"onEnd\" />
</template>

<script setup>
import { Countdown } from '@pet/ones-ui.countdown';

const onEnd = (isInitEnd) => {
  console.log('倒计时结束', isInitEnd);
};
</script>
```

## 注意事项

-   `transform` 函数必须返回一个字符串。
-   如果 `timeFormat` 和 `transform` 同时存在，优先使用 `transform`。
-   `useServerTime` 在 Yoda PC 容器中默认为 `false`，其他情况下默认为 `true`。

## 依赖项

-   `vue`
-   `@alive-ui/actions`
