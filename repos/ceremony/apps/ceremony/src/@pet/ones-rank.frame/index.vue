<script lang="ts" setup>
import PageFrame from '@pet/ones-ui.page-frame/index.vue';
import RankHeader from '@pet/ones-rank.list-head/index.vue';
import RankClear from '@pet/ones-rank.clear/index.vue';
import { ElseStatus } from '@alive-ui/base';
import type { PropType } from 'vue';
import type { RankFrameBaseData } from '@pet/ones-rank.schema/global';
const emits = defineEmits(['home-refresh', 'rank-refresh']);

const props = defineProps({
    contextName: {
        type: Symbol,
        default: Symbol.for('ctx'),
    },
    data: {
        type: Object as PropType<RankFrameBaseData>,
        require: true,
        default: () => {
            return {};
        },
    },
    onlyRank: {
        type: Boolean,
        default: false,
    },
    needCenterStyle: {
        type: Boolean,
        default: false,
    },
    needRefresh: {
        type: Boolean,
        default: true,
    },
    // 榜单包裹容器 （头部卡片/带标题/副标题 ，或者自定义传入）
    rankHeaderContainer: {
        type: Object,
        default: RankHeader,
    },
});
const homeRefresh = () => {
    emits('home-refresh');
};
const rankRefresh = () => {
    emits('rank-refresh');
};
const dataRefresh = () => {
    window.scrollTo(0, 0);
    rankRefresh();
};
</script>

<template>
    <PageFrame
        :need-refresh="needRefresh"
        :data="data"
        :need-center-style="needCenterStyle"
        @refresh="homeRefresh"
        @data-refresh="dataRefresh"
    >
        <template #left-top>
            <slot name="left-top"> </slot>
        </template>
        <template #right-top>
            <slot name="right-top"> </slot>
        </template>
        <template #kv-middle>
            <slot name="kv-middle"></slot>
        </template>
        <template #kv-other>
            <slot name="kv-other"></slot>
        </template>
        <template #main-container>
            <div class="rank-frame-container">
                <!-- 休赛或者放置未开启兜底 -->
                <slot name="rest-container">
                    <!-- 榜单页面中间区域，可自定义赛程、玩法卡片、其他等 -->
                    <slot name="page-center"> </slot>
                    <component
                        :is="rankHeaderContainer"
                        :data="data?.headerData"
                        class="card-margin-self"
                    >
                        <template #rank-title-extra>
                            <slot name="rank-title-extra"> </slot>
                        </template>
                        <!-- 榜单列表区域头部信息，如放tab组件，奖励之类 -->
                        <slot name="rank-header"> </slot>
                        <div
                            :class="
                                !onlyRank
                                    ? 'position-relative container-height'
                                    : ''
                            "
                        >
                            <template v-if="data?.rankStatus?.success">
                                <!-- 结算状态 -->
                                <RankClear
                                    v-if="data?.isClearing"
                                    :context-name="contextName"
                                    @end="rankRefresh"
                                />
                                <!-- 榜单列表区, 可向ones-rank贡献新的列表组件（基于已有的组件修改） -->
                                <slot v-else name="rank-list"></slot>
                            </template>
                            <!-- 榜单列表异常状态 -->
                            <ElseStatus
                                v-else
                                class="else-status"
                                :page-status="data.rankStatus"
                                :is-show-refresh="true"
                                :no-data="false"
                                @refresh="rankRefresh"
                            />
                        </div>
                    </component>
                </slot>
            </div>
        </template>
        <template #bottomLeft>
            <slot name="bottomLeft"></slot>
        </template>
    </PageFrame>
</template>
<style lang="less" scoped>
.rank-frame-container {
    --marginTop: var(--marginTop);
    --pagHeadHeight: var(--pagHeadHeight);
    --yImgHeight: var(--yImgHeight);
    --yImgWidth: var(--yImgWidth);
}
.card-margin-self {
    margin-top: var(--rankCardMarginTop, 20px);
    min-height: 400px;
}
.else-status {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100px;
    transform: translate(-50%, -50%);
}
.container-height {
    min-height: 250px;
}
</style>
