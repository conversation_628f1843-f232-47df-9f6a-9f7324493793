## 组件概述

ones-rank.frame 是一个用于构建榜单页面的基础容器组件，提供了内置的请求状态管理、KV区配置、榜单展示等功能。该组件支持通过插槽自定义内容，适用于多种榜单展示场景。
功能支持

1. 请求状态管理
   ● 内置状态：加载中、成功、失败兜底。
2. KV区配置
   ● 支持配置返回按钮和规则按钮。
   ● KV区中间区域可展示图片。
3. 榜单展示
   ● 支持自定义插槽展示榜单内容。
   ● 插槽包括：
   ○ left-top：配置KV区左侧入口icon。
   ○ right-top：配置KV区右侧入口icon。
   ○ kv-middle：KV区中间区域，支持展示图片。
   ○ rest-container：放置休赛内容或未开启兜底展示。
   ○ page-center：自定义赛程、玩法卡片等内容。
   ○ rank-header：榜单列表区域头部信息。
   ○ rank-list：榜单列表区，支持自定义组件。

## schema

```typescript
export interface FrameSchema {
    kvData?: {
        // 右上角规则链接，可选
        rule?: string;
        // kv图配置，可选
        image?: string;
        // 是否展示返回按钮
        showBack?: boolean;
        // 返回方式，可选退出容器可返回上一页
        backAction?: string; // "back" | "exit"
        // 榜单容器标题，可选
        headerData?: {
            title?: string;
            subTitle?: string;
        };
        // 自定义返回方式
        backFunc?: () => void;
    };
    showRefresh?: boolean;
    // 整体页面状态
    homeStatus?: Status;
    // 榜单整体状态
    rankStatus: Status;
    // 是否在结算
    isClearing?: boolean;
}
```

## 该组件接收以下 props：

| Prop Name           | Type    | Default Value | Description                    |
| ------------------- | ------- | ------------- | ------------------------------ |
| contextName         | String  | ''            | 上下文名称，用于数据提供和注入 |
| onlyRank            | Boolean | false         | 是否仅展示榜单区域             |
| data                | Object  | FrameSchema   | 符合 FrameSchema 类型          |
| needRefresh         | Boolean | true          | 是否需要刷新按钮               |
| isCurrent           | Boolean | false         | 是否为当前项                   |
| rankHeaderContainer | Object  | RankHeader    | 榜单头部组件，可以自定义传入。 |

## 使用示例

以下是如何在 Vue 组件中使用 ones-rank.frame 的示例：

```vue
<script lang="ts" setup>
import { provide, defineAsyncComponent } from "vue";
import { storeToRefs } from "pinia";
import RankFrame from "@pet/ones-rank.frame/index.vue";
const RankPanel = defineAsyncComponent(() => {
    return import("@pet/ones-rank.common-list/index.vue");
});

const contextName = "cereMonyIndex";
const homeStore = useHomeStore();
const { homeData } = storeToRefs(homeStore);

homeStore.init();

provide(contextName, homeData);
</script>

<template>
    <RankFrame :data="homeData.frameData" :context-name="contextName" @home-refresh="homeStore.refresh" @rank-refresh="rankStore.refresh">
        <template #rank-list>
            <RankPanel :data="homeData.rankData" />
        </template>
    </RankFrame>
</template>
```
