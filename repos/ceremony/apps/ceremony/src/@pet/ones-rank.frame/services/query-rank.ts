import {
    request,
    activityBiz,
    liveStreamId,
    authorId,
} from '@alive-ui/actions';
import type {
    QueryRankPostResponse,
    QueryRankExtraPostResponse,
} from '@pet/ones-rank.schema/query-rank';

// 打榜赛事玩法-榜单页：https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiDocTab=docs&apiId=52050&version=-1
export const queryRank = async (
    params: {
        rankId?: number | string;
        bizName?: string;
        periodId?: number;
        rankAlias?: string;
        refreshFlag?: boolean;
    },
    path?: string,
) => {
    const PATH =
        path || '/webapi/live/revenue/operation/activity/ranklist/v3/queryRank'; // TODO: 后续改成动态路径
    const res = await request.post<QueryRankPostResponse>(PATH, {
        activityBiz,
        liveStreamId,
        authorId,
        ...params,
    });

    return res?.data;
};

export const queryRankExtra = async (params: {
    activityBiz: string;
    rankId: number;
    periodId: number;
    bizName: string;
    authorId: string | number;
    liveStreamId: string;
    rankAlias: string;
    extraTabDataKeyList?: string[];
    subBiz: string;
}) => {
    const PATH =
        '/webapi/live/revenue/operation/activity/complex/rankFrameworkMoreTab';
    const res = await request.post<QueryRankExtraPostResponse>(PATH, params);
    return res?.data;
};
