import { request, liveStreamId, activityBiz } from '@alive-ui/actions';
import type { RankFrameworkPostResponse } from '@pet/ones-rank.schema/index-home';

// 首页赛程数据：https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=41020&version=-1
export const rankFramework = async (
    params: {
        type: number | string;
        appointTime?: number;
        subBiz?: 'main' | 'sub' | 'invite'; // sub是B类赛事,main是主赛事,invite是邀请赛
        rankId?: string;
    },
    path?: string,
) => {
    const PATH =
        path || '/webapi/live/revenue/operation/activity/complex/rankFramework'; // TODO: 后续改成动态路径
    const res = await request.post<RankFrameworkPostResponse>(PATH, {
        activityBiz,
        liveStreamId,
        ...params,
    });

    return res?.data;
};
