/* eslint-disable max-lines-per-function */
import { ref, nextTick, watch } from 'vue';
import { defineStore } from 'pinia';
import { debounce } from 'lodash-es';
import {
    usePartySwitch,
    usePartySwitchPromiseStore,
    supportNativeVisible,
} from '@pet/ones-use.usePartySwitch';
import { useDowngrade, useLowPowerPromise } from '@pet/ones-use.useDowngrade';
import {
    type CancelableContext,
    ContextManager,
} from '@pet/ones-use.context-manager';
import {
    setWebLogger,
    createVideoContext,
    VideoType,
    LogLevel,
    CancelToken,
} from '@ks/yoda-video-player';
import { weblog } from '@gundam/weblogger';
import { getKwaiVersion } from '@alive-ui/system';
import { isOutLiveRoom, Report } from '@alive-ui/actions';
import type { IVideoContext } from '@ks/yoda-video-player';

// 设置 weblogger, 否则不会上报埋点
if (weblog) {
    setWebLogger(weblog);
}

export default defineStore('use-yoda-video-play', () => {
    const useH5Video = ref<boolean>(false); // 是否使用h5播放器
    // 是否正在播放, 如果为 undefined, 表示中间态, 此时界面隐藏按钮（避免用户反复点）, 这个是预留的能力, 现在只在初始化时会被设为 undefined。
    const playing = ref<boolean | undefined>(void 0);
    // playing 的修改加上防抖: 延迟 100 毫秒修改, 避免页面的暂停/播放图标出现闪跳。
    const playStateUpdateDelay = 100;
    const muted = ref<boolean>(!isOutLiveRoom); // 是否静音，间内 true 间外 false
    const playStatus = ref<'normal' | 'error'>('normal');
    const swiperIdx = ref(0);
    const downGradeSwitch = usePartySwitch();
    const logicDownGrade = useDowngrade();
    const lowPowerStore = useLowPowerPromise();
    const switchPromise = usePartySwitchPromiseStore();

    // 保存所有创建的实例
    const resourceHistory: {
        // yoda 播放器上下文
        videoContext?: IVideoContext | null | undefined;
        // yoda 播放器上下文的 promise
        videoContextPromise?:
            | undefined
            | Promise<IVideoContext | null | undefined>;
        cancelToken: CancelToken;
        ctx: CancelableContext;
    }[] = [];

    // 视频上下文管理器
    const VideoContextManager = new ContextManager();

    // 容器节点
    let container: HTMLDivElement | null | undefined;

    /**
     * 设置/更新容器节点
     * @param container$0 新的容器节点, 只对后续创建的播放器有效, 如果传入空置, 后续不会创建播放器。
     */
    function updateContainer(container$0: HTMLDivElement | null | undefined) {
        container = container$0;
    }

    // 销毁所有实例
    const destroy = async () => {
        if (!isOutLiveRoom) {
            return;
        }
        VideoContextManager.cancelAllContext();
        try {
            for (const his of resourceHistory) {
                // 销毁播放器js 实例和 native 实例，防止内存泄漏
                his.cancelToken.cancel();
                // 如果之前的 yoda 实例正在 init, 需要等其 ready, 否则 yoda 内部的 create 会直接返回上一次的
                await his.videoContextPromise;
                // 取消player创建过程，防止player异步创建过程中player?.value为空
                await his.videoContext?.destroy();
            }
            resourceHistory.length = 0;
        } catch (error) {
            Report.biz.warning('【视频轮播】destroy 报错', { error });
        }
    };

    const create = async (payload: {
        videoUrl?: string | null | undefined;
        retry?: boolean;
    }) => {
        // 上报 create 环境日志
        Report.biz.info('【视频轮播】-create', {
            isOutLiveRoom,
            logicDownGrade: logicDownGrade.value,
            videoUrl: payload.videoUrl,
            container: !!container,
            videoDowngradeSwitch: downGradeSwitch.value.videoDowngradeSwitch,
        });

        if (
            !isOutLiveRoom ||
            logicDownGrade.value ||
            !payload.videoUrl ||
            !container ||
            downGradeSwitch.value.videoDowngradeSwitch
        ) {
            Report.biz.warning('【视频轮播】命中降级-静态兜底图', {
                videoDowngradeSwitch:
                    downGradeSwitch.value.videoDowngradeSwitch,
                logicDownGrade: logicDownGrade.value,
            });
            return null;
        }

        if (downGradeSwitch.value.muted) {
            muted.value = true;
            Report.biz.warning('【视频轮播】命中降级-静音', {
                kwaiVersion: getKwaiVersion(),
            });
        }

        await destroy();

        // 获取新的上下文
        const ctx = VideoContextManager.getContext();

        // pl8(`# ${ctx.ctxRound} | 创建新的 video context`, payload.videoUrl);

        const cancelToken = new CancelToken();
        const resource = {
            videoContext: void 0 as IVideoContext | null | undefined,
            videoContextPromise: void 0 as
                | undefined
                | Promise<IVideoContext | null | undefined>,
            cancelToken,
            ctx,
        };
        resourceHistory.push(resource);

        // 创建播放器
        resource.videoContextPromise = createVideoContext({
            container, // 挂载的 dom 节点
            // 播放器的详细配置项
            playerConfig: {
                src: [{ url: payload.videoUrl }],
                videoFallbackSrc: [{ url: payload.videoUrl }],
                autoplay: true,
                muted: muted.value,
                videoType: VideoType.MP4,
                objectFit: 'cover',
                loop: true,
            },
            plugins: [{ name: 'background', args: {} }],
            useH5FallbackPlayer: useH5Video.value,
            cancelToken,
            logLevel: LogLevel.warn, // 控制台日志等级。详细日志使用LogLevel.log
        });
        resource.videoContext = await resource.videoContextPromise;

        if (ctx.isCancellationRequested()) return;

        // 如果不支持同层渲染，降级到 h5 video 播放器
        if (!resource.videoContext) {
            if (!useH5Video.value) {
                useH5Video.value = true;
                create({
                    retry: true,
                    ...payload,
                }).catch((error) => {
                    Report.biz.error('【视频轮播】create 报错', { error });
                });
                Report.biz.warning('【视频轮播】降级为 h5 播放器', {});
            }
            return;
        }

        // 同步播放/暂停状态
        resource.videoContext.listen('playing', () => {
            // rl8(`# ${ctx.ctxRound} | 触发 playing`);
            if (ctx.isCancellationRequested()) return;
            setTimeout(() => {
                if (ctx.isCancellationRequested()) return;
                playing.value = true;
            }, playStateUpdateDelay);
        });
        resource.videoContext.listen('pause', () => {
            // rl8(`#${ctx.ctxRound} | 触发 pause`);
            if (ctx.isCancellationRequested()) return;
            setTimeout(() => {
                if (ctx.isCancellationRequested()) return;
                playing.value = false;
            }, playStateUpdateDelay);
        });
        resource.videoContext.listen('destroy', () => {
            // rl8(`#${ctx.ctxRound} | 触发 destroy`);
            if (ctx.isCancellationRequested()) return;
            setTimeout(() => {
                if (ctx.isCancellationRequested()) return;
                playing.value = false;
            }, playStateUpdateDelay);
        });
        // resource.videoContext.listen('ended', () => {
        //     // rl8(`#${ctx.ctxRound} | 触发 ended`);
        //     if (ctx.isCancellationRequested()) return;
        //     // 播放结束后, 重新播放
        //     // togglePlay(true);
        //     setTimeout(() => {
        //         if (ctx.isCancellationRequested()) return;
        //         resource.videoContext?.play();
        //     }, playStateUpdateDelay);
        // });

        resource.videoContext.listen('error', async (error) => {
            // rl8(`#${ctx.ctxRound} | 触发 error`, error);
            if (ctx.isCancellationRequested()) return;
            // 处理 error 时发生的新错误
            let handleError = null;
            try {
                let fixed = false;
                // 发生节点丢失，可以尝试修复
                if (
                    error.data.type === 'domLost' &&
                    resource.videoContext?.fixLost
                ) {
                    fixed = await resource.videoContext.fixLost();
                    // gl8(`#${ctx.ctxRound} | fixLost() 返回`, fixed);
                    if (ctx.isCancellationRequested()) return;
                    console.warn('节点修复', fixed ? '成功' : '失败');
                }
                if (error.data.type !== 'domLost') {
                    Report.biz.error('【视频轮播】非预期错误', {
                        type: error.data.type,
                    });
                }
                if (!fixed) {
                    playStatus.value = 'error';
                    destroy();
                    Report.biz.error('【视频轮播】fixLost 失败', {});
                } else {
                    // cc
                    // pv8(resource);
                    await resource.videoContext?.resize?.();
                    await resource.videoContext?.play();
                }
            } catch (e) {
                handleError = e;
            }
            console.warn('播放器运行时发生错误', { error, handleError });
            Report.biz.error('【视频轮播】播放器运行时发生错误', {
                error,
                handleError,
            });
        });

        // pl8(`# ${ctx.ctxRound} | video context 创建成功:`, );
        // pv8(resource);
    };

    const changeSwiper = async (idx: number, headPhotoFeedData: any) => {
        await lowPowerStore.lowPromiseFunc();
        await switchPromise.switchPromise();
        swiperIdx.value = idx;
        nextTick(() => {
            create({
                videoUrl:
                    headPhotoFeedData?.photoFeeds[swiperIdx.value]?.playUrl,
            }).catch((error) => {
                Report.biz.error('【视频轮播】create 报错', { error });
            });
        });
    };

    // const toggleMuted = () => {
    //     if (!player.value) {
    //         return;
    //     }
    //     muted.value = !muted.value;
    //     player.value?.setMuted(muted.value);
    // };
    // 切换播放/暂停状态
    const togglePlay = debounce(
        async (play?: boolean) => {
            try {
                // 取回最新的资源
                const resource = resourceHistory[resourceHistory.length - 1];
                // 还没有 ready, 退出
                if (!resource) return;
                if (typeof play === 'boolean' ? !play : playing.value) {
                    await resource.videoContext?.pause();
                } else {
                    await resource.videoContext?.play();
                }
            } catch (error) {
                Report.biz.warning('【视频轮播】togglePlay 报错', { error });
            }
        },
        200,
        {
            leading: true,
            maxWait: 200,
        },
    );

    /**
     * 记录离开时的播放状态
     * 如果是 true, 表示离开时是播放状态, 离开时暂停, 后续进入时恢复
     * 如果是 false, 表示离开时是暂停状态, 恢复时, 也不做操作
     * 如果是 undefined, 直接调 onTabEnter, 为非预期调用, 直接返回。
     */
    let playingOnLeave: boolean | undefined;

    /**
     * tab 切换时不析构/重新创建播放器实例, 要不然要恢复的东西太多了, 先简单地暂停/恢复播放吧。
     */
    // # if (target === '4tab')
    // tab 离开时调用
    function onTabLeave() {
        // rl8('onTabLeave', playing.value)
        // 取回最新的资源
        const resource = resourceHistory[resourceHistory.length - 1];
        // 还没有 ready, 退出
        if (!resource) return;
        playingOnLeave = playing.value;
        if (playingOnLeave) {
            resource.videoContext?.pause();
        }
    }

    // tab 重新进入时调用
    function onTabEnter() {
        // gl8('onTabEnter', playing.value, playingOnLeave)
        // 取回最新的资源
        const resource = resourceHistory[resourceHistory.length - 1];
        // 还没有 ready, 退出
        if (!resource) return;
        if (typeof playingOnLeave !== 'boolean') return;
        if (playingOnLeave) {
            resource.videoContext?.play();
        }
        // 此次消费（离开、进入 Tab）完成，把这个变量设为 undefined
        playingOnLeave = void 0;
    }
    // # endif

    watch(
        () => downGradeSwitch.value.muted,
        (switchVal) => {
            if (switchVal) {
                // 取回最新的资源
                nextTick(() => {
                    const resource =
                        resourceHistory[resourceHistory.length - 1];
                    // 还没有 ready, 退出
                    if (!resource) return;
                    resource.videoContext?.setMuted(true);
                    Report.biz.warning('【视频轮播】命中降级-静音', {
                        kwaiVersion: getKwaiVersion(),
                    });
                });
            }
        },
        {
            immediate: true,
        },
    );

    return {
        updateContainer,
        destroy,
        create,
        playing,
        muted,
        playStatus,
        swiperIdx,
        changeSwiper,
        // toggleMuted,
        togglePlay,
        // # if (target === '4tab')
        onTabEnter,
        onTabLeave,
        // # endif
    };
});
