/* eslint-disable max-lines-per-function */
import { ref, watch } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import { invoke } from '@yoda/bridge';
import {
    usePartySwitch,
    supportNativeVisible,
} from '@pet/ones-use.usePartySwitch';
import useKconfStore from '@pet/ones-use.useKconf';
import {
    setWebLogger,
    createVideoContext,
    PlayerType,
    LogLevel,
} from '@ks/yoda-video-player';
import { CancelToken, type IVideoContext } from '@ks/yoda-video-player';
import { weblog } from '@gundam/weblogger';
import { Report, getKwaiVersion } from '@alive-ui/actions';
import type { LiveStreamData } from '@pet/ones-ui.video-live/schema/index';

// 设置 weblogger, 否则不会上报埋点
if (weblog) {
    setWebLogger(weblog);
}

interface CreatePayload {
    src: LiveStreamData;
    fallbackSrc: string;
    retry?: boolean;
    onEnded?: () => void;
}

// eslint-disable-next-line max-lines-per-function
export default defineStore('use-yoda-live', () => {
    const player = ref<IVideoContext<PlayerType.LIVE> | null>(null); // 播放器上下文
    const cancelToken = ref<CancelToken>(); // 取消player创建过程（player创建过程是异步的）
    const useH5Video = ref<boolean>(false); // 是否使用h5播放器
    const playStatus = ref<'normal' | 'ended' | 'error'>('normal');
    const mutedStateInFact = ref(false); // 是否静音
    const downGradeSwitch = usePartySwitch();
    const lock = ref(false);

    const updateLock = (val: boolean) => {
        lock.value = val;
    };

    // 设置 weblogger, 否则不会上报埋点
    // setWebLogger(webloggerInstance);

    /**
     * 异步获取当前是否网络异常
     */
    const getNetWorkErrorAsync = () => {
        return new Promise<boolean>(async (resolve) => {
            try {
                const res = await invoke('system.getNetworkType');
                // 无网情况下，IOS res.net为：'MOBILE无网络' 安卓为 “DIABLE”
                if (res.net.includes('无网络') || res.net === 'DISABLE') {
                    console.log('无网络');
                    resolve(true);
                } else {
                    resolve(false);
                }
            } catch (error) {
                resolve(false);
                console.log('请使用客户端打开');
            }
        });
    };
    /**
     * 降级相关
     */
    const showFallbackError = () => {
        playStatus.value = 'error';
    };
    const showEnded = () => {
        playStatus.value = 'ended';
    };

    const destroy = () => {
        cancelToken?.value?.cancel?.(); // 取消player创建过程，防止player异步创建过程中player?.value为空
        player?.value?.destroy?.(); // 销毁播放器js 实例和 native 实例，防止内存泄漏
    };

    const setMuted = () => {
        mutedStateInFact.value = true;
        player?.value?.setMuted?.(true);
    };

    const create = async (
        payload: CreatePayload,
    ): Promise<IVideoContext<PlayerType.LIVE> | null> => {
        const { conf4Tab } = storeToRefs(useKconfStore());

        if (downGradeSwitch.value.muted) {
            setMuted();
            Report.biz.warning('【直播流】命中降级-静音', {
                kwaiVersion: getKwaiVersion(),
            });
        }
        // TODO:使用id
        const dom = document.querySelector(`#party-live-dom`);

        if (!dom) {
            showFallbackError();
            Report.biz.error('【直播流】获取dom失败', {});
            return null;
        }
        /**
         * 初始化播放器
         */
        cancelToken.value = new CancelToken();
        const ctx = await createVideoContext<PlayerType.LIVE>({
            container: dom, // 挂载的 dom 节点
            type: PlayerType.LIVE,
            // 播放器的详细配置项
            playerConfig: {
                src: payload.src, // 后端下发的直播资源object
                fallbackSrc: payload.fallbackSrc,
                autoplay: true,
                muted: mutedStateInFact.value,
                objectFit: 'cover',
                // 在native上禁用播放器
                disableNativeControl: !conf4Tab.value?.closeAudioHotfix
                    ? true
                    : false,
            },
            plugins: [
                {
                    name: 'background',
                    args: {
                        enableOnIos: !conf4Tab.value?.closeAudioHotfix
                            ? true
                            : false,
                    },
                },
            ],
            useH5FallbackPlayer: useH5Video.value,
            cancelToken: cancelToken.value,
            logLevel: LogLevel.warn, // 控制台日志等级。详细日志使用LogLevel.log
        });

        // 如果不支持同层渲染，降级到 h5 video 播放器
        if (!ctx) {
            // useH5Video.value = true;
            // if (!payload.retry) {
            //     Report.biz.warning('【直播流】降级为 h5 播放器', {});
            //     ctx = await create({
            //         retry: true,
            //         ...payload,
            //     });
            // }
            showFallbackError();
        } else {
            player.value = ctx;
            ctx.listen('error', async (error) => {
                Report.biz.error('【直播流】播放器错误', {
                    type: error.data.type,
                });
                switch (error.data.type) {
                    case 'noMoreSource':
                        /**
                         * 资源问题：
                         * 1. 可能是资源本身有问题(or直播下播了)；
                         * 2. 或者当前网络有问题，展示兜底
                         */
                        const isNetworkError = await getNetWorkErrorAsync();
                        if (isNetworkError) {
                            /**
                             * 网络错误：展示兜底
                             */
                            showFallbackError();
                            destroy();
                        } else {
                            /**
                             * 资源问题 or 下播了：展示直播结束
                             */
                            showEnded();
                            payload.onEnded?.();
                            destroy();
                        }
                        break;
                    case 'domLost':
                        const fixResult = await ctx?.fixLost?.();
                        // 修复失败
                        if (!fixResult) {
                            showFallbackError();
                            destroy();
                        }
                        console.warn('节点修复', fixResult ? '成功' : '失败');
                        break;
                    default:
                        /**
                         * 其他error：展示兜底
                         */
                        showFallbackError();
                        destroy();
                }
                console.warn('播放器运行时发生错误', error);
            });
        }
        return ctx;
    };

    watch(
        () => [downGradeSwitch.value.muted, player.value],
        ([mutedVal, playerVal]) => {
            if (playerVal && mutedVal) {
                setMuted();
                Report.biz.warning('【直播流】命中降级-静音', {
                    kwaiVersion: getKwaiVersion(),
                });
            }
        },
    );

    return {
        player,
        destroy,
        create,
        playStatus,
        setMuted,
        showFallbackError,
        lock,
        updateLock,
    };
});
