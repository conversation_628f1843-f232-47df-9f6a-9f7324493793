import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';
export const dataFun = (contextName: symbol) => {
    const rankInfo = contextName
        ? inject<Ref<QueryRankPostResponse>>(contextName)
        : null;
    const data = computed(() => {
        return {
            promotionCount: rankInfo?.value?.promotionCount,
            promotionText: rankInfo?.value?.extraData?.promotionText,
            topNTrafficAward: rankInfo?.value?.extraData?.topNTrafficAward,
            topNTrafficAwardTips:
                rankInfo?.value?.extraData?.topNTrafficAwardTips,
            specialTip: rankInfo?.value?.extraData?.specialTip,
            addCardPrivilegeShowIndex:
                rankInfo?.value?.extraData?.addCardPrivilegeShowIndex,
            addCardPrivilegeTip:
                rankInfo?.value?.extraData?.addCardPrivilegeTip,
            showTrafficIndex: true,
        };
    });
    return data;
};
