<!-- 分割线top提示 -->
<template>
    <Divider
        v-if="
            rankShowIndex === data?.addCardPrivilegeShowIndex &&
            data?.addCardPrivilegeTip
        "
        class="promotion-divider"
        :class="needWidth ? 'width-auto' : ''"
    >
        <template #default>
            {{ data?.addCardPrivilegeTip }}
        </template>
    </Divider>
    <Divider
        v-if="rankShowIndex === data?.promotionCount && data?.promotionText"
        class="promotion-divider"
        :class="needWidth ? 'width-auto' : ''"
    >
        <template #default>
            {{ data?.promotionText }}
        </template>
    </Divider>
    <Divider
        v-if="finalTop5Tip[rankShowIndex]"
        class="promotion-divider"
        :class="needWidth ? 'width-auto' : ''"
    >
        <template #default>
            {{ finalTop5Tip[rankShowIndex]?.[0] }}
        </template>
    </Divider>
    <Divider
        v-if="
            data.showTrafficIndex &&
            trafficAward &&
            trafficAward.position === rankShowIndex
        "
        class="promotion-divider"
        :class="needWidth ? 'width-auto' : ''"
    >
        <template #default>
            <div class="line">
                <span
                    v-for="(textItem, textIndex) in trafficAward.strArray"
                    :key="textIndex + '' + textItem"
                    :class="{ 'highlight-award': textItem.highlight }"
                >
                    {{ textItem.text }}
                </span>
            </div>
        </template>
    </Divider>
    <Divider
        v-if="
            trafficAwardTipItem &&
            trafficAwardTipItem.position === rankShowIndex
        "
        class="promotion-divider"
        :class="needWidth ? 'width-auto' : ''"
    >
        <template #default>
            <div class="line">
                <span
                    v-for="(
                        textItem, textIndex
                    ) in trafficAwardTipItem.strArray"
                    :key="textIndex + '' + textItem"
                    :class="{ 'highlight-award': textItem.highlight }"
                >
                    {{ textItem.text }}
                </span>
            </div>
        </template>
    </Divider>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { Divider } from '@alive-ui/base';
import { dataFun } from './data';
import type { DividerSchema, PropsCommonParams } from './schema';
interface DividerData extends PropsCommonParams {
    data?: DividerSchema;
    rankShowIndex: number;
    needWidth?: boolean;
}
const props = withDefaults(defineProps<DividerData>(), {
    data: () => {
        return {};
    },
    rankShowIndex: 0,
    useProps: false,
    contextName: Symbol.for('ctx'),
    needWidth: false,
});
const data = props.useProps ? ref(props.data) : dataFun(props.contextName);

const formatTrafficAwardTpl = (
    timeStr: string,
    count: string,
    textTemplate: string,
) => {
    const awardStr = textTemplate
        .replace('%s', timeStr ? `%s${timeStr}%s` : '')
        .replace('%d', count ? `%s${count || ''}%s` : '');

    return awardStr
        .split('%s')
        .filter((text) => text)
        .map((text) => ({
            text,
            highlight: text === count || text === timeStr,
        }));
};

const formatNumber = (v: number) => {
    if (Number.isNaN(v)) {
        return '';
    }

    if (v < 10000) {
        return v;
    }

    return `${Math.floor(v / 10000)}w`;
};
const trafficAward = computed(() => {
    const topNTrafficAward = data.value?.topNTrafficAward;

    if (
        !topNTrafficAward ||
        Object.keys(topNTrafficAward).length === 0 ||
        !topNTrafficAward?.textTemplate
    ) {
        return null;
    }
    const { rankPointEnd, timeStr, count, textTemplate } = topNTrafficAward;
    const strArray = formatTrafficAwardTpl(
        timeStr,
        formatNumber(+count).toString(),
        textTemplate,
    );

    if (!strArray.length) {
        return null;
    }

    return {
        position: +rankPointEnd,
        strArray,
    };
});
const trafficAwardTipItem = computed(() => {
    const item = (data.value?.topNTrafficAwardTips ?? []).find(
        (tip) => +tip.rankPointEnd === props.rankShowIndex,
    );
    if (!item || Object.keys(item).length === 0 || !item?.textTemplate) {
        return null;
    }
    const { rankPointEnd, timeStr, count, textTemplate } = item;
    const strArray = formatTrafficAwardTpl(
        timeStr,
        formatNumber(+count).toString(),
        textTemplate,
    );

    if (!strArray.length) {
        return null;
    }

    return {
        position: +rankPointEnd,
        strArray,
    };
});

const finalTop5Tip = computed(() => {
    const textMap = {} as any;

    if (Array.isArray(data.value?.specialTip)) {
        data.value?.specialTip?.map((item: any) => {
            textMap[item.rank] = item?.descList;
        });
    }

    return textMap;
});
</script>
<style lang="less" scoped>
.width-auto {
    width: auto;
}
.promotion-divider {
    perspective: 1000px;
}
</style>
