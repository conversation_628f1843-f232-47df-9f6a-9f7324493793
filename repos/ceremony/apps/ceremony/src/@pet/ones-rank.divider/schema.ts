import type {
    TopNTrafficAward,
    SpecialTip,
} from '@pet/ones-rank.schema/query-rank';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
interface DividerSchema {
    promotionCount?: number;
    promotionText?: string;
    topNTrafficAward?: TopNTrafficAward;
    topNTrafficAwardTips?: TopNTrafficAward[];
    showTrafficIndex?: boolean;
    specialTip?: SpecialTip[];
    addCardPrivilegeShowIndex?: number;
    addCardPrivilegeTip?: string;
}

export type { DividerSchema, PropsCommonParams };
