import { isHonor } from './process';
import type { AuthorHonorTitlePostResponse } from '../schema';
// 名次和对应奖项的映射关系
const titleMap = {
    第1名: '冠军',
    第2名: '亚军',
    第3名: '季军',
};

// 获取荣誉称号，将第x名转换为对应的冠亚季军称号
export const getHonorTitle = (data: AuthorHonorTitlePostResponse) => {
    // 如果没有获奖的情况下，直接返回null
    if (!isHonor(data)) null;
    const title = data?.honorTitles?.find((title) => {
        // 检查是否包含任意一个名次
        return Object.keys(titleMap).some((rank) => title.includes(rank));
    });

    if (!title) return data?.honorTitles?.[0]; // 如果没找到，直接返回原始值（undefined）

    // 替换名次为对应的奖项名称
    let result = title;
    Object.entries(titleMap).forEach(([rank, honor]) => {
        if (title.includes(rank)) {
            // 将第x名替换为对应的奖项名称
            result = title.replace(rank, honor);
        }
    });
    return result;
};
