import { posConfig } from '../posConfig';
import { isProcessing, isElimination } from './process';
import type { Ref } from 'vue';
import type { AuthorHonorTitlePostResponse } from '../schema';

export enum ECompetitionSceneType {
    // 进行中
    Processing = 'scene1',
    // 淘汰
    Elimination = 'scene2',
    // 获奖
    Honor = 'scene3',
}

export const competitionSloganImgUrlMap = {
    [`${ECompetitionSceneType.Processing}_1`]: 'picture_1@2x',
    [`${ECompetitionSceneType.Processing}_2`]: 'picture_2@2x',
    [`${ECompetitionSceneType.Processing}_3`]: 'picture_3@2x',
    [`${ECompetitionSceneType.Processing}_4`]: 'picture_4@2x',
    [`${ECompetitionSceneType.Processing}_5`]: 'picture_5@2x',
    [`${ECompetitionSceneType.Processing}_6`]: 'picture_6@2x',
    [`${ECompetitionSceneType.Processing}_7`]: 'picture_7@2x',
    [`${ECompetitionSceneType.Processing}_8`]: 'picture_8@2x',
    [`${ECompetitionSceneType.Elimination}_1`]: 'picture_21@2x',
    [`${ECompetitionSceneType.Elimination}_2`]: 'picture_22@2x',
    [`${ECompetitionSceneType.Elimination}_3`]: 'picture_23@2x',
    [`${ECompetitionSceneType.Elimination}_4`]: 'picture_24@2x',
    [`${ECompetitionSceneType.Honor}_1`]: 'picture_11@2x',
    [`${ECompetitionSceneType.Honor}_2`]: 'picture_12@2x',
    [`${ECompetitionSceneType.Honor}_3`]: 'picture_13@2x',
    [`${ECompetitionSceneType.Honor}_4`]: 'picture_14@2x',
    [`${ECompetitionSceneType.Honor}_5`]: 'picture_15@2x',
};

// 生成从minNum到maxNum的随机数
export const randomNum = function (minNum: number, maxNum: number) {
    switch (arguments.length) {
        case 1:
            // eslint-disable-next-line radix
            return parseInt(String(Math.random() * minNum + 1), 10);
        case 2:
            // eslint-disable-next-line radix
            return parseInt(
                String(Math.random() * (maxNum - minNum + 1) + minNum),
                10,
            );
        default:
            return 0;
    }
};

export const drawSloganImg = async (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
) => {
    return new Promise(async (resolve, reject) => {
        const _canvas = canvasRef.value;
        const ctx = _canvas.getContext('2d');
        const bottomImg = new Image();
        bottomImg.setAttribute('crossOrigin', 'Anonymous');
        let textSrc = '';

        if (isProcessing(data)) {
            textSrc = (
                await import(
                    `../imgs/${competitionSloganImgUrlMap[`${ECompetitionSceneType.Processing}_${randomNum(1, 8)}` as keyof typeof competitionSloganImgUrlMap]}.png`
                )
            ).default;
        } else if (isElimination(data)) {
            textSrc = (
                await import(
                    `../imgs/${competitionSloganImgUrlMap[`${ECompetitionSceneType.Elimination}_${randomNum(1, 4)}` as keyof typeof competitionSloganImgUrlMap]}.png`
                )
            ).default;
        } else {
            textSrc = (
                await import(
                    `../imgs/${competitionSloganImgUrlMap[`${ECompetitionSceneType.Honor}_${randomNum(1, 5)}` as keyof typeof competitionSloganImgUrlMap]}.png`
                )
            ).default;
        }
        bottomImg.src = textSrc;
        const { x, y, imgHeight, imgWidth } = posConfig.bottomImgPos;

        bottomImg.onload = () => {
            if (ctx) {
                ctx.save();
                ctx.drawImage(bottomImg, x, y, imgWidth, imgHeight);
                ctx.restore();
                resolve(canvasRef);
            } else {
                console.log('drawSloganImg 不存在');
                // eslint-disable-next-line prefer-promise-reject-errors
                reject('canvas 不存在');
            }
        };

        bottomImg.onerror = () => {
            console.log('标语图片加载失败');
            // eslint-disable-next-line prefer-promise-reject-errors
            reject('标语图片加载失败');
        };
    });
};
