import { bolIsAuthor } from '@alive-ui/actions';
import { posConfig } from '../posConfig';
import type { Ref } from 'vue';
import type { AuthorHonorTitlePostResponse } from '../schema';

function drawTextTips(
    canvasRef: Ref<HTMLCanvasElement>,
    txt: string,
    y: number,
) {
    const canvas = canvasRef.value;
    const ctx = canvas.getContext('2d');
    const { lineHeight, fontSize, x } = posConfig.tipsPos;
    const y_middle = y + lineHeight / 2;

    if (ctx) {
        ctx.font = `400 ${fontSize}px PingFangSC, PingFangSC-Regular`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#ffffff';
        ctx.fillText(txt, x, y_middle);
    }
}

export const drawAssistText = (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
) => {
    const { lineHeight, y } = posConfig.tipsPos;

    if (bolIsAuthor) {
        drawTextTips(canvasRef, '我正在参加2025快手直播夏季盛典', y);
        drawTextTips(canvasRef, '请大家为我助力', y + lineHeight);
    } else {
        drawTextTips(canvasRef, '我支持的主播正在参加2025快手直播夏季盛典', y);
        drawTextTips(canvasRef, '快来一起为TA助力', y + lineHeight);
    }

    return Promise.resolve();
};
