import type { AuthorHonorTitlePostResponse } from '../schema';

// 参与中
export const isProcessing = (data: AuthorHonorTitlePostResponse) => {
    const { processing } = data;

    return processing;
};

// 已淘汰 & 未获奖
export const isElimination = (data: AuthorHonorTitlePostResponse) => {
    const { processing, honorTitles } = data;

    return (
        !processing &&
        (honorTitles === null || (honorTitles && honorTitles.length === 0))
    );
};

// 已获奖
export const isHonor = (data: AuthorHonorTitlePostResponse) => {
    const { processing, honorTitles } = data;

    return !processing && honorTitles?.length > 0;
};
