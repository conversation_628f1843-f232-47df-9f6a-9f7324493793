import useKconf from '@pet/ones-use.useKconfBatch';
import { posConfig } from '../posConfig';
import { isHonor, isElimination, isProcessing } from './process';
import { getHonorTitle } from './getHonorTitle';
import { findBg } from './findBg';
import type { Ref } from 'vue';
import type { AuthorHonorTitlePostResponse } from '../schema';

// 添加新的绘制函数
export const drawTextNew = async (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
    isChampion?: boolean,
) => {
    return new Promise((resolve) => {
        const ctx = canvasRef.value.getContext('2d');
        if (!ctx) {
            console.error('canvas 上下文不存在');
            resolve(null);
            return;
        }

        const {
            x,
            y,
            paddingx,
            paddingy,
            fontSize,
            x_honor,
            y_honor,
            fontSize_honor,
        } = posConfig.newTextPos;
        const text = getHonorTitle(data) || '';
        // 根据用户状态确定显示文本
        let honorText = '';
        if (isElimination(data)) {
            honorText = '';
        } else if (isHonor(data) && isChampion) {
            honorText = '恭喜获得';
        } else if (isProcessing(data)) {
            honorText = '正在冲刺';
        } else {
            honorText = '';
        }
        const { curStageType } = data;
        const kconfStore = useKconf();

        // 参赛状态绘制
        if (findBg(kconfStore.kconfData?.common?.stageTypeList, curStageType)) {
            // 绘制文本
            ctx.font = `${fontSize_honor}px PingFangSC, PingFangSC-Medium`;
            ctx.fillStyle = '#FAEAD3';
            ctx.textBaseline = 'middle';
            ctx.fillText(honorText, x_honor, y_honor);
        }

        // 获奖标签绘制
        if (
            text &&
            text !== '' &&
            findBg(kconfStore.kconfData?.common?.stageTypeList, curStageType) &&
            isChampion
        ) {
            // 设置字体
            ctx.font = `${fontSize}px PingFangSC, PingFangSC-Medium`;

            // 测量文本宽度
            const textMetrics = ctx.measureText(text);
            const textWidth = textMetrics.width;
            const textHeight = fontSize;

            // 计算文本框总宽高（包含padding）
            const boxWidth = textWidth + paddingx * 2;
            const boxHeight = textHeight + paddingy * 2;

            // 开始绘制圆角矩形背景
            ctx.beginPath();
            // 从左上角开始，顺时针绘制
            ctx.moveTo(x + 10, y); // 移动到左上角圆弧的起点
            ctx.lineTo(x + boxWidth - 10, y); // 上边
            ctx.arc(x + boxWidth - 10, y + 10, 10, -Math.PI / 2, 0); // 右上角圆弧
            ctx.lineTo(x + boxWidth, y + boxHeight - 10); // 右边
            ctx.arc(x + boxWidth - 10, y + boxHeight - 10, 10, 0, Math.PI / 2); // 右下角圆弧
            ctx.lineTo(x + 10, y + boxHeight); // 下边
            ctx.arc(x + 10, y + boxHeight - 10, 10, Math.PI / 2, Math.PI); // 左下角圆弧
            ctx.lineTo(x, y + 10); // 左边
            ctx.arc(x + 10, y + 10, 10, Math.PI, -Math.PI / 2); // 左上角圆弧

            // 填充背景色
            ctx.fillStyle = '#FFE2BB';
            ctx.fill();

            // 绘制文本
            ctx.fillStyle = '#521600';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + paddingx, y + boxHeight / 2);
            resolve({
                width: boxWidth,
                height: boxHeight,
            });
        } else {
            resolve(null);
        }
    });
};
