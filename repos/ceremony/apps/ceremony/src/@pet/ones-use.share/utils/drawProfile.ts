import useKconf from '@pet/ones-use.useKconfBatch';
import { posConfig } from '../posConfig';
import type { Ref } from 'vue';
import type { AuthorHonorTitlePostResponse } from '../schema';

// 海报用户默认头像
const DEFAULT_AVATAR =
    'https://p4-live.wskwai.com/kos/nlav112317/spring-2025-assets/poster/default-avatar.688ceb81c216c731.png';

export const drawProfile = (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
) => {
    return new Promise((resolve, reject) => {
        const kconfStore = useKconf();
        const { authorInfo } = data;
        const _canvas = canvasRef.value;
        const ctx = _canvas.getContext('2d');
        const img = new Image();
        img.setAttribute('crossOrigin', 'Anonymous');
        img.src = authorInfo.headUrl || DEFAULT_AVATAR;

        img.onload = () => {
            if (ctx) {
                const { x, y, x_side, y_side, size, side, border } =
                    posConfig.profilePos;
                // 头像边框
                ctx.beginPath();
                ctx.fillStyle = '#FFD7B2';
                ctx.arc(
                    x + size / 2,
                    y + size / 2,
                    border + size / 2,
                    0,
                    2 * Math.PI,
                );
                ctx.fill();
                // 头像
                ctx.beginPath();
                ctx.arc(x + size / 2, y + size / 2, size / 2, 0, 2 * Math.PI);
                ctx.clip();
                console.log(666, x, y, img);
                ctx.drawImage(img, x, y, size, size);
                ctx.restore();
                // 画头像奖励框
                const awardImg = new Image();
                awardImg.setAttribute('crossOrigin', 'Anonymous');
                awardImg.src = kconfStore.kconfData?.common?.avatarFrame;

                awardImg.onload = () => {
                    ctx.drawImage(awardImg, x_side, y_side, side, side);
                    ctx.restore();
                };

                // 用户名
                const { nickPos } = posConfig;
                const { lineHeight, fontSize } = nickPos;
                const xName = nickPos.x; // canvasWidth / 2 设置水平居中
                const yName = nickPos.y + lineHeight / 2;
                ctx.font = `${fontSize}px PingFangSC,PingFangSC-Medium`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillStyle = '#ffffff';
                ctx.beginPath();
                ctx.fillText(authorInfo.userName, xName, yName);
                ctx.restore();
                resolve(canvasRef);
            } else {
                console.log('drawProfile onload reject');
                // eslint-disable-next-line prefer-promise-reject-errors
                reject('canvas 不存在');
            }
        };

        img.onerror = () => {
            console.log('drawProfile onerror reject');
            if (img.src !== DEFAULT_AVATAR) {
                img.src = DEFAULT_AVATAR;
            } else {
                // eslint-disable-next-line prefer-promise-reject-errors
                resolve(canvasRef);
            }
        };
    });
};
