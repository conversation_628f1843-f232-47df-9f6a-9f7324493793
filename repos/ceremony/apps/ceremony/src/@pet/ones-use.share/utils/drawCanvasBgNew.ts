import useKconf from '@pet/ones-use.useKconfBatch';
import { posConfig } from '../posConfig';
import { isElimination, isHonor } from './process';
import { findBg } from './findBg';
import type { Ref } from 'vue';
import type { AuthorHonorTitlePostResponse } from '../schema';

const canvasWidth = posConfig.posterSize.width;
const canvasHeight = posConfig.posterSize.height;

export const drawCanvasBgNew = (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
    isChampion?: boolean,
) => {
    const kconfStore = useKconf();

    return new Promise((resolve, reject) => {
        const _canvas = canvasRef.value;
        const ctx = _canvas.getContext('2d');
        let { curStageType } = data;
        console.log('curStageType', curStageType, 1);

        if (isHonor(data) && !isChampion) {
            curStageType = 9999;
        } else if (isElimination(data)) {
            curStageType = 999;
        }
        console.log('curStageType', curStageType, 2);
        const img: any = new Image();
        img.setAttribute('crossOrigin', 'Anonymous');
        img.src =
            findBg(kconfStore.kconfData?.common?.stageTypeList, curStageType) ||
            kconfStore.kconfData?.common?.competitionBgUrlDefault;

        img.onload = () => {
            if (ctx) {
                ctx.save();
                // ctx.drawImage(img, 0, 0, _canvas.width, _canvas.height);
                ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
                // ctx.drawImage(img, 0, 0, 414, 736);
                resolve(canvasRef);
            } else {
                console.log('drawCanvasBg onload reject');
                // eslint-disable-next-line prefer-promise-reject-errors
                reject('canvas 不存在');
            }
        };

        img.onerror = () => {
            console.log('drawCanvasBg onerror reject');
            // eslint-disable-next-line prefer-promise-reject-errors
            reject('背景图加载失败');
        };
    });
};
