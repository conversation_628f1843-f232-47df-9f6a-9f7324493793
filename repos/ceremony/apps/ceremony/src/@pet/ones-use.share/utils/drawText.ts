import { posConfig } from '../posConfig';
import { isProcessing, isElimination, isHonor } from './process';
import type { Ref } from 'vue';
import type { AuthorHonorTitlePostResponse } from '../schema';

// eslint-disable-next-line @typescript-eslint/default-param-last
const drawTitleText = (
    canvasRef: Ref<HTMLCanvasElement>,
    title = '恭喜主播荣获',
    isHonor = false,
) => {
    const _canvas = canvasRef.value;
    const ctx = _canvas.getContext('2d');

    const { lineHeight, x, y, y_honor, fontSize } = posConfig.titlePos;
    const useY = isHonor ? y_honor : y; //  这里已获奖只展示两行文案，整体向上移动10
    const y_middle = useY + lineHeight / 2;

    if (ctx) {
        ctx.restore();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = `400 ${fontSize}px PingFangSC, PingFangSC-Regular`;
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.fillText(title || '恭喜主播荣获', x, y_middle);
    }
};

const drawSubTitleText = (
    canvasRef: Ref<HTMLCanvasElement>,
    text = '主播正在参加夏季盛典',
) => {
    const _canvas = canvasRef.value;
    const ctx = _canvas.getContext('2d');
    const { lineHeight, fontSize, x, y } = posConfig.subTitlePos;
    const y_middle = y + lineHeight / 2;

    if (ctx) {
        ctx.restore();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = `500 ${fontSize}px PingFangSC, PingFangSC-Regular`;
        ctx.fillStyle = '#ffffff';
        ctx.beginPath();
        ctx.fillText(text, x, y_middle);
    }
};

const drawHonorTitlesText = (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
) => {
    const _canvas = canvasRef.value;
    const ctx = _canvas.getContext('2d');
    const {
        lineHeight,
        x,
        y_scene1,
        y_scene3,
        fontSizeL,
        fontSizeM,
        fontSizeS,
    } = posConfig.honorTitlePos;
    const y_middle = (isHonor(data) ? y_scene3 : y_scene1) + lineHeight / 2;
    let titleStr = '';

    if (data.processing && data.curRank) {
        titleStr = data.curRank;
    } else {
        // titleStr = data?.honorTitles?.length > 1 ? data?.honorTitles?.map(item => '【' + item + '】').join('') : data?.honorTitles![0];
        const txt = data?.honorTitles?.length >= 1 ? data?.honorTitles[0] : '';
        titleStr = sessionStorage.getItem('item') || txt;
    }

    let finalFontSize = fontSizeL;

    if (titleStr.length > 16 && titleStr.length <= 22) {
        finalFontSize = fontSizeM;
    } else if (titleStr.length > 22) {
        finalFontSize = fontSizeS;
    }

    if (ctx) {
        ctx.restore();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        // sceneName === 'scene3' 等价于 data?.honorTitles.length > 1 和后端约定
        ctx.font = `bolder ${finalFontSize}px PingFangSC, PingFangSC-Semibold`;
        const grd = ctx.createLinearGradient(
            0,
            y_scene1,
            0,
            y_scene1 + lineHeight,
        );
        grd.addColorStop(0, '#FFFCF6');
        // grd.addColorStop(0.11, '#fff');
        grd.addColorStop(1, '#FFD59C');
        ctx.fillStyle = grd;
        ctx.beginPath();
        ctx.fillText(titleStr || '', x, y_middle);
    }
};

export const drawText = (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
) => {
    if (isProcessing(data)) {
        drawTitleText(canvasRef, data.title, true);
        drawSubTitleText(canvasRef, data.subTitle || '当前排名');
        drawHonorTitlesText(canvasRef, data);
    } else if (isHonor(data)) {
        drawTitleText(canvasRef, data.title);
        // drawSubTitleText(canvasRef, data.subTitle || '恭喜主播荣获');
        drawHonorTitlesText(canvasRef, data);
    } else if (isElimination(data)) {
        drawTitleText(canvasRef, '主播正在参加夏季盛典');
    }

    return Promise.resolve();
};
