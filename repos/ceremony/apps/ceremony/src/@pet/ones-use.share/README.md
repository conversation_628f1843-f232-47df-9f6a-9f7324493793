# SharePoster 组件文档

## 功能概述

`SharePoster` 是一个用于生成并分享海报的功能模块。它允许用户自定义绘制海报的内容，并提供了回调接口以便在分享完成后执行特定操作。该模块利用 `@pet/ones-ui.SharePoster` 和 `@alive-ui/system` 等库来实现海报的生成和分享功能。

## 属性和方法

### ShareOptions 接口

- **onShareCompleted** (`VoidFunction`): 可选。分享完成后的回调函数。
- **customDrawCanvas** (`VoidFunction`): 可选。自定义绘制海报的函数。默认使用 `useDrawCanvas`。
- **customPosterData** (`(AuthorHonorTitlePostResponse) => any`): 可选。用于修改海报数据的函数。

### 方法

- **shareFactory** (`(options?: ShareOptions) => Promise<Function>`): 创建一个分享海报的工厂函数。
  - **参数**: 
    - `options` (`ShareOptions`): 配置选项。
  - **返回值**: 一个异步函数，接受 `AuthorHonorTitlePostResponse` 类型的数据并生成海报。

- **toShare** (`(options?: ShareOptions) => Promise<void>`): 执行分享操作。
  - **参数**: 
    - `options` (`ShareOptions`): 配置选项。

## 使用示例

### 创建分享海报

```javascript
import { shareFactory } from '@pet/ones-use.share';

const options = {
    onShareCompleted: () => {
        console.log('分享完成');
    },
    customDrawCanvas: (canvasRef, posterData) => {
        // 自定义绘制逻辑
    },
    customPosterData: (data) => {
        // 修改海报数据
        return { ...data, title: '自定义标题' };
    }
};

const share = await shareFactory(options);
await share({
    title: '原始标题',
    content: '海报内容'
});
```

### 执行分享操作

```javascript
import { toShare } from '@pet/ones-use.share';

const options = {
    onShareCompleted: () => {
        console.log('分享完成');
    }
};

await toShare(options);
```

## 注意事项

1. 在 PC 调试此功能时，需要屏蔽 `KsCanvas.close()` 和 `.ks-canvas { display: none; }`。
2. 在手机 staging 环境下，需要设置 app 中台服务器为 `zt.staging.kuaishou.com`。
3. 分享操作会触发加载动画，确保在调用 `toShare` 之前没有其他加载动画正在显示。

## 依赖项

- `@pet/ones-ui.SharePoster`
- `@alive-ui/system`
- `@alive-ui/actions`
- `@pet/ones-ui.SharePoster/loading`
- `vue`
- `@/common/logger`

## 海报埋点

### 事件类型

- **save**: 保存本地
- **shareAny/qq**: QQ好友
- **shareAny/wechat**: 微信好友
- **shareAny/wechatMoments**: 微信朋友圈

### 事件处理

```javascript
const sendUserAction = (e: SharePosterEvent) => {
    sendClick({
        action: 'OP_ACTIVITY_SHARE_SELECT_CARD',
        params: {
            type: typeMap[e.actionKey],
            live_stream_id: query.liveStreamId,
            author_id: query.authorId,
        },
    });
};

const sharehander = (e: any) => {
    console.log(e);
    console.log('event native_share_dialog_event');

    switch (e.eventId) {
        case 'panel_show': // 海报show
            sendShow({
                action: 'OP_ACTIVITY_SHARE_CARD',
                params: {},
            });
            break;
        case 'user_select': // 用户点击某个渠道
            sendUserAction(e);
            break;
        default:
            break;
    }
};

addListener('native_share_dialog_event', sharehander).catch(
    (err: Record<string, any>) => {
        console.log(err);
    },
);
```