import { authorId, activityBiz } from '@alive-ui/actions';
import type { AuthorHonorTitlePostResponse } from '../schema';
import { request } from '@/common/http/index';
const PATH = {
    url1: '/webapi/live/revenue/operation/activity/yearendcere/authorHonorTitle',
    url2: '/rest/wd/live/plutus/audienceTask/completed',
};

export const getCeremonyGasStationInfo = async (liveStreamId: string) => {
    try {
        const res = await request.post<AuthorHonorTitlePostResponse>(
            PATH.url1,
            {
                liveStreamId,
                plutusBiz: activityBiz,
            },
        );
        return Promise.resolve(res.data);
    } catch (e) {
        return Promise.reject(e);
    }
};

export const completed = async (
    typeKey: string,
    completeAuthorId: string | number = authorId,
) => {
    try {
        const res = await request.post<boolean>(PATH.url2, {
            authorId: completeAuthorId || authorId,
            typeKey,
            biz: activityBiz, // 需传此字段以区分不同业务
        });
        return Promise.resolve(res.data);
    } catch (e) {
        return Promise.reject(e);
    }
};
