/**
 * 响应: 盛典加油站接口
 */
export interface AuthorHonorTitlePostResponse {
    subTitle: string;
    authorInfo: AuthorInfo;
    curRank: string;
    /**
     * 2：地区赛；3: 品类赛； 4：总决赛 5：人气赛 （0: 淘汰获奖 -1: 淘汰 前端自己处理的）
     */
    curStageType: number;
    honorTitles: string[];
    /**
     * 是否还在赛程中
     */
    processing: boolean;
    text: string;
    title: string;
}

export interface AuthorInfo {
    /**
     *
     * blobstore-nginx.staging.kuaishou.com/uhead/AB/2021/10/25/16/BMjAyMTEwMjUxNjAyMjZfMjE4NjY2NDA1M18yX2hkNzEyXzA=.jpg"
     */
    headUrl: string;
    userId: number;
    userName: string;
}
