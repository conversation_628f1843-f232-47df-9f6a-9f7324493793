const posterRatio = 2.5;

const posterSize = {
    width: 414,
    height: 736,
};

const profilePos = {
    x: 157,
    y: 183.5,
    x_side: 142,
    y_side: 169,
    size: 100,
    side: 130,
    border: 1,
};
const newProfilePos = {
    x: 47,
    y: 199,
    x_side: 30,
    y_side: 180,
    size: 100,
    side: 130,
    border: 1,
};
// 用户昵称往下2像素
const nickPos = {
    x: posterSize.width / 2,
    y: 310,
    lineHeight: 28,
    fontSize: 20,
};
// 25夏季新样式海报昵称位置
const newNickPos = {
    x: 186,
    y: 220,
    lineHeight: 28,
    fontSize: 23,
};
const SpringOffsetY = 30;
// eg. 主播正在冲刺北京赛区10强
const subTitlePos = {
    x: posterSize.width / 2,
    y: 544 + SpringOffsetY,
    lineHeight: 21,
    fontSize: 16,
};
// 恭喜主播获得、当前排名
const titlePos = {
    x: posterSize.width / 2,
    y: 544 + SpringOffsetY,
    y_honor: 515 + SpringOffsetY,
    lineHeight: 20,
    fontSize: 16,
};
// 大字
const honorTitlePos = {
    x: posterSize.width / 2,
    y_scene1: 573 + SpringOffsetY,
    y_scene3: 573 + SpringOffsetY,
    lineHeight: 28,
    fontSizeL: 24,
    fontSizeM: 18,
    fontSizeS: 15,
};

const tipsPos = {
    x: posterSize.width / 2,
    y: 821,
    lineHeight: 24,
    fontSize: 13,
};
const bottomImgPos = {
    x: 0,
    y: 10,
    imgHeight: 142,
    imgWidth: 414,
};
const newBottomImgPos = {
    x: 0,
    y: 0,
    imgHeight: 142,
    imgWidth: 414,
};
// 新布局提示标签
const newTextPos = {
    x: 184,
    y: 275,
    lineHeight: 24,
    paddingx: 7,
    paddingy: 3,
    fontSize: 16,
    x_honor: 184,
    y_honor: 258,
    fontSize_honor: 16,
};

const rawConfig = {
    posterSize,
    profilePos,
    nickPos,
    titlePos,
    subTitlePos,
    honorTitlePos,
    tipsPos,
    bottomImgPos,
    newBottomImgPos,
    newProfilePos,
    newNickPos,
    newTextPos,
};

const dealRatio: <T extends Record<string, any>>(config: T) => T = (config) => {
    const _config = { ...config };

    Object.keys(config as any).forEach((key: string) => {
        const item = config[key];

        Object.keys(item).forEach((key2: string) => {
            _config[key][key2] = item[key2] * posterRatio;
        });
    });

    return _config;
};

const posConfig = dealRatio(rawConfig);

export { posterRatio, posConfig };
