/* eslint-disable @typescript-eslint/no-explicit-any */
import ksLoading from '@pet/ones-ui.SharePoster/loading/index';
import KsShare from '@pet/ones-ui.SharePoster';
import { invoke, addListener } from '@alive-ui/system';
import { liveStreamId, query } from '@alive-ui/actions';
import { useDrawCanvas } from './useDrawCanvas';
import { getCeremonyGasStationInfo, completed } from './service/index';
import { posConfig, posterRatio } from './posConfig';
import type { Ref } from 'vue';
import type { AuthorHonorTitlePostResponse } from './schema';
import { sendShow, sendClick } from '@/common/logger';

export interface ShareOptions {
    onShareCompleted?: VoidFunction;
    // 可以参考useDrawCanvas自定义绘制函数
    customDrawCanvas?: VoidFunction;
    // 提供修改海报数据能力
    customPosterData?: (response: AuthorHonorTitlePostResponse) => any;
    // 是否是挂榜请求分享
    isChampion?: boolean;
}

// const ksLoading = KsLoading();
const shareTaskKey = 'sharePoster';

// eslint-disable-next-line @typescript-eslint/require-await
export const shareFactory = async (options?: ShareOptions) => {
    const {
        onShareCompleted,
        customDrawCanvas,
        customPosterData,
        isChampion = false,
    } = options || {};

    return async (data: AuthorHonorTitlePostResponse) => {
        const emitShare = await KsShare.createShareFactory({
            canvasProps: {
                width: posConfig.posterSize.width,
                height: posConfig.posterSize.height,
                ratio: posterRatio,
                useCanvas: async (
                    canvasRef: Ref<HTMLCanvasElement | undefined>,
                ) => {
                    const drawCanvas = customDrawCanvas
                        ? customDrawCanvas
                        : useDrawCanvas;
                    const posterData = customPosterData
                        ? customPosterData(data)
                        : data;

                    canvasRef.value &&
                        (await drawCanvas(
                            canvasRef as Ref<HTMLCanvasElement>,
                            posterData,
                            isChampion,
                        ));
                },
            },
            shareHandler: async (imageBytes: string) => {
                return new Promise((resolve, reject) => {
                    invoke('social.share', {
                        param: {
                            subBiz: 'KS_CNYZB_PVP', // pm申请  @pm
                            // subBiz: 'CNY_WISHROOM_C', // 临时测试用
                            shareObjectId: 'userId', //  唯一标识一个活动
                            showSharePanel: true, //   不拉起面板的时候，也就是 showSharePanel 传false 的时候，会走到新海报协议里，这时候支持不加二维码
                            // shareDirectActionUrl: "kwaishare://shareAny/wechat",
                            posterConfigs: {
                                posterImageBytes: imageBytes, // 或者通过posterImageBytes 传入base64
                                posterImageAspectRatio: 0.5652,
                                qrImageAspectRatio: 1,
                                // qrImageRelativeY: 0,
                                qrImageRelativeX: '0',
                                qrImageRelativeWidth: '0.0001',
                            },
                            // shareInitConfigs: {
                            //     extInitPosterParams: { // 海报的布局
                            //         absoluteTopMargin: 45,
                            //         absoluteBottomMargin: 45,
                            //         relativeWidth: 0.75,
                            //     },
                            //     // extTokenStoreParams: {
                            //     //     wishId,
                            //     //     userId,
                            //     //     liveStreamId,
                            //     // },
                            //     // 站内地址： 分享到私信，点击的跳转地址     站外地址:只写path， 中台补充域名. 和（落地页地址）差不多，或者提供一个curl给中台，帮助配置
                            //     // 测试域名: 使用中台落脚页，由中台提供
                            //     extInitPainterParams: {
                            //         type: 'short_pic', // 不能滚 固定传这个值
                            //         imageBytes, // social-share-bridge   base64图片
                            //         qrImageRelativeY: 0,
                            //         qrImageRelativeX: 0,
                            //         qrImageRelativeWidth: 0.0001,
                            //     },
                            // },
                            // extAnyPainterParams: {
                            //     type: 'short_pic',
                            //     imageBytes,
                            //     qrImageRelativeY: 0,
                            //     qrImageRelativeX: 0,
                            //     qrImageRelativeWidth: 0.0001,
                            // },
                        },
                    })
                        .then(async (res: any) => {
                            console.log('分享调用成功 - res', res.result);

                            await completed(shareTaskKey).catch((err: any) => {
                                console.log('分享调用失败 - err');
                                reject(err);
                            });

                            if (
                                onShareCompleted &&
                                Object.prototype.toString
                                    .call(onShareCompleted)
                                    .slice(8, -1) === 'function'
                            ) {
                                onShareCompleted();
                            }
                            resolve(res);
                        })
                        .catch((err: any) => {
                            console.log('分享调用失败 - err', err);
                            reject(err?.error_msg);
                        });
                });
            },
        });
        emitShare();
    };
};

// 在pc调试此功能需要 1、屏蔽 // KsCanvas.close(); 2、屏蔽 .ks-canvas { display: none; }
// 手机staging需要设置app中台服务器 zt.staging.kuaishou.com

export const toShare = async (options?: ShareOptions) => {
    try {
        if (ksLoading.visible.value) {
            return;
        }

        ksLoading.show();
        const data = await getCeremonyGasStationInfo(liveStreamId);
        console.log('分享数据-start', data);

        const share = await shareFactory(options);
        await share(data);
        ksLoading.hide();
    } catch (error) {
        ksLoading.hide();
    }
};
/*
 * 海报埋点相关
 * https://docs.corp.kuaishou.com/k/home/<USER>/fcAAo2eiCEllG3GLx7QTubNiQ#section=h.72fbi87dzi33
 */

type SharePosterEvent = {
    eventId: string;
    kpn: string;
    actionKey: string;
    actionUrl: string;
    extraInfo: unknown;
};
const typeMap: Record<string, string> = {
    save: '保存本地',
    'shareAny/qq': 'QQ好友',
    'shareAny/wechat': '微信好友',
    'shareAny/wechatMoments': '微信朋友圈',
};

const sendUserAction = (e: SharePosterEvent) => {
    sendClick({
        action: 'OP_ACTIVITY_SHARE_SELECT_CARD',
        params: {
            type: typeMap[e.actionKey],
            live_stream_id: query.liveStreamId,
            author_id: query.authorId,
        },
    });
};

const sharehander = (e: any) => {
    console.log(e);
    console.log('event native_share_dialog_event');

    switch (e.eventId) {
        case 'panel_show': // 海报show
            sendShow({
                action: 'OP_ACTIVITY_SHARE_CARD',
                params: {},
            });
            break;
        case 'user_select': // 用户点击某个渠道
            sendUserAction(e);
            break;
        default:
            break;
    }
};

addListener('native_share_dialog_event', sharehander).catch(
    (err: Record<string, any>) => {
        console.log(err);
    },
);
