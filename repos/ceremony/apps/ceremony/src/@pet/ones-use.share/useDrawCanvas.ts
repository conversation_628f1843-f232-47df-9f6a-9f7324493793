/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import {
    drawCanvasBg,
    drawProfile,
    drawText,
    drawSloganImg,
    drawAssistText,
    drawProfileNew,
    drawSloganImgNew,
    drawTextNew,
    drawCanvasBgNew,
} from './utils';
import type { Ref } from 'vue';
import type { AuthorHonorTitlePostResponse } from './schema';
// 模块具体参考https://docs.corp.kuaishou.com/k/home/<USER>/fcAAncgkolrbcL1kkXlBDX91U
const genShareImgData = async (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
) => {
    // 绘制整体背景
    await drawCanvasBg(canvasRef, data);
    // 绘制用户信息
    await drawProfile(canvasRef, data);
    // 绘制参赛状态
    await drawText(canvasRef, data);
    // 绘制口号图片
    await drawSloganImg(canvasRef, data);
    // // 绘制助力文案
    // await drawAssistText(canvasRef, data);
};

const newGenShareImgData = async (
    canvasRef: Ref<HTMLCanvasElement>,
    data: AuthorHonorTitlePostResponse,
    isChampion?: boolean,
) => {
    // 绘制整体背景
    await drawCanvasBgNew(canvasRef, data, isChampion);
    // 绘制用户信息
    await drawProfileNew(canvasRef, data);
    // 绘制参赛状态
    await drawTextNew(canvasRef, data, isChampion);
    // 绘制口号图片
    await drawSloganImgNew(canvasRef, data);
};

export const useDrawCanvas = async (
    canvasRef: Ref<HTMLCanvasElement>,
    shareData: AuthorHonorTitlePostResponse,
    isChampion?: boolean,
) => {
    if (canvasRef.value) {
        const { curStageType } = shareData;
        const kconfStore = useKconfStore();
        const { kconfData } = storeToRefs(kconfStore);
        const isHaveNewBg = (type: number) => {
            return (
                kconfData.value?.common?.stageGroup?.shareBgGroup.indexOf(
                    type,
                ) === -1
            );
        };
        if (isHaveNewBg(curStageType)) {
            await newGenShareImgData(canvasRef, shareData, isChampion);
        } else {
            await genShareImgData(canvasRef, shareData);
        }
    } else {
        console.log('useDrawCanvas 找不到canvas');
        console.error('找不到canvas');
    }
};
