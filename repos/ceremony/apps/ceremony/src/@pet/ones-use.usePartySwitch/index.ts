/* eslint-disable max-lines-per-function */
import { ref, watch } from 'vue';
import { storeToRefs, defineStore } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconf';
import * as Scene from '@pet/ones-use.is4Tab';
import {
    isAndroid,
    isGteVersion,
    isIOS,
    osName,
    getKwaiVersion,
} from '@alive-ui/system';

export const SceneTypeMap = {
    FourTab: '4tab',
    SearchTab: '-1tab',
    Live: 'live',
    Share: 'share',
    AllApp: 'allApp',
    SearchBanner: 'banner',
    Android: 'android',
    IOS: 'ios',
} as const;

type SceneValType = (typeof SceneTypeMap)[keyof typeof SceneTypeMap];

export type SwitchType = {
    pageFallback: boolean;
    muted: boolean;
    liveDowngradeSwitch: boolean;
    videoDowngradeSwitch: boolean;
    jinLiJumpDowngradeSwitch: boolean;
    jinLiApiDowngradeSwitch: boolean;
    videoCardDowngradeSwitch: boolean;
    popCloseSwitch: boolean;
    showBack: boolean;
    mutedVersionList: {
        android: {
            lower: string;
            equal: string[];
        };
        ios: {
            lower: string;
            euqal: string[];
        };
    };
};

export const MutedVersionList = {
    android: {
        lower: '11.4.40',
        equal: [],
    },
    ios: {
        lower: '11.10.20',
        equal: [],
    },
};

//  双重保险，都判断下吧
const isAndroidFromOs = osName === 'android';
const isIOSFromOs = osName === 'ios';
const innerIsAndroid = isAndroidFromOs || isAndroid;
const innerIsIOS = isIOSFromOs || isIOS;
export const supportNativeVisible =
    (innerIsAndroid && isGteVersion('11.4.40')) ||
    (innerIsIOS && isGteVersion('11.10.20'));

export type SceneSwitchType = {
    [key in keyof SwitchType]: string[];
};

function handleSwitch(
    res: any,
    switchKey: keyof SwitchType,
    switchVal: any,
    scene: SceneValType,
) {
    if (switchKey === 'mutedVersionList') {
        //  默认禁音处理
        if (res.muted) return;
        if (innerIsAndroid) {
            const { lower = '11.4.40', euqal = [] } =
                switchVal?.android ?? MutedVersionList;
            if (!isGteVersion(lower)) {
                res.muted = true;
                return;
            }
            if (euqal.includes(getKwaiVersion())) {
                res.muted = true;
                return;
            }
        } else if (innerIsIOS) {
            const { lower = '11.10.20', euqal = [] } =
                switchVal?.ios ?? MutedVersionList;
            if (!isGteVersion(lower)) {
                res.muted = true;
                return;
            }
            if (euqal.includes(getKwaiVersion())) {
                res.muted = true;
                return;
            }
        }
    } else {
        res[switchKey] = switchVal.includes(scene);
    }
}

export function usePartySwitch() {
    const { conf4Tab } = storeToRefs(useKconfStore());
    const downGradeSwitch = ref<SwitchType>({} as SwitchType);
    const switchPromise = usePartySwitchPromiseStore();

    watch(
        () => conf4Tab.value.switch,
        (val) => {
            if (
                val?.mutedVersionList?.android?.lower &&
                val?.showBack?.length
            ) {
                const sceneSwitch = (val || {
                    mutedVersionList: MutedVersionList,
                }) as SceneSwitchType;
                const moduleSwitch = {} as SwitchType;

                for (const key in sceneSwitch) {
                    const switchKey = key as keyof SwitchType;
                    const item = sceneSwitch[switchKey];
                    if (Scene.is4Tab) {
                        // moduleSwitch[switchKey] = item.includes(SceneTypeMap.FourTab);
                        handleSwitch(
                            moduleSwitch,
                            switchKey,
                            item,
                            SceneTypeMap.FourTab,
                        );
                    } else if (Scene.isAllApp) {
                        // moduleSwitch[switchKey] = item.includes(SceneTypeMap.AllApp);
                        handleSwitch(
                            moduleSwitch,
                            switchKey,
                            item,
                            SceneTypeMap.AllApp,
                        );
                    } else if (Scene.isLive) {
                        // moduleSwitch[switchKey] = item.includes(SceneTypeMap.Live);
                        handleSwitch(
                            moduleSwitch,
                            switchKey,
                            item,
                            SceneTypeMap.Live,
                        );
                    } else if (Scene.isSeachGuessList) {
                        // moduleSwitch[switchKey] = item.includes(
                        //     SceneTypeMap.SearchBanner,
                        // );
                        handleSwitch(
                            moduleSwitch,
                            switchKey,
                            item,
                            SceneTypeMap.SearchBanner,
                        );
                    } else if (Scene.isSearchTab) {
                        // moduleSwitch[switchKey] = item.includes(SceneTypeMap.SearchTab);
                        handleSwitch(
                            moduleSwitch,
                            switchKey,
                            item,
                            SceneTypeMap.SearchTab,
                        );
                    } else if (Scene.isShare) {
                        // moduleSwitch[switchKey] = item.includes(SceneTypeMap.Share);
                        handleSwitch(
                            moduleSwitch,
                            switchKey,
                            item,
                            SceneTypeMap.Share,
                        );
                    }

                    if (
                        switchKey === 'videoDowngradeSwitch' &&
                        !moduleSwitch.videoDowngradeSwitch
                    ) {
                        //  安卓和ios统一降级
                        if (innerIsAndroid) {
                            handleSwitch(
                                moduleSwitch,
                                switchKey,
                                item,
                                SceneTypeMap.Android,
                            );
                        } else if (innerIsIOS) {
                            handleSwitch(
                                moduleSwitch,
                                switchKey,
                                item,
                                SceneTypeMap.IOS,
                            );
                        }
                    }
                }

                downGradeSwitch.value = moduleSwitch;
                switchPromise.resolveFn?.();
            }
        },
        {
            immediate: true,
        },
    );

    return downGradeSwitch;
}

export const usePartySwitchPromiseStore = defineStore(
    'party_switch_promise',
    () => {
        const resolveFn = ref();
        const pendingPromise = new Promise((r) => {
            resolveFn.value = r;
        });
        const switchPromise = async () => {
            await pendingPromise;
        };

        return {
            switchPromise,
            resolveFn,
        };
    },
);
