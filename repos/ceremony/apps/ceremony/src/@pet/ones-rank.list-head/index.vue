<template>
    <Card class="rank-header-card">
        <ATitle v-if="data.title">
            {{ data.title }}
            <template #extra>
                <slot name="rank-title-extra">
                    <CardTitleTag v-if="data.tag">
                        {{ data.tag }}
                    </CardTitleTag>
                </slot>
            </template>
        </ATitle>
        <slot></slot>
    </Card>
</template>
<script lang="ts" setup>
import { Card as ACard } from '@alive-ui/base';
import type { PropType } from 'vue';
const { Card, Title: ATitle, CardTitleTag } = ACard;
const props = defineProps({
    data: {
        type: Object as PropType<{
            title?: string;
            tag?: string;
        }>,
        required: false,
        default: () => {
            return {
                title: '',
                tag: '',
            };
        },
    },
});
</script>
