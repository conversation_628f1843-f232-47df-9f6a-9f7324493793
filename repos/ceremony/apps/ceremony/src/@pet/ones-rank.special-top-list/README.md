# top3并排的榜单列表组件

## 功能概述

此组件用于展示排行榜信息，特别是针对榜单的TOP3部分进行了特殊处理，并且支持更多列表项的展示。组件支持自定义榜单单项样式、关注按钮以及额外信息显示等特性。

## 属性和方法

### Props

- **data** (`RankConfigInfo`): 榜单相关数据，包括列表、关注ID、分数标签名称等。
  - 默认值：`{ list: [], scoreLabel: '', focusActivityId: '' }`
- **rankType** (`string`): 榜单单项组件类型，可自定义传入不同的单项展示方式。
  - 默认值：`'base'`
- **groupType** (`string`): 榜单分段类型。
  - 默认值：`'normal'`
- **contextName** (`string`): 注入上下文名称。
  - 默认值：`''`
- **extraType** (`SpecialRankExtraType`): 榜单额外类型，影响额外信息的显示。
  - 默认值：`SpecialRankExtraType.FOCUS`

### Emit Events

- **无**

## 使用示例

```vue
<template>
  <RankList
    :data="rankData"
    :rank-type="'custom'"
    :group-type="'highlight'"
    :context-name="'specialContext'"
    :extra-type="SpecialRankExtraType.ALL"
  />
</template>

<script setup>
import { ref } from 'vue';
import { SpecialRankExtraType } from '@pet/ones-rank.schema/global';
import RankList from './RankList.vue';

const rankData = ref({
  list: [
    { itemId: 1, headUrl: 'url1', itemName: 'User1', h5ShowScore: 1000, followStatus: 0 },
    { itemId: 2, headUrl: 'url2', itemName: 'User2', h5ShowScore: 900, followStatus: 1 },
    { itemId: 3, headUrl: 'url3', itemName: 'User3', h5ShowScore: 800, followStatus: 0 },
    // 更多列表项...
  ],
  scoreLabel: '盛典值',
  focusActivityId: '12345',
  logParams: { /* 埋点参数 */ },
});
</script>
```

## 注意事项

- `data` 中的 `list` 需要是一个包含多个对象的数组，每个对象代表一个榜单条目。
- `extraType` 的值会影响额外信息的显示，具体类型请参考 `SpecialRankExtraType` 枚举。
- 组件内部会根据 `extraType` 和 `bolIsAuthor` 等条件动态显示关注按钮和其他额外信息。

## 依赖项

- `@pet/ones-rank.schema/global`
- `@pet/ones-rank.common-list/index.vue`
- `@alive-ui/pro`
- `@alive-ui/base`
- `@alive-ui/actions`