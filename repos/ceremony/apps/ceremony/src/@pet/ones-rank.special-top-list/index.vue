<script lang="ts" setup>
import { ref, computed } from 'vue';
import { SpecialRankExtraType } from '@pet/ones-rank.schema/global';
import RankList from '@pet/ones-rank.common-list/index.vue';
import { FocusButton, BaseRankList as APBaseRankList } from '@alive-ui/pro';
import {
    ARankUsername,
    AAvatar,
    ARankCompactItem,
    ARankInfoItem,
} from '@alive-ui/base';
import { nameSlice, bolIsAuthor, authorId } from '@alive-ui/actions';
import type { RankConfigInfo } from '@pet/ones-rank.schema/global';
// 定义枚举

interface RankData {
    // 榜单相关数据: 列表，关注id，分数标签名称
    data: RankConfigInfo;
    // 注入上下文
    contextName?: symbol;
    // 榜单单项组件 （类型多，有单头像，双头像，三头像等，分数值分布排列位置不同，可以自定义传入）
    rankType: string;
    // 榜单分段类型
    groupType?: string;
    // TOP3
    extraType?: SpecialRankExtraType;
}
const props = withDefaults(defineProps<RankData>(), {
    data: () => {
        return {
            list: [],
            // 榜单分值标签名称，如盛典值
            scoreLabel: '',
            // 榜单关注id
            focusActivityId: '',
        };
    },
    rankType: 'base',
    groupType: 'normal',
    contextName: Symbol.for('ctx'),
    extraType: SpecialRankExtraType.FOCUS,
});

// 迁移现金榜单前三逻辑
const topArray = computed(() => {
    const list = props.data.list?.slice(0, 3) || [];
    return list;
});

const restArray = computed(() => {
    return props.data?.list?.slice(3);
});
const commonParams = computed(() => {
    return {
        scoreLabel: props.data?.scoreLabel,
        // 榜单通用埋点
        logParams: props.data?.logParams,
        // 榜单关注id
        focusActivityId: props.data?.focusActivityId,
    };
});
</script>

<template>
    <div
        class="rank-special-top"
        :class="{ 'rank-list-two': topArray.length === 2 }"
    >
        <APBaseRankList :log-params="data.logParams">
            <ARankCompactItem
                v-for="item in topArray"
                :key="item?.item?.itemId"
                :order="item?.rankShowIndex"
                :class="{
                    'rank-list-extra-all':
                        extraType === SpecialRankExtraType.ALL,
                }"
            >
                <template #avatar>
                    <AAvatar :src="item?.item?.headUrl" />
                </template>
                <ARankUsername>
                    {{ nameSlice(item?.item?.itemName || '', 7) }}
                </ARankUsername>
                <template #extra>
                    <div class="rank-top-extra-column">
                        <ARankInfoItem
                            v-if="
                                extraType === SpecialRankExtraType.SCORE ||
                                extraType === SpecialRankExtraType.ALL
                            "
                        >
                            <template #key>
                                <div class="a-text-main-o2">
                                    {{ data.scoreLabel }}：
                                </div>
                            </template>
                            <template #value>
                                <div class="flex-center">
                                    <div class="score-value">
                                        {{ item?.h5ShowScore }}
                                    </div>
                                </div>
                            </template>
                        </ARankInfoItem>
                        <FocusButton
                            v-if="
                                (extraType === SpecialRankExtraType.FOCUS ||
                                    extraType === SpecialRankExtraType.ALL) &&
                                !(
                                    bolIsAuthor &&
                                    item.itemId === Number(authorId)
                                )
                            "
                            :action-status="item?.followStatus"
                            :focus-params="{
                                userId: item?.item?.itemId.toString(),
                                activityId: data?.focusActivityId || '',
                            }"
                        />
                    </div>
                </template>
            </ARankCompactItem>
        </APBaseRankList>
        <RankList
            v-if="restArray?.length"
            :rank-type="rankType"
            :data="{
                list: restArray,
                ...commonParams,
            }"
        />
    </div>
</template>

<style lang="less" scoped>
.rank-special-top {
    :deep(.rank-username-attr) {
        margin-top: 15px;
    }
    :deep(.rank-normal-compact-extra-attr) {
        margin-top: 0px;
    }
}
.rank-list-two {
    :deep(.rank-list-attr) {
        margin: 0 15px;
        justify-content: flex-start;
    }
}

.rank-list-extra-all {
    :deep(.rank-compact-1-default-mt) {
        margin-top: 20px;
    }
    :deep(.rank-compact-default-mt) {
        margin-top: 13px;
    }
    .rank-top-extra-column {
        display: flex;
        flex-flow: column;
        margin-top: -16px;
    }
}
</style>
