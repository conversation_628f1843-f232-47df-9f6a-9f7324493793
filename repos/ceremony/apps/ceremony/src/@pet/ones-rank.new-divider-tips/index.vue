<!-- 分割线top提示 -->
<template>
    <div v-if="marqueeList?.length" class="mt-14px mb-14px">
        <AInfo type="solid" class="justify-style">
            <AMarquee class="a-text-main">
                <AInfoHighlight
                    v-for="(item, index) in marqueeList"
                    :key="index"
                    :text="item"
                />
            </AMarquee>
        </AInfo>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { Info, Marquee as AMarquee } from '@alive-ui/base';
const { Info: AInfo, InfoHighlight: AInfoHighlight } = Info;
interface ComponentSchema {
    addCardPrivilegeTip?: string;
    directPromotionTip?: string;
    homeTrafficAwardText?: string;
}
const props = withDefaults(defineProps<ComponentSchema>(), {
    addCardPrivilegeTip: '',
    directPromotionTip: '',
    homeTrafficAwardText: '',
});

const formatTrafficAwardTpl = (
    timeStr: string,
    count: string,
    textTemplate: string,
) => {
    const awardStr = textTemplate
        .replace('%s', timeStr ? `%s${timeStr}%s` : '')
        .replace('%d', count ? `%s${count || ''}%s` : '');

    return awardStr
        .split('%s')
        .filter((text) => text)
        .join('');
};

const formatNumber = (v: number) => {
    if (Number.isNaN(v)) {
        return '';
    }

    if (v < 10000) {
        return v;
    }

    return `${Math.floor(v / 10000)}w`;
};

const marqueeList = computed(() => {
    const list = [];
    if (props.addCardPrivilegeTip) {
        list.push(props.addCardPrivilegeTip);
    }
    if (props.directPromotionTip) {
        list.push(props.directPromotionTip);
    }
    if (props.homeTrafficAwardText) {
        list.push(props.homeTrafficAwardText);
    }
    console.log('marqueeList===', list);
    return list;
    // return trafficAward.value?.strArray || [];
});
</script>
<style lang="less" scoped>
.width-auto {
    width: auto;
}
.promotion-divider {
    perspective: 1000px;
}
.justify-style {
    justify-content: flex-start;
    width: 100%;
}
</style>
