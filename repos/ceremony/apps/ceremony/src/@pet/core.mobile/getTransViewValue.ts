import { ref, onMounted } from 'vue-demi';
import { createInjectionState } from '@vueuse/core';
import { useDomListener } from '@pet/yau.core/event/useDomListener';
import { getViewRatio, getMaxViewWidth, targetViewWidth } from './index';

/**
 * 全局缩放处理函数
 *
 * 返回一个整体的缩放倍率 `ratio` 和一个按照缩放倍率设置值的函数 `getValue`
 *
 * 在挂载后，根据当前屏幕尺寸计算并设置缩放比例
 *
 * 当窗口尺寸变更时也会重新计算并设置 `ratio`
 */
const [provide, inject] = createInjectionState(() => {
    // 整体的缩放倍率
    const ratio = ref(1);

    // 获取并设置当前屏幕尺寸的倍率
    function setRatio() {
        const maxWidth = getMaxViewWidth();
        ratio.value = getViewRatio(targetViewWidth, maxWidth)();
    }

    // 获取转换后的值
    function getValue(val: number) {
        return val * ratio.value;
    }

    // 初始化
    onMounted(() => {
        setRatio();
    });

    // 监听resize事件
    useDomListener(() => window, 'resize', setRatio);

    return {
        ratio,
        getValue,
    };
});

export {
    /**
     * @description 全局注入resize监听
     * @example
     * // App.vue
     * import { provideTransView } from '@pet/core.mobile/getTransViewValue';
     * provideTransView();
     */
    provide as provideTransView,
    /**
     * @description 在组件中获取转换值
     * @example
     * // your-component.vue
     * import { injectTransView } from '@pet/core.mobile/getTransViewValue';
     * const { getValue } = injectTransView()!;
     * const dynamicValue = getValue(100);
     */
    inject as injectTransView,
};
