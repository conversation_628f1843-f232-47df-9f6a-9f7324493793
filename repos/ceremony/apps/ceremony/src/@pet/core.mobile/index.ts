import { isBrowser } from '@pet/yau.core';

export const rootValue = 100;
export const targetViewWidth = 414;

export const getMaxViewWidth = () => {
    return isBrowser ? document.documentElement.offsetWidth : 0;
};

export const maxViewWidth = getMaxViewWidth();

export function px2rem(px: number) {
    if (typeof px !== 'number') {
        // TODO: runtime error?
        throw new Error('px2rem: px must be a number');
    }

    return `${px / rootValue}rem`;
}

export function getViewRatio(
    targetWidth: number,
    screenMax = maxViewWidth,
): () => number {
    if (typeof targetWidth !== 'number' && typeof screenMax !== 'number') {
        return () => 1;
    }
    const value = screenMax / targetWidth;
    return () => {
        return value;
    };
}

export function transViewValue(value: number): number {
    if (typeof value !== 'number') {
        return 0;
    }
    const viewRatio = getViewRatio(targetViewWidth, getMaxViewWidth());
    return value * viewRatio();
}
