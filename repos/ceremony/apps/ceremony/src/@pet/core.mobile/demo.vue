<script setup lang="ts">
import Test from './test.vue';
import { provideTransView, injectTransView } from './getTransViewValue';
import { px2rem, getViewRatio, transViewValue } from './index';

provideTransView();

const { getValue } = injectTransView()!;
</script>

<template>
    <div class="demo">
        <h1>@pet/core.mobile</h1>
        <div class="container">
            <p>100px = {{ px2rem(100) }}</p>
            <p>当前页面相对于 414 比例为 {{ getViewRatio(414, 516)() }}</p>
            <p>
                414设计稿为375时在当前设备中的像素值为 {{ transViewValue(375) }}
            </p>

            <p>支持屏幕缩放的值 {{ getValue(100) }} {{ getValue(200) }}</p>

            <Test />
        </div>
    </div>
</template>

<style lang="stylus" scoped>
h1
    font-size 40px

p
    font-size 30px
</style>
