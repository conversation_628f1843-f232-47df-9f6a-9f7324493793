<template>
    <div class="live-photo">
        <ACard>
            <ACardTitle> {{ title }} </ACardTitle>
            <ACardContent class="live-photo__content">
                <ATabs
                    :selected-index="activeTabIdx"
                    type="solid"
                    @change="changeTab"
                >
                    <ATabList>
                        <ATab>{{ liveTabName }}</ATab>
                        <ATab>{{ photoTabName }}</ATab>
                    </ATabList>
                </ATabs>
                <div class="container">
                    <template v-if="isShowEmpty">
                        <div class="empty">
                            <Empty class="empty__icon a-text-main" />
                            <span class="empty__desc a-text-main">{{
                                activeTabIdx === 0
                                    ? '暂无热门直播间'
                                    : '暂无攻略视频'
                            }}</span>
                        </div>
                    </template>
                    <template v-else-if="activeTabIdx === 0">
                        <Layout2Col
                            v-slot="{ item }"
                            :list="hotAuthorList"
                            key-name="liveStreamId"
                            class="mt-[20px] px-[12px]"
                        >
                            <div class="video-item">
                                <AProfileCard
                                    v-show-log="{
                                        action: 'OP_ACTIVITY_AUTHOR_CARD',
                                        params: {
                                            module: '热门主播',
                                            author_id: item?.userInfo?.userId,
                                            live_stream_id: item?.liveStreamId,
                                        },
                                    }"
                                    v-click-log="{
                                        action: 'OP_ACTIVITY_AUTHOR_CARD',
                                        params: {
                                            module: '热门主播',
                                            author_id: item?.userInfo?.userId,
                                            live_stream_id: item?.liveStreamId,
                                        },
                                    }"
                                    :poster="getCoverImg(item?.imgUrl)"
                                    :avatar="item?.userInfo?.headUrl"
                                    @click="goLiveDetail(item?.liveStreamId)"
                                >
                                    <template #name>
                                        <span class="video-item__name">
                                            {{
                                                nameSlice(
                                                    item?.userInfo?.userName,
                                                    5,
                                                )
                                            }}
                                        </span>
                                    </template>
                                    <template #extra>
                                        <div class="video-item__extra">
                                            <Like />
                                            <span style="margin-left: 2px">{{
                                                item?.audienceNum
                                            }}</span>
                                        </div>
                                    </template>
                                </AProfileCard>
                                <div
                                    class="pcard-tag-attr flex-center-center pcard-tag-color pcard-tag-bg text-11 pl-[2px] pr-[5px]"
                                >
                                    <div class="pcard-tag-icon icon-size"></div>
                                    <span
                                        class="text-bold pcard-tag-text w-no-wrap"
                                    >
                                        {{ getShowLabel(item) }}
                                    </span>
                                </div>
                            </div>
                        </Layout2Col>
                    </template>
                    <template v-else-if="activeTabIdx === 1">
                        <Layout2Col
                            v-slot="{ item }"
                            :list="rankingStrategy"
                            key-name="photoId"
                            class="mt-[20px] px-[12px]"
                        >
                            <div class="video-item">
                                <AProfileCard
                                    v-show-log="{
                                        action: 'OP_ACTIVITY_AUTHOR_CARD',
                                        params: {
                                            module: '冲榜攻略',
                                            author_id: item?.userInfo?.userId,
                                            video_id: item?.photoId,
                                        },
                                    }"
                                    v-click-log="{
                                        action: 'OP_ACTIVITY_AUTHOR_CARD',
                                        params: {
                                            module: '冲榜攻略',
                                            author_id: item?.userInfo?.userId,
                                            video_id: item?.photoId,
                                        },
                                    }"
                                    :poster="getCoverImg(item.imgUrl)"
                                    :avatar="item?.userInfo?.headUrl"
                                    @click="
                                        goWorkDetail(
                                            item?.photoId,
                                            item?.userInfo?.userId,
                                        )
                                    "
                                >
                                    <template #name>
                                        <span class="video-item__name">
                                            {{
                                                nameSlice(
                                                    item?.userInfo?.userName,
                                                    5,
                                                )
                                            }}
                                        </span>
                                    </template>
                                    <template #extra>
                                        <div class="video-item__extra">
                                            <Profile />
                                            <span style="margin-left: 2px">{{
                                                item?.likeCount
                                            }}</span>
                                        </div>
                                    </template>
                                </AProfileCard>
                            </div>
                        </Layout2Col>
                    </template>
                </div>
            </ACardContent>
        </ACard>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import useTab from '@pet/ones-use.use-tab/index';
import Layout2Col from '@pet/ones-ui.layout-2col/index.vue';
import { Toast } from '@lux/sharp-ui-next';
import { Like, Profile, Empty } from '@alive-ui/icon';
import {
    ACard,
    ACardContent,
    ACardTitle,
    AProfileCard,
    ATabs,
    ATabList,
    ATab,
} from '@alive-ui/base';
import {
    nameSlice,
    bolIsAuthor,
    openUpDown,
    toWorkVideoPage,
    isYodaPCContainer,
    liveStreamId as curLiveStreamId,
} from '@alive-ui/actions';
import type { HotAuthorItem, RankingStrategyItem } from './schemes';

interface ImgItem {
    url: string;
    cdn: string;
}

const { activeTabIdx, changeTab } = useTab();

const props = withDefaults(
    defineProps<{
        title?: string;
        liveTabName?: string;
        photoTabName?: string;
        hotAuthorList: HotAuthorItem[];
        rankingStrategy: RankingStrategyItem[];
    }>(),
    {
        title: '盛典人气王',
        liveTabName: '热门主播',
        photoTabName: '赛事聚焦',
        hotAuthorList: () => [],
        rankingStrategy: () => [],
    },
);
const getCoverImg = (imgList: ImgItem[]) => {
    return imgList?.[0].url;
};
// 是否展示兜底
const isShowEmpty = computed(() => {
    if (activeTabIdx.value === 0 && props.hotAuthorList?.length === 0) {
        return true;
    }
    if (activeTabIdx.value === 1 && props.rankingStrategy?.length === 0) {
        return true;
    }
    return false;
});
const goWorkDetail = (workId: string, userId: string) => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    toWorkVideoPage(workId, userId);
};
const goLiveDetail = (liveStreamId: string) => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    if (curLiveStreamId === liveStreamId) {
        // 如果已经在当前直播间,提示
        Toast.info('您已经在直播间哦～');
        return;
    }
    const idList = props.hotAuthorList
        .map((item) => item?.liveStreamId)
        .filter((x) => x);
    // const url = `kwai://liveaggregatesquare?liveSquareSource=10014&path=/rest/n/live/feed/common/slide/more&selectedIndex=${idx}&liveStreamId=${idList.join(',')}`;
    openUpDown(idList, liveStreamId, {});
};
const getShowLabel = (item: { label: string; liveStreamId: string }) => {
    if (item?.label) {
        return item.label?.slice(0, 11);
    }
    return '直播中';
};
</script>

<style lang="less" scoped>
.live-photo {
    position: relative;
    &__content {
        position: relative;
        .container {
            .video-item {
                box-sizing: border-box;
                width: 172px;
                height: 298px;
                position: relative;

                &__name {
                    width: max-content;
                }

                &__extra {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .icon-size {
                    width: calc(14px * 12 / 11);
                    height: calc(14px * 12 / 11);
                }
                .w-no-wrap {
                    display: inline-block;
                    width: max-content;
                }

                /deep/ .pcard-attr {
                    width: 172px;
                    height: 268px;
                    overflow: hidden;
                    position: static;
                    border-bottom-left-radius: 0;
                    border-bottom-right-radius: 0;
                    background-position: center !important;
                    background-color: #000 !important;
                    .pcard-user-attr {
                        margin: 0;
                        background: rgba(242, 242, 255, 0.06);
                    }
                }
            }
            .video-item:nth-child(n + 3) {
                margin-top: 12px;
            }
            .video-item:nth-child(2n) {
                margin-left: 12px;
            }
            .empty {
                display: flex;
                justify-content: center;
                height: 300px;
                align-items: center;
                flex-direction: column;
                &__icon {
                    width: 96px;
                    height: 96px;
                }
                &__desc {
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
