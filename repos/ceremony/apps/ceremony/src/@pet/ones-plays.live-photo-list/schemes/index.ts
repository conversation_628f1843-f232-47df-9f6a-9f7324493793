export interface ImgItem {
    url: string;
    cdn: string;
}

export interface IUserInfo {
    userId: string;
    userName: string;
    userSex: string;
    headUrl: string;
}

export interface StrategyGuideItem {
    imgUrl: ImgItem[];
    photoId: string;
    name: string;
    userInfo: IUserInfo;
}

export interface HotAuthorItem {
    imgUrl: ImgItem[];
    label: string;
    liveStreamId: string;
    audienceNum: string;
    following: boolean;
    userInfo: IUserInfo;
}
export interface RankingStrategyItem {
    imgUrl: ImgItem[];
    photoId: string;
    likeCount: string;
    userInfo: IUserInfo;
}
