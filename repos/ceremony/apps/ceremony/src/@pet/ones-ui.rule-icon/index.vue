<template>
    <div v-if="ruleConfig?.show" class="icon-box" :class="ruleIconWrapStyle">
        <div
            v-show-log="funcBtnLogParams"
            v-click-log="funcBtnLogParams"
            class="rule-icon"
            type="rule"
            :position="position"
            :style="iconStyle"
            :isRenderToBody="false"
            @click="goRule"
        />
    </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { Report } from '@alive-ui/system';
import { goOtherPage, activityBiz, getServerTime } from '@alive-ui/actions';
const { kconfData } = storeToRefs(useKconfStore());

type ruleMapItem = {
    show?: boolean;
    icon?: string;
    url?: string;
    timeUrlList?: Array<{
        timeSlot: [number, number];
        url: string;
    }>;
};

// 获取 kconf 配置
type PositionType = 'static' | 'absolute' | 'fixed';
const props = withDefaults(
    defineProps<{
        ruleKey: string;
        positionType?: PositionType;
        funcBtnLogParams?: object;
        position?: string;
    }>(),
    {
        positionType: 'static',
        funcBtnLogParams: () => {
            return {
                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                params: {
                    action_name: '规则',
                },
            };
        },
        position: 'right-top',
    },
);

// 获取当前页面path
const renderIcon = () => {
    if (!props.ruleKey) {
        Report.biz.error('规则入口配置', {
            info: '未配置规则 key',
        });
        return;
    }
};

const ruleConfig = computed(() => {
    const ruleList: Record<string, ruleMapItem> =
        kconfData.value?.common?.ruleMap?.list || {};
    if (!ruleList) {
        return {
            show: false,
            icon: '',
            url: '',
            timeUrlList: [],
        };
    }

    const config = ruleList[props.ruleKey];

    // 检查配置是否存在
    if (!config) {
        Report.biz.info('规则入口配置', { info: '当前 key 未配置' });
        return {
            show: false,
            icon: '',
            url: '',
            timeUrlList: [],
        };
    }

    // 检查是否显示
    if (!config.show) {
        Report.biz.warning('规则入口配置', { info: '当前 key 配置不展示' });
        return {
            show: false,
            icon: '',
            url: '',
            timeUrlList: [],
        };
    }

    // 检查 URL
    if (!config.url) {
        Report.biz.error('规则入口配置', { info: '当前 key 未配置跳转 url' });
        return {
            show: false,
            icon: '',
            url: '',
            timeUrlList: [],
        };
    }

    return config;
});
const iconUrlBg = computed(() => {
    if (ruleConfig.value?.show) {
        return (
            ruleConfig.value?.icon || kconfData.value?.common?.ruleMap?.iconUrl
        );
    }
    return '';
});
onMounted(() => {
    renderIcon();
});
const iconStyle = computed(() => {
    if (iconUrlBg.value) {
        return `background-image: url(${iconUrlBg.value})`;
    }
    return '';
});
const ruleIconWrapStyle = computed(() => {
    if (!props.positionType) {
        return [];
    }
    return props.positionType
        ? [props.positionType, ...props.position.split('-')]
        : [];
});

function getHostFromUrl(url: string) {
    try {
        const parsedUrl = new URL(url);
        return parsedUrl.host; // 返回 host 部分
    } catch (error) {
        console.error('Invalid URL:', error);
        return null; // 如果 URL 无效，返回 null
    }
}
const jumpUrl = (ruleUrl: string) => {
    const jumpType =
        getHostFromUrl(ruleUrl) === 'ppg.viviv.com' ? 'jimu' : 'h5';
    if (jumpType === 'h5') {
        const url = new URL(ruleUrl);
        // 修改参数
        url.searchParams.set('activityBiz', activityBiz);
        // 更新 URL
        goOtherPage('jimu', String(url.toString()));
    } else {
        goOtherPage('jimu', String(ruleUrl));
    }
};
const goRule = async () => {
    let ruleUrl = ruleConfig.value.url;
    if (ruleConfig.value.timeUrlList) {
        let tmsp = Date.now();
        console.log('Date.now', tmsp);
        try {
            const { serverTimeStamp } = await getServerTime();
            tmsp = serverTimeStamp;
        } catch (e) {
            console.log(e);
        }
        ruleConfig.value.timeUrlList?.forEach((timeItem: any) => {
            const { timeSlot, url } = timeItem;
            console.log(timeSlot);
            if (tmsp >= timeSlot?.[0] && tmsp <= timeSlot?.[1] && url) {
                ruleUrl = url;
            }
        });
        if (ruleUrl) {
            jumpUrl(ruleUrl);
        } else {
            Report.biz.error('规则入口点击-无跳转链接', {});
        }
    } else if (ruleUrl) {
        jumpUrl(ruleUrl);
    } else {
        Report.biz.error('规则入口点击-无跳转链接', {});
    }
};
</script>
<style lang="less" scoped>
.rule-icon {
    width: 40px;
    height: 40px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-left: 0.16rem;
}
.icon-box {
    &.absolute {
        position: absolute;
        z-index: 10;

        &.left {
            left: var(--HorizontalPadding, 12px);
        }

        &.right {
            right: var(--HorizontalPadding, 12px);
        }

        &.middle {
            top: 50%;
        }

        &.bottom {
            bottom: var(--BottomPadding, 120px);
        }

        &.top {
            top: var(--VerticalPadding, 18px);
        }
    }
    &.fixed {
        position: fixed;
        z-index: 10;

        &.left {
            left: var(--HorizontalPadding, 12px);
        }

        &.right {
            right: var(--HorizontalPadding, 12px);
        }

        &.middle {
            top: 50%;
        }
        &.bottom {
            bottom: var(--BottomPadding, 120px);
        }

        &.top {
            top: var(--VerticalPadding, 18px);
        }
    }
}
</style>
