# Rule Icon 组件文档

## 功能概述

`Rule Icon` 组件用于在页面中显示一个规则图标，并根据配置决定是否显示以及点击后的跳转行为。组件支持多种定位方式，并且可以记录用户的点击行为。

## 属性和方法

### Props

| 名称               | 类型                           | 默认值                 | 描述                                                         |
|------------------|-------------------------------|----------------------|------------------------------------------------------------|
| `ruleKey`         | `string`                      | -                    | 规则的唯一标识符，用于从 `kconfData` 中获取规则配置。                           |
| `postionType`     | `'static' \\| 'absolute' \\| 'fixed'` | `'static'`           | 图标的定位方式。                                           |
| `funcBtnLogParams`| `object`                      | `{ action: 'OP_ACTIVITY_GASHAPON_MACHINE_FUNCTION_BUTTON', params: { action_name: '规则' } }` | 点击图标时记录的日志参数。                                 |
| `position`        | `string`                      | `'right-top'`        | 图标的位置，支持 `left-top`, `right-top`, `left-bottom`, `right-bottom`, `middle-top`, `middle-bottom`。 |

## 使用示例

```vue
<template>
  <RuleIcon
    :ruleKey=\"'exampleRule'\"
    :postionType=\"'absolute'\"
    :funcBtnLogParams=\"{
      action: 'OP_ACTIVITY_GASHAPON_MACHINE_FUNCTION_BUTTON',
      params: {
        action_name: '规则'
      }
    }\"
    :position=\"'right-top'\"
  />
</template>

<script setup>
import RuleIcon from '@pet/ones-ui.rule-icon/index.vue';
</script>
```

## 注意事项

1. **`ruleKey` 必填**：必须提供有效的 `ruleKey`，否则组件将不会显示。
2. **配置检查**：如果 `kconfData` 中没有对应的 `ruleKey` 或者 `show` 属性为 `false`，组件将不会显示。
3. **跳转链接**：如果 `ruleConfig` 中没有配置 `url`，点击图标时将不会进行跳转，并记录错误日志。

## 依赖项

- `vue`
- `pinia`
- `@pet/ones-use.useKconf/index`
- `@alive-ui/system`
- `@alive-ui/actions`