<template>
    <ACard v-if="isShow">
        <ACardTitle class="title">
            {{ title }}
        </ACardTitle>
        <ACardSubtitle class="sub-title">
            {{ hotListSub }}
        </ACardSubtitle>
        <ACardContent class="card">
            <div
                v-for="item in hotList"
                :key="item.jumpUrl"
                v-click-log="
                    HotListRankItemBtnLog(item.jumpUrl + `&type=${item.type}`)
                "
                v-show-log="
                    HotListRankItemBtnLog(item.jumpUrl + `&type=${item.type}`)
                "
                class="hotList-item"
                @click="onJumpUrl(item.jumpUrl)"
            >
                <div
                    class="item-main-title"
                    :class="
                        item.type === HotType.HOTSPOT
                            ? 'item-main-titlehot'
                            : ' a-text-highlight'
                    "
                >
                    {{ item.type === HotType.HOTSPOT ? '热点榜' : '娱乐榜' }}
                    <span class="item-main-title-line">|</span> {{ item.topN }}
                </div>
                <div class="item-main-subTitle a-text-main">
                    {{ item.subTitle }}
                </div>
            </div>
            <div
                v-click-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: {
                        btn_type: '查看全部热点',
                    },
                }"
                class="see-all-hot a-text-main"
                @click="onSeeAllHot"
            >
                <span>查看全部热点</span>
                <Right />
            </div>
        </ACardContent>
    </ACard>
</template>
<script setup lang="ts">
import { getCurrentInstance, computed } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { isYodaPCContainer } from '@alive-ui/system';
import { Right } from '@alive-ui/icon';
import { ACard, ACardContent, ACardTitle, ACardSubtitle } from '@alive-ui/base';
import { bolIsAuthor } from '@alive-ui/actions';
import { HotListRankItemBtnLog } from './logger';
import { HotType } from './const';
import type { HotListItemType } from './schemes';

const { proxy } = getCurrentInstance() as any;
const onJumpUrl = (url: string) => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
        return;
    }
    if (bolIsAuthor) {
        Toast.info('直播中，不可跳转');
        return;
    }
    window.location.href = url;
};
const onSeeAllHot = () => {
    proxy?.$router?.push({
        name: 'hot-list',
    });
};
const props = withDefaults(
    defineProps<{
        title?: string;
        hotListSub: string;
        hotList: HotListItemType[];
        extraCardKey?: string;
    }>(),
    {
        title: '盛典热榜',
        hotListSub: '',
        hotList: () => {
            return [];
        },
        extraCardKey: '',
    },
);
const isShow = computed(() => {
    return props.hotList?.length > 0;
});
</script>
<style lang="less" scoped>
.sub-title {
    margin-bottom: 12px;
}
.card {
    padding: 0 12px;
    .hotList-item {
        display: flex;
        padding: 7px 0;
        width: 358px;
        height: 32px;
        align-items: center;
        .item-main-title {
            width: 88px;
            font-family: 'PingFang SC', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            margin-right: 4px;
            .item-main-title-line {
                opacity: 0.3;
            }
        }
        .item-main-titlehot {
            color: #ffd400;
        }
        .item-main-subTitle {
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            font-family: 'PingFang SC', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
        }
    }
}
.see-all-hot {
    margin-top: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'PingFang SC', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    opacity: 0.6;
    .kvIcon {
        margin-left: 2px;
        width: 10px;
        height: 10px;
        opacity: 0.6;
    }
}
</style>
