<template>
    <div>
        <ACard>
            <ACardTitle class="title"> {{ title }} </ACardTitle>
            <ACardContent>
                <div class="user-info-box">
                    <AAvatar :src="hotInfo.authorHeadUrl" />
                    <div class="">
                        <div class="user-name a-text-main">
                            {{ hotInfo.authorName }}
                        </div>
                        <div
                            class="stage-info a-text-main-o2 flex align-center"
                        >
                            {{
                                hotInfo.categoryLaneName
                                    ? `${participate1 || '将参与 品类赛-'}${hotInfo.categoryLaneName} 等`
                                    : participate2 || '将参与盛典比拼'
                            }}
                            <div
                                class="more-stage-icon"
                                @click="showSchedulePanel()"
                            ></div>
                        </div>
                        <div
                            v-if="
                                authorLaneTips &&
                                hotInfo.hasLaneName &&
                                bolIsAuthor &&
                                isShowPopover
                            "
                            class="popover"
                            @click="handlePopover"
                        >
                            {{ authorLaneTips }}
                            <img
                                class="popover-arrow"
                                src="./assets/popver-arrow_2x.png"
                            />
                        </div>
                    </div>
                    <div class="rongyao-icon" @click="gotoHonorPage"></div>
                </div>
                <img class="preview-user-intro-img" :src="previewUserImg" />
            </ACardContent>
        </ACard>
        <Popup
            v-model="schedulePanel"
            :show-mask="true"
            :mask-closeable="true"
            :closeable="true"
            :disable-scroll="true"
            position="bottom"
            popup-class="region-picker-popup"
        >
            <div class="popup-wrapper">
                <div class="popup-content card-bg-img">
                    <div class="popup-content-title flex-center">
                        <div
                            class="card-title-icon card-title-icon-attrs"
                        ></div>
                        <span>{{ popupTitle || '主播将参与以下赛道' }}</span>
                        <div
                            class="card-title-icon card-title-icon-attrs card-title-icon-right"
                        ></div>
                    </div>
                    <AInfo
                        v-if="hotInfo.modifyLabelTip?.length"
                        class="info info-wrap"
                    >
                        <AMarquee>
                            <AInfoHighlight
                                v-for="item in hotInfo.modifyLabelTip"
                                :key="item"
                                :text="item"
                            />
                        </AMarquee>
                    </AInfo>
                    <div class="popup-content-list">
                        <div
                            v-for="(item, index) in newSCHEDULE_CONFIG"
                            :key="index"
                            class="popup-content-item"
                            :style="{ backgroundImage: `url(${item.url})` }"
                        >
                            <img
                                v-if="
                                    bolIsAuthor &&
                                    hotInfo?.customLabelView[item.value]
                                "
                                v-click-log="{
                                    action: 'OP_ACTIVITY_ADJUST_BUTTON',
                                }"
                                src="./assets/<EMAIL>"
                                class="popup-content-tag"
                                @click="showScheduleSelectPanel(item)"
                            />
                            <div class="popup-content-text">
                                <span>{{
                                    nameSlice(hotInfo[item.value], 7)
                                }}</span>
                            </div>
                            <div class="popup-content-text2 a-text-main-o1">
                                {{ item.name }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Popup>
        <Popup
            v-model="scheduleSelectPanel"
            :show-mask="true"
            :mask-closeable="true"
            :closeable="false"
            :disable-scroll="true"
            position="bottom"
            @mask-click="selectCancel()"
        >
            <div class="popup-content-select">
                <div class="popup-content-select-head">
                    <span
                        class="popup-content-select-cancel"
                        @click="selectCancel()"
                        >取消</span
                    >
                    <span class="popup-content-select-title"
                        >选择{{ curSelectItem.name }}赛道</span
                    >
                    <span
                        class="popup-content-select-confirm"
                        @click="selectConfirm()"
                        >完成</span
                    >
                </div>

                <div class="popup-content-select-content">
                    <div
                        v-for="(scheduleItem, key) in curSelectSchedule?.labels"
                        :key="key"
                        class="content-item"
                        :class="{
                            'content-item-select': curSelectIndex === key,
                        }"
                        @click="
                            handleItem(
                                key,
                                scheduleItem.showName,
                                scheduleItem.originName,
                            )
                        "
                    >
                        {{ scheduleItem.showName }}
                    </div>
                </div>
            </div>
        </Popup>
    </div>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { Popup, Toast } from '@lux/sharp-ui-next';
import {
    AAvatar,
    ACard,
    ACardContent,
    ACardTitle,
    Marquee as AMarquee,
    Info,
} from '@alive-ui/base';
const {
    Info: AInfo,
    InfoHighlight: AInfoHighlight,
    InfoButton: AInfoButton,
} = Info;
import { bolIsAuthor, nameSlice } from '@alive-ui/actions';
import useStore from '../stores/preheat';
import { goHonorWall } from '@/utils/tools';

const props = withDefaults(
    defineProps<{
        title?: string;
        participate1: string;
        participate2: string;
        authorLaneTips: string;
        scheduleConfig: any;
        previewUserImg: string;
        popupTitle: string;
    }>(),
    {
        title: '主播参赛信息',
    },
);
const store = useStore();
const { hotInfo } = storeToRefs(store);

// store.init();
const schedulePanel = ref(false);

const showSchedulePanel = () => {
    schedulePanel.value = true;
};
const SCHEDULE_CONFIG = props.scheduleConfig;
const curSelectItem = ref({
    name: '',
}); // 当前选中的比赛的信息
const scheduleSelectPanel = ref(false);
const curSelectIndex = ref(0); // 选中赛道的index
const curSelectName = ref(''); // 选中赛道的showName
const curSelectOriginName = ref(''); // 选中赛道的originName
let originSelectName = ''; // 最初选中的赛道名
const newSCHEDULE_CONFIG = computed(() => {
    return SCHEDULE_CONFIG.filter(
        (item: { value: keyof typeof hotInfo.value }) => {
            return hotInfo.value[item.value]?.length > 0;
        },
    );
});
// 当前选中比赛的赛道信息
const curSelectSchedule = computed(() => {
    return hotInfo.value?.customLabelView?.[
        (curSelectItem.value as any)?.value
    ];
});
const showScheduleSelectPanel = (item: any) => {
    curSelectItem.value = item;
    originSelectName = hotInfo.value[item.value];
    scheduleSelectPanel.value = true;
    curSelectName.value = hotInfo.value[(curSelectItem.value as any)?.value];
    curSelectSchedule.value?.labels.forEach((item1: any, index1: number) => {
        if (item1.showName === curSelectName.value) {
            curSelectIndex.value = index1;
        }
    });
};
const selectCancel = () => {
    scheduleSelectPanel.value = false;
    Toast.info('未修改赛道');
};

const selectConfirm = () => {
    //   console.log('curSelectIndex当前选中赛道index', curSelectIndex.value);
    //   console.log('curSelectIndex当前选中赛道name', curSelectName.value);
    scheduleSelectPanel.value = false;
    if (
        curSelectName.value ===
        hotInfo.value[(curSelectItem.value as any)?.value]
    ) {
        Toast.info('未修改赛道');
        return;
    }

    hotInfo.value[(curSelectItem.value as any)?.value] = curSelectName.value;
    store.setCustomLabel(
        curSelectSchedule.value?.alias,
        curSelectOriginName.value,
        originSelectName,
    );
};
const handleItem = (index: number, name: string, originName: string) => {
    curSelectIndex.value = index;
    curSelectName.value = name;
    curSelectOriginName.value = originName;
};
const isShowPopover = ref(false);
// 仅主播今天第一次进入页面，展示气泡
if (bolIsAuthor) {
    if (
        localStorage.getItem('24-today-first-visited-preview') ===
        String(new Date().getDate())
    ) {
        isShowPopover.value = false;
    } else {
        localStorage.setItem(
            '24-today-first-visited-preview',
            String(new Date().getDate()),
        );
        isShowPopover.value = true;
    }
}

const handlePopover = () => {
    isShowPopover.value = false;
};
// 跳转荣誉殿堂
const gotoHonorPage = () => {
    if (bolIsAuthor) {
        Toast.info('直播中，不可跳转');
        return;
    }

    goHonorWall();
};
</script>

<style lang="less" scoped>
:deep(.spu-icon-dialog_colse) {
    color: #ffdfbf;
    opacity: 0.5;
}
.popover {
    position: absolute;
    top: 58px;
    height: 37px;
    padding: 0 16px;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-weight: bold;
    line-height: 21px;
    line-height: 37px;
    color: #fff;
    white-space: nowrap;
    background: rgba(0, 0, 0, 70%);
    border-radius: 4px;

    .popover-arrow {
        position: absolute;
        top: -4px;
        right: 16px;
        width: 8px;
        height: 4px;
    }
}
.user-info-box {
    padding: 4px 16px;
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .user-name {
        font-family: PingFang SC;
        font-size: 14px;
        font-weight: 500;
        line-height: 21px;
        text-align: left;
        margin-bottom: 4px;
    }
    .stage-info {
        width: 224px;
    }
    .more-stage-icon {
        width: 62px;
        height: 16px;
        background: url('./assets/more-icon.png') no-repeat 100% / 100%;
        margin-left: 5px;
    }
    .rongyao-icon {
        width: 56px;
        height: 56px;
        background: url('./assets/rongyao-icon.png') no-repeat 100% / 100%;
    }
}
.preview-user-intro-img {
    display: block;
    width: 100%;
    margin-top: 20px;
}
.region-picker-popup {
    &.spu-popup--bottom /deep/ .spu-popup__box {
        display: flex;
        justify-content: center;
    }
}
.popup-wrapper {
    @apply a-bg-page;
    border-radius: 16px 16px 0 0;
    width: 414px;
}
.popup-content {
    width: 414px;
    padding-top: 20px;
    padding-bottom: 44px;
    font-family: MF;
    @apply a-bg-part;
    @apply card-bg-img-attrs;
    border-radius: 16px 16px 0 0;

    &-title {
        margin-bottom: 2px;
        font-family: HYYakuHei;
        text-align: center;

        span {
            @apply a-text-title;
            font-size: 20px;
        }
    }

    &-subtitle {
        height: 18px;
        overflow: hidden;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        text-align: center;
        opacity: 0.6;

        @apply a-text-main;
    }

    &-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        width: 382px;
        margin: 26px 16px 0;
    }

    &-item {
        position: relative;
        width: 186px;
        height: 60px;
        background-repeat: no-repeat;
        background-size: 100%;
        background-position: left top;
        margin-bottom: 10px;
    }

    &-tag {
        position: absolute;
        top: 0;
        right: 0;
        width: 44px;
        height: 16px;
    }

    &-text {
        margin-top: 13px;
        margin-left: 12px;
        font-family: HYYaKuHei;
        font-size: 14px;
        line-height: 14px;

        span {
            @apply a-text-title;
        }
    }

    &-text2 {
        margin-top: 3px;
        margin-left: 12px;
        font-family: 'PingFang SC';
        opacity: 0.6;
        font-size: 12px;
    }
}
.popup-content-select {
    width: 414px;
    height: 348px;
    background: #fff;
    border-radius: 16px 16px 0 0;

    &-head {
        display: flex;
        height: 56px;
        padding: 0 16px;
        font-family: 'PingFang SC';
        font-weight: bold;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-bottom: solid 1px #eaeaea;
    }

    &-cancel {
        font-size: 16px;
        color: #666;
    }

    &-title {
        font-size: 17px;
        color: #000;
    }

    &-confirm {
        font-size: 16px;
        color: #ff5477;
    }

    &-content {
        display: flex;
        flex-wrap: wrap;
        padding: 13px 13px 0;
        overflow-y: auto;
        background: #fff;

        .content-item {
            display: flex;
            width: 120px;
            height: 48px;
            margin-right: 14px;
            margin-bottom: 13px;
            font-family: 'PingFang SC';
            font-size: 16px;
            color: #222;
            text-align: center;
            background: rgba(217, 217, 217, 20%);
            justify-content: center;
            align-items: center;

            &:nth-child(3n) {
                margin-right: 0;
            }
        }

        .content-item-select {
            color: #fe3666;
            background: rgba(255, 84, 119, 10%);
            border: 1px solid #fe3666;
        }
    }
}
.info-wrap {
    background: none;
    :deep(.info-highlight-container-attr) {
        text-align: center;
    }
}
.popup-content-list {
    margin-top: 10px;
}
</style>
