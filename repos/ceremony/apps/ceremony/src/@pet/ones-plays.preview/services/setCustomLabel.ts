import { request } from '@alive-ui/actions';
import type {
    SetCustomLabelPostRequest,
    SetCustomLabelPostResponse,
} from '../schemas/setCustomLabel';

const PATH = {
    setCustomLabel:
        '/webapi/live/revenue/operation/activity/summerCeremony24/setCustomLabel',
};

// 预热-修改赛道： https://mock.corp.kuaishou.com/project/941/interface/api/1212041
export const setCustomLabel = async (params: SetCustomLabelPostRequest) => {
    const res = await request.post<SetCustomLabelPostResponse>(
        PATH.setCustomLabel,
        params,
    );

    return res?.data;
};
