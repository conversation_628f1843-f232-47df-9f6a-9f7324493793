import exp from 'constants';
import { authorId, request } from '@alive-ui/actions';
import type { ApiRes } from '@alive-ui/actions';
export type UserItem = {
    rank?: number;
    authorId: number; // 主播id
    follow: boolean; // 是否关注
    living: boolean; // 是否正在直播
    liveStreamId: string; // 直播间id
    authorHeadUrl: string;
    authorName: string;
};
export const getAuthorInfoList = async (authorIdList: number[]) => {
    const result = await request.post<ApiRes<UserItem[]>>(
        '/rest/wd/live/plutus/followStatus',
        {
            requestView: {
                authorIdList,
            },
        },
    );
    return result?.data?.data;
};
export type IconUrl = {
    url: string;
    cdn: string;
}[];
export type PhotoItem = {
    photoId: string; // 视频id
    coverUrl: IconUrl; // 封面
    authorId: string; // 作者id
    authorHeadUrl: IconUrl; // 作者头像
    authorName: string; // 作者名
};
export type HotInfo = {
    hotPhotoApiViews: PhotoItem[]; // 砍掉
    authorId: number; // 主播id
    authorName: string; // 主播名称
    category: string; // 品类
    area: string; // 地区
    authorHeadUrl: IconUrl;
    countyLaneName: string; // 区县赛
    singerLaneName: string; // 歌手赛
    customLabelView: CustomLabelView;
};
export type AuthorInfo = {
    userId: number;
    userName: string;
    headUrl: string; // 作者头像
};

export type CustomLabelViewKey = {
    alias: string;
    labels: {
        originName: string;
        showName: string;
    }[];
};

export type CustomLabelView = {
    [key: string]: any;
    countyLaneName: CustomLabelViewKey; // 区县赛
    cityLaneName: CustomLabelViewKey; // 城市赛
    areaLaneName: CustomLabelViewKey; // 地区赛
    categoryLaneName: CustomLabelViewKey; // 品类赛
    starlightLaneName: CustomLabelViewKey; // 星光总决选
};

export type WarmUpPageInfo = {
    authorInfo: AuthorInfo;
    categoryLaneName: string; // 品类赛 - 父赛道
    cityLaneName: string; // 品类赛 - 子赛道
    areaLaneClassifyName: string; // 地区赛 - 父赛道
    areaLaneName: string; // 地区赛 - 子赛道
    countyLaneName: string; // 区县赛
    singerLaneName: string; // 歌手赛
    starlightLaneName: string; // 星光总决选
    customLabelView: CustomLabelView; // 主播可以修改的赛道范围
    modifyLabelTip: string; // 提示文案
};

export interface HotInfoType {
    [key: string]: any;
    hasLaneName: string;
    categoryLaneName: string;
    areaLaneName: string;
    countyLaneName: string;
    singerLaneName: string;
    starlightLaneName: string;
    cityLaneName: string;
    authorId: string;
    authorName: string;
    authorHeadUrl: string;
    customLabelView: CustomLabelView;
    modifyLabelTip: string;
}

export const getWarmUpPageInfo = async (authorId: number | string) => {
    const result = await request.post<WarmUpPageInfo>(
        '/webapi/live/revenue/operation/activity/springCeremony23/warmUp',
        {
            authorId,
        },
    );
    return result?.data;
};

export const getHotInfo = async (authorId: number | string) => {
    const data = await getWarmUpPageInfo(authorId);
    const {
        cityLaneName = '',
        categoryLaneName = '',
        areaLaneClassifyName = '',
        countyLaneName = '',
        singerLaneName = '',
        starlightLaneName = '',
        areaLaneName = '',
    } = data || {};

    // 所有赛道均获取不到时, 不展示展示更多入口
    const hasLaneName =
        categoryLaneName ||
        areaLaneName ||
        cityLaneName ||
        countyLaneName ||
        starlightLaneName;

    return {
        hasLaneName,
        categoryLaneName,
        areaLaneName,
        countyLaneName,
        singerLaneName,
        starlightLaneName,
        cityLaneName,
        authorId: data?.authorInfo?.userId as unknown as string,
        authorName: data?.authorInfo?.userName,
        authorHeadUrl: data?.authorInfo?.headUrl,
        customLabelView: data?.customLabelView,
        modifyLabelTip: data?.modifyLabelTip,
    };
};
