export interface Root {
    /**
     * 请求: 预热-修改赛道
     */
    SetCustomLabelPostRequest: SetCustomLabelPostRequest;
    /**
     * 响应: 预热-修改赛道
     */
    SetCustomLabelPostResponse: { [key: string]: any };
}

/**
 * 请求: 预热-修改赛道
 */
export interface SetCustomLabelPostRequest {
    /**
     * 预热接口 customLabelView 字段的key
     */
    alias: string;
    /**
     * 主播ID
     */
    authorId: number | string;
    biz: string;
    /**
     * 预热接口的originName字段
     */
    label: string;
    /**
     *
     */
    oldLabel: string;
}

export interface SetCustomLabelPostResponse {
    [key: string]: any;
    result: number;
}
