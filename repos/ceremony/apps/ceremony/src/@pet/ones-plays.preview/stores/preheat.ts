import { defineStore } from 'pinia';
import { Toast } from '@lux/sharp-ui-next';
import { authorId } from '@alive-ui/actions';
import { setCustomLabel } from '../services/setCustomLabel';
import { getHotInfo } from '../services/preheat';
import type { HotInfo, WarmUpPageInfo, HotInfoType } from '../services/preheat';

export default defineStore('preview', {
    state: () => {
        return {
            hotInfo: {} as HotInfoType,
        };
    },
    actions: {
        async init() {
            try {
                this.hotInfo = await getHotInfo(authorId);
            } catch (error) {
                console.log(error);
            }
        },
        async setCustomLabel(alias: string, label: string, oldLabel: string) {
            try {
                await setCustomLabel({
                    biz: activityBiz,
                    alias,
                    authorId,
                    label,
                    oldLabel,
                });
                Toast.info('赛道调整完成');
            } catch (error) {
                // Toast.info(error.data.error_msg || '网络异常');
            }
        },
    },
});
