<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import {
    ATabs,
    ATabList,
    ATabPanel,
    ATabPanels,
    ATab,
    ACard,
    ACardContent,
    ACardTitle,
} from '@alive-ui/base';
import { isOutLiveRoom } from '@alive-ui/actions';
import useStore from './stores/preheat';
import AuthorCard from './author-card//index.vue';
const store = useStore();
store.init();
const activeIndex = ref(0);
// const store = useStore();

const change = (_: any, index: number) => {
    activeIndex.value = index;
};

onMounted(() => {
    // if (store.scrollToReward) {
    //     setTimeout(() => {
    //         goAnchor('#rewardDom');
    //     }, 500);
    // }
});

const props = withDefaults(
    defineProps<{
        title: string;
        previewPrize: any;
        subTitle: string;
        participate1: string;
        participate2: string;
        authorLaneTips: string;
        scheduleConfig: any;
        previewUserImg: string;
        popupTitle: string;
    }>(),
    {
        title: '春季盛典奖励概览',
        previewPrize: () => [],
    },
);
</script>

<template>
    <div>
        <ACard>
            <ACardTitle class="title"> {{ title }} </ACardTitle>
            <ACardContent>
                <ATabs
                    :selected-index="activeIndex"
                    type="solid"
                    @change="change"
                >
                    <ATabList>
                        <ATab v-for="item in previewPrize" :key="item.rankId">{{
                            item.displayName
                        }}</ATab>
                    </ATabList>

                    <ATabPanels>
                        <ATabPanel
                            v-for="item in previewPrize"
                            :key="item.rankId"
                        >
                            <img class="prize-img mt-1" :src="item.img"
                        /></ATabPanel>
                    </ATabPanels>
                </ATabs>
            </ACardContent>
        </ACard>
        <AuthorCard
            v-if="!isOutLiveRoom"
            :title="subTitle"
            :participate1="participate1"
            :participate2="participate2"
            :author-lane-tips="authorLaneTips"
            :schedule-config="scheduleConfig"
            :preview-user-img="previewUserImg"
            :popup-title="popupTitle"
        ></AuthorCard>
    </div>
</template>

<style scoped>
.c-card-content-216-19122 {
    width: 358px;
    margin: 20px 12px 0;
    border-radius: 8px;
}
.prize-img {
    width: 358px;
    margin-left: 12px;
    margin-right: 12px;
}
</style>
