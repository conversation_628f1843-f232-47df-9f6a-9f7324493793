<template>
    <TrackTab
        :context-name="contextName"
        use-props
        :data="data?.subTabData"
        :city-data="data.cityData"
        @change="tabChange"
    />
</template>

<script lang="ts" setup>
import TrackTab from '@pet/ones-rank.track-tab/index.vue';
import { dataFun } from '@pet/ones-rank.hour-rotate/data';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
const emits = defineEmits(['change']);

const props = withDefaults(defineProps<PropsCommonParams>(), {
    contextName: Symbol.for('ctx'),
});

const data = dataFun(props.contextName);
const tabChange = (rankId: number) => {
    emits('change', rankId);
};
</script>

<style lang="less" scoped></style>
