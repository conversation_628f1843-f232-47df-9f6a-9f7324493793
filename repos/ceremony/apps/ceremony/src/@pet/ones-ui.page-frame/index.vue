<script lang="ts" setup>
import { useRouter } from 'vue-router';
import YodaImage from '@pet/yoda.image/img.vue';
import RuleIcon from '@pet/ones-ui.rule-icon/index.vue';
import { BackStatic } from '@alive-ui/icon';
import {
    PageHeader,
    PageAdaptor,
    ElseStatus,
    RefreshIcon,
} from '@alive-ui/base';
import { exitWebView, isOutLiveRoom, Report } from '@alive-ui/actions';
import type { FrameSchema } from './schema';
const router = useRouter();
const emits = defineEmits(['refresh']);
const props = withDefaults(defineProps<FrameSchema>(), {
    data: () => {
        return {
            image: '',
            rule: '',
            showBack: true,
            homeStatus: {
                success: true,
            },
        };
    },
    needCenterStyle: false,
    needRefresh: true,
});
const handleBack = () => {
    if (props?.data?.backFunc) {
        props.data.backFunc();
        return;
    }
    const backFunction = {
        back: () => {
            router.back();
        },
        exit: () => {
            exitWebView();
        },
    };
    const backAction = props.data?.backAction || 'back';
    if (backAction !== 'back' && backAction !== 'exit') {
        throw new Error('Invalid backAction value');
    }
    backFunction[backAction]();
};
const homeRefresh = () => {
    emits('refresh');
};
const dataRefresh = () => {
    emits('refresh');
};
const onYodaImageError = (e: Event, level: string) => {
    Report.biz.error('kv区yoda-image异常', {
        error: e,
        level,
    });
};
</script>

<template>
    <PageAdaptor :type="'cut'">
        <PageHeader class="page-head-height">
            <template #leftTop>
                <!-- 返回按钮 -->
                <BackStatic
                    v-if="data?.showBack"
                    class="back-btn"
                    @click="handleBack"
                />
                <slot name="left-top"> </slot>
            </template>
            <template #rightTop>
                <!-- 规则按钮 -->
                <RuleIcon
                    v-if="data.rule"
                    :rule-key="data.rule"
                    :func-btn-log-params="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: '规则',
                        },
                    }"
                />
                <slot name="right-top"> </slot>
            </template>
            <div class="kv-middle">
                <slot name="kv-middle">
                    <!-- kv区图片 -->
                    <YodaImage
                        v-if="data?.image"
                        class="kv-image"
                        :src="data.image"
                        @error="(e: Event) => onYodaImageError(e, 'l1')"
                    ></YodaImage>
                </slot>
                <slot name="kv-other"></slot>
            </div>
        </PageHeader>
        <div v-if="data?.homeStatus?.success" class="page-frame-container">
            <!-- 主内容区 -->
            <slot name="main-container"> </slot>
        </div>
        <!-- 主页异常状态 -->
        <ElseStatus
            v-else
            :class="needCenterStyle ? 'else-status' : ''"
            :page-status="data?.homeStatus"
            :is-show-refresh="true"
            :no-data="false"
            @refresh="homeRefresh"
        />
        <!-- 刷新按钮 -->
        <RefreshIcon
            v-if="needRefresh"
            :refresh-time="1500"
            @refresh="dataRefresh"
        />
        <slot name="bottomLeft"></slot>
    </PageAdaptor>
</template>
<style lang="less" scoped>
.page-frame-container {
    margin-top: var(--marginTop);
    position: relative;
    padding-bottom: 20px;
    min-height: 160px;
}
.rank-kv-rule {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}
.page-head-height {
    min-height: var(--pagHeadHeight, 350px);
    height: auto;
    --y-img-height: var(--yImgHeight, 350px);
    --y-img-width: var(--yImgWidth, 414px);
}
.kv-middle {
    position: relative;
}
.else-status {
    position: absolute;
    top: 50%;
    left: 50%;
    min-width: 100px;
    transform: translate(-50%, -50%);
}
</style>
