## 组件概述
ones-ui.page-frame 是一个用于构建页面的基础容器组件，提供了KV区配置、返回按钮、规则、刷新等其他区展示等功能。该组件支持通过插槽自定义内容，。
功能支持
1. 请求状态管理
● 内置状态：加载中、成功、失败兜底。
2. KV区配置
● 支持配置返回按钮和规则按钮。
● KV区中间区域可展示图片。
● 插槽包括：
	○ left-top：配置KV区左侧入口icon。
	○ right-top：配置KV区右侧入口icon。
	○ kv-middle：KV区中间区域，支持展示图片。
	○ main-container：放置主内容。

## schema
```typescript
export interface FrameSchema {
    kvData?: {
        // 右上角规则链接，可选
        rule?: string;
        // kv图配置，可选
        image?: string;
        // 是否展示返回按钮
        showBack?: boolean;
        // 返回方式，可选退出容器可返回上一页
        backAction?: string; // "back" | "exit"
        // 自定义返回方式
        backFunc?: () => void;
    };
    showRefresh?: boolean;
    // 整体页面状态
    homeStatus?: Status;
}
```
## 该组件接收以下 props：

| Prop Name     | Type     | Default Value | Description                               |
|---------------|----------|---------------|-------------------------------------------|
| contextName   | String   | ''            | 上下文名称，用于数据提供和注入                 |
| data          | Object   | FrameSchema   | 符合 FrameSchema 类型                       |
| needRefresh   | Boolean  | true          | 是否需要刷新按钮                             |
## 使用示例
以下是如何在 Vue 组件中使用 ones-ui.page-frame 的示例：

```vue

<script lang="ts" setup>
import { provide, defineAsyncComponent } from 'vue';
import { storeToRefs } from 'pinia';
import PageFrame from '@pet/ones-ui.page-frame/index.vue';
import ViewDetailRank from '@pet/ones-rank.view-detail-rank/index.vue';
import useEntryStore from '@pet/ones-rank.secondary-entry/models/index';
import { sendFmp } from '@gundam/weblogger';
import useHomeStore from './models/index-home';
const SceneAll = defineAsyncComponent(() => {
    return import('@pet/ones-rank.scene-all-main/index.vue');
});
const SecondaryEntry = defineAsyncComponent(() => {
    return import('@pet/ones-rank.secondary-entry/index.vue');
});
const AwardPop = defineAsyncComponent(() => {
    return import('@pet/ones-rank.award-pop/index.vue');
});
const contextName = 'liveChickenMainHome';
const contextPkName = 'liveChickenMainPk';
const homeStore = useHomeStore();
const entryStore = useEntryStore();
const { homeData, indexInfoData } = storeToRefs(homeStore);
const { secondaryEntryInfo } = storeToRefs(entryStore);
homeStore.init().finally(() => {
    sendFmp();
    // todo
    // pk 接口
    entryStore.init(homeData.value?.rankAliasName, 'sub');
});
provide(contextName, indexInfoData);
// todo
// provide(contextPkName, PkData);
</script>

<template>
    <PageFrame
        class="page-chicken-main"
        :data="homeData.frameData"
        :context-name="contextName"
        @refresh="homeStore.refresh"
    >
        <template #left-top>
            <SceneAll :context-name="contextName" @change="homeStore.change" />
            <!-- 其他二级入口，待定 -->
            <SecondaryEntry
                flex-gap="8px"
                :entry-data="secondaryEntryInfo?.topLeftArea"
                need-margin
            />
        </template>
        <template #right-top>
            <SecondaryEntry
                flex-gap="8px"
                :entry-data="secondaryEntryInfo?.topRightArea"
                need-margin
            />
        </template>
        <template #kv-middle>
            <!-- kv 标题,暂缓 -->
            <!-- <KvTitle /> -->
            <!-- 奖励弹窗, 新增生态组件，todo： @guozhao, context-name 传给数据的上下文 -->
            <AwardPop :context-name="contextPkName" />
        </template>
        <template #main-container>
            <!-- 当前主播组件,待定，先放-->
            <!-- 吃鸡赛核心玩法组件， todo：@guozhao-->
            <!-- 查看完整榜单 todo： @zhouxianxian -->
            <ViewDetailRank path-name="universal-rank" />
        </template>
    </PageFrame>
</template>
<style lang="less" scoped>
.page-chicken-main {
    --pagHeadHeight: 200px;
    --marginTop: -40px;
    height: auto;
    background-image: url('./assets/universal_bg.png');
    background-position: center top;
    background-size: 100%;
    background-repeat: no-repeat;
    background-color: #530400;
}
</style>
```
