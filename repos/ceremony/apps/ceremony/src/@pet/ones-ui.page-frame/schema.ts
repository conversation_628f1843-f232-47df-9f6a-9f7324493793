export interface Status {
    init?: boolean;
    loading?: boolean;
    error?: boolean;
    success?: boolean;
}
export interface BaseData {
    // 右上角规则kconf key，可选
    rule?: string;
    // kv图配置，可选
    image?: string;
    // 是否展示返回按钮
    showBack?: boolean;
    // 返回方式，可选退出容器可返回上一页
    backAction?: string; // "back" | "exit"
    // 自定义返回方式
    backFunc?: () => void;
    // 整体页面状态
    homeStatus?: Status;
}

export interface FrameSchema {
    data: BaseData;
    needRefresh?: boolean;
    needCenterStyle?: boolean;
}
