<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import pkgStore from '@pet/ones-ui.kv-redpack/store';
import { RecommendSource } from '@pet/ones-ui.kv-redpack/interface/type';
import RedpackVertical from '@pet/ones-ui.kv-redpack/index.vue';
import {
    ATabs,
    ATabList,
    ATabPanel,
    ATabPanels,
    ATab,
    ACard,
    ACardContent,
    ACardTitle,
} from '@alive-ui/base';
import { isYodaPCContainer } from '@alive-ui/actions';
const store = pkgStore();
const { redPkgList } = storeToRefs(store);

const props = withDefaults(
    defineProps<{
        title?: string;
        extraCardKey: string;
    }>(),
    {
        title: '看直播 红包抢不停',
    },
);

const emits = defineEmits(['correct-ref-top']);

function correctRefTop(payload: boolean) {
    emits('correct-ref-top', {
        extraCardKey: props.extraCardKey,
        show: payload,
    });
}
</script>

<template>
    <div>
        <ACard v-show="redPkgList && redPkgList.length && !isYodaPCContainer">
            <ACardTitle class="title">{{ title }}</ACardTitle>
            <ACardContent class="mt-2 redbag-box">
                <RedpackVertical
                    self-refresh
                    :data-config="{ liveSquareSource: 10044 }"
                    :pack-type="2"
                    :recommend-source="RecommendSource.CONTENT"
                    :new-storm-style="true"
                    @render-finish="correctRefTop"
                />
            </ACardContent>
        </ACard>
    </div>
</template>

<style scoped lang="less">
.redbag-box {
    padding: 0 13px;
}
</style>
