README
### 组件功能描述
该组件实现了一个关注用户的功能，用户可以通过点击按钮来关注或取消关注。组件会根据传入的 followStatus 属性来判断当前的关注状态，并在用户点击按钮时触发关注操作。该组件使用了 Vue 3 的 Composition API，并结合了 lodash 的节流函数来优化点击事件的处理。

### 主要功能
- 显示关注按钮，当用户未关注时可点击进行关注。
- 通过 focusUsers 方法实现关注用户的操作。
- 使用 watchEffect 监听 followStatus 的变化，动态更新按钮状态。
### 该组件接受以下 props：
### Prop Name

| Prop Name     | Type    | Description                                                                 |
|---------------|---------|-----------------------------------------------------------------------------|
| followStatus  | Boolean | 关注状态，必传，表示用户是否已关注。                                        |
| focusParams   | Object  | 关注功能必传，包含关注用户所需的参数，类型为 FocusUserProps 的前三个参数，去掉 info 属性。 |

---


### Vue 使用代码 Demo
以下是如何在 Vue 组件中使用该关注用户组件的示例：

```vue
<template>
    <div>
        <FollowButton
            :followStatus="isFollowed"
            :focusParams="focusParams"
            @click="handleFollowClick"
        />
    </div>
</template>
<script setup lang="ts">
import { ref } from 'vue';
import FollowButton from './FollowButton.vue'; // 假设组件文件名为 FollowButton.vue

const isFollowed = ref(false);
const focusParams = {
    userId: '12345', // 示例用户 ID
    // 其他关注所需参数
};

const handleFollowClick = () => {
    // 处理关注点击事件
    console.log('关注按钮被点击');
};
</script>
```

