<template>
    <AddStatic
        v-if="!isFollowed"
        class="w-16px h-16px"
        @click.stop="actionButtonEvent"
    />
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { throttle } from 'lodash-es';
import { useBaseRankItemContext } from '@alive-ui/pro';
import { AddStatic } from '@alive-ui/icon';
import {
    type FocusUserProps,
    activityBiz,
    focusUsers,
    getPageCode,
} from '@alive-ui/actions';

const props = defineProps<{
    /* 关注功能必传，关注状态 */
    followStatus?: boolean;
    anchorStageType?: number;
    /* 关注功能必传 FocusUserProps 的前三个参数 */
    focusParams: Omit<FocusUserProps, 'info'>;
}>();

const { logRankItemClick, getRankListLogParams } =
    useBaseRankItemContext() || {};

const baseLogParams = () => {
    return getRankListLogParams
        ? {
              info: {
                  followSource: 187,
                  bizCustomParams: JSON.stringify({
                      live_activity_name: activityBiz,
                      position: 'RANK_LIST_FOLLOW_OVERT',
                      schedule_id: getRankListLogParams().scheduleId,
                      extraFollow: JSON.stringify({
                          page_code: getPageCode(),
                      }),
                  }),
              },
          }
        : {};
};

const isFollowed = ref<boolean>(false);

setTimeout(() => {
    console.log(props.anchorStageType, 'props.anchorStageType');
}, 3000);

watchEffect(() => {
    isFollowed.value = props.followStatus;
});

const focusUser = async () => {
    if (isFollowed.value) {
        return;
    }
    // 用在榜单内才会有这个方法
    logRankItemClick?.('FOLLOW_BTN');
    try {
        const success = await focusUsers({
            ...props.focusParams,
            ...baseLogParams(),
        });
        isFollowed.value = true;
    } catch (error) {}
};

const actionButtonEvent = throttle(focusUser, 800, {
    leading: true,
    trailing: false,
});
</script>
