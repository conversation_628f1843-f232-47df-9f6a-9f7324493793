<template>
    <div class="video-wrapper">
        <KwaiPlayer class="my-player" width="100%" height="100%">
            <KwaiPlayerYodaVideo
                ref="player"
                :disable-native-control="true"
                :kernel="createVideoContext"
                :config="config"
                @ended="endVideo"
                @error="handlePlayerError"
            >
            </KwaiPlayerYodaVideo>
        </KwaiPlayer>
    </div>
</template>
<script lang="ts" setup>
import { computed, ref, onMounted, onBeforeUnmount } from 'vue';
import { createVideoContext, PlayerType } from '@ks/yoda-video-player';
import { KwaiPlayer, KwaiPlayerYodaVideo } from '@ks/player';
import { Report } from '@alive-ui/actions';
import '@ks/player/dist/vue3/esm/style.css';
interface VideoUrl {
    url: string;
}

const player = ref<typeof KwaiPlayerYodaVideo | null>(null);

const handlePlayerError = (err: any) => {
    // TODO:雷达自定义事件
    Report.biz.error('视频播放错误', {
        error: err,
    });
    console.log('Player Error: ', err);
};
const curVideoUrl = ref('');
const curVideoIndex = ref(0);
const props = defineProps({
    videoUrl: {
        // 视频地址
        type: Array as () => VideoUrl[],
        required: true,
    },
    posterUrl: {
        // 封面图
        type: String,
        default: '',
    },
    autoplay: {
        // 自动播放
        type: Boolean,
        required: false,
        default: true,
    },
    objectFit: {
        // 视频展示方式
        type: String,
        required: false,
        default: 'cover',
    },
    loop: {
        // 循环
        type: Boolean,
        require: false,
        default: true,
    },
    lastVideoLoop: {
        // 最后一个视频循环
        type: Boolean,
        default: false,
    },
    muted: {
        // 静音
        type: Boolean,
        default: true,
    },
});
const emits = defineEmits(['playEnd']);
// 多个视频循环，默认播放第一个视频
curVideoUrl.value = props.videoUrl[0]?.url;
// 如果多个视频，则loop为false，多视频循环播放，如果只有一个，则props的loop
let isEveryLoop = props.videoUrl?.length > 1 ? false : props.loop;
// 播放下一个视频
const nextVideo = async () => {
    if (!(props.videoUrl?.length > 1)) {
        return;
    }
    // 整体不循环但是最后一个视频需要循环
    if (
        !props.loop &&
        curVideoIndex.value === props.videoUrl.length - 2 &&
        props.lastVideoLoop
    ) {
        curVideoIndex.value++;
        isEveryLoop = true;
        curVideoUrl.value = props.videoUrl[curVideoIndex.value].url;
        videoReload();
        return;
    }
    // 整体不循环，则到最后一个视频后，不循环到第0个视频
    if (!props.loop && curVideoIndex.value === props.videoUrl.length - 1) {
        return;
    }
    // 下一个 || 第一个
    curVideoIndex.value =
        curVideoIndex.value < props.videoUrl.length - 1
            ? curVideoIndex.value + 1
            : 0;
    curVideoUrl.value = props.videoUrl[curVideoIndex.value].url;
    await videoReload();
};
const endVideo = async () => {
    // index如果是最后一个，emits end
    await nextVideo();
    emits('playEnd');
};
const preVideo = async () => {
    if (!(props.videoUrl?.length > 1)) {
        return;
    }
    // 上一个 || 最后一个
    curVideoIndex.value =
        curVideoIndex.value > 0
            ? curVideoIndex.value - 1
            : props.videoUrl?.length - 1;
    curVideoUrl.value = props.videoUrl[curVideoIndex.value].url;
    await videoReload();
};
// 修改视频资源
const changeUrl = async (url: string) => {
    curVideoUrl.value = url;
    await videoReload();
};
const videoReload = async () => {
    await player.value?.load({
        muted: true,
    });
    player.value?.play();
};

defineExpose({
    preVideo,
    nextVideo,
    changeUrl,
    videoReload,
});
const config = computed(() => {
    return {
        type: PlayerType.VIDEO,
        playerConfig: {
            autoplay: props.autoplay,
            muted: false,
            videoType: 'mp4',
            triggerTimeUpdate: true,
            poster: props.posterUrl,
            loop: isEveryLoop,
            src: [
                {
                    url: curVideoUrl.value,
                },
            ],
            videoFallbackSrc: [
                {
                    url: curVideoUrl.value,
                },
            ],
        },
    };
});
onMounted(async () => {
    if (config.value?.playerConfig?.src[0]?.url) {
        await player.value?.load({
            muted: true,
        });
        try {
            player.value?.play();
        } catch (err) {
            console.log(err);
        }
    }
});
</script>

<style scoped lang="less">
.video-wrapper {
    width: 100%;
    height: 100%;
    background-color: transparent;
    :deep(.kwai-player-plugins) {
        z-index: auto;
    }
    :deep(.kwai-player-container-video),
    :deep(.kwai-yoda-player-container-video) {
        background-color: transparent;
    }
}
</style>
