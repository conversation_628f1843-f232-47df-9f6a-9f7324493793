# 快手视频播放器组件文档
## 功能概述
`@pet/ones-ui.video-ks-player` 是一个基于快手播放器（`@ks/player` 和 `@ks/yoda-video-player`）的视频播放组件。该组件支持多个视频的循环播放、自动播放、静音等功能，并提供了自定义事件处理和错误报告。
## 属性
| 属性名 | 类型 | 默认值 | 描述 |
| ------ | ---- | ------ | ---- |
| `videoUrl` | `Array<VideoUrl>` | - | 视频地址数组，每个元素是一个对象，包含 `url` 属性。 |
| `posterUrl` | `String` | `''` | 视频封面图的 URL。 |
| `autoplay` | `Boolean` | `true` | 是否自动播放。 |
| `objectFit` | `String` | `'cover'` | 视频展示方式，可选值为 `'contain'` 或 `'cover'`。 |
| `loop` | `Boolean` | `true` | 是否循环播放所有视频。 |
| `lastVideoLoop` | `Boolean` | `false` | 是否最后一个视频循环播放。 |
| `muted` | `Boolean` | `true` | 是否静音。 |
## 方法
| 方法名 | 参数 | 返回值 | 描述 |
| ------ | ---- | ------ | ---- |
| `preVideo` | - | `Promise<void>` | 播放上一个视频。 |
| `nextVideo` | - | `Promise<void>` | 播放下一个视频。 |
| `changeUrl` | `url: string` | `Promise<void>` | 更改当前播放的视频 URL。 |
| `videoReload` | - | `Promise<void>` | 重新加载当前视频。 |

## 注意事项
- `videoUrl` 是必需的属性，必须提供至少一个视频 URL。
- `posterUrl` 是可选的，如果不提供则默认为空字符串。
- `autoplay` 和 `muted` 的默认值分别为 `true` 和 `true`。
- `loop` 和 `lastVideoLoop` 控制视频的循环播放行为，具体逻辑见上述代码中的 `nextVideo` 方法。
- 组件在挂载时会自动加载并播放第一个视频。
## 依赖项
- `@ks/yoda-video-player`
- `@ks/player`
- `@alive-ui/actions`