import { is4Tab } from '@pet/ones-use.is4Tab/index';

const HTML_VERSION_CACHE_KEY = '24_ceremony_4tab_html_version_cache_key';

/**
 * 背景：app 启动后， 4 tab webview 容器会保持常驻，除非用户冷启动 app，否则前端主文档不会重新加载。
 * 为了避免重要的 Bug Fix 和需求变更发布无法快速触达用户，
 * 在页面可见性变化自动刷新接口的基础上，配合接口下发最新前端版本字段，
 * 和用户本地缓存版本做对比，检测到有更新时主动 reload() 一次 HTML 文档；
 * @param newHtmlVersion 新的 HTML 版本号
 */
export function autoRefresh(newHtmlVersion?: string) {
    if (!is4Tab) {
        return;
    }
    if (!newHtmlVersion) {
        return;
    }
    const oldHtmlVersion = localStorage.getItem(HTML_VERSION_CACHE_KEY) || '';
    let needReload = false;
    if (oldHtmlVersion) {
        if (oldHtmlVersion !== newHtmlVersion) {
            needReload = true;
        }
    }
    localStorage.setItem(HTML_VERSION_CACHE_KEY, newHtmlVersion);
    if (needReload) {
        location.reload();
    }
}
