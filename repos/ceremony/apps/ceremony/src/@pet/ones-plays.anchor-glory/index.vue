<template>
    <div v-if="isShow" class="anchor-glory">
        <ACard>
            <ACardTitle> {{ title }} </ACardTitle>
            <ACardSubtitle> {{ subTitle }} </ACardSubtitle>
            <ACardContent class="anchor-glory__content">
                <Layout2Col
                    v-slot="{ item }"
                    :list="list"
                    key-name="photoId"
                    class="mt-[20px] px-[12px]"
                >
                    <div
                        v-show-log="{
                            action: 'OP_ACTIVITY_AUTHOR_CARD',
                            params: {
                                module: '主播的荣耀',
                                author_id: item?.userInfo?.userId,
                                video_id: item?.photoId,
                            },
                        }"
                        v-click-log="{
                            action: 'OP_ACTIVITY_AUTHOR_CARD',
                            params: {
                                module: '主播的荣耀',
                                author_id: item?.userInfo?.userId,
                                video_id: item?.photoId,
                            },
                        }"
                        class="video-item"
                        @click="goDetail(item?.photoId, item?.userInfo?.userId)"
                    >
                        <div
                            class="pic"
                            :style="{ background: getImgStyle(item.imgUrl) }"
                        ></div>
                        <span class="desc a-text-main-o2">
                            <TextLineCut
                                :text="item.name"
                                :max-lines="2"
                            ></TextLineCut>
                        </span>
                    </div>
                </Layout2Col>
                <div
                    v-if="showMoreKwaiUrl && moreKwaiUrl"
                    v-click-log="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: { btn_type: '查看全部' },
                    }"
                    class="take-more"
                    @click="toMore"
                >
                    <span class="a-text-main-o2 text-12">查看全部</span>
                    <Right class="anchor-glory__icon a-text-main-o2" />
                </div>
            </ACardContent>
            <div
                v-click-log="{
                    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                    params: { btn_type: '换一换' },
                }"
                class="refresh-item"
                @click="throttleRefresh"
            >
                <span class="a-text-main-o2 text-12">换一换</span>
                <Refresh class="anchor-glory__icon a-text-main-o2 ml-2px" />
            </div>
        </ACard>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { throttle } from 'lodash-es';
import TextLineCut from '@pet/ones-ui.text-line-cut/index.vue';
import Layout2Col from '@pet/ones-ui.layout-2col/index.vue';
import { Toast } from '@lux/sharp-ui-next';
import { Right, Refresh } from '@alive-ui/icon';
import { ACard, ACardContent, ACardTitle, ACardSubtitle } from '@alive-ui/base';
import {
    // nameSlice,
    bolIsAuthor,
    toWorkVideoPage,
    isYodaPCContainer,
} from '@alive-ui/actions';
import { shakeAnchorGlory } from './services/shake';
import type { authorHonorItem } from './schemes';

interface ImgItem {
    url: string;
    cdn: string;
}
const props = withDefaults(
    defineProps<{
        title?: string;
        subTitle: string;
        authorHonorList: authorHonorItem[];
        moreKwaiUrl: string;
        showMoreKwaiUrl: boolean;
    }>(),
    {
        title: '主播的荣耀',
        subTitle: '直击荣耀主播高光时刻',
        authorHonorList: () => {
            return [];
        },
        moreKwaiUrl: '',
        showMoreKwaiUrl: false,
    },
);
const list = ref<authorHonorItem[]>(props.authorHonorList);

const getImgStyle = (imgList: ImgItem[]) => {
    const imgUrl = imgList?.[0].url;
    return `#000 url("${imgUrl}") center / 100% no-repeat`;
};
const goDetail = (workId: string, userId: string) => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    toWorkVideoPage(workId, userId);
};
const isShow = computed(() => {
    return list.value?.length > 0;
});
const toMore = () => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    location.href = props.moreKwaiUrl;
};
const refresh = async () => {
    const res = await shakeAnchorGlory();
    list.value = res?.authorHonorList?.list;
};
const throttleRefresh = throttle(refresh, 1000); // 频控,限制每1000ms执行一次
</script>

<style lang="less" scoped>
.anchor-glory {
    position: relative;

    &__icon {
        width: 10px;
        height: 10px;
    }
    .refresh-item {
        display: flex;
        justify-content: center;
        align-items: center;
        position: absolute;
        top: 25px;
        right: 12px;
    }
    &__content {
        position: relative;
        .video-item {
            box-sizing: border-box;
            width: 173px;
            display: flex;
            flex-direction: column;
            .pic {
                box-sizing: border-box;
                width: 173px;
                height: 129px;
                border-radius: 8px;
            }
            .desc {
                margin-top: 4px;
                width: 173px;
                // max-height: 36px;
                font-size: 12px;
                line-height: 18px;
            }
        }
        .video-item:nth-child(n + 3) {
            margin-top: 12px;
        }
        .video-item:nth-child(2n) {
            margin-left: 12px;
        }
        .take-more {
            margin-top: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}
</style>
