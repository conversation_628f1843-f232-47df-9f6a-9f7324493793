<script lang="ts" setup>
import { computed, ref, onBeforeUnmount, watch } from 'vue-demi';
import { transViewValue } from '@pet/core.mobile';
import { bez, type BezierData, type PathBezier } from './path/pathBezier';
import { type BezierEasing, getBezierEasing } from './bezier/bezierEasing';

interface Props {
    /**
     * 飞向的dom id
     */
    flyToTarget?: string;
    /**
     * 飞向的位置xy坐标，优先级比flyToTarget低
     */
    flyToTargetPosition?: { x: number; y: number };
    /**
     * 渐变贝塞尔曲线参数
     */
    fadeBezier?: number[];
    /**
     * 动效持续时间
     */
    duration?: number;
    /**
     * 右侧路径贝塞尔参数，另一侧会根据坐标镜像
     */
    bezier?: number[];
    /**
     * 时间贝塞尔参数，路径的时间beizer和缩放bezier共用
     */
    timeBezier?: number[];
    /**
     * 整体延迟时间
     */
    delay?: number;
}

const props = defineProps<Props>();

// 飞顶部、飞底部、飞中间，三套规则
// 终点高于起点 200就用飞顶部、低于起点200就飞底部、200内用飞中间的
// 左右用镜像
const flyTopBezierConfig = {
    bezier: [0, -147, 0, 2],
    timeBezier: [0.17, 0, 0.09, 1],
    fadeBezier: [0, 0, 1, 1],
    duration: 667,
};
const flyMiddleBezierConfig = {
    bezier: [-1, -146, 1, -223],
    timeBezier: [0.17, 0, 0.09, 1],
    fadeBezier: [0, 0, 1, 1],
    duration: 700,
};
const flyBtmBezierConfig = {
    bezier: [1.5, -93, 21, -340],
    timeBezier: [0.73, 0, 0.86, 1],
    fadeBezier: [1, 0, 0.89, 1],
    duration: 600,
};

const emits = defineEmits<{
    (event: 'end'): void;
}>();

let frameId: number;
let doFly: ReturnType<typeof setTimeout> | null;
const show = ref(true);
const isStart = ref(false);
const timeBezierRef = ref([0.17, 0, 0.09, 1]);
const durationRef = ref(667);
const fadeBezierRef = ref([0.17, 0, 0.09, 1]);
const translateX = ref<number | undefined>(0);
const translateY = ref<number | undefined>(0);
const flyItem = ref<HTMLElement | null>();

const xRef = ref<number | undefined>();
const yRef = ref<number | undefined>();
const toXRef = ref<number | undefined>();
const toYRef = ref<number | undefined>();
const xValue = computed(() => xRef.value);
const yValue = computed(() => yRef.value);
const toXValue = computed(() => toXRef.value);
const toYValue = computed(() => toYRef.value);
const FLY_MIN_OFFSET = 200;

function getDist(from?: number, to?: number) {
    if (from !== undefined && to !== undefined) {
        return to - from;
    }
}

const distX = computed(() => getDist(xValue.value, toXValue.value));
const distY = computed(() => getDist(yValue.value, toYValue.value));

const translate = computed(() => {
    if (translateX.value === undefined || translateY.value === undefined) {
        return 'none';
    }
    return `translate(${translateX.value}px,${translateY.value}px)`;
});

function getAutoFlyInfo(
    target?: string,
    targetPosition?: { x: number; y: number },
    selfEl?: HTMLElement | null,
) {
    let targetElRect;
    if (target) {
        targetElRect = document.getElementById(target)?.getBoundingClientRect();
    }
    const selfRect = selfEl?.getBoundingClientRect();
    let infoX;
    let infoY;
    let infoToX;
    let infoToY;
    if (selfRect) {
        infoX = selfRect.left + selfRect.width / 2;
        infoY = selfRect.top + selfRect.height / 2;
    }
    if (targetElRect) {
        infoToX = targetElRect.left + targetElRect.width / 2;
        infoToY = targetElRect.top + targetElRect.height / 2;
    } else if (targetPosition) {
        infoToX = transViewValue(targetPosition.x);
        infoToY = transViewValue(targetPosition.y);
    }
    return {
        infoX,
        infoY,
        infoToX,
        infoToY,
    };
}

type FrameInfo = {
    bezier: BezierData;
    timeBezier: BezierEasing;
    distX: number;
    distY: number;
    startTime: number;
};

function calcPath(bezier: BezierData, percent: number) {
    const distanceInLine = bezier.segmentLength * percent;
    let addedLength = 0;
    let j = 0;
    let flag = true;
    const jLen = bezier.points.length;
    let kLen = -1;
    let k = -1;
    const newValue: number[] = [];

    while (flag) {
        addedLength += bezier.points[j].partialLength;
        if (
            distanceInLine === 0 ||
            percent === 0 ||
            j === bezier.points.length - 1
        ) {
            kLen = bezier.points[j].point.length;
            for (k = 0; k < kLen; k++) {
                newValue[k] = bezier.points[j].point[k]!;
            }
            break;
        } else if (
            distanceInLine >= addedLength &&
            distanceInLine < addedLength + bezier.points[j + 1].partialLength
        ) {
            const segPerc =
                (distanceInLine - addedLength) /
                bezier.points[j + 1].partialLength;
            kLen = bezier.points[j].point.length;
            for (k = 0; k < kLen; k++) {
                newValue[k] =
                    bezier.points[j].point[k] +
                    (bezier.points[j + 1].point[k] -
                        bezier.points[j].point[k]) *
                        segPerc;
            }
            break;
        }
        if (j < jLen - 1) {
            j++;
        } else {
            flag = false;
        }
    }

    return newValue;
}

function run({ bezier, timeBezier, distX, distY, startTime }: FrameInfo) {
    const per = (new Date().getTime() - startTime) / durationRef.value;
    const percent = timeBezier.get(per);

    if (per < 1) {
        const newValue = calcPath(bezier, percent);
        translateX.value = newValue[0];
        translateY.value = newValue[1];
        cancelAnimationFrame(frameId);
        /**
         * https://stackoverflow.com/questions/37673711/what-happen-when-i-call-requestanimationframe-multiple-times
         */
        frameId = requestAnimationFrame(
            run.bind(null, { bezier, timeBezier, distX, distY, startTime }),
        );
        return;
    }
    translateX.value = distX;
    translateY.value = distY;
    emits('end');
}

const flyingAnimation = computed(() =>
    isStart.value ? { animation: 'var(--adapt-bezier-fly-animation)' } : {},
);

const getAnimationStyle = computed(() => {
    return {
        '--adapt-bezier-fly-animation': `bezier-fly-scale ${
            durationRef.value
        }ms cubic-bezier(${timeBezierRef.value?.join(',')}) forwards, bezier-fly-fade ${durationRef.value}ms cubic-bezier(${fadeBezierRef.value?.join(',')}) forwards`,
    };
});

const flyTargetType = computed(() => {
    const distance = transViewValue(FLY_MIN_OFFSET);
    if (
        toYRef.value === undefined ||
        toXRef.value === undefined ||
        xRef.value === undefined ||
        yRef.value === undefined
    ) {
        return 'middle';
    }
    if (yRef.value - toYRef.value > distance) {
        return 'top';
    }
    if (toYRef.value - yRef.value > distance) {
        return 'bottom';
    }
    return 'middle';
});

const defaultBezierConfig = computed(() => {
    if (flyTargetType.value === 'top') {
        return flyTopBezierConfig;
    }
    if (flyTargetType.value === 'bottom') {
        return flyBtmBezierConfig;
    }
    return flyMiddleBezierConfig;
});

watch(
    () => props.timeBezier,
    (val) => {
        if (val) {
            timeBezierRef.value = val;
        } else {
            timeBezierRef.value = defaultBezierConfig.value.timeBezier;
        }
    },
    {
        immediate: true,
    },
);

watch(
    () => props.fadeBezier,
    (val) => {
        if (val) {
            fadeBezierRef.value = val;
        } else {
            fadeBezierRef.value = defaultBezierConfig.value.fadeBezier;
        }
    },
    {
        immediate: true,
    },
);

watch(
    () => props.duration,
    (val) => {
        if (val) {
            durationRef.value = val;
        }
    },
    {
        immediate: true,
    },
);

const bezierPath = computed(() => {
    if (
        toYRef.value === undefined ||
        toXRef.value === undefined ||
        xRef.value === undefined ||
        yRef.value === undefined
    ) {
        // 缺少值就默认用中部的
        return flyMiddleBezierConfig.bezier;
    }
    let bezierPath;
    if (props.bezier) {
        // 优先传入的 bezier 值
        bezierPath = props.bezier;
    } else {
        // 如果没有传参数用默认判断参数，动效同 wangzaichao 出的
        bezierPath = defaultBezierConfig.value.bezier;
    }

    return toXRef.value >= xRef.value
        ? bezierPath
        : [-bezierPath[0], bezierPath[1], -bezierPath[2], bezierPath[3]];
});

const boot = () => {
    if (props.flyToTarget || props.flyToTargetPosition) {
        const { infoX, infoY, infoToX, infoToY } = getAutoFlyInfo(
            props.flyToTarget,
            props.flyToTargetPosition,
            flyItem.value,
        );
        xRef.value = infoX;
        yRef.value = infoY;
        toXRef.value = infoToX;
        toYRef.value = infoToY && infoToY < 0 ? 0 : infoToY;
    }
    if (distX.value !== undefined && distY.value !== undefined) {
        const bezier = bez.buildBezierData(
            [0, 0],
            [distX.value, distY.value],
            [bezierPath.value[0], bezierPath.value[1]],
            [bezierPath.value[2], bezierPath.value[3]],
        );
        const timeBezier = getBezierEasing(
            timeBezierRef.value[0],
            timeBezierRef.value[1],
            timeBezierRef.value[2],
            timeBezierRef.value[3],
        );

        if (doFly) {
            clearTimeout(doFly);
        }

        doFly = setTimeout(() => {
            // emit('start');
            isStart.value = true;
            run({
                bezier,
                timeBezier,
                distX: distX.value!,
                distY: distY.value!,
                startTime: new Date().getTime(),
            });
        }, props.delay ?? 0);
    }
};

onBeforeUnmount(() => {
    cancelAnimationFrame(frameId);
    if (doFly) {
        clearTimeout(doFly);
    }
});

defineExpose({
    boot,
});
</script>

<template>
    <div v-if="!flyToTarget && !flyToTargetPosition">
        <slot />
    </div>
    <div
        v-else-if="show"
        :style="{
            transform: translate,
        }"
        class="bezier-fly"
    >
        <div
            ref="flyItem"
            class="bezier-fly-item"
            :style="[flyingAnimation, getAnimationStyle]"
        >
            <slot />
        </div>
    </div>
</template>

<style lang="scss" scoped>
.bezier-fly-item {
    position: relative;
    vertical-align: top;
    display: inline-block;
    transform-origin: center;
}
</style>

<style>
@keyframes bezier-fly-scale {
    from {
        transform: scale(1);
    }
    to {
        transform: scale(0.15);
    }
}

@keyframes bezier-fly-fade {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.1;
    }
}
</style>
