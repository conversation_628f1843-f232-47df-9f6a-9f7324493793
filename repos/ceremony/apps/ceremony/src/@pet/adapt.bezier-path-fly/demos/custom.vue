<pet-info lang="json">
{ "title": "参数配置", "description": "自定义飞入的参数" }
</pet-info>
<script setup lang="ts">
import { ref, nextTick, computed, watch } from 'vue-demi';
import Pilot from '../index.vue';

type PilotType = {
    boot: () => void;
};
const show = ref(true);
const targetId = ref('');
const pilotRef = ref<PilotType | null>(null);
const selfRef = ref<HTMLElement | null>(null);
const centerPosition = ref({
    x: 0,
    y: 0,
});

const unWatch = watch(
    selfRef,
    (el) => {
        const selfRect = el?.getBoundingClientRect();
        centerPosition.value = {
            x: selfRect?.x ?? 0,
            y: selfRect?.y ?? 0,
        };
        unWatch();
    },
    {
        flush: 'post',
    },
);

const deltaY = ref(0);

const fly = (id: string) => {
    targetId.value = id;
    show.value = true;
    const el = document.getElementById(id)?.getBoundingClientRect();
    deltaY.value = centerPosition.value.y - (el?.y ?? 0);
    nextTick(() => {
        pilotRef.value?.boot();
    });
};

const flyEnd = () => {
    show.value = false;
    setTimeout(() => {
        show.value = true;
    }, 1000);
};

function toPropsValue(props: string) {
    return props.split(',').map((n) => +n);
}

const pathBezierRef = ref('1, 0, 0, 268');
const pathBezier = computed(() => toPropsValue(pathBezierRef.value));

const timeBezierRef = ref('.61, .02, .59, 1');
const timeBezier = computed(() => toPropsValue(timeBezierRef.value));

const fadeBezierRef = ref('.5,0,.71,.99');
const fadeBezier = computed(() => toPropsValue(fadeBezierRef.value));

const durationRef = ref(1000);
const delayRef = ref(0);
</script>

<template>
    <div class="container">
        <div>deltaY: {{ deltaY }}</div>
        <div>
            路径贝塞尔：
            <input v-model="pathBezierRef" type="text" />
        </div>
        <div>
            时间贝塞尔：
            <input v-model="timeBezierRef" type="text" />
        </div>
        <div>
            渐隐贝塞尔：
            <input v-model="fadeBezierRef" type="text" />
        </div>
        <div>
            飞行总时间：
            <input v-model="durationRef" type="number" />
        </div>
        <div>
            延迟总时间：
            <input v-model="delayRef" type="number" />
        </div>
        <button @click="fly('top-left')">飞左上</button>
        <button @click="fly('top-right')">飞右上</button>
        <button @click="fly('left')">飞左中</button>
        <button @click="fly('right')">飞右中</button>
        <button @click="fly('bottom-left')">飞左下</button>
        <button @click="fly('bottom-right')">飞右下</button>
        <div class="wrapper">
            <Pilot
                v-if="show"
                ref="pilotRef"
                :fly-to-target="targetId"
                :bezier="pathBezier"
                :time-bezier="timeBezier"
                :fade-bezier="fadeBezier"
                :duration="durationRef || 0"
                :delay="delayRef || 0"
                @end="flyEnd"
            >
                <div ref="selfRef" class="start">起始点</div>
            </Pilot>
        </div>
        <div id="top-left" class="target top-left">左上</div>
        <div id="top-right" class="target top-right">右上</div>
        <div id="left" class="target left">左</div>
        <div id="right" class="target right">右</div>
        <div id="bottom-left" class="target bottom-left">左下</div>
        <div id="bottom-right" class="target bottom-right">右下</div>
    </div>
</template>

<style>
html,
body {
    margin: 0;
}
</style>

<style lang="scss" scoped>
.container {
    position: relative;
    width: 100vw;
    height: 100vh;
}

div {
    font-size: 16px;
}

.start,
.target {
    width: 200px;
    height: 200px;
}

.wrapper {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.start {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: aqua;
}

.target {
    font-size: 10px;
    background-color: sandybrown;
    position: absolute;
    width: 20px;
    height: 20px;
}

.top-left {
    top: 80px;
    left: 60px;
}

.top-right {
    top: 80px;
    right: 60px;
}

.left {
    top: 450px;
    left: 20px;
}

.right {
    top: 450px;
    right: 20px;
}

.bottom-left {
    bottom: 60px;
    left: 60px;
}

.bottom-right {
    bottom: 60px;
    right: 60px;
}
</style>
