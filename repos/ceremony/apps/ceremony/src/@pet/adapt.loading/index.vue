<script lang="ts">
export default {
    name: 'AdaptLoading',
};
</script>

<script lang="ts" setup>
import InfinityLoading from './infinity-loading.vue';
import CircleLoading from './circle-loading.vue';
import type { LoadingType } from './types';

interface Props {
    /**
     * Loading类型
     */
    type?: LoadingType;
    /**
     * 是不是占整屏
     */
    fullscreen?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    type: 'infinity',
});
</script>

<template>
    <CircleLoading v-if="type === 'circle'" colorful />
    <InfinityLoading v-else :fullscreen="fullscreen" />
</template>
