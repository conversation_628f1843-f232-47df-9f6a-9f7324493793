<script lang="ts">
export default {
    name: 'BaseCircleLoading',
};
</script>

<script lang="ts" setup>
import { computed } from 'vue-demi';

interface Props {
    /**
     * colorful为false的时候loading是白色
     */
    colorful?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    colorful: false,
});

const circleType = computed(() => [
    'circle',
    props.colorful && 'circle-colorful',
]);
</script>

<template>
    <span :class="circleType" />
</template>
<style>
:root {
    --adapt-circleLoading-width: 1em;
    --adapt-circleLoading-height: 1em;
    --adapt-circleLoading-background-image: url('./assets/circle.png');
}
</style>
<style lang="scss" scoped>
.circle {
    display: inline-block;
    width: var(--adapt-circleLoading-width);
    height: var(--adapt-circleLoading-height);
    background-image: var(--adapt-circleLoading-background-image);
    background-size: 100% 100%;
    animation: rolling 1s linear infinite;
    &.circle-colorful {
        --adapt-circleLoading-background-image: url('./assets/circle-dark.png');
    }
}

@keyframes rolling {
    100% {
        transform: rotate(-360deg);
    }
}
</style>
