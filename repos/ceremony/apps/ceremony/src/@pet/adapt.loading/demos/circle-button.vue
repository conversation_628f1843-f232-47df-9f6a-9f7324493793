<pet-info lang="json">
{ "title": "loading", "description": "圆圈加载" }
</pet-info>
<script setup lang="ts">
import Button from '@pet/adapt.button/index.vue';
</script>

<template>
    <div class="demo">
        <div>按钮中圆圈loading</div>
        <div class="container">
            <Button loading class="button" />
            <Button type="plain" loading class="button" />
        </div>
    </div>
</template>

<style src="@pet/adapt.reset/reset.css"></style>

<style lang="scss" scoped>
div {
    font-size: 14px;
    margin-bottom: 40px;
    text-align: center;
}
.button {
    margin: 10px;
}
</style>
