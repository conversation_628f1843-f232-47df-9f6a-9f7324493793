<script lang="ts">
export default {
    name: 'BaseInfinityLoading',
};
</script>

<script lang="ts" setup>
import { computed } from 'vue-demi';
import InfinityLoading from './assets/infinity-svg.vue';

interface Props {
    /**
     * loading
     */
    loading?: boolean;
    /**
     * 是不是占整屏
     */
    fullscreen?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    loading: true,
});

const loadingType = computed(() => [
    props.fullscreen && 'loading-fullscreen loading-center',
]);
</script>

<template>
    <div :class="loadingType">
        <InfinityLoading class="infinity" :loading="loading" />
    </div>
</template>
<style>
:root {
    --adapt-infinityLoading-color: #fff;
    --adapt-infinityLoading-size: 40px;
    --adapt-infinityLoading-fllscreen-background-color: rgba(0, 0, 0, 0.8);
}
</style>
<style lang="scss" scoped>
.infinity {
    display: inline-block;
    vertical-align: middle;
    width: var(--adapt-infinityLoading-size);
    height: var(--adapt-infinityLoading-size);
    color: var(--adapt-infinityLoading-color);
}

.loading-fullscreen {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: var(--adapt-infinityLoading-fllscreen-background-color);
}
</style>
