import { useRoute } from 'vue-router';
import { ref, onMounted, watch } from 'vue';
import { defineStore } from 'pinia';
import { getCookie, getQuery } from '@alive-ui/actions';

export default defineStore('bolls-author-store', () => {
    const route = useRoute();
    const isAuthorMatch = ref(false);

    const checkAuthorMatch = () => {
        const authorId = getQuery('authorId');
        const cookieUserId = getCookie('ud') || getCookie('userId'); // 当前用户id
        if (authorId && cookieUserId) {
            isAuthorMatch.value = authorId === cookieUserId;
        } else {
            isAuthorMatch.value = false;
        }
    };
    // onMounted(() => {
    //     checkAuthorMatch();
    // })

    checkAuthorMatch();

    // 监听路由变化
    watch(() => route?.query, checkAuthorMatch, { deep: true });

    return {
        isAuthorMatch,
    };
});
