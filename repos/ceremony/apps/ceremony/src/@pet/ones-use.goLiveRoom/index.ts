import { throttle } from 'lodash-es';
import { Toast } from '@lux/sharp-ui-next';
import {
    bolIsAuthor,
    isYodaPCContainer,
    liveStreamId as curLiveStreamId,
    openUpDown,
} from '@alive-ui/actions';
/**
 *
 * @param liveStreamId 当前要跳转的直播间'livestreamid'
 * @param liveStreamArr ['livestreamid1','livestreamid2','livestreamid3']数组格式
 * @param extraInfo  额外参数: targetposition 当前要跳转的直播间位置
 * @returns
 */

const threshold = 500;
export const goToLiveRoom = throttle(
    (liveStreamArr: string[], liveStreamId: string, extraInfo = {}) => {
        if (isYodaPCContainer) {
            Toast.info('请在快手APP内打开');
        }
        if (bolIsAuthor) {
            // 如果当前是主播端,不跳转
            Toast.info('开播中，不可跳转');
            return;
        }

        if (curLiveStreamId === liveStreamId) {
            // 如果已经在当前直播间,提示
            Toast.info('您已经在直播间哦～');
            return;
        }
        // 跳转到对应的直播间
        openUpDown(liveStreamArr, `${liveStreamId}`, extraInfo);
    },
    threshold,
);
