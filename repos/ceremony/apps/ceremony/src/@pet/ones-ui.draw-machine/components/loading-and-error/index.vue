<template>
    <div class="spring-loading-error">
        <LoadingErr
            class="data-load-error"
            :class="`data-load-error-${pageCase}`"
            :net-error-icon="'gis-draw-net-error'"
            :net-no-data-icon="'gis-draw-no-data'"
            :need-refresh="needRefresh"
            :loading="pageStatus.loading"
            :error-status="pageStatus.error"
            @refresh-data="refresh"
        >
            <template #error-text>
                <div class="error-text">
                    {{ errorText }}
                </div>
            </template>
            <template #refresh>
                <div
                    class="refresh-button margin-auto flex-center gis-draw-net-error-btn text-bold"
                    @click="refresh"
                >
                    {{ refreshText }}
                </div>
            </template>
        </LoadingErr>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';
import LoadingErr from './LoadingErr.vue';
import type { PropType } from 'vue';
import type { statusType } from './type';

export default defineComponent({
    components: {
        LoadingErr,
    },
    props: {
        refreshText: {
            type: String,
            default: '刷新',
        },
        pageCase: {
            type: String,
            default: 'main', // main表示主页兜底，other表示其他区块兜底，follow关注弹窗兜底
        },
        pageStatus: {
            type: Object as PropType<statusType>,
            default: () => {
                return {
                    error: false,
                    loading: true,
                    nodata: false,
                };
            },
        },
        loadingSize: {
            type: Number,
            default: 40,
        },
    },
    setup(props, { emit }) {
        const refresh = () => {
            console.log('refresh');
            emit('refresh');
        };
        const netText = {
            main: {
                error: '啊哦，加载失败了，刷新试试吧',
                nodata: '啊哦，暂无数据',
            },
            other: {
                error: '网络错误，刷新试试吧',
                nodata: '暂无中奖记录',
            },
            follow: {
                error: '网络错误，刷新试试吧',
                nodata: '暂无可认识的新主播',
            },
        };
        const needRefresh = computed(() => {
            return props.pageStatus.error;
        });
        const errorText = computed(() => {
            // eslint-disable-next-line
            return netText[props.pageCase as unknown as keyof typeof netText]?.[
                props.pageStatus.error ? 'error' : 'nodata'
            ];
        });

        return {
            errorText,
            refresh,
            needRefresh,
        };
    },
});
</script>

<style lang="less" scoped>
.spring-loading-error {
    position: absolute;
    left: 50%;
    min-width: 100px;
    top: 50%;
    transform: translate(-50%, -50%);
    .error-text {
        margin-top: 12px;
        margin-bottom: 23px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-size: 12px;
        font-weight: 500;
        color: var(--netErrorTxtColor);
        text-align: center;
        opacity: 0.4;
    }
    .refresh-button {
        font-family: PingFangSC, PingFangSC-Semibold;
        font-size: 14px;
        line-height: 46px;
        color: #611d00;
        text-align: center;
    }
}
</style>
