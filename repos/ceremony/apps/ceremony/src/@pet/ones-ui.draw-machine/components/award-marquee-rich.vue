<template>
    <div class="txt-container">
        <div
            v-if="marqueeList && marqueeList.length"
            class="award-marquee flex-center"
        >
            <!-- <span class="icon-sound" /> -->
            <MQ :marquee-list="marqueeList" />
        </div>
        <div class="award-richtext">
            <!-- eslint-disable-next-line vue/no-v-html -->
            <div class="richtext-main text-bold" v-html="mustWinToast1" />
            <div class="richtext-sub">
                {{ mustWinToast2 }}
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { DRAW_EVENT } from '../views/magic/config';
import { useMain } from '../store/useMain';
import { queryAwardMarquee } from '../api';
import MQ from './marquee/index.vue';

export default defineComponent({
    components: {
        MQ,
    },
    setup() {
        const mainStore = useMain();
        const marqueeList = ref([] as string[]);
        const mustWinToast1 = ref('');
        const mustWinToast2 = ref('');

        const initData = async () => {
            try {
                const res = await queryAwardMarquee();
                marqueeList.value = res.records;
                mustWinToast1.value = res.mustWinToast1;
                mustWinToast2.value = res.mustWinToast2 ?? '';
            } catch (error) {
                console.log('error', error);
            }
        };
        initData();

        mainStore.busEvent.on(DRAW_EVENT.updateMarquee, () => {
            initData();
        });

        return {
            marqueeList,
            mustWinToast1,
            mustWinToast2,
        };
    },
});
</script>

<style lang="less" scoped>
.txt-container {
    display: flex;
    justify-content: center;
    width: 100%;
}
.award-richtext,
.award-marquee {
    position: absolute;
    z-index: 11;
    font-size: 11px;
    line-height: 16px;
    color: var(--marqueeItemColor, #fff);
}
.award-richtext {
    top: 227px;
    min-width: 250px;
    text-align: center;
    .richtext-sub {
        color: #cfd2e2;
    }
}
.award-marquee {
    top: 180px;
    width: 218px;
    height: 28px;
    padding: 0 22px;
    margin: 0 auto;
    background-image: url('../variable/assets/marquee-bg_2x.png');
    background-size: cover;
    line-height: 28px;
    border-radius: 36px;
    .icon-sound {
        width: 16px;
        height: 16px;
        background-image: url('../variable/assets/sound_2x.png');
        background-size: 16px 16px;
    }
    /deep/ .marquee {
        width: 150px;
        height: 100%;
        // margin-left: 8px;
        overflow: hidden;
        .list-wrap {
            position: relative;
            height: 100%;
            overflow: hidden;
            box-sizing: border-box;
        }
        .marquee-item {
            height: 28px;
            overflow: hidden;
            font-size: 11px;
            line-height: 28px;
            text-align: center;
            text-overflow: ellipsis;
            word-break: break-all;
            white-space: nowrap;
            // justify-content: center;
        }
    }
    .msg-item {
        position: relative;
        width: 100%;
        height: 100%;
        font-family: PingFangSC, PingFangSC-Medium;

        // @apply text-bold;
        // @apply flex-center-center;

        line-height: normal;
        border-radius: 14px;
    }
}
</style>
