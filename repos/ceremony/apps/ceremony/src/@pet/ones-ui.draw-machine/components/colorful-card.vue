<template>
    <div class="colorful-card a-bg-part">
        <div class="gis-card-top-img" />
        <slot name="title">
            <div v-if="title" class="title-content">
                <!-- <div
                    v-if="carStarIcon && !extra"
                    class="icon"
                    :class="carStarIcon"
                /> -->
                <div class="title-wrap">
                    <p v-if="title" class="title">
                        {{ title }}
                    </p>
                    <p v-if="extra" class="extra">
                        {{ extra }}
                    </p>
                </div>
                <!-- <div
                    v-if="carStarIcon && !extra"
                    class="icon rotate-icon"
                    :class="carStarIcon"
                /> -->
            </div>
            <div v-if="subTitle" class="sub-title">
                {{ subTitle }}
            </div>
        </slot>
        <div>
            <slot name="other" />
        </div>
        <div class="card-content">
            <slot />
        </div>
        <!-- 预留一个插槽 -->
        <slot name="bottom" />
    </div>
</template>

<script lang="ts" setup>
defineProps({
    title: {
        type: String,
        default: '',
    },
    extra: {
        type: String,
        default: '',
    },
    subTitle: {
        type: String,
        default: '',
    },
    carStarIcon: {
        type: String,
        default: 'gis-card-star-icon',
    },
});
</script>

<style lang="less" scoped>
.colorful-card {
    position: relative;
    width: var(--cardWidth, 386px);
    height: auto;
    padding-bottom: var(--cardPaddingBottom, 0);
    margin: 14px auto;
    background-image: var(--headerBgImg);
    background-position: center top;
    background-size: 100%;
    background-repeat: no-repeat;
    border-radius: var(--cardBorderRadius, 18px);
    .gis-card-top-img {
        position: absolute;
        top: var(--cardTopImgTop, 0);
    }
    .card-content {
        position: relative;
        padding-top: var(--cardContentPaddingTop, 0);
        overflow: hidden;
    }

    .title-content {
        position: relative;
        z-index: 1;
        display: flex;
        height: var(--titleHeight, 28px);
        padding-top: var(--titleContentPaddingTop, 10px);
        line-height: var(--titleHeight, 28px);
        text-align: center;
        box-sizing: content-box;
        align-items: center;
        justify-content: center;
        transform: perspective(1000);
        .title-wrap {
            position: relative;
            display: flex;
            align-items: center;
        }
        .title {
            margin: 0 4px;
            font-family: var(--titleFontFamily, mf-zhehei);
            font-size: var(--titleFontSize, 20px);
            font-weight: var(--fontWeightTitle, 400);
            color: var(--titleColor, transparent);
            background: var(
                --titleLinearGradient,
                linear-gradient(140deg, #ff6499 8%, #fc58ff 99%)
            );
            transform: perspective(1000);
            -webkit-text-fill-color: var(--titleColor, transparent);
            background-clip: text;
        }
        .extra {
            position: absolute;
            left: 100%;
            height: 18px;
            padding: 0 4px;
            font-size: 10px;
            font-weight: 600;
            line-height: 18px;
            color: #fff;
            text-align: center;
            white-space: nowrap;
            background: var(
                --backgroundExtra,
                linear-gradient(279.09deg, #ff4242 0%, #ff42b3 100%)
            );
            border-radius: 4px;
        }
        .rotate-icon {
            transform: var(--iconRotate, rotateY(180deg)) translate3d(0, 0, 0);
        }
        .icon {
            display: inline-block;
            width: 24px;
            height: 24px;
        }
    }
    .sub-title {
        margin-top: 2px;
        font-family: var(--fontFamilySubTitle, 'PingFang SC');
        font-size: var(--fontSizeSubTitle, 12px);
        font-style: normal;
        font-weight: var(--fontWeightSubTitle, 400);
        line-height: 18px;
        color: var(--colorSubTitle, #fd3c97);
        text-align: center;
        opacity: 0.6;
    }
}
</style>
