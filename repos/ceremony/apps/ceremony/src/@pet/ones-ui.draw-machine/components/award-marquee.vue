<template>
    <div
        v-if="marqueeList && marqueeList.length"
        class="award-marquee flex-center"
    >
        <MQ :marquee-list="marqueeList">
            <template #default="{ item }">
                <div v-if="item.type === 'txt'" class="msg-item">
                    {{ item.value }}
                </div>
                <!-- eslint-disable-next-line vue/no-v-html -->
                <div v-else class="msg-item" v-html="item.value" />
            </template>
        </MQ>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { DRAW_EVENT } from '../views/magic/config';
import { useMain } from '../store/useMain';
import { queryAwardMarquee } from '../api';
import MQ from './marquee/index.vue';
import type { AwardMarqueeInfo } from '../api';

export default defineComponent({
    components: {
        MQ,
    },
    setup() {
        const marqueeList = ref([] as { type: string; value: string }[]);

        const initData = async () => {
            try {
                const res = await queryAwardMarquee();
                marqueeList.value = dealData(res);
            } catch (error) {
                console.log('error', error);
            }
        };
        initData();

        const dealData = (data: AwardMarqueeInfo) => {
            const { records, mustWinToast1, mustWinToast2 } = data;
            const arr = [
                {
                    type: 'must',
                    value: mustWinToast1,
                },
                {
                    type: 'must',
                    value: mustWinToast2,
                },
            ];

            records?.slice(0, 50).forEach((element, index) => {
                arr.push({
                    type: 'txt',
                    value: element,
                });

                arr.push({
                    type: 'must',
                    value: index % 2 ? mustWinToast2 : mustWinToast1,
                });
            });

            return arr.filter((item) => item.value);
        };

        const mainStore = useMain();
        mainStore.busEvent.on(DRAW_EVENT.updateMarquee, () => {
            initData();
        });

        return {
            marqueeList,
        };
    },
});
</script>

<style lang="less" scoped>
.award-marquee {
    position: relative;
    width: 270px;
    height: 16px;
    padding: 0 10px;
    margin: 0 auto;
    // background: url(../../assets/marquee-bg.png) no-repeat center/250px;
    line-height: 16px;
    color: var(--marqueeItemColor, #fff);
    border-radius: 3px;
    /deep/ .marquee {
        height: 100%;
        overflow: hidden;
        .list-wrap {
            position: relative;
            height: 100%;
            overflow: hidden;
            box-sizing: border-box;
        }
        .msg-item {
            height: 16px;
            font-size: 12px;
            line-height: 16px;
            color: #fff;
            text-align: center;

            @apply line-cut1;
        }
    }
}
</style>
