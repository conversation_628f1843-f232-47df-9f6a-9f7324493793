<template>
    <div v-if="showMarqueen" class="marquee">
        <div v-if="renderList.length" class="marquee-all">
            <div class="list-wrap">
                <div class="list" :style="styles">
                    <div
                        v-for="(item, index) in renderList"
                        :key="index"
                        class="marquee-item"
                    >
                        <slot :index="index" :item="item">
                            {{ item }}
                        </slot>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    ref,
    computed,
    onMounted,
    onBeforeUnmount,
} from 'vue';
import { useVisibility } from '@alive-ui/actions';
import type { PropType } from 'vue';

export default defineComponent({
    props: {
        // 自动播放时间（ms）
        autoplay: {
            type: Number,
            default: 3000,
        },
        // 切换速度（ms）
        speed: {
            type: Number,
            default: 1500,
        },
        marqueeList: {
            type: Array as PropType<any[]>,
            default: () => [],
        },
        limitNum: {
            type: Number,
            default: 3,
        },
    },
    setup(props, { emit }) {
        let timer: number | null = null;
        let timer2: number | null = null;
        const showMarqueen = ref(true);
        const indexMaerqueeq = ref(0);
        const running = ref(false);
        const limit = computed(() =>
            Math.min(props.marqueeList.length, props.limitNum || 2),
        );
        const renderList = computed(() =>
            props.marqueeList.slice(0, limit.value),
        );

        const styles = computed(() => {
            const val = `${((-1 / limit.value) * 100).toFixed(3)}%`;

            if (running.value) {
                return {
                    transform: `translate3d(0, ${val}, 0)`,
                    transitionDuration: `${props.speed}ms`,
                };
            }

            return {};
        });

        const update = () => {
            // 仅1条时无动画
            if (props.marqueeList.length < 2) {
                return;
            }
            // 计算下一个值的下标值
            const idx =
                (indexMaerqueeq.value + limit.value) % props.marqueeList.length;
            indexMaerqueeq.value += 1;
            emit('update', idx);
            running.value = true;

            timer2 = window.setTimeout(() => {
                if (indexMaerqueeq.value >= props.marqueeList.length) {
                    indexMaerqueeq.value = 0;
                    emit('update', indexMaerqueeq.value);
                }
                running.value = false;

                if (renderList.value.length > 1) {
                    renderList.value.shift();
                    renderList.value.push(props.marqueeList[idx]);
                }
            }, props.speed);
        };

        const clearTimer = () => {
            indexMaerqueeq.value = 0;
            timer && window.clearInterval(timer);
            timer2 && window.clearTimeout(timer2);
            timer = null;
            timer2 = null;
        };

        const start = () => {
            indexMaerqueeq.value = 0;
            timer = window.setInterval(update, props.autoplay);
        };

        useVisibility({
            hiddenHandler: () => {
                showMarqueen.value = false;
            },
            visibleHandler: () => {
                showMarqueen.value = true;
            },
        });

        onMounted(() => {
            start();
        });

        onBeforeUnmount(() => {
            clearTimer();
        });

        return {
            styles,
            showMarqueen,
            renderList,
        };
    },
});
</script>

<style lang="less" scoped>
.marquee {
    &-all,
    .list-wrap {
        width: 100%;
        height: 100%;
    }
    .list-wrap {
        position: relative;
        overflow: hidden;
    }

    .marquee-item {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
}
</style>
