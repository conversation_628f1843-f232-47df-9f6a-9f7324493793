<template>
    <div class="new-transparent-video">
        <img v-if="showStaticPoster" :src="posterUrl" />
        <canvas v-show="!showStaticPoster" ref="canvasRef" />
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    nextTick,
    onBeforeUnmount,
    onUnmounted,
    watch,
    ref,
    type Ref,
} from 'vue';
import { TransparentVideoPlayer } from '@ks/tvplayer';
import {
    useVisibility,
    useLowPowerMode,
    getIOSVersion,
    osName,
} from '@alive-ui/system';
import { query, Report } from '@alive-ui/actions';
import { UDevice } from '@ad/utils';

export default defineComponent({
    components: {},
    props: {
        needSync: {
            // 是否需要异步加载，如果是首屏幕优化Fmp建议异步加载
            type: Boolean,
            default: false,
        },
        beginInit: {
            // 开始初始化实例,videoUrl也需要begin之后价值
            type: <PERSON>olean,
            default: false,
        },
        autoplay: {
            type: Boolean,
            default: false,
        },
        loop: {
            type: Boolean,
            default: false,
        },
        playing: {
            type: Boolean,
            default: true,
        },
        videoUrl: {
            type: String,
            default: '',
        },
        posterUrl: {
            type: String,
            default: '',
        },
        width: {
            type: Number,
            default: 828,
        },
        height: {
            type: Number,
            default: 1000,
        },
        needDestroy: {
            type: Boolean,
            default: true,
        },
    },
    // eslint-disable-next-line max-lines-per-function
    setup(props, { emit }) {
        const canvasRef = ref() as Ref<HTMLCanvasElement>;
        let player: TransparentVideoPlayer | null;
        const { isLowPowerModeStatus } = useLowPowerMode();
        const showStaticPoster = ref(!props.videoUrl || false);

        // 低电量模式
        watch(
            () => isLowPowerModeStatus.value,
            (o) => {
                if (o && props.posterUrl) {
                    showStaticPoster.value = true;
                    // 对外抛出降级为图片
                    emit('videoStart', 'img');
                }
            },
            { immediate: true },
        );

        watch(
            () => props.videoUrl,
            (val, old) => {
                if (val && player) {
                    player.loader.video.src = val;
                }
            },
        );

        const initPlayer = () => {
            // 在这个函数内标识是否已经抛出过播放事件
            let hasEmitVideoStart = false;

            try {
                const can = canvasRef.value;
                // const can: HTMLCanvasElement = document.getElementById('can') as HTMLCanvasElement;
                can.width = props.width;
                can.height = props.height;

                player =
                    player ||
                    new TransparentVideoPlayer({
                        canvas: can,
                        // TODO: 下次需要升级transpraent video 组件，有锯齿本质原因还是ratio 参数没生效，需要基于传 wrapper才行，
                        // 本次解决方案是外部传来的width和height 为实际显示尺寸乘以dpr，也就是二倍，即解决了锯齿问题
                        // wrapper,
                        videoUrl:
                            query.uiTestTransparentVideoUrl || props.videoUrl,
                        autoPlay: props.autoplay,
                        loop: props.loop,
                        ratio: window.devicePixelRatio,
                        useBlob:
                            osName === 'ios' && getIOSVersion() >= 16
                                ? false
                                : true,
                    });

                player.addEventListener('videoEnded', () => {
                    emit('videoEnded');
                });

                player.addEventListener('canplay', () => {
                    emit('canplay');
                });

                player.addEventListener('play', () => {
                    emit('play');
                });

                player.addEventListener('playing', () => {
                    emit('playing');

                    // 对外抛出为：正常视频播放，只抛一次
                    if (!hasEmitVideoStart) {
                        emit('videoStart', 'video');
                        hasEmitVideoStart = true;
                    }
                });

                // 历史原因，外部需要监听该事件，然后影藏背景进入播放状态
                player.addEventListener('seeked', () => {
                    emit('seeked');
                });

                player.addEventListener('error', (e) => {
                    emit('error');
                    Report.biz.error('【扭蛋机】透明视频加载异常', {
                        error: e,
                    });
                    showStaticPoster.value = true;
                });
                // @ts-expect-error
                player.addEventListener('videoLoaded', (bol: boolean) => {
                    // 拿到视频资源url预加载完成时机
                    emit('videoLoaded', bol);
                });

                player.addEventListener('videoRendererError', (e) => {
                    console.warn('videoRendererError');
                    emit('videoRendererError');
                    Report.biz.error('【扭蛋机】透明视频渲染异常', {
                        error: e,
                    });
                    showStaticPoster.value = true;
                });
            } catch (error) {
                emit('videoRendererError');
                Report.biz.error('【扭蛋机】透明视频初始化异常', {
                    error,
                });
                console.error(error);
            }
        };

        nextTick(() => {
            // 如果不需要异步加载，则正常初始化
            if (!props.needSync) {
                try {
                    initPlayer();
                } catch (err) {
                    console.log(err);
                }
            }
        });
        let timer = 0;
        let timer2 = 0;

        // 手动控制初始化视频, 延时实例化，可以让FMP不包括视频资源的加载
        watch(
            () => props.beginInit,
            (val, old) => {
                if (val !== old && !!val) {
                    timer = window.setTimeout(() => {
                        console.log('init===player');
                        initPlayer();
                    }, 400);
                }
            },
            {
                immediate: true,
            },
        );

        watch(
            // 手动调用paly时机
            () => props.playing,
            (val, old) => {
                if (val !== old && !!val) {
                    timer2 = window.setTimeout(() => {
                        try {
                            player?.play?.();
                        } catch (err) {
                            console.log(err);
                        }
                    }, 200);
                }
            },
            {
                immediate: true,
            },
        );

        //  因为IOS设备上返回页面不刷新
        if (props.autoplay && UDevice.isIOS()) {
            try {
                useVisibility({
                    visibleHandler: () => {
                        // 部分手机返回后状态是playing
                        if (player?.status === 'playing') {
                            player.pause();

                            setTimeout(() => {
                                player?.play();
                            }, 500);
                        }

                        if (player?.status === 'paused') {
                            player.play();
                        }
                    },
                    hiddenHandler: () => {
                        player?.pause?.();
                    },
                });
            } catch (err) {
                console.log(err);
            }
        }

        onUnmounted(() => {
            clearTimeout(timer);
            clearTimeout(timer2);
        });

        onBeforeUnmount(() => {
            if (canvasRef.value) {
                canvasRef.value.style.display = 'none';
                canvasRef.value.remove();
            }
            // tvplayer在destroy之前会把canvas的webgl context给释放掉, 某些机型会导致冠军弹窗偶现白屏。
            props.needDestroy && player?.destroy();
            player = null;
        });

        return {
            canvasRef,
            showStaticPoster,
        };
    },
});
</script>

<style lang="less" scoped>
.new-transparent-video {
    background-color: transparent;
    img {
        width: 100%;
        height: 100%;
    }
}
canvas {
    width: 100%;
    height: 100%;
    background-color: transparent;
    background-size: cover;
    background-position: center;
}
</style>
