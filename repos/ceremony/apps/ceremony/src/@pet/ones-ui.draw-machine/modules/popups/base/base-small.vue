<template>
    <spu-popup
        ref="refPopup"
        v-model="showModel"
        transfer-dom
        popup-class="draw-lottery"
        position="center"
        @before-hide="hide"
    >
        <div class="pop-content">
            <div
                class="pop-bg gis-small-modal"
                :style="`background-image: url(${smallModalBg})`"
            >
                <div v-if="title" class="title-content flex-center-center">
                    <div class="icon gis-title-extra-icon" />
                    <div class="gift-title title-font mf-font">
                        {{ title }}
                    </div>
                    <div class="icon gis-title-extra-icon rotate-icon" />
                </div>
                <div v-if="subTitle" class="gift-sub-title text-12">
                    {{ subTitle }}
                </div>
                <slot name="content" :hide="hide" />
                <slot name="action-box" :hide="hide" />
            </div>
        </div>
        <div class="gis-icon-close margin-auto" @click.prevent="hide" />
    </spu-popup>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, onUnmounted, ref, watch, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import useKconf from '@pet/ones-use.useKconfBatch';
import { Popup } from '@lux/sharp-ui-next';
import { resultPopupBtnLog } from '../../../logger';

export default defineComponent({
    components: {
        'spu-popup': Popup,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '记录',
        },
        subTitle: {
            type: String,
            default: '',
        },
    },
    setup(props, { emit }) {
        const { kconfData } = storeToRefs(useKconf());

        const showModel = ref<boolean>(false);
        // let stopMoveObj: StopFunItem;
        const refPopup = ref(null);
        const smallModalBg = ref(
            kconfData.value?.niudan?.lotteryConfigs?.smallModalBg,
        );

        const hide = () => {
            // stopMoveObj.cancelMove();
            // const popup = refPopup.value as any;

            // if (popup?.$refs?.popup) {
            //     document.body.removeChild(popup.$refs.popup);
            // }
            emit('close');
        };

        watch(
            () => props.show,
            (o) => {
                // if (o) {
                //     stopMoveObj = stopMove();
                // }
                showModel.value = o;
            },
            { immediate: true },
        );

        onUnmounted(() => {
            // stopMoveObj?.destroy?.();
        });

        onMounted(async () => {});
        return {
            refPopup,
            hide,
            showModel,
            resultPopupBtnLog,
            smallModalBg,
        };
    },
});
</script>

<style lang="less" scoped>
// /deep/ .spu-popup__box {
//   margin-top: -40px;
// }
.pop-content {
    position: relative;
    .pop-bg {
        padding-top: var(--popupTitleSmallTop, var(--popupTitleRecordTop));
        .title-content {
            position: relative;
            z-index: 1;
            text-align: center;
            box-sizing: content-box;
        }
        .gift-title {
            width: fit-content;
            font-family: var(--popupTitleFamily);
            font-size: var(--popupTitleFontSize);
            line-height: 28px;
            color: transparent;
            text-align: center;
            background: var(--popupTitleColor);
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .rotate-icon {
            transform: var(--iconRotate, rotateY(180deg));
        }
        .icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            margin-top: 2px;
        }
        .gift-sub-title {
            margin: 4px auto 12px;
            line-height: 18px;
            color: var(--fontSubColor);
            text-align: center;
            opacity: 0.6;
        }
    }
}
.gis-icon-close {
    margin-top: 5px;
}
</style>
