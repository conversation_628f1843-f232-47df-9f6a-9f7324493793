<template>
    <SmallModal :show="show" :title="'预约盛典直播'" @close="handleClose">
        <template #content="action">
            <div v-if="authorInfo" class="content">
                <div class="user-head-area">
                    <img
                        :src="authorInfo.headUrl"
                        alt=""
                        class="author-img-avatar"
                    />
                    <div class="user-name text-14 text-bold">
                        {{ nameSlice(authorInfo.userName, 8) }}
                    </div>
                </div>
                <div class="text text-12">
                    {{ authorInfo.reservationTitle }}
                </div>
                <div class="time text-12">{{ toLiveTime }} 精彩直播中</div>
                <div
                    class="ks-button flex-center text-16 text-bold"
                    @click="handleBookReservation(action.hide)"
                >
                    <div
                        class="gis-modal-button flex-center"
                        :class="{ 'actived-button': btnPress }"
                    >
                        预约直播
                    </div>
                </div>
                <div class="tips text-12">
                    已有{{ authorInfo.reservationCount }}人预约成功
                </div>
            </div>
        </template>
    </SmallModal>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, ref, computed } from 'vue';
import dayjs from 'dayjs';
import { Toast } from '@lux/sharp-ui-next';
import { nameSlice } from '@alive-ui/actions';
import useConfigStore from '../../store/config';
import { queryRecoAuthor, bookReservation } from '../../api/index';
import SmallModal from './base/base-small.vue';
import type { SmallAuthorItem } from '../../api/index';

export default defineComponent({
    components: {
        SmallModal,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        let hide = () => {};

        const handleClose = () => {
            emit('close');
        };
        const configStore = useConfigStore();
        const authorInfo = ref({} as SmallAuthorItem);

        const toLiveTime = computed(() => {
            return dayjs(authorInfo.value.reservationLiveTime).format(
                'MM月DD日 HH时mm分',
            );
        });

        const queryRecoAuthorInfo = async () => {
            const res = await queryRecoAuthor('reservation');
            authorInfo.value = res?.recoAuthorList?.[0];

            if (!authorInfo.value) {
                Toast.info('暂无可预约主播');
                emit('refresh');

                setTimeout(() => {
                    hide();
                }, 1000);
            }
            console.log('queryRecoAuthorInfo====', authorInfo.value);
        };
        queryRecoAuthorInfo();

        const btnPress = ref(false);

        const handleBookReservation = async (fnHide: () => void) => {
            const reservationId = authorInfo.value?.reservationId;

            if (typeof fnHide === 'function') {
                hide = fnHide;
            }

            if (!reservationId) {
                Toast.info('暂无可预约主播');
                hide();

                return;
            }
            btnPress.value = true;

            try {
                const res = await bookReservation(reservationId);
                console.log('预约接口', res);

                if (res.result === 1) {
                    Toast.info('预约成功');

                    setTimeout(() => {
                        emit('refresh');
                    }, 500);
                } else {
                    Toast.error('预约失败');
                }
            } catch (err) {
                console.error(err);
            }
            setTimeout(hide, 1500);
        };

        return {
            ...configStore,
            authorInfo,
            btnPress,
            handleBookReservation,
            handleClose,
            nameSlice,
            toLiveTime,
        };
    },
});
</script>

<style lang="less" scoped>
.content {
    @apply a-text-main;

    text-align: center;

    .author-img-avatar {
        width: 88px;
        height: 88px;
        border: 1px solid #fff;
        border-radius: 50%;
    }
    .user-name {
        max-width: 200px;
        margin: 8px auto;
        font-style: normal;
        line-height: 21px;
        color: #000;
        text-align: center;
    }
    .time,
    .text {
        margin: 4px auto;
        line-height: 18px;
        color: #000;
        opacity: 0.5;
    }
    .tips {
        position: absolute;
        bottom: 70px;
        left: 50%;
        opacity: 0.6;
        transform: translateX(-50%);
        color: #000;
        opacity: 0.5;
    }
    .ks-button {
        position: absolute;
        bottom: var(--popBtnBottom, 28px);
        left: 50%;
        margin: 0 auto;

        @apply a-text-button;

        text-align: center;
        transform: translateX(-50%);
        .actived-button {
            opacity: 0.5;
        }
    }
    .user-head-area {
        margin-top: 62px;
    }
}
</style>
