<template>
    <RecordModal
        :show="show"
        :title="'中奖记录'"
        bg-class="prize-record-pop"
        close-class="prize-record-close"
        :top-text="topText"
        @close="handleClose"
    >
        <template #content>
            <div class="content">
                <LoadingAndError
                    v-if="
                        pageStatus.loading ||
                        pageStatus.nodata ||
                        pageStatus.error
                    "
                    :page-case="'other'"
                    :page-status="pageStatus"
                    @refresh="queryRecord"
                />
                <div v-else class="record">
                    <div
                        v-for="(item, index) in records"
                        :key="index"
                        class="coin-record-item"
                        :class="{ extraTopPad: showPanelIcon }"
                    >
                        <div
                            v-if="showPanelIcon"
                            class="des-icon"
                            :class="{
                                'gis-icon-xyjc':
                                    item.desc && item.desc.includes('幸运'),
                                'gis-icon-zzjc':
                                    item.desc && item.desc.includes('至尊'),
                            }"
                        />
                        <div class="flex flex-wrap">
                            <div
                                v-for="(elem, i) in item.items"
                                :key="i"
                                class="gift-item flex align-center"
                                :class="{ wide: elem.count === 10 }"
                            >
                                <img
                                    class="gift-item-icon"
                                    :src="elem.icon"
                                    alt=""
                                />
                                <div class="gift-amount inline-block text-bold">
                                    x{{ elem.count }}
                                </div>
                            </div>
                        </div>
                        <div class="gain-award-time">
                            获奖时间：
                            {{
                                formatTime(
                                    item.timestamp,
                                    'MM月dd日 HH:mm',
                                ).replace(/^0/, '')
                            }}
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template #action-box="action">
            <div
                v-if="!(pageStatus.error || pageStatus.loading)"
                class="action-button-area flex margin-auto text-bold"
                :class="needFillAddress ? 'flex-between' : 'flex-center'"
            >
                <div
                    v-if="needFillAddress"
                    class="btn-address flex-center"
                    @click="inputAddress(action.hide)"
                >
                    填写地址
                </div>
                <div
                    class="btn-continue flex-center"
                    :class="
                        needFillAddress ? 'gis-short-btn' : 'gis-modal-button'
                    "
                    @click="action.hide"
                >
                    继续抽奖
                </div>
            </div>
            <div
                v-if="!(pageStatus.error || pageStatus.loading)"
                class="address-tip"
            >
                仅展示最近100条记录
            </div>
        </template>
    </RecordModal>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, ref, reactive, computed, inject } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { loadUrlOnNewPage, formatTime, bolIsAuthor } from '@alive-ui/actions';
import useConfigStore from '../../store/config';
import LoadingAndError from '../../components/loading-and-error/index.vue';
import { queryLotteryRecord } from '../../api/index';
import RecordModal from './base/base-record.vue';
import type { Records } from '../../api/index';

export default defineComponent({
    components: {
        RecordModal,
        LoadingAndError,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        showPanelIcon: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        const configStore = useConfigStore();
        const records = ref<Records[]>([]);
        const pageStatus = reactive({
            loading: false,
            error: false,
            nodata: false,
        });
        const needFillAddress = ref(false);

        const goAddress: any = inject('goAddress');

        const topText = computed(() => {
            return needFillAddress.value
                ? '温馨提示：请于活动结束前填写地址'
                : '';
        });

        const queryRecord = async () => {
            pageStatus.loading = true;
            pageStatus.error = false;

            try {
                const data = await queryLotteryRecord();
                records.value = data?.records || [];
                needFillAddress.value = data?.needFillAddress;
                pageStatus.nodata = !records.value?.length;
            } catch (err) {
                pageStatus.error = true;
                console.error('get record error:', err);
            }
            pageStatus.loading = false;
        };

        queryRecord();

        const inputAddress = (fnHide: () => void) => {
            if (bolIsAuthor) {
                Toast.info('开播中，不可填写哦～');

                return;
            }
            fnHide?.();

            // 跳转地址
            goAddress(true);
        };

        const handleClose = () => {
            emit('close');
        };

        return {
            pageStatus,
            records,
            topText,
            formatTime,
            queryRecord,
            needFillAddress,
            inputAddress,
            handleClose,
        };
    },
});
</script>

<style lang="less" scoped>
.address-tip {
    margin-top: 8px;
    font-family: PingFangSC, PingFangSC-Regular;
    font-size: 12px;
    line-height: 15px;
    color: var(--fontSubColor);
    text-align: center;
    opacity: 0.6;
}

.content {
    height: var(--recordContentHeight, 220px);
    margin: 10px 17px 0;
    overflow: scroll;

    /deep/ .spring-loading-error {
        bottom: auto;
        margin-top: 18px;
        .refresh-button {
            // margin-top: 80px;
        }
    }
    .coin-record-item {
        position: relative;
        width: 100%;
        padding: 12px 14px;
        margin: 0 auto;
        margin-bottom: 8px;
        background: var(--recordItemBg);
        border-radius: 14px;
        &.extraTopPad {
            padding-top: 26px;
        }
        .des-icon {
            position: absolute;
            top: 0;
            left: 0;
        }
    }
    .record-title {
        font-family: PingFangSC, PingFangSC-Medium;
        font-size: 14px;

        @apply text-bold;
    }
    .coin-get-time {
        margin-top: 6px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-size: 12px;
        line-height: 17px;
        opacity: 0.7;
    }
    .gift-item {
        width: 50px;
        margin-right: 16px;
        margin-bottom: 12px;
        &.wide {
            width: auto;
        }
        &:nth-child(4n) {
            margin-right: 0;
        }
    }
    .gift-item-icon {
        width: 30px;
        height: 30px;
    }
    .gift-amount {
        margin-left: 4px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-size: 12px;
        color: var(--rewardGiftNumColor);
    }
    .gain-award-time {
        margin-top: 2px;
        font-family: PingFangSC, PingFangSC-Regular;
        font-size: 10px;
        line-height: 14px;
        opacity: 0.6;
        color: var(--rewardGiftNumColor);
    }
}
.action-button-area {
    width: 234px;
    margin-top: 10px;
    font-family: PingFangSC, PingFangSC-Semibold;
    font-size: 16px;
    text-align: center;
    .btn-address {
        width: 114px;
        height: 48px;
        margin-right: 15px;
        color: var(--popupBtnDoColor);
        border: 1px solid;
        border-radius: var(--popBtnAddRadius, 28px);
    }
    .btn-continue {
        color: var(--popupBtnDoColor);
    }
}
</style>
