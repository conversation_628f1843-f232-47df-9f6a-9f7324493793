<template>
    <div
        v-show="showModel"
        class="draw-lottery anim-pop buff-popup-mask flex-center-center"
        @click="handleMask"
    >
        <TransparentVideo
            v-if="!videoClose"
            class="light-video"
            :width="828"
            :height="1284"
            :playing="showModel"
            :video-url="lightMp4"
            @video-start="videoPlay"
            @video-ended="videoEnd"
            @video-loaded="playerLoaded"
            @error="playerError"
            @video-renderer-error="playerError"
        />
        <div ref="refPopup" class="pop-content">
            <transition name="drawOpen">
                <div
                    v-if="showModel && videoPlayed"
                    class="pop-bg gis-small-modal"
                    :style="`background-image: url(${kconfData?.niudan?.lotteryConfigs?.smallModalBg})`"
                >
                    <div v-if="title" class="title-content flex-center-center">
                        <div class="icon gis-title-extra-icon" />
                        <div
                            class="gift-title title-font mf-font text-text-hyykh"
                        >
                            {{ title }}
                        </div>
                        <div class="icon gis-title-extra-icon rotate-icon" />
                    </div>
                    <div v-if="subTitle" class="gift-sub-title text-12">
                        {{ subTitle }}
                    </div>
                    <slot name="content" :hide="hide" />
                    <slot name="action-box" :hide="hide" />
                </div>
            </transition>
        </div>
        <div
            v-click-log="
                sceneType === MODAL_TYPE.resultModal && resultPopupBtnLog(3)
            "
            class="gis-icon-close margin-auto"
            @click.prevent="hide"
        />
    </div>
</template>

<script lang="ts" setup>
/* eslint-disable @typescript-eslint/no-explicit-any */
import { onUnmounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import useKConf from '@pet/ones-use.useKconfBatch';
import { useModelDisableScroll } from '@alive-ui/actions';
import { MODAL_TYPE } from '../../../views/magic/config';
import { useMain } from '../../../store/useMain';
import { resultPopupBtnLog, sendShow } from '../../../logger';
import TransparentVideo from '../../../components/transparent-video/index.vue';
import lightMp4 from './light.mp4';
const { kconfData } = storeToRefs(useKConf());

const props = defineProps({
    show: {
        type: Boolean,
        default: false,
    },
    title: {
        type: String,
        default: '记录',
    },
    subTitle: {
        type: String,
        default: '',
    },
    sceneType: {
        type: Number,
        default: 0,
    },
});
const mainStore = useMain();
const emits = defineEmits([
    'close',
    'player-error',
    'video-end',
    'player-loaded',
]);
const showModel = ref<boolean>(false);
const refPopup = ref();
useModelDisableScroll(showModel);
const videoPlayed = ref(false);
const videoClose = ref(false);
let timer: any;

const hide = () => {
    emits('close');
};

watch(
    () => props.show,
    (o) => {
        showModel.value = o;

        if (!o) {
            videoPlayed.value = o;
        } else {
            // 写在上面不上报
            if (props.sceneType === MODAL_TYPE.resultModal) {
                sendShow(resultPopupBtnLog(3));
            }
            if (mainStore.isLowDev) {
                videoPlayed.value = true;
                videoClose.value = true;

                return;
            }
            videoClose.value = false;

            setTimeout(() => {
                videoPlayed.value = true;

                timer = setTimeout(() => {
                    videoClose.value = true;
                }, 2000);
            }, 500);
        }
    },
    { immediate: true },
);

onUnmounted(() => {
    // stopMoveObj?.destroy?.();
    clearTimeout(timer);
});

const handleMask = (ev: Event) => {
    const dom = refPopup.value;

    if (dom?.contains(ev.target)) {
        return;
    }
    hide();
};

const videoPlay = () => {
    videoPlayed.value = true;
};

const videoEnd = () => {
    console.log('listen====end');
    videoClose.value = true;
    emits('video-end');
};

const playerError = () => {
    console.log('listen====error');
    videoPlayed.value = true;
    videoClose.value = true;
    emits('player-error');
};

const playerLoaded = (bol: boolean) => {
    emits('player-loaded', bol);
};
</script>

<style lang="less" scoped>
.buff-popup-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 70%);
    flex-direction: column;
}
.pop-content {
    min-height: 455px;
    .pop-bg {
        position: relative;
        will-change: transform;
        backface-visibility: visible;
        padding-top: var(--popupTitleSmallTop, var(--popupTitleRecordTop));
        .title-content {
            position: relative;
            z-index: 1;
            text-align: center;
            box-sizing: content-box;
        }
        .gift-title {
            width: fit-content;
            font-family: var(--popupTitleFamily);
            font-size: var(--popupTitleFontSize);
            line-height: 28px;
            color: transparent;
            text-align: center;
            background: var(--popupTitleColor);
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .rotate-icon {
            transform: var(--iconRotate, rotateY(180deg));
        }
        .icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            margin-top: 2px;
        }
        .gift-sub-title {
            margin: 4px auto 12px;
            line-height: 18px;
            color: var(--fontSubColor);
            text-align: center;
            opacity: 0.6;
        }
    }
}
.gis-icon-close {
    margin-top: 5px;
}

.light-video {
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    width: 414px;
    margin: 0 auto;
    transform: translateY(-50%) translateZ(-500px);
    will-change: transform;
    /deep/ video {
        will-change: transform;
    }
}
.drawOpen-enter {
    opacity: 0;
}
.drawOpen-enter-to {
    opacity: 1;
}

.drawOpen-enter-active {
    transition: opacity 1s ease;
    animation:
        draw-open-680 680ms 200ms cubic-bezier(0.3, 0, 0.6, 1) both,
        draw-open-680-1000 320ms 880ms cubic-bezier(0.3, 0, 0.39, 1);
}

@keyframes draw-open-680 {
    0% {
        transform: scale(0) rotateY(360deg);
    }
    100% {
        transform: scale(0.96) rotateY(0deg);
    }
}

@keyframes draw-open-680-1000 {
    0% {
        transform: scale(0.96);
    }
    100% {
        transform: scale(1);
    }
}
</style>
