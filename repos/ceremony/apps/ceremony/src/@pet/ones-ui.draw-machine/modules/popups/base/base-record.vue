<template>
    <spu-popup
        ref="refPopup"
        v-model="showModel"
        transfer-dom
        popup-class="draw-lottery"
        position="center"
        @before-hide="hide"
    >
        <div class="pop-content">
            <div
                class="pop-bg gis-record-modal"
                :class="bgClass"
                :style="`background-image: url(${bigModalBg})`"
            >
                <div class="title-content flex-center-center">
                    <div class="icon gis-title-extra-icon" />
                    <div class="gift-title title-font mf-font">
                        {{ title }}
                    </div>
                    <div class="icon gis-title-extra-icon rotate-icon" />
                </div>
                <div class="gift-sub-title text-12">
                    {{ topText }}
                </div>
                <slot name="content" />
                <slot name="action-box" :hide="hide" />
            </div>
        </div>
        <div
            class="gis-icon-close margin-auto"
            :class="closeClass"
            @click.prevent="hide"
        />
    </spu-popup>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, onUnmounted, ref, watch, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import useKconf from '@pet/ones-use.useKconfBatch';
import { Popup } from '@lux/sharp-ui-next';
import { stopMove } from '@alive-ui/actions';
import type { StopFunItem } from '@alive-ui/actions';
export default defineComponent({
    components: {
        'spu-popup': Popup,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '记录',
        },
        bgClass: {
            type: String,
            default: '',
            required: false,
        },
        closeClass: {
            type: String,
            default: '',
            required: false,
        },
        topText: {
            type: String,
            default: '仅展示最近100条记录',
            required: false,
        },
    },
    setup(props, { emit }) {
        const { kconfData } = storeToRefs(useKconf());

        const showModel = ref<boolean>(false);
        let stopMoveObj: StopFunItem;
        const refPopup = ref(null);
        const bigModalBg = ref(
            kconfData.value?.niudan?.lotteryConfigs?.bigModalBg,
        );

        const hide = () => {
            stopMoveObj.cancelMove();
            emit('close');
        };

        watch(
            () => props.show,
            (o) => {
                if (o) {
                    stopMoveObj = stopMove();
                }
                showModel.value = o;
            },
            { immediate: true },
        );

        onUnmounted(() => {
            stopMoveObj?.destroy?.();
        });

        onMounted(async () => {});

        return {
            refPopup,
            hide,
            showModel,
            bigModalBg,
        };
    },
});
</script>

<style lang="less" scoped>
/deep/ .spu-popup__box {
    height: 100%;
    max-height: unset;
    margin-top: 104px;
}
.pop-content {
    position: relative;
    .pop-bg {
        padding: 0 9px;
        padding-top: 124px;
        .title-content {
            position: relative;
            z-index: 1;
            text-align: center;
            box-sizing: content-box;
        }
        .gift-title {
            width: fit-content;
            font-family: var(--popupTitleFamily);
            font-size: var(--popupTitleFontSize);
            line-height: 28px;
            color: transparent;
            text-align: center;
            background: var(--popupTitleColor);
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .rotate-icon {
            transform: var(--iconRotate, rotateY(180deg));
        }
        .icon {
            display: inline-block;
            width: 24px;
            height: 24px;
            margin-top: 2px;
        }
        .gift-sub-title {
            margin: 4px auto 12px;
            line-height: 18px;
            color: var(--fontSubColor);
            text-align: center;
            opacity: 0.6;
        }
    }
}
.gis-icon-close {
    position: absolute;
    right: 50%;
    margin-top: 5px;
    transform: translateX(50%);
}
</style>
