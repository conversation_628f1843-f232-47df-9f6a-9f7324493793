<template>
    <SmallModal
        :show="show"
        :title="configStore.dataConfig.coinName + '记录'"
        @close="handleClose"
    >
        <template #content>
            <div class="content">
                <div class="gift-sub-title text-12">仅展示最近100条记录</div>
                <div class="scroll">
                    <LoadingAndError
                        v-if="
                            pageStatus.loading ||
                            pageStatus.nodata ||
                            pageStatus.error
                        "
                        class="loading-and-error"
                        :page-case="'other'"
                        :page-status="pageStatus"
                        @refresh="queryRecord"
                    />
                    <div v-else class="record">
                        <div
                            v-for="(item, index) in records"
                            :key="index"
                            class="coin-record-item flex flex-between align-center"
                        >
                            <div class="record-left">
                                <div class="record-title text-14 text-bold">
                                    {{ item.title }}
                                </div>
                                <div class="coin-get-time text-12">
                                    {{
                                        formatTime(
                                            item.timestamp,
                                            'MM月dd日 HH:mm',
                                        ).replace(/^0/, '')
                                    }}
                                </div>
                            </div>
                            <div
                                class="gain-amount text-14"
                                :class="
                                    item.operationType == 1
                                        ? 'color-red'
                                        : 'color-gray'
                                "
                            >
                                {{ item.amount }}
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    v-if="records && records.length > 3"
                    class="coin-record-mask"
                />
            </div>
        </template>
    </SmallModal>
</template>

<script lang="ts">
/* eslint-disable @typescript-eslint/no-explicit-any */
import { defineComponent, ref, reactive } from 'vue';
import { formatTime } from '@alive-ui/actions';
import useConfigStore from '../../store/config';
import LoadingAndError from '../../components/loading-and-error/index.vue';
import { coinGainRecord } from '../../api/index';
import SmallModal from './base/base-small.vue';
import type { CoinRecordItem } from '../../api/index';

export default defineComponent({
    components: {
        SmallModal,
        LoadingAndError,
    },
    props: {
        show: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, { emit }) {
        const records = ref<CoinRecordItem[]>([]);

        const pageStatus = reactive({
            loading: false,
            error: false,
            nodata: false,
        });
        const configStore = useConfigStore();

        const queryRecord = async () => {
            pageStatus.loading = true;
            pageStatus.error = false;

            try {
                const data = await coinGainRecord();
                records.value = data?.statements?.list || [];
                pageStatus.nodata = !records?.value?.length;
            } catch (err) {
                pageStatus.error = true;
                console.error('get record error:', err);
            }
            pageStatus.loading = false;
        };
        queryRecord();

        const handleClose = () => {
            emit('close');
        };

        return {
            configStore,
            pageStatus,
            records,
            formatTime,
            queryRecord,
            handleClose,
        };
    },
});
</script>

<style lang="less" scoped>
.scroll {
    height: 222px;
    overflow: scroll;

    @apply a-text-main;
}
.content {
    /deep/ .spring-loading-error {
        bottom: auto;
        margin-top: 35px;
        .refresh-button {
            // margin-top: 50px;
        }
    }
    .gift-sub-title {
        margin: 4px auto 12px;
        line-height: 18px;
        color: var(--fontSubColor);
        text-align: center;
        opacity: 0.6;
    }
    .coin-record-item {
        width: 236px;
        height: 57px;
        padding: 0 16px;
        margin: 0 auto 8px;
        background: var(--recordItemBg);
        border-radius: 8px;
    }
    .record-left {
        text-align: left;
        .record-title {
            font-family: PingFangSC, PingFangSC-Medium;
        }
        .coin-get-time {
            margin-top: 6px;
            font-family: PingFangSC, PingFangSC-Regular;
            line-height: 17px;
            opacity: 0.6;
        }
    }
    .gain-amount {
        font-family: 'AlteDIN1451Mittelschrift';
        height: 17px;
        line-height: 17px;
        text-align: right;
    }
    .color-red {
        color: var(--fontRedColor);
        &::before {
            content: '+';
        }
    }
    .color-gray {
        color: #e39b26;
        &::before {
            content: '-';
        }
    }
}

.coin-record-mask {
    width: 236px;
    height: 29px;
    margin-left: 22px;
    margin-top: -28px;
    position: relative;
    z-index: 1;
}
</style>
