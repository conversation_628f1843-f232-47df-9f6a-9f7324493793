/* eslint-disable @typescript-eslint/no-explicit-any */
import {
    authorId,
    liveStreamId,
    bolIsAuthor,
    activityBiz,
    request,
} from '@alive-ui/actions';

const usePreFetchApiService = async <T>(
    apiService: () => Promise<T>,
): Promise<T> => {
    const usePreFetchApiService = window.usePreFetchApiService?.() || {};
    const { preFetchService } = usePreFetchApiService;

    if (preFetchService) {
        try {
            const res = await preFetchService;
            console.log('preFetchService', res);

            return res[0] || {};
        } catch (error) {
            return await apiService();
        }
    } else {
        return await apiService();
    }
};

type ApiRes<T> = {
    result: number;
    msg: string;
    data: T;
};

const params = {
    liveStreamId,
    authorId,
    biz: activityBiz,
};

export type UserInfo = {
    userId: number;
    headUrl: string;
    userName: string;
    liveStreamId?: string;
};

export type PropsCardItem = {
    id: number;
    typeKey: string;
    name: string;
    desc: string;
    icon: string;
    expireTime: number;
    createTime: number;
    sourceName: string;
};

// 道具卡-列表查询
export interface AllPropsCardInfo {
    unusedList: PropsCardItem[]; // 未使用
    alreadyUsedList: PropsCardItem[]; // 已使用
    alreadyExpiredList: PropsCardItem[]; // 已过期
}

export const queryPropsCardList = async () => {
    const res = await request.post<AllPropsCardInfo>(
        '/rest/wd/live/plutus/propsCard/queryPropsCardList',
        {
            ...params,
        },
    );

    return res.data;
};

// 道具卡-最近可用道具卡
export interface RecentPropsCardInfo {
    propsCardView: PropsCardItem;
}

export const queryRecentPropsCard = async () => {
    const res = await request.post<RecentPropsCardInfo>(
        '/rest/wd/live/plutus/propsCard/queryRecentPropsCard',
        {
            ...params,
        },
    );

    return res.data;
};

export type AwardItem = {
    id: number;
    name: string;
    count: number;
    icon: string;
    displayId: number;
    bizId?: any;
};

// 抽奖接口
export interface DrawRewardResponse {
    [x: string]: any;
    awards: AwardItem[];
    needFillAddress: boolean; // 是否需要填写地址
    needJumpBackPack: boolean;
    needJumpWallet: boolean;
    toast: string;
    subTitle: string;
}

/**
 * 普通抽奖/使用道具卡
 * https://mock.corp.kuaishou.com/project/941/interface/api/828321
 * @param ruleId
 * @param propsCardId
 * @returns
 */
export const drawReward = async (ruleId: number, propsCardId?: number) => {
    const res = await request.post<DrawRewardResponse>(
        '/rest/wd/live/plutus/luckyBag/draw',
        {
            ...params,
            ruleId,
            ...(propsCardId ? { propsCardId } : {}),
        },
    );

    return res.data;
};

// 任务+余额聚合接口
export type TaskItem = {
    typeKey: string; // 任务类型
    name: string; // 任务名称
    desc: string; // 任务描述
    iconUrl: string; // 任务icon
    finishCount: number; // 完成进度
    needCount: number; // 目标值
    status: number; // 1 未完成 2 已完成
    buttonText: string;
    kwaiLink: string; // 任务kwai链
    expandMap: Record<string, any>;
    receiveTask: boolean;
    rulePageLink: string;
};

export interface TaskResponse {
    balance: number; // 余额
    taskItemViewList: TaskItem[];
}

export const postLotteryTask = async () => {
    const res = await request.post<TaskResponse>(
        '/rest/wd/live/plutus/aggregate/audienceTask',
        {
            ...params,
            // ...taskParams,
        },
    );

    return res.data;
};

export type AwardMarqueeInfo = {
    records: string[];
    mustWinToast1: string;
    mustWinToast2: string;
};

/**
 * 扭蛋抽奖-h5跑马灯轮播
 * https://mock.corp.kuaishou.com/project/941/interface/api/828361
 * @returns
 */
export const queryAwardMarquee = async () => {
    const res = await request.post<AwardMarqueeInfo>(
        '/rest/wd/live/plutus/luckyBag/getTopBanner',
        {
            ...params,
        },
    );

    return res.data;
};

export type LotteryRecordItem = {
    id: number;
    name: string;
    count: number;
    icon: string;
};

export type Records = {
    timestamp: number;
    desc: string; // "普通奖池获得" | "至尊奖池获得"
    items: LotteryRecordItem[];
};

export type DrawRewardInfo = {
    records: Records[];
    needFillAddress: boolean; // 是否需要填写地址
};

/**
 * 扭蛋抽奖-抽奖奖励记录
 * https://mock.corp.kuaishou.com/project/941/interface/api/828391
 * @returns
 */
export const queryLotteryRecord = async () => {
    const res = await request.post<DrawRewardInfo>(
        '/rest/wd/live/plutus/luckyBag/getLotteryRecords',
        {
            ...params,
        },
    );

    return res.data;
};

//
export type CoinRecordItem = {
    title: string;
    timestamp: number;
    amount: number;
    operationType: number; // 1 正 2 负
};

export type CoinGainResponse = {
    statements: {
        list: CoinRecordItem[];
    };
};

// 福币获取记录:todo:pcursor
export const coinGainRecord = async () => {
    const res = await request.post<CoinGainResponse>(
        '/rest/wd/live/plutus/virtualWallet/queryUserStatement',
        {
            ...params,
            pcursor: '',
            pageSize: 100, // 可不传默认20
        },
    );

    return res.data;
};

// 获取推荐主播列表
export type SmallAuthorItem = {
    authorId: number;
    headUrl: string;
    userName: string;
    liveStreamId?: string | null;
    reservationId: string;
    reservationTitle: string;
    reservationLiveTime: number;
    reservationCount: number;
    orderId: string;
};

export type RecomResponse = {
    recoAuthorList: SmallAuthorItem[];
};

export const queryRecoAuthor = async (typeKey: string) => {
    const res = await request.post<RecomResponse>(
        '/rest/wd/live/plutus/audienceTask/recoAuthor',
        {
            typeKey,
            biz: activityBiz,
        },
    );

    return res.data;
};

// 关注任务回调
export const focusTaskCallBack = async (
    followAuthorId: string | number,
    orderId: string,
) => {
    const res = await request.post(
        '/rest/wd/live/plutus/audienceTask/h5Follow',
        {
            followAuthorId,
            biz: activityBiz,
            orderId,
        },
    );

    return res.data;
};

// 每日登录任务
export const dailyLogin = async () => {
    const res = await request.post<ApiRes<boolean>>(
        '/rest/wd/live/plutus/audienceTask/dailyLogin',
        {
            // 服务端对主播id和userId相同的情况有过滤，为了完成任务，主播侧传0
            authorId: bolIsAuthor ? 0 : authorId || 0,
            biz: activityBiz,
        },
    );

    return res.data;
};

// 搜索关键词回调
export const searchKeyWord = async (keyWord: string) => {
    const res = await request.post<boolean>(
        '/rest/wd/live/plutus/audienceTask/searchKeyWord',
        {
            biz: activityBiz,
            keyWord,
        },
    );

    return res.data;
};

export const getBalance = async () => {
    const res = await request.post<{ balance: number }>(
        '/rest/wd/live/plutus/virtualWallet/queryAccountBalance',
        {
            biz: activityBiz,
        },
    );

    return res.data;
};

// 老虎机配置信息
export type BtnRule = {
    ruleId: number;
    coinCount: number;
    drawCount: number;
};

export type BoxConfigInfo = {
    balance: number;
    normalPanel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
    superPanel: {
        prizeList: AwardItem[];
        btnList: BtnRule[];
    };
};

/**
 * 抽奖页面初始化
 * https://mock.corp.kuaishou.com/project/941/interface/api/897323
 * */
export const getBoxConfig = async () => {
    return await usePreFetchApiService(async () => {
        const res = await request.post<BoxConfigInfo>(
            '/rest/wd/live/plutus/luckyBag/getInitInfo',
            {
                biz: activityBiz,
            },
        );

        return res.data;
    });
};

// 预约直播
export const bookReservation = async (reservationId: string) => {
    const res = await request.post(
        '/rest/wd/live/plutus/reservation/user/book',
        { reservationId },
    );

    return res.data;
};

// 获取激励任务参数
export const getAdInspireScheme = async () => {
    const res = await request.post<{ scheme: string }>(
        '/rest/wd/live/plutus/audienceTask/getAdInspireScheme',
        {
            biz: activityBiz,
        },
    );

    return res.data;
};

// 领取任务上报
export const receiveTask = async (typeKey: string) => {
    const res = await request.post<{ scheme: string }>(
        '/rest/wd/live/plutus/audienceTask/receive',
        {
            authorId,
            biz: activityBiz,
            typeKey,
        },
    );

    return res.data;
};
