<template>
    <div class="parallel-tab row-center gis-lottery-tab-bg">
        <div
            class="tab-bar gis-lottery-tab-select"
            :class="lotteryType === 2 ? 'slide-r' : 'slide-l'"
        />
        <div
            class="row-center item"
            :class="{ active: lotteryType === 1 || lotteryType === -1 }"
            @click="handleChange(1)"
        >
            <div class="text text-16">幸运奖池</div>
        </div>
        <div
            class="row-center item"
            :class="{ active: lotteryType === 2 }"
            @click="handleChange(2)"
        >
            <div class="text text-16">至尊奖池</div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import useBoxInfo from '../hooks/useBoxInfo';

export default defineComponent({
    props: {
        lotteryType: {
            type: Number,
            default: 1,
        },
        isRotating: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, ctx) {
        const boxStore = useBoxInfo();

        const handleChange = (lotteryType: number) => {
            if (props.isRotating || boxStore.pageStatus.loading) {
                return;
            }
            console.log('切换奖池', lotteryType);
            ctx.emit('change', lotteryType);
        };

        return {
            handleChange,
        };
    },
});
</script>

<style lang="less" scoped>
.parallel-tab {
    width: 175px;
    height: 34px;
    padding: 1px;
    margin: auto;
    .item {
        z-index: 1;
        width: 50%;
        height: 100%;
        margin-top: -2px;
        color: transparent;
        .text {
            font-family: var(--drawTabTitleFontFamily);
            font-weight: var(--drawTabFontWeight, 400);
            line-height: 22px;
            transition: color 0.3s;
            @apply a-text-white;
        }
        &.active {
            .text {
                color: var(--tabActiveColor, #fff);
                @apply a-text-tab-solid-active;
            }
        }
    }
    .tab-bar {
        position: absolute;
        transition: transform 0.3s;
        left: 0;
        &.slide-l {
        }
        &.slide-r {
            transform: translateX(var(--tabSwitchWidth, 82px));
        }
    }
}
</style>
