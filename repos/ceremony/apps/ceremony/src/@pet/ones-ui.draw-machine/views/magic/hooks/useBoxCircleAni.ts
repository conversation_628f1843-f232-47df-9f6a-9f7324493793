/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, onBeforeUnmount, watch, computed, onMounted } from 'vue';
import { storeToRefs, defineStore } from 'pinia';
import { limitFPS } from '@alive-ui/actions';
import {
    giftsNum,
    BoxSpeedType,
    BTN_STATUS,
    CircleState,
    DRAW_EVENT,
    AMIN_AUDIO,
} from '../config';
import { useMain } from '../../../store/useMain';
import { useBoxInfo } from './useBoxInfo';
import { useAudioContext } from './audio';
import type { Rate } from '../config';
/**
 * 通过设置帧数间隔 调整动画执行频率
 * 例如 每四帧 增加一步
 * */
let frameCount = 1;

export const getStep = (target: number) => {
    if (frameCount >= target) {
        frameCount = 0;

        return 1;
    }
    frameCount++;

    return 0;
};

// eslint-disable-next-line max-lines-per-function
export const useBoxCircleAni = defineStore('lottery-box-anim', () => {
    const mainStore = useMain();
    const boxInfo = useBoxInfo();
    const {
        btnStatus,
        isRotating,
        isCountDownTime,
        delayedFn,
        lotteryType,
        lotteryKey,
        giftsList,
    } = storeToRefs(boxInfo);
    const boxCircle = ref<{ status?: CircleState; params?: any }>({});

    let audioActivePlay = async () => {};

    let audioLastPlay = async () => {};

    let audioLastPause = () => {};

    const resetAudioContext = () => {
        const audioActive = useAudioContext({
            source: AMIN_AUDIO.active,
        });
        const audioLast = useAudioContext({
            source: AMIN_AUDIO.last,
        });
        audioActivePlay = audioActive.play;
        audioLastPlay = audioLast.play;
        audioLastPause = audioLast.pause;
    };

    watch(
        () => mainStore.isLowDev,
        () => {
            if (mainStore.isLowDev) {
                audioActivePlay = async () => {};

                audioLastPlay = async () => {};

                audioLastPause = () => {};
            }
        },
        { immediate: true },
    );

    // 更新数据信息
    function updateGiftClassStyle(
        ind: number,
        updatePragram: Record<string, any>,
    ) {
        const gift = giftsList.value[ind];

        for (const key in updatePragram) {
            Object.assign(gift[key], updatePragram[key]);
        }
    }

    const giftId = ref(-1);
    const posList = ref([] as Array<number>);
    let isComb = false;
    const DefaultCircle = 2;
    const DefaultFrame = 4;
    const DefaultFramsArr = [9, 24, 42, 72, 108, 153, 210, 270];
    let circle = DefaultCircle;
    let giftFrame = DefaultFrame; // 匀速阶段每个礼品高亮间隔的帧数
    const fastAniTime = ref(200); // 减少执行动画时间
    const lastAniTime = ref(840); // 减少延迟执行动画时间
    const slowAniTime = ref(200);
    let frameArray = DefaultFramsArr; // 倒计时阶段的数组
    let frameInd = 0;
    let lastFrame = 0;
    let lastRoundGiftInd = -1;
    let totalInd = Infinity;
    let ind: number | null = null;
    let frameNum = 0; // 当前所处的渲染帧数
    const shake = ref(false);
    const lightAni = ref(false);
    let currInd = 0;
    let raf = 0;
    let curCir = 0; // 当前的圈数
    let preInd = -1;
    // const animationDegradationStrategy = ref(0); // 动画降级策略
    let isRotatingAndChangeType = false;

    let isRotateBreak = false; // 是否停止循环

    let stoping = false; // 用户手动停止 处于倒计时阶段
    let isRateChange = false;
    let rateList: Rate[] = [];

    const rotateReset = () => {
        isRateChange = false;
        rateList = [];
        circle = DefaultCircle;
        giftFrame = DefaultFrame;
        fastAniTime.value = 200;
        lastAniTime.value = 840;
        slowAniTime.value = 200;
        frameArray = DefaultFramsArr;
        stoping = false;
        isRotating.value = false;
        btnStatus.value = BTN_STATUS.init;
        giftId.value = -1;
    };

    const DefaultGiftClass = {
        upClass: { active: false, 'active-last': false, light: false },
        iconClass: { active: false, 'active-last': false, zoom: false },
        upStyle: { animationDuration: '200ms', animationDelay: '60ms' },
    };

    const drawEnd = (params: { res: boolean }) => {
        mainStore.busEvent.emit(DRAW_EVENT.animEndDelay, params);
        mainStore.busEvent.emit(DRAW_EVENT.showResultPop, { isEmpty: false });
        audioLastPause();

        // 清理奖品激活态
        for (let i = 0; i < giftsNum; i++) {
            updateGiftClassStyle(i, {
                upClass: { active: false },
                iconClass: { active: false },
            });
        }
    };

    const clearGiftClass = () => {
        for (let i = 0; i < giftsNum; i++) {
            updateGiftClassStyle(i, DefaultGiftClass);
        }
    };

    /**
     * 设置抽奖动效 旋转频率
     * 从第几圈开始到第几圈结束
     * 频率是多少 frame越大越慢
     */
    const changeRate = (begin: number, end: number, frame: number) => {
        rateList.push({
            beginCir: begin,
            endCir: end,
            frame: frame || DefaultFrame,
        });
        isRateChange = true;
    };

    /**
     * 在倒计时阶段之前通过调整帧率改变速度
     * **/
    const changeRateCountDown = (frames: number[]) => {
        frameArray = frames;
    };
    // 匹配中奖奖品，每次抽奖需要归零posCurInd
    let posCurInd = 0;

    const matchPosList = (pos: number) => {
        if (pos === posList.value[posCurInd]) {
            posCurInd++;

            return true;
        }

        return false;
    };

    // 开始动画之前的初始化操作
    const renderBefore = () => {
        boxCircle.value = { status: CircleState.renderBefore };
        frameInd = 0;
        lastFrame = 0;
        posCurInd = 0;

        // 清理选中的礼物的状态
        if (lastRoundGiftInd !== -1) {
            preInd = lastRoundGiftInd;

            for (let i = 0; i < giftsNum; i++) {
                updateGiftClassStyle(i, DefaultGiftClass);
            }
        }
        // 生成0-9的随机数，代表礼品的下标
        ind = null;
        totalInd = Infinity;
        frameNum = 0; // 当前所处的渲染帧数
        // const countDownCount = 6;
        preInd = -1;
        currInd = 0;
        raf = 0;
        curCir = 0; // 当前的圈数
        // 清理至尊xx最后的抖动
        shake.value = false;
        lightAni.value = false;
        isRotatingAndChangeType = false;

        // 优化动画层级
        giftsList.value.forEach((gift: any) => {
            if (gift.style) {
                gift.style['z-index'] = 0;
            }
        });
        isRotating.value = true;
        delayedFn.value = false;
        isCountDownTime.value = false;
        isRotateBreak = false;
    };

    const renderStart = () => {
        if (giftId.value !== -1 && ind === null) {
            boxCircle.value = {
                status: CircleState.renderStart,
                params: {
                    ind: giftId.value,
                },
            };
            ind = giftId.value;
            const nativeInd = ind; // 实际需要旋转的个数
            let reallyCircle = circle; // 最后需要旋转的圈数
            // 这儿如果比较的是8个的话会有一个边界值bug，比如把下面的totalInd设置为7
            reallyCircle =
                reallyCircle * giftsNum + nativeInd < 9
                    ? reallyCircle + 1
                    : reallyCircle;
            totalInd = reallyCircle * giftsNum + nativeInd;
        }
    };

    const renderStartComb = () => {
        if (posList.value?.length > 0 && ind === null) {
            boxCircle.value = {
                status: CircleState.renderStart,
            };
            totalInd = posList.value[posList.value.length - 1];
            console.log(posList.value, totalInd);
        }
    };

    const downFrame = () => {
        boxCircle.value = { status: CircleState.downFrame };
    };

    const endFrame = () => {
        boxCircle.value = { status: CircleState.endFrame };

        setTimeout(() => {
            if (lotteryType.value === 1) {
                shake.value = true;
            } else {
                lightAni.value = true;
            }
        }, 500);
    };

    const rotateEnd = (res: boolean) => {
        boxCircle.value = { status: CircleState.rotateEnd };

        if (res) {
            setTimeout(() => {
                rotateReset();
                drawEnd({ res });
            }, 1500);
        } else {
            rotateReset();
            drawEnd({ res });
        }
    };

    const toRotateBreak = () => {
        isRotateBreak = true;
    };

    const updateRotateSpeed = (speedType: number) => {
        switch (speedType) {
            case BoxSpeedType.normal:
                circle = 2;
                giftFrame = DefaultFrame;

                return;
            case BoxSpeedType.fast:
                circle = 2;
                giftFrame = 3;

                return;
        }
    };
    // 切换至尊xx和幸运xx的响应
    const rotateType = computed(() => {
        return lotteryType.value;
    });

    watch(
        () => rotateType.value,
        () => {
            if (isRotating.value) {
                isRotatingAndChangeType = true;
            }

            if (lastRoundGiftInd >= 0) {
                for (let i = 0; i < giftsNum; i++) {
                    updateGiftClassStyle(i, DefaultGiftClass);
                }
                lightAni.value = false;
            }
        },
    );
    // 页面销毁
    let isUnmounted = false;

    onBeforeUnmount(() => {
        isUnmounted = true;
    });

    const resetAnim = () => {
        isUnmounted = false;
        resetAudioContext();
    };

    const animFunc = () => {
        if (preInd !== currInd) {
            preInd = currInd;
            lastRoundGiftInd = currInd;
            // 下一帧动画提前清理
            const nextInd = (currInd + 1) % giftsNum;

            updateGiftClassStyle(nextInd, {
                upClass: { active: false, 'active-last': false },
                iconClass: { active: false, 'active-last': false, zoom: false },
                upStyle: { animationDuration: '200ms', animationDelay: '60ms' },
            });

            updateGiftClassStyle(currInd % giftsNum, {
                style: { 'z-index': currInd % giftsNum },
            });

            // 旋转到最后一格
            if (currInd >= totalInd) {
                endFrame();

                updateGiftClassStyle(currInd % giftsNum, {
                    upClass: {
                        active: false,
                        'active-last': true,
                        light: true,
                    },
                    iconClass: {
                        active: false,
                        'active-last': true,
                        zoom: true,
                    },
                    upStyle: {
                        animationDuration: `${lastAniTime.value}ms`,
                        animationDelay: '700ms',
                    },
                });
                setTimeout(audioLastPlay, 700);
                rotateEnd(true);

                return; // 这里直接返回 终止循环
            }

            // 连抽时命中奖品
            if (isComb && matchPosList(currInd)) {
                console.log('命中', currInd);

                updateGiftClassStyle(currInd % giftsNum, {
                    upClass: {
                        active: false,
                        'active-last': false,
                        light: true,
                    },
                    iconClass: {
                        active: false,
                        'active-last': false,
                        zoom: true,
                    },
                    upStyle: {
                        animationDuration: `${fastAniTime.value}ms`,
                        animationDelay: '60ms',
                    },
                });
                setTimeout(audioLastPlay, 60);
            }

            // 旋转在前期 快速阶段
            if (currInd <= totalInd - 8) {
                updateGiftClassStyle(currInd % giftsNum, {
                    upClass: { active: true, 'active-last': false },
                    iconClass: { active: true, 'active-last': false },
                    upStyle: {
                        animationDuration: `${fastAniTime.value}ms`,
                        animationDelay: '60ms',
                    },
                });
                setTimeout(audioActivePlay, 60);
            } else {
                // 旋转在倒计时阶段
                const step = 7 - (totalInd - currInd) + 1;
                const durationUnit = (740 - 200) / 6;
                const delayUnit = (380 - 60) / 6;
                downFrame();

                updateGiftClassStyle(currInd % giftsNum, {
                    upClass: { active: true, 'active-last': false },
                    iconClass: { active: true, 'active-last': true },
                    upStyle: {
                        animationDuration: `${slowAniTime.value + durationUnit * step}ms`,
                        animationDelay: `${60 + delayUnit * step}ms`,
                    },
                });
                setTimeout(audioActivePlay, 60 + delayUnit * step);
            }
        }
    };

    const render = limitFPS(() => {
        if (isUnmounted || isRotatingAndChangeType) {
            rotateEnd(false);

            return;
        }

        if (isRotateBreak) {
            rotateReset();

            return;
        }
        curCir = Math.floor(currInd / giftsNum);
        boxCircle.value = { status: CircleState.render };

        // 通过调节频率 调整快速阶段的转速
        if (isRateChange) {
            const item = rateList.find(
                (it) => curCir >= it.beginCir && curCir < it.endCir,
            );
            giftFrame = item?.frame || DefaultFrame;
        }

        // 快速阶段
        if (totalInd - preInd > 8) {
            currInd += getStep(giftFrame);
            lastFrame = frameNum;
        } else {
            // 倒计时阶段 可以通过frameArray调节频率
            currInd = preInd;

            if (isCountDownTime.value && !delayedFn.value) {
                delayedFn.value = true;
            }
            isCountDownTime.value = true;

            if (frameNum - lastFrame >= frameArray[frameInd]) {
                frameInd++;
                currInd = preInd + 1;
            }
        }

        if (stoping && currInd <= totalInd - 8) {
            // 用户手动停止 直接进入倒计时阶段
            totalInd = currInd + ((totalInd - currInd) % giftsNum);

            if (totalInd - currInd <= 3) {
                totalInd += giftsNum;
                fastAniTime.value = 400;
            }
        }

        animFunc();

        frameNum++;

        if (raf) {
            window.cancelAnimationFrame(raf);
        }
        raf = window.requestAnimationFrame(render);
    }, 60);

    /**
     * 要停止的礼物位置
     */
    const startRotate = (toGiftPosition: number) => {
        lotteryKey.value = +new Date();
        giftId.value = toGiftPosition;
        isComb = false;
        renderBefore();
        renderStart();
        window.requestAnimationFrame(render);
    };

    const startRotateComb = (resList: number[]) => {
        lotteryKey.value = +new Date();
        isComb = true;
        posList.value = resList.map((item) => item + circle * giftsNum);
        renderBefore();
        renderStartComb();
        console.log('Comb frame', giftFrame);
        window.requestAnimationFrame(render);
    };

    /**
     * 多个相同礼物的情况下
     * 仅仅闪烁第一个
     */
    const flashItem = (id: number) => {
        const itemIndex = giftsList.value.findIndex(
            (item: any) => item.giftId === id,
        );

        if (itemIndex < 0) {
            return;
        }

        giftsList.value[itemIndex] = {
            ...giftsList.value[itemIndex],
            flash: true,
            winLottery: true,
        };

        // giftsList.value = [...giftsList.value];
        setTimeout(() => {
            giftsList.value = giftsList.value.map((item: any) => {
                return { ...item, flash: false };
            });
        }, 2000);
    };

    const changeCirlcle = (count: number) => {
        circle = count;
    };

    return {
        shake,
        resetAnim,
        isRotating,
        lightAni,
        updateRotateSpeed,
        changeRate,
        changeCirlcle,
        renderBefore,
        renderStart,
        lastFrame,
        rotateEnd,
        changeRateCountDown,
        rotateReset,
        render,
        endFrame,
        updateGiftClassStyle,
        boxCircle,
        startRotate,
        startRotateComb,
        toRotateBreak,
        flashItem,
        clearGiftClass,
    };
});

export default useBoxCircleAni;
