<template>
    <div class="lottery-main" :class="{ super: lotteryType === 2 }">
        <VideoKv
            :lottery-type="lotteryType"
            :header-video="
                dataConfig.headerVideoMap[
                    lotteryType === 2 ? 'super' : 'normal'
                ]
            "
        />
        <div class="pic-title gis-machine-title" />
        <!-- 顶部副标题区域 -->
        <div
            v-if="dataConfig.cardSubTitle"
            class="pic-sub-title text-12"
            :style="{ ...dataConfig.cardSubTitleStyle }"
        >
            {{ dataConfig.cardSubTitle }}
        </div>
        <!-- 顶部切换tab区域 -->
        <component
            :is="tabComp"
            v-if="dataConfig.isDoublePanel"
            :lottery-type="lotteryType"
            class="panel-tab"
            :is-rotating="isRotating"
            @change="bindChangeTab"
        />
        <!-- 中奖记录跑马灯区域 -->
        <AwardMarquee />
        <!-- 奖品池面板区域 -->
        <PrizeList
            class="price-box"
            :gifts-list="giftsList"
            :lottery-key="lotteryKey"
            :lottery-type="lotteryType"
            :no-fire-png="dataConfig.noFirePng"
        />
        <BtnList class="btn-box" :btn-list="btnList" />
        <div
            class="flex account-number position-absolute align-center text-din text-14 a-text-main"
        >
            <div class="icon-currency gis-icon-currency" />
            {{ balance ? `x${balance}` : 0 }}
        </div>
        <div
            v-pcDirectives:intercept
            v-show-log="funcLog.drawRecord"
            class="award-record-entry flex-center text-12 a-text-main"
            @click="switchLotteryRecord(true)"
        >
            中奖记录 <SvgArrow class="icon-jt" />
        </div>
        <ClientOnly>
            <!-- 中奖结果弹窗 -->
            <ResultModal
                :show="resultPopShow"
                :sub-title="subTitle"
                :award-list="resultList"
                :need-fill-address="needFillAddress"
                :need-jump-back-pack="needJumpBackPack"
                :need-jump-wallet="needJumpWallet"
                :enable-show-emoticon-tab="enableShowEmoticonTab"
                :need-send-emoticon="needSendEmoticon"
                :scene-type="1"
                @close="switchResultPop(false)"
            />
        </ClientOnly>
        <!-- 中奖记录弹窗 -->
        <RecordModal
            v-if="lotteryRecordVisible"
            :show="lotteryRecordVisible"
            :show-panel-icon="dataConfig.isDoublePanel"
            @close="switchLotteryRecord(false)"
        />
        <!-- <ClientOnly> -->
        <!-- 第一次进入页面弹窗 -->
        <!-- <OnceModal
                v-if="configStore.showOnceModal"
                :show="configStore.showOnceModal"
                :show-panel-icon="dataConfig.isDoublePanel"
                @close="configStore.dataConfig.onceModal = false"
            /> -->
        <!-- </ClientOnly> -->
        <div class="gradient-overlay"></div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onUnmounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { usePopAdapter } from '@pet/ones-use.usePopAdapter/index';
import { ClientOnly } from '@live/ssg';
import { useMain, COMMON_EVENT } from '../../store/useMain';
import { useConfigStore } from '../../store/config';
import RecordModal from '../../modules/popups/reward-modal.vue';
import ResultModal from '../../modules/popups/result-modal.vue';
import OnceModal from '../../modules/popups/once-modal.vue';
import { funcLog, sendClick } from '../../logger';
import AwardMarquee from '../../components/award-marquee.vue';
import SvgArrow from '../../components/arrow.vue';
import useClickLotteryFlow from './hooks/useClickLotteryFlow';
import useBoxInfo from './hooks/useBoxInfo';
import useBoxCircleAni from './hooks/useBoxCircleAni';
import { DRAW_EVENT, MODAL_TYPE } from './config';
import VideoKv from './comps/video-kv.vue';
import PrizeList from './comps/prize-list.vue';
import ParallelTab from './comps/parallel-tab.vue';
import BtnList from './comps/btn-list.vue';
import BaseTab from './comps/base-tab.vue';
import type { PropType } from 'vue';

export default defineComponent({
    components: {
        VideoKv,
        ParallelTab,
        BaseTab,
        AwardMarquee,
        PrizeList,
        BtnList,
        ClientOnly,
        ResultModal,
        RecordModal,
        OnceModal,
        SvgArrow,
    },
    props: {
        source: {
            type: String as PropType<'coinModal' | ''>,
            default: '',
        },
        isLowDev: {
            type: Boolean,
            default: false,
        },
        cardSubTitle: {
            type: String,
            default: '',
            required: false,
        },
        cardSubTitleStyle: {
            type: Object,
            default: () => ({}),
            required: false,
        },
    },
    setup(props) {
        const configStore = useConfigStore();
        const { dataConfig } = storeToRefs(configStore);

        const tabComp = computed(() =>
            dataConfig.value.notParallelTab ? BaseTab : ParallelTab,
        );
        const mainStore = useMain();
        const boxStore = useBoxInfo();
        const {
            lotteryType,
            lotteryKey,
            giftsList,
            isRotating,
            btnList,
            balance,
        } = storeToRefs(boxStore);

        const {
            subTitle,
            resultList,
            needFillAddress,
            needJumpBackPack,
            needJumpWallet,
            enableShowEmoticonTab,
            needSendEmoticon,
        } = storeToRefs(useClickLotteryFlow());
        const { clearGiftClass, resetAnim } = useBoxCircleAni();
        const { drawRecord } = funcLog;
        const { adapter, recover } = usePopAdapter('left');

        const initBox = () => {
            boxStore.init();
            resetAnim();
        };
        initBox();

        onMounted(() => {
            configStore.setVisitImg();
        });

        if (props.isLowDev) {
            mainStore.isLowDev = true;
        }
        const lotteryRecordVisible = ref(false);

        const switchLotteryRecord = (status: boolean) => {
            sendClick(drawRecord);
            if (status) {
                setTimeout(() => {
                    adapter();
                }, 0);
            }
            lotteryRecordVisible.value = status;
            if (!status) {
                recover();
            }
        };

        const resultPopShow = ref(false);

        const switchResultPop = (status: boolean) => {
            if (status) {
                setTimeout(() => {
                    adapter();
                }, 0);
            }
            resultPopShow.value = status;
            if (!status) {
                recover();
            }
        };

        const bindChangeTab = (val: any) => {
            lotteryType.value = val;
        };

        mainStore.busEvent.on(COMMON_EVENT.updateBallance, (val: number) => {
            boxStore.updateBalance(val);
        });

        mainStore.busEvent.on(
            DRAW_EVENT.showResultPop,
            (data?: { isEmpty: boolean }) => {
                switchResultPop(true);
                boxStore.isRotating = false;

                // 更新余额
                boxStore.updateBalance();
                //  只有中奖才更新 updateMarquee
                if (!data?.isEmpty) {
                    mainStore.busEvent.emit(DRAW_EVENT.updateMarquee);
                }
            },
        );

        mainStore.busEvent.on(DRAW_EVENT.showEmpty, () => {
            switchResultPop(true);
        });

        onUnmounted(() => {
            // 解绑所有事件
            mainStore.busEvent?.all?.clear();
        });

        return {
            configStore,
            dataConfig,
            tabComp,
            lotteryType,
            lotteryKey,
            giftsList,
            btnList,
            balance,

            isRotating,
            resultPopShow,
            lotteryRecordVisible,
            switchLotteryRecord,
            switchResultPop,
            subTitle,
            resultList,
            needFillAddress,
            needJumpBackPack,
            needJumpWallet,
            enableShowEmoticonTab,
            needSendEmoticon,
            funcLog,
            bindChangeTab,
        };
    },
});
</script>

<style lang="less" scoped>
.lottery-main {
    position: relative; /* Ensure positioning context for the overlay */
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 414px;
    height: var(--kvBgHeight);
    margin-bottom: var(--headBgMarginBottom, -48px);
}
.pic-title {
    position: absolute;
    top: 52px;
    left: 50%;
    transform: translateX(-50%);
}
.award-marquee {
    position: absolute;
    top: var(--marqueeTop, 203px);
    left: 50%;
    z-index: 11;
    transform: translateX(-50%);
}
.panel-tab {
    position: absolute;
    top: var(--panelTabTop, 56px);
    left: 50%;
    transform: translateX(-50%);
}
.price-box {
    position: absolute;
    top: var(--priceBoxTop, 295px);
    left: 50%;
    transform: translateX(-50%);
}
.btn-box {
    position: absolute;
    bottom: var(--btnBoxBottom, 121px);
    left: var(--btnBoxLeft, 73px);
}
.account-number {
    bottom: var(--coinBottom, 66px);
    left: var(--coinLeft, 75px);
    line-height: 19px;
    text-align: center;
}
.icon-currency {
    width: 15px;
    height: 15px;
    margin-right: 4px;
}
.award-record-entry {
    right: var(--entryRight, 50px);
    bottom: var(--entryBottom, 67px);
    padding-bottom: 1.5px;
    line-height: 17px;
    text-align: left;
    // pc上兼容性问题，style上加了一个relative
    position: absolute !important;
}
.icon-jt {
    width: 10px;
    height: 10px;
}

.pic-sub-title {
    position: absolute;
    top: 88px;
    left: 50%;
    width: 256px;
    height: 26px;
    line-height: 26px;
    color: #ffdfbf;
    text-align: center;
    transform: translateX(-50%);
}
.gradient-overlay {
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 118px; /* Adjust height as needed */
    background: linear-gradient(
        180deg,
        rgba(0, 5, 64, 0) 0%,
        rgba(0, 5, 64, 0.65) 43.91%,
        #000540 73.44%
    );
    pointer-events: none; /* Allow clicks to pass through */
}
</style>
