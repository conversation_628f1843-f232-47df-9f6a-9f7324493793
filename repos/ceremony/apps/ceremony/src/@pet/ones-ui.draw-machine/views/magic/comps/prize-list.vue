<template>
    <div class="gift-box">
        <div
            v-for="(item, index) of giftsList"
            :key="item.name + index"
            class="item col-center"
            :class="[
                `gis-nd-fxz-${lotteryType}`,
                item.class,
                item?.isSpecial ? 'gis-prize-special' : '',
            ]"
        >
            <div
                class="giftUp gis-nd-xzt-1"
                :class="[`gis-nd-xzt-${lotteryType}`, item.upClass]"
                :style="item.upStyle"
            />
            <img
                v-if="item.upClass.light && !noFirePng"
                class="light-anim"
                :src="`https://p4-live.wskwai.com/kos/nlav12706/get.22058f7e03367f3d.png?_t=_${item.itemId}`"
            />
            <div class="gift-icon-wrap">
                <img
                    v-lazy="{
                        src: item.icon,
                        error: configStore.dataConfig.giftEmptyIcon,
                        loading: configStore.dataConfig.giftEmptyIcon,
                    }"
                    class="gift-icon"
                    :class="item.iconClass"
                    alt=""
                />
            </div>
            <div class="gift-name">
                {{ item.name }}
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useConfigStore } from '../../../store/config';
import type { PropType } from 'vue';

export default defineComponent({
    props: {
        giftsList: {
            type: Array as PropType<Record<string, any>[]>,
            default: () => [],
        },
        lotteryKey: {
            type: Number,
            default: 0,
        },
        lotteryType: {
            type: Number,
            default: 1,
        },
        noFirePng: {
            type: Boolean,
            default: false,
        },
    },
    setup() {
        const configStore = useConfigStore();

        return {
            configStore,
        };
    },
});
</script>

<style lang="less" scoped>
.gift-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: center;
    width: 266px;
    margin: 0 auto;
}
.item {
    position: relative;
    width: 84px;
    height: 79px;
    margin-left: 7px;
    margin-top: 6px;

    &:nth-child(3n + 1) {
        margin-left: 0;
    }
    &:nth-child(-n + 3) {
        margin-top: 0;
    }

    .gift-icon-wrap {
        z-index: 10;
        width: 55px;
        height: 55px;
        .gift-icon {
            width: 100%;
            height: auto;
            background: none;
        }
    }
    .gift-name {
        z-index: 10;
        font-size: 10px;
        line-height: 14px;
        color: #fff;
    }
    .light-anim {
        position: absolute;
        width: 86px;
        height: 81px;
    }
}
.button {
    width: 100%;
    height: 100%;
    background: #ccc;
}
.giftUp {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    // background: no-repeat left top / 100% auto;
    opacity: 0;
    transform: translate(-50%, -50%);
}
.gift-icon.active {
    animation: giftIconScale 120ms linear 0ms 1 normal none;
}

@keyframes giftIconScale {
    0% {
        transform: scale(1);
    }
    33.33% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}
.gift-icon.zoom {
    animation: zoomAni 260ms linear 0ms 1 normal none;
}

@keyframes zoomAni {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.5);
    }
    100% {
        transform: scale(1);
    }
}
.giftUp.active {
    opacity: 1;
    animation: upAni 200ms cubic-bezier(0.42, 0, 1, 1) 60ms 1 normal forwards;
}

@keyframes upAni {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
.giftUp.light {
    opacity: 1;
    &.active {
        animation-direction: forwards;
        animation-name: upAniLight;
    }
}

@keyframes upAniLight {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
    }
}
.giftUp.active-last {
    opacity: 1;
    animation: lastUpAni 840ms cubic-bezier(0.42, 0, 1, 1) 700ms 1 normal none;
}

@keyframes lastUpAni {
    0% {
        opacity: 1;
    }
    16.67% {
        opacity: 0.6;
    }
    33.33% {
        opacity: 1;
    }
    50% {
        opacity: 0.6;
    }
    66.67% {
        opacity: 1;
    }
    83.33% {
        opacity: 0.6;
    }
    100% {
        opacity: 1;
    }
}
</style>
