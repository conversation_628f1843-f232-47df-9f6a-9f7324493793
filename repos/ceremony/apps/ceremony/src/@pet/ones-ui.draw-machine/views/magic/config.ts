/* eslint-disable @typescript-eslint/no-explicit-any */

export enum CircleState {
    'renderBefore' = 'renderBefore',
    'renderStart' = 'renderStart',
    'endFrame' = 'endFrame',
    'rotateEnd' = 'rotateEnd',
    'render' = 'render',
    'downFrame' = 'downFrame',
}

export enum DRAW_EVENT {
    'showResultPop' = 'showResultPop',
    'showEmpty' = 'showEmpty',
    'updateMarquee' = 'updateMarquee',
    'animEndDelay' = 'animEndDelay',
}

export interface Rate {
    beginCir: number;
    endCir: number;
    frame: number;
}

export enum BTN_STATUS {
    'init' = 1,
    'lucking' = 2,
    'stop' = 3,
}

export enum BoxSpeedType {
    slow = 1,
    normal = 2,
    fast = 3,
}

export const giftsNum = 9; // 列表中礼品的个数;

export const AMIN_AUDIO = {
    active: 'https://p4-live.wskwai.com/kos/nlav12706/lottery/active.4a22f81c314188fc.mp3',
    last: 'https://p4-live.wskwai.com/kos/nlav12706/lottery/last.4d931e90278fa4d0.mp3',
};

export const DrawResult = {
    error: 0,
    success: 1,
    empty: 2,
    offseason: 3, // 休赛
} as const;

export enum MODAL_TYPE {
    resultModal = 1, // 中奖结果弹窗
}
