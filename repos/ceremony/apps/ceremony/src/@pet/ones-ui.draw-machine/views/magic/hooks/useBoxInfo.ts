/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref, watch, reactive } from 'vue';
import { defineStore } from 'pinia';
import { Report } from '@alive-ui/actions';
import { BTN_STATUS } from '../config';
import { useMain, COMMON_EVENT } from '../../../store/useMain';
import { useConfigStore } from '../../../store/config';
import { getBoxConfig, getBalance } from '../../../api';
import type { BoxConfigInfo, BtnRule } from '../../../api';

export const useBoxInfo = defineStore('lottery-box', () => {
    const mainStore = useMain();
    const configStore = useConfigStore();

    //    1 幸运奖池；2 至尊奖池
    const lotteryType = ref(1);
    const lotteryKey = ref(+new Date());
    const isRotating = ref(false); // 动画旋转中
    const disabled = ref(false); // 开启抽奖 页面不可点击
    const btnStatus = ref(BTN_STATUS.init); // 面板按钮状态
    const isCountDownTime = ref(false); // 倒计时阶段
    const delayedFn = ref(false); // 倒计时样式有关
    const pageStatus = reactive({
        loading: true,
        error: false,
    });
    const balance = ref(0);
    const boxConfig = ref({} as BoxConfigInfo);

    // 获取老虎机配置
    const init = async () => {
        try {
            pageStatus.loading = true;
            const res = await getBoxConfig();
            pageStatus.error = false;
            const data = res;
            boxConfig.value = data ?? {};
            balance.value = data?.balance ?? 0;
        } catch (error: any) {
            if (error?.data?.result === 109) {
                mainStore.busEvent.emit(COMMON_EVENT.noLogin);
            } else {
                //    自定义埋点，初始化面板失败，基本是接口原因，将接口传入的相关参数作为信息上报
                Report.biz.error('【扭蛋机】：初始化面板失败', {
                    error,
                });
            }
            console.error(' error:', error);
            pageStatus.error = true;
        }
        pageStatus.loading = false;
        mainStore.busEvent.emit(COMMON_EVENT.sendFmp);
    };

    const updateConfig = async () => {
        try {
            const res = await getBoxConfig();

            if (res) {
                boxConfig.value = res;
            }
        } catch (error) {
            console.error('update error:', error);
        }
    };

    // TODO 删除 面板的giftsList
    const dealPrize = (giftObj: any, ind: number) => {
        return {
            ...giftObj,
            ind,
            itemId: `${giftObj.count}-${giftObj.id}`,
            class: {
                firstGift: ind === 0,
                gift: ind !== 0,
                nth0: ind !== 0 && ind % 2 === 0,
                nth1: ind !== 0 && ind % 2 === 1,
                active: false,
            },
            style: {
                'z-index': 0,
            },
            upClass: { active: false, 'active-last': false },
            upStyle: { animationDuration: '200ms', animationDelay: '60ms' },
            iconClass: { active: false, 'active-last': false },
        };
    };
    const giftsList = ref<any[]>([]);
    const btnList = ref<BtnRule[]>([]);

    watch(
        () => ({ lotteryType: lotteryType.value, boxConfig: boxConfig.value }),
        () => {
            const { normalPanel, superPanel } = boxConfig.value;
            const curPanel = (lotteryType.value === 1
                ? normalPanel
                : superPanel) || {
                prizeList: [],
                btnList: [],
            };

            if (curPanel.prizeList.length < 9) {
                curPanel.prizeList = [
                    ...curPanel.prizeList,
                    ...new Array(9 - curPanel.prizeList.length).fill({
                        id: -10086,
                        name: '奖品',
                        count: 1,
                        icon: configStore.dataConfig.giftEmptyIcon,
                    }),
                ];
            }
            giftsList.value = curPanel.prizeList.map(dealPrize);
            // giftsList.value = prizeInfoList.map(dealPrize);
            btnList.value = curPanel.btnList;
        },
        { immediate: true },
    );

    const updateBalance = async (val?: number) => {
        try {
            if (typeof val === 'number') {
                balance.value = val;
            } else {
                const res = await getBalance();
                balance.value = res?.balance || 0;
            }
        } catch (error) {
            console.error('刷新余额失败');
        }
    };

    return {
        init,
        pageStatus,
        lotteryType,
        lotteryKey,
        isRotating,
        disabled,
        btnStatus,
        isCountDownTime,
        delayedFn,

        balance,
        updateBalance,
        updateConfig,
        boxConfig,
        btnList,
        giftsList,
    };
});

export default useBoxInfo;
