<!-- eslint-disable vue/no-v-html -->
<template>
    <div class="task-area-container">
        <div class="flex my-coin flex-between coin-record-wrap">
            <div class="coin-text flex-center" />
            <div
                v-if="needCoin"
                v-pcDirectives:intercept
                v-show-log="funcLog.coinRecord"
                v-click-log="funcLog.coinRecord"
                class="coin-text a-text-main-o2 flex-center"
                @click="switchRewardRecord(true)"
            >
                {{ dataConfig.coinName }}记录 <SvgArrow class="icon-jt" />
            </div>
        </div>

        <ColorfulCard :title="dataConfig.cardTitle" class="task-container">
            <div class="position-relative">
                <div class="task-area">
                    <div
                        v-if="
                            taskStore.taskList.length &&
                            !taskStore.pageStatus.loading &&
                            !taskStore.pageStatus.error
                        "
                        class="task-list-area"
                    >
                        <div
                            v-for="(item, index) in taskStore.taskList"
                            :key="index"
                            class="flex task-item flex-between align-center a-bg-substrate"
                        >
                            <div
                                v-show-log="
                                    taskBtnLog(item.buttonText, item.typeKey)
                                "
                                class="left-area flex-center"
                            >
                                <div
                                    class="icon-img"
                                    :class="{ 'empty-task': item.status !== 1 }"
                                >
                                    <img
                                        :src="item.iconUrl"
                                        :alt="item.typeKey"
                                    />
                                </div>
                                <div
                                    class="title-desc"
                                    :class="{ 'empty-task': item.status !== 1 }"
                                >
                                    <div class="first-title a-text-main">
                                        {{ item.name }}
                                        <span
                                            v-show="item.typeKey !== 'h5Follow'"
                                            class="task-value a-text-highlight"
                                        >
                                            {{ item.finishCount }}/{{
                                                item.needCount
                                            }}
                                        </span>
                                    </div>
                                    <!-- eslint-disable-next-line vue/no-v-html -->
                                    <div class="sub-reward-desc-box">
                                        <div
                                            class="sub-reward-desc a-text-main-o2"
                                            v-html="descComputed(item)"
                                        />
                                        <div
                                            v-if="item.rulePageLink"
                                            class="rule-page-link"
                                            @click="goRulePageLink(item)"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div
                                class="right-action"
                                :class="{ 'empty-task': item.status !== 1 }"
                            >
                                <div
                                    v-pcDirectives:intercept
                                    class="action-button flex-center gis-action-btn a-text-button"
                                    :class="[
                                        item.status !== 1
                                            ? 'empty-btn a-text-main a-stroke-main'
                                            : '',
                                    ]"
                                    @click="taskStore.buttonAction(item)"
                                >
                                    {{ item.buttonText }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <LoadingAndError
                        v-else
                        :page-case="'main'"
                        :page-status="taskStore.pageStatus"
                        @refresh="taskStore.refetchTask"
                    />
                </div>
            </div>
            <RecordModal
                v-if="rewardRecordVisible"
                :show="rewardRecordVisible"
                @close="switchRewardRecord(false)"
            />
            <!-- 关注弹窗 -->
            <FollowModal
                v-if="taskStore.followModal"
                :show="taskStore.followModal"
                @close="taskStore.closeModal"
                @refresh="taskStore.refetchTask"
            />
            <!-- 预约弹窗 -->
            <ReservationModal
                v-if="taskStore.reservationModal"
                :show="taskStore.reservationModal"
                @close="taskStore.closeModal"
                @refresh="taskStore.refetchTask"
            />
        </ColorfulCard>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { goOtherPage } from '@alive-ui/actions';
import useTaskStore from '../../store/task';
import useConfigStore from '../../store/config';
import ReservationModal from '../../modules/popups/reservation-modal.vue';
import FollowModal from '../../modules/popups/follow-modal.vue';
import RecordModal from '../../modules/popups/coin-modal.vue';
import { funcLog, taskBtnLog } from '../../logger/index';
import LoadingAndError from '../../components/loading-and-error/index.vue';
import ColorfulCard from '../../components/colorful-card.vue';
import SvgArrow from '../../components/arrow.vue';

export default defineComponent({
    components: {
        ColorfulCard,
        LoadingAndError,
        RecordModal,
        FollowModal,
        ReservationModal,
        SvgArrow,
    },
    props: {
        needCoin: {
            type: Boolean,
            default: true,
        },
        needDailyTask: {
            type: Boolean,
            default: true,
        },
    },
    setup(props) {
        const configStore = useConfigStore();
        const { dataConfig } = storeToRefs(configStore);
        const taskStore = useTaskStore();
        taskStore.watchUpdate?.();

        const init = async () => {
            // taskStore.getAppInfo();

            try {
                if (props.needDailyTask) {
                    await taskStore.initLogin();
                }
                await taskStore.queryTaskInfo();
            } catch (err) {
                console.error('lucky machine init', err);
            }
        };
        init();

        const rewardRecordVisible = ref(false);

        const switchRewardRecord = (status: boolean) => {
            rewardRecordVisible.value = status;
        };

        // 搜索描述数据
        const descComputed = (item: any) => {
            let { desc } = item;

            if (item.typeKey === 'searchKeyWord') {
                // 国庆扭蛋机需求
                desc = item.desc.replace(
                    /%k/g,
                    `<span class="strong">${item.expandMap.keyWord}</span>`,
                );
            }

            return desc;
        };

        const goRulePageLink = (item: any) => {
            if (item?.rulePageLink) {
                goOtherPage('jimu_location', item.rulePageLink);
            }
        };

        return {
            dataConfig,
            taskStore,
            rewardRecordVisible,
            switchRewardRecord,
            descComputed,
            taskBtnLog,
            funcLog,
            goRulePageLink,
        };
    },
});
</script>

<style lang="less" scoped>
.data-status-deal {
    margin-top: 40px;
}
.rotate90 {
    transform: rotate(90deg);
}
.gis-action-btn {
    width: var(--taskBtnWidth, 71px);
    height: var(--taskBtnHeight, 36px);
    font-size: 14px;
    line-height: var(--taskBtnHeight, 36px);
    text-align: center;
    background: url('../../variable/assets/icon-task-1_2x.png') center / 100%
        no-repeat;
    background-size: contain;
}
.empty-btn {
    background: none !important;
    border-style: solid;
    border-width: 1px;
    border-radius: 30px;
}
.gis-icon-currency {
    margin-right: 4px;
}

.my-coin {
    padding: 0 10px 0 34px;
    margin: var(--taskNavCoinMargin, auto);
}
.task-value {
    margin-left: 2px;
    font-family: 'AlteDIN1451Mittelschrift';
    font-size: 16px;
    font-weight: 500;
    line-height: 14px;
}
.coin-text {
    font-family: PingFangSC, PingFangSC-Regular;
    font-size: 12px;
    font-weight: 400;
    .icon-jt {
        width: 10px;
        height: 10px;
    }
}
.task-area {
    position: relative;
    z-index: 11;
    min-height: var(--taskAreaHeight, 294px);
    border-radius: 20px;
    .icon-img {
        width: 60px;
        height: 60px;
        img {
            width: inherit;
            height: inherit;
        }
    }
    .first-title {
        font-family: PingFangSC, PingFangSC-Medium;
        font-size: 17px;
        font-weight: 500;
        text-align: left;
    }
    .sub-reward-desc-box {
        display: flex;
        margin-top: 5px;
        align-items: center;
    }
    .sub-reward-desc {
        display: inline-block;
        font-family: PingFangSC, PingFangSC-Regular;
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        text-align: left;
    }
    .task-list-area {
        padding-bottom: 5px;
        margin: 5px 12px 0;
    }
    .task-item {
        width: 358px;
        height: 92px;
        padding: 12px 11px;
        margin: 0 auto 10px;
        border-radius: 8px;
        .title-desc {
            margin-left: 8px;
        }
        .strong {
            font-size: 700;
            color: var(--fontRedColor);
        }
        .empty-task {
            opacity: 0.6;
        }
    }
    .action-button {
        font-family: PingFangSC, PingFangSC-Semibold;
        font-size: 14px;
        font-weight: 700;
        text-align: center;
    }
}

.task-area-container {
    position: relative;
}
.coin-record-wrap {
    position: absolute;
    top: 14px;
    right: 16px;
    z-index: 2;
}
.rule-page-link {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 4px;
    background: url('../../variable/assets/rule-page-link_2x.png') center / 100%
        no-repeat;
}
</style>
