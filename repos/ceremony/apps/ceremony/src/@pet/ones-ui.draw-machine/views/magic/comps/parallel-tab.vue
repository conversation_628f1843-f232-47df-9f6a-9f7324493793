<template>
    <div class="parallel-tab row-center">
        <div class="border gis-lottery-tab-bg" />
        <div
            class="tab-bar gis-lottery-tab-bar"
            :class="lotteryType === 2 ? 'slide-r' : 'slide-l'"
        />
        <div
            class="row-center item"
            :class="{ active: lotteryType === 1 || lotteryType === -1 }"
            @click="handleChange(1)"
        >
            幸运奖池
        </div>
        <div
            class="row-center item"
            :class="{ active: lotteryType === 2 }"
            @click="handleChange(2)"
        >
            至尊奖池
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import useBoxInfo from '../hooks/useBoxInfo';

export default defineComponent({
    props: {
        lotteryType: {
            type: Number,
            default: 1,
        },
        isRotating: {
            type: Boolean,
            default: false,
        },
    },
    setup(props, ctx) {
        const boxStore = useBoxInfo();

        const handleChange = (lotteryType: number) => {
            if (props.isRotating || boxStore.pageStatus.loading) {
                return;
            }
            console.log('切换奖池', lotteryType);
            ctx.emit('change', lotteryType);
        };

        return {
            handleChange,
        };
    },
});
</script>

<style lang="less" scoped>
.parallel-tab {
    width: 175px;
    height: 34px;
    padding: 1px;
    margin: auto;
    overflow: hidden;
    background: rgba(25, 27, 51, 100%);
    border-radius: 20px;
    transform: translateZ(0);
    .border {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 175px;
        height: 34px;
    }
    .item {
        z-index: 1;
        width: 50%;
        height: 100%;
        margin-top: -2px;
        font-family: var(--drawTabTitleFontFamily);
        font-size: 13px;
        font-weight: var(--drawTabFontWeight, 400);
        color: rgba(255, 248, 230, 100%);
        transition: color 0.3s;
        &.active {
            color: rgba(25, 27, 51, 100%);
        }
    }
    .tab-bar {
        position: absolute;
        transition: transform 0.3s;
        width: 200px;
        height: 32px;
        &.slide-l {
            transform: translateX(-80px);
        }
        &.slide-r {
            transform: translateX(80px);
        }
    }
}
</style>
