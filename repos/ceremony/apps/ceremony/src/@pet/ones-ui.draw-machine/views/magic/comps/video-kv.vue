<template>
    <div :key="lotteryType" class="page-header-component">
        <div class="video-img">
            <VideoPlay
                not-click
                autoplay
                :poster-url="headerVideo.posterUrl"
                :video-url="[{ url: headerVideo.videoUrl }]"
            />
        </div>
        <div
            v-show="isPlaying"
            v-if="headerVideo.drawUrl"
            class="video-img playing"
        >
            <VideoPlay
                ref="kvPlayer"
                class="video-play-class"
                not-click
                :autoplay="false"
                :range="{ from: 1.2, to: 4 }"
                :video-url="[{ url: headerVideo.drawUrl }]"
            />
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onUnmounted, watch } from 'vue';
import { useBoxInfo } from '../hooks/useBoxInfo';
import { DRAW_EVENT } from '../config';
import { useMain } from '../../../store/useMain';
import VideoPlay from '../../../components/video-play.vue';

export default defineComponent({
    components: {
        VideoPlay,
    },
    props: {
        lotteryType: {
            type: Number,
            default: 0,
        },
        headerVideo: {
            type: Object,
            default: () => ({}),
        },
    },
    setup() {
        const kvPlayer = ref();
        const mainStore = useMain();
        const boxStore = useBoxInfo();
        const isPlaying = ref(false);

        const play = () => {
            if (!mainStore.isLowDev) {
                console.log('drawVideo play');
                kvPlayer.value?.handlePlay();
                isPlaying.value = true;
            }
        };

        const stop = () => {
            console.log('drawVideo stop');
            !mainStore.isLowDev && kvPlayer.value?.handlePause();
            isPlaying.value = false;

            if (kvPlayer.value?.videoInstance) {
                kvPlayer.value.videoInstance.currentTime = 0;
            }
        };

        watch(
            () => boxStore.isRotating,
            (val) => {
                val ? play() : stop();
            },
        );
        mainStore.busEvent.on(DRAW_EVENT.animEndDelay, stop);

        onUnmounted(() => {
            mainStore.busEvent.off(DRAW_EVENT.animEndDelay, stop);
        });

        return {
            kvPlayer,
            isPlaying,
        };
    },
});
</script>

<style lang="less" scoped>
.page-header-component {
    position: relative;
    width: 414px;
    height: var(--kvBgHeight);
    .video-img {
        width: 100%;
        height: var(--kvBgHeight);
        /deep/ .video-play {
            height: var(--kvBgHeight);
            padding: 0;
        }
    }
    .playing {
        position: absolute;
        top: 0;
        left: 0;
    }
}
</style>
