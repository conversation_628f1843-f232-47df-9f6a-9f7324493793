/* eslint-disable @typescript-eslint/no-explicit-any */
// import type { ExtractPropTypes, PropType } from 'vue';
import { ref, onBeforeUnmount, onMounted } from 'vue';
import { Report, useVisibility } from '@alive-ui/actions';

const getAudioBufferAsync = async (
    source: string,
    audioContext: AudioContext,
): Promise<AudioBuffer | null> => {
    const request = new XMLHttpRequest();

    try {
        request.open('GET', source, true);
        request.responseType = 'arraybuffer';
    } catch (error) {
        console.log(error);
    }

    return new Promise((resolve) => {
        request.onload = () => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            audioContext.decodeAudioData(request.response, (buffer) => {
                console.log('buffer==value', buffer);
                console.log('buffer===type', typeof buffer);
                resolve(buffer);
            });
        };
        request.send();
    });
};

// 经验值 - 后续看情况是否需要区分设备处理
const audioThreshold = 0.24; // audio标签循环播放超前阙值
const audioContextThreshold = 0.1; // audioContext循环播放超前阙值

const playFunc = (strategy: string, audioFile: any) => {
    if (strategy === 'mute') {
        return;
    }

    if (
        audioFile &&
        audioFile.currentTime > audioFile.duration - audioThreshold
    ) {
        audioFile.currentTime = 0;
    }

    audioFile?.play().catch((err: any) => {
        console.error(err);
    });
};

export const useAudioContext = ({
    source = '',
    autoPlay = false,
    loop = false,
    strategy = 'audio',
}) => {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    const audioFile = strategy === 'audio' ? new Audio(source) : null;
    let audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
    const currentBufferSource = ref<AudioBufferSourceNode | null>(null);
    const destroyed = ref(false);
    const buffer = ref<AudioBuffer | null>(null);

    const resetContext = () => {
        console.log('resetContext');
        audioContext?.close(); // 关闭旧的，重新创建
        audioContext = new (window.AudioContext ||
            (window as any).webkitAudioContext)();
    };

    const pause = () => {
        if (!audioContext) {
            audioFile?.pause?.();
            return;
        }
        if (currentBufferSource.value) {
            currentBufferSource.value.stop();
            currentBufferSource.value = null;
        }
    };

    const play = async () => {
        if (!audioContext) {
            playFunc(strategy, audioFile);

            return;
        }

        try {
            if (!buffer.value && source) {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                buffer.value = await getAudioBufferAsync(source, audioContext);
            }
            if (currentBufferSource.value) {
                currentBufferSource.value?.stop();
                currentBufferSource.value = null;
            }
            currentBufferSource.value = audioContext?.createBufferSource();
            currentBufferSource.value.buffer = buffer.value;
            currentBufferSource.value?.connect(audioContext?.destination);
            // audioContext循环播放无缝衔接， loop设置顺序需要在start后
            // AudioBufferSourceNode.start([when][, offset][, duration]);
            // when: 可选The time声音开始播放的时间，0表示立即被播放
            // offset 可选：表示偏移
            // duration 可选将要播放的声音的持续时间
            // add 50ms latency to work well across systems - tune this if you like
            currentBufferSource.value?.start(
                audioContext?.currentTime,
                0.05,
                (buffer.value?.duration || 0) -
                    (loop ? audioContextThreshold : 0),
            );
            currentBufferSource.value.loop = loop; // 必须放在后面
            audioContext?.resume().catch(console.error);

            // 修复音频被卸载后destroyAudio先执行, bufferSource还未被清除又开始下一轮的connect/start
            if (destroyed.value) {
                pause();
            }
        } catch (error) {
            console.log(error);

            Report.biz.error('【扭蛋机】：初始化音频失败', {
                source,
                audioContext,
                error,
            });
        }
    };

    const destroyAudio = () => {
        // 内核bug https://docs.corp.kuaishou.com/d/home/<USER>
        destroyed.value = true;

        // 清空资源，防止泄露
        if (currentBufferSource.value) {
            audioContext?.close(); // audioContext.suspend();
            currentBufferSource.value?.stop();
            currentBufferSource.value = null;
        } else if (audioFile) {
            audioFile.pause();
            audioFile.src = '';
            audioFile.load();
        }
    };

    onMounted(() => {
        if (autoPlay) {
            play();
        }
    });

    onBeforeUnmount(() => {
        destroyAudio();
    });

    useVisibility({
        visibleHandler: () => {
            resetContext();
            autoPlay && play();
        },
        hiddenHandler: () => pause(),
    });

    return {
        audioContext,
        resetContext,
        play,
        pause,
    };
};
