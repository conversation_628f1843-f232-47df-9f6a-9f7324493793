<template>
    <TaskArea :need-coin="needCoin" :need-daily-task="needDailyTask" />
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import TaskArea from './task-area-origin.vue';

export default defineComponent({
    components: {
        TaskArea,
    },
    props: {
        needCoin: {
            type: Boolean,
            default: true,
        },
        needDailyTask: {
            type: Boolean,
            default: true,
        },
    },
    setup() {
        return {};
    },
});
</script>

<style lang="less" scoped>
.my-coin {
    padding: 0 24px;
    margin: 10px auto;
}
</style>
