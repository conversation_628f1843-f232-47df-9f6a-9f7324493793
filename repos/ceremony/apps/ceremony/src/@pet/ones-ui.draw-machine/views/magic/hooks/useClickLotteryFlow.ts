/* eslint-disable @typescript-eslint/no-explicit-any */
import { ref } from 'vue';
import { storeToRefs, defineStore } from 'pinia';
import { Toast } from '@lux/sharp-ui-next';
import { bolIsAuthor } from '@alive-ui/actions';
import { giftsNum, DRAW_EVENT, BTN_STATUS, DrawResult } from '../config';
import { COMMON_EVENT, useMain } from '../../../store/useMain';
import { drawBtnClick } from '../../../logger';
import { drawReward } from '../../../api';
import useBoxInfo from './useBoxInfo';
import { useBoxCircleAni } from './useBoxCircleAni';
import type { AwardItem } from '../../../api';

// eslint-disable-next-line max-lines-per-function
export const useClickLotteryFlow = defineStore('lottery-box-draw', () => {
    const boxInfo = useBoxInfo();
    const { balance, btnList, giftsList, isRotating, btnStatus, lotteryType } =
        storeToRefs(boxInfo);
    const { startRotate, startRotateComb } = useBoxCircleAni();
    const mainStore = useMain();
    const currentId = ref(1);
    const subTitle = ref('');
    const resultList = ref([] as AwardItem[]);
    const needFillAddress = ref(false);
    const needJumpBackPack = ref(false);
    const needJumpWallet = ref(false);
    const needSendEmoticon = ref(false);
    const enableShowEmoticonTab = ref(false);

    const checkBalance = () => {
        const needCount =
            btnList.value?.find((btn) => btn.ruleId === currentId.value)
                ?.coinCount || Infinity;
        console.log(balance.value, needCount);

        if (balance.value < needCount) {
            // emit('not-enough');
            mainStore.busEvent.emit(COMMON_EVENT.noEnoughBalance);

            return false;
        }

        return true;
    };

    const getResult = async () => {
        try {
            const res = await drawReward(currentId.value);
            subTitle.value = res?.subTitle || '';
            resultList.value = res?.awards;

            if (res?.toast) {
                Toast.info(res.toast);

                return DrawResult.offseason;
            }

            if (!resultList.value?.length) {
                // 没有返回奖励数据，则未中奖
                return DrawResult.empty;
            }
            needFillAddress.value = res?.needFillAddress;
            needJumpBackPack.value = res?.needJumpBackPack;
            needJumpWallet.value = res?.needJumpWallet;
            enableShowEmoticonTab.value = res?.enableShowEmoticonTab;
            needSendEmoticon.value = res?.needSendEmoticon;

            return DrawResult.success;
        } catch (err: any) {
            const { error_msg, result } = err?.data || {};

            if (result === 81381) {
                // 表示余额不足，需要自动定位到任务
                // emit('not-enough');
                mainStore.busEvent.emit(COMMON_EVENT.noEnoughBalance);
            }
            error_msg && Toast.info(error_msg);

            return DrawResult.error;
        }
    };

    const findResPos = () => {
        const resId = resultList.value[0].displayId || resultList.value[0].id;

        return giftsList.value.findIndex((item) => item.id === resId);
    };

    const sigleAnim = () => {
        const pos = findResPos();
        console.log(`抽中第${pos + 1}号奖品`);

        // 执行动画，动画结束后emit展示结果事件
        if (pos >= 0) {
            startRotate(pos);
        } else {
            mainStore.busEvent.emit(DRAW_EVENT.showResultPop, {
                isEmpty: false,
            });
        }
    };

    const CombAnim = () => {
        // 拉平打散10个礼物
        // const combResult = genShuffledResult(resultList.value);
        // 不管个数，展示前五种
        const combResult = resultList.value.slice(0, 5);
        let isOutOfList = false;
        const combPosList = combResult.map((result: AwardItem, i: number) => {
            const resId = result.displayId || result.id;
            const itemIndex = giftsList.value.findIndex(
                (item) => item.id === resId,
            );

            if (itemIndex < 0) {
                isOutOfList = true;
            }

            return itemIndex + giftsNum * i;
        });
        console.log('isOutOfList', isOutOfList, combResult, combPosList);

        if (isOutOfList) {
            mainStore.busEvent.emit(DRAW_EVENT.showResultPop, {
                isEmpty: false,
            });
        } else {
            startRotateComb(combPosList);
        }
    };

    const drawAward = async (ruleId: number, btnIndex: number) => {
        // 主播端不可以抽奖
        if (bolIsAuthor) {
            Toast.info('开播中，不可参与哦～');

            return;
        }
        // 检查抽奖中
        if (btnStatus.value !== BTN_STATUS.init || isRotating.value) {
            Toast.info('正在抽奖中');

            return;
        }
        // 检查余额
        currentId.value = ruleId;

        if (!checkBalance()) {
            return;
        }
        // 请求抽奖接口
        isRotating.value = true;
        btnStatus.value = BTN_STATUS.lucking;
        drawBtnClick(btnIndex, lotteryType.value);
        const result = await getResult();

        switch (result) {
            case DrawResult.success:
                // 抽奖成功，定位抽到的🎁位置。
                switch (ruleId) {
                    /**
                     * 1、3单抽
                     * 2、4连抽
                     */
                    case 1:
                    case 3:
                        sigleAnim();
                        break;
                    case 2:
                    case 4:
                        CombAnim();
                        break;
                    default:
                        break;
                }
                break;
            case DrawResult.empty:
                console.log('恭喜未中奖');
                isRotating.value = false;
                mainStore.busEvent.emit(DRAW_EVENT.showResultPop, {
                    isEmpty: true,
                });
                break;
            default:
                isRotating.value = false;
                break;
        }

        btnStatus.value = BTN_STATUS.init;
    };

    return {
        currentId,
        needFillAddress,
        needJumpBackPack,
        needJumpWallet,
        needSendEmoticon,
        enableShowEmoticonTab,
        subTitle,
        resultList,
        drawAward,
    };
});

export default useClickLotteryFlow;
