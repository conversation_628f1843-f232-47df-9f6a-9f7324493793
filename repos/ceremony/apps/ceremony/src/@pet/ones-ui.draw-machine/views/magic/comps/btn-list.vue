<template>
    <div class="flex draw-button position-absolute">
        <div
            v-for="(item, index) in btnList"
            :key="index"
            v-pcDirectives:intercept
            class="draw-btn-item"
            :class="[
                `gis-luck-task-${+(index - 1 + lotteryType * 2)}`,
                {
                    'press-animation':
                        showPress && item.ruleId === drawStore.currentId,
                },
            ]"
            @click="drawStore.drawAward(item.ruleId, index)"
        >
            <div v-show-log="drawBtnLog(index, lotteryType)" class="draw-ten" />
            <div class="sub-text">
                <span class="inline-block scale-font83" />
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useClickLotteryFlow } from '../hooks/useClickLotteryFlow';
import { useBoxInfo } from '../hooks/useBoxInfo';
import { BTN_STATUS } from '../config';
import { drawBtnLog } from '../../../logger';
import type { PropType } from 'vue';
import type { BtnRule } from '../../../api';

export default defineComponent({
    props: {
        btnList: {
            type: Array as PropType<BtnRule[]>,
            default: () => [],
        },
    },
    setup() {
        const drawStore = useClickLotteryFlow();
        const { btnStatus, lotteryType } = storeToRefs(useBoxInfo());
        const showPress = ref(false);

        watch(
            () => btnStatus.value,
            () => {
                if (btnStatus.value === BTN_STATUS.lucking) {
                    showPress.value = true;

                    setTimeout(() => {
                        showPress.value = false;
                    }, 600);
                }
            },
        );

        return {
            drawStore,
            showPress,
            lotteryType,
            drawBtnLog,
        };
    },
});
</script>

<style lang="less" scoped>
.draw-button {
    .draw-btn-item:first-child {
        margin-right: 16px;
    }
    // .luck-btn-1{
    //     width: 127px;
    //     height: 61px;
    //     margin-right: 16px;
    //     background: url('../../../variable/assets/luck-task-1_2x.png') center / 100% no-repeat
    // }
    // .luck-btn-2{
    //     width: 127px;
    //     height: 61px;
    //     background: url('../../../variable/assets/luck-task-2_2x.png') center / 100% no-repeat
    // }
}

@keyframes pressY {
    0% {
        transform: translateY(0);
    }
    30% {
        transform: translateY(5px);
    }
    100% {
        transform: translateY(0);
    }
}
.press-animation {
    animation: pressY 500ms ease-in-out forwards;
    animation: pressY 500ms ease-in-out forwards;
}
</style>
