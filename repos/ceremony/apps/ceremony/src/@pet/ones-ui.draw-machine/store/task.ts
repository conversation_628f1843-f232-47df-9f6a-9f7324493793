/* eslint-disable @typescript-eslint/no-unused-vars */
import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import { debounce } from 'lodash-es';
import { toShare } from '@pet/ones-use.share';
import { Toast } from '@lux/sharp-ui-next';
import {
    useVisibility,
    bolIsAuthor,
    dispatchLiveRouter,
    activityBiz,
    liveStreamId,
    appendParam,
    startNeoAdVideo,
    exitWebView,
    invoke,
    loadUrlOnNewPage,
} from '@alive-ui/actions';
import { sendClick, taskBtnLog } from '../logger/index';
import {
    postLotteryTask,
    queryRecoAuthor,
    dailyLogin,
    getAdInspireScheme,
    receiveTask,
    searchKeyWord,
} from '../api/index';
import { useMain, COMMON_EVENT } from './useMain';
import type { TaskItem } from '../api/index';

const RecieveTasks = ['joinFansGroup', 'sendPopularityGift', 'sendGift'];

// eslint-disable-next-line max-lines-per-function
export default defineStore('lottery-task', () => {
    const mainStore = useMain();
    const pageStatus = reactive({
        loading: true,
        error: false,
    });
    const taskList = ref([] as TaskItem[]);
    const isRefetch = ref(false);
    //   const appInfo = ref({
    //     appType: '',
    //     appVer: '',
    //     clientId: -1,
    //   });

    const queryTaskInfo = async () => {
        if (!isRefetch.value) {
            pageStatus.loading = true;
        }

        try {
            const res = await postLotteryTask();
            pageStatus.error = false;
            const data = res;
            mainStore.busEvent.emit(COMMON_EVENT.updateBallance, data.balance);
            taskList.value = data.taskItemViewList || [];
        } catch (error) {
            console.error(' error:', error);
            pageStatus.error = true;
        }
        pageStatus.loading = false;
    };

    const refetchTask = () => {
        isRefetch.value = true;
        queryTaskInfo();
    };

    const curTaskList = computed(() => {
        return taskList.value.filter((task) => {
            return liveStreamId ? true : task.typeKey !== 'h5Follow';
        });
    });

    const queryRecoAuthorInfo = async (typeKey: string) => {
        try {
            const res = await queryRecoAuthor(typeKey);

            return res?.recoAuthorList || [];
        } catch (error) {
            console.log('error', error);
        }

        return [];
    };

    const jump2room = async (item: TaskItem, kwaiLink = '') => {
        const list = await queryRecoAuthorInfo(item.typeKey);
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const lsIds = list?.map((item: any) => item.liveStreamId).join(',');

        if (!lsIds) {
            return;
        }

        location.href = appendParam('kwai://liveaggregatesquare', {
            sourceType: 269,
            liveStreamId: lsIds,
            exp_tag: (window as any).GLOBAL_EXPTAG || activityBiz,
            liveSquareSource: 10012,
            path: '/rest/n/live/feed/common/slide/more',
            internaljump: kwaiLink,
        });
    };

    // 页面visiable时刷新余额
    const watchUpdate = () => {
        useVisibility({
            visibleHandler: debounce(() => {
                refetchTask();
            }, 100),
        });
    };

    // 每日登录任务
    const initLogin = async () => {
        try {
            console.log('dailyLogin');
            await dailyLogin();
        } catch (err) {
            console.error('lucky machine login-task', err);
        }
    };

    const searchKeyWordCallBack = async (keyWord: string) => {
        try {
            await searchKeyWord(keyWord);
        } catch (err) {
            console.error('lucky machine search-task', err);
        }
    };

    const followModal = ref(false);
    const reservationModal = ref(false);

    const closeModal = () => {
        followModal.value = false;
        reservationModal.value = false;
    };

    const taskListFunc = async (item: any) => {
        const { status, kwaiLink } = item;

        switch (item.typeKey) {
            case 'sharePoster': //  分享
                await toShare({ onShareCompleted: refetchTask });
                return;
            case 'watchVideo':
                if (!item.expandMap.photoId || !item.expandMap.watchDuration) {
                    Toast.error('库存暂时不足，请稍后再试');

                    return;
                }
                // 以新打开webview形式跳转到视频观看页
                // 当前页面所有参数加上photoId & watchDuration
                const search = location.search.startsWith('?')
                    ? location.search
                    : `?${location.search}`;
                // eslint-disable-next-line max-len
                //  观看视频任务链接
                const url = `${location.origin}/live/act/common-business/video-play${search}&layoutType=4&photoId=${item.expandMap.photoId}&watchDuration=${item.expandMap.watchDuration}`;

                loadUrlOnNewPage({
                    url,
                    type: 'back',
                    ignoreHalfScreenDisplay: 1, // 顶部状态栏，仅ios使用
                });
                return;
            case 'h5Follow': // 关注任务的逻辑不一样,是打开弹窗
                followModal.value = true;

                return;
            case 'watchLive': // 跳转推荐直播间
                return jump2room(item);

            case 'searchKeyWord': // 跳搜索推荐页
                try {
                    await searchKeyWordCallBack(item.expandMap.keyWord);
                    location.href = item.kwaiLink;
                } catch (error) {
                    console.error('跳搜索推荐页', error);
                }

                return;
            case 'reservation': // 预约任务
                console.log('预约任务');
                reservationModal.value = true;

                return;
            case 'adInspire': // 激励任务
                console.log('激励任务');

                try {
                    const adInspireRes = await getAdInspireScheme();
                    console.log('AdInspire', adInspireRes);

                    if (adInspireRes?.scheme) {
                        const adRes = await startNeoAdVideo(
                            adInspireRes.scheme,
                        );
                        console.log(adRes);
                        setTimeout(exitWebView, 500);
                    } else {
                        Toast.info('获取激励任务失败');
                    }
                } catch (error) {
                    console.error('激励任务失败', error);
                }

                return;
            case 'smallBell': // 小铃铛下载
                invoke('platform.showToast', {
                    type: 'normal',
                    text: '小铃铛在直播间右下角哦～',
                    isAddToWindow: false,
                });
                exitWebView();
                break;
            default:
                break;
        }

        if (item.typeKey !== 'sendGift' && status === 1 && kwaiLink) {
            // 未做任务
            if (liveStreamId) {
                dispatchLiveRouter({
                    path: kwaiLink, // kwaiLink,
                    keepDisplayWebView: false,
                }).catch(console.error);
            } else {
                if (item.typeKey === 'grabRedPack') {
                    // 抢红包本身是跳转直播间，不需要特殊处理
                    location.href = kwaiLink;

                    return;
                }
                jump2room(item, kwaiLink);
            }
        }
    };
    const buttonAction = debounce(async (item: TaskItem) => {
        if (item.status === 2) {
            return;
        }
        const { typeKey, status, kwaiLink } = item;
        const videoId = item.expandMap?.photoId || '';
        // status 1-未完成，2-已完成
        sendClick(taskBtnLog(item.buttonText, typeKey, videoId));

        if (bolIsAuthor) {
            Toast.info('开播中，不可参与哦～');

            return;
        }
        mainStore.busEvent.emit(COMMON_EVENT.taskBtnClick, {
            item,
            refresh: refetchTask,
        });

        // 所有任务均增加领取接口，后端看情况是否使用。
        try {
            await receiveTask(typeKey);

            if (
                item.typeKey === 'sendGift' &&
                status === 1 &&
                kwaiLink &&
                liveStreamId
            ) {
                // 未做任务
                dispatchLiveRouter({
                    path: kwaiLink, // kwaiLink,
                    keepDisplayWebView: false,
                }).catch(console.error);
            }
        } catch {
            if (RecieveTasks.includes(item.typeKey) || item.receiveTask) {
                Toast.error('网络异常，请稍后再试～');

                return;
            }
            console.log('receiveTask error');
        }

        taskListFunc(item);
    }, 500);

    return {
        taskList: curTaskList,
        pageStatus,
        isRefetch,
        followModal,
        reservationModal,
        queryTaskInfo,
        initLogin,
        refetchTask,
        queryRecoAuthorInfo,
        watchUpdate,
        buttonAction,
        closeModal,
        // getAppInfo,
    };
});
