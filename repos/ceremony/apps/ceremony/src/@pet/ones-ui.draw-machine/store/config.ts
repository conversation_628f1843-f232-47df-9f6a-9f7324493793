import { defineStore } from 'pinia';

export const useConfigStore = defineStore('lottery-config', {
    state: () => {
        return {
            dataConfig: {} as any,
            visitImg: localStorage.getItem('LOTTERY_VISIT'),
        };
    },
    getters: {
        logConf(state) {
            return state.dataConfig?.trackerConfig || {};
        },
        lotteryOnceImg(state) {
            return state.dataConfig?.lotteryOnceImg ?? '';
        },
        isOnceImgSame(state) {
            return state.visitImg === state.dataConfig?.lotteryOnceImg;
        },
        /**
         * 首次进入页面的弹窗：
         * 开启这个配置 && 首次进入
         */
        showOnceModal(state) {
            return (
                state.dataConfig?.onceModal &&
                state.visitImg !== state.dataConfig?.lotteryOnceImg
            );
        },
    },
    actions: {
        updateConfig(config: Record<string, string>) {
            this.dataConfig = config;
        },
        setVisitImg() {
            this.visitImg = localStorage.getItem('LOTTERY_VISIT');
        },
        switchVisit() {
            localStorage.setItem('LOTTERY_VISIT', this.lotteryOnceImg);
        },
    },
});

export default useConfigStore;
