import { ref } from 'vue';
import { defineStore } from 'pinia';
import { activityBiz, request } from '@alive-ui/actions';

export const getBalance = async (biz = activityBiz) => {
    const res = await request.post<{ balance: number }>(
        '/rest/wd/live/plutus/virtualWallet/queryAccountBalance',
        {
            biz,
        },
    );

    return res.data;
};

export default defineStore('update-ballance', () => {
    const balance = ref(0);
    const updateBalance = async (val?: number, biz = activityBiz) => {
        try {
            if (typeof val === 'number') {
                balance.value = val;
            } else {
                const res = await getBalance(biz);
                balance.value = res?.balance || 0;
            }
        } catch (error) {
            console.error('刷新余额失败');
        }
    };
    return {
        balance,
        updateBalance,
    };
});
