<script setup lang="ts">
import { ref } from 'vue';
import { storeToRefs } from 'pinia';
import {
    ATabs,
    ATabList,
    ATabPanel,
    ATabPanels,
    ATab,
    ACard,
    ACardContent,
    ACardTitle,
} from '@alive-ui/base';

const props = withDefaults(
    defineProps<{
        title: string;
        imgUrl: string;
    }>(),
    {
        title: '盛典玩法大升级',
        imgUrl: '',
    },
);
</script>

<template>
    <div>
        <ACard>
            <ACardTitle class="title"> {{ title }} </ACardTitle>
            <ACardContent class="mt-2">
                <img class="preview-bottom-img" :src="imgUrl" />
            </ACardContent>
        </ACard>
    </div>
</template>

<style scoped>
.prize-img {
    width: 100%;
}
.preview-bottom-img {
    margin: 0 16px;
    width: 350px;
    height: auto;
}
</style>
