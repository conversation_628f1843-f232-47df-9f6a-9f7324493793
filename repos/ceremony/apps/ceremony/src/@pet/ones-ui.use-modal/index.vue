<template>
    <div v-if="visible">
        <AModal
            :open="visible"
            modal-classes="buff-popup-modal"
            @cancel="handleClose"
        >
            <AModalTitle>
                <template #default>
                    <span class="a-text-modal-title">
                        {{ info.title }}
                    </span>
                </template>
            </AModalTitle>

            <AModalContent class="modal-content">
                <div class="buff-popup-gift">
                    <img class="img" :src="info.iconUrl" />
                </div>
                <div class="buff-popup-gift-desc">{{ info.desc }}</div>
            </AModalContent>

            <AModalFooter>
                <AButton
                    type="primary"
                    size="lg"
                    class="btn"
                    @click="handleClick"
                >
                    确认使用
                </AButton>
            </AModalFooter>
            <AModalClose pos="bottom"> </AModalClose>
        </AModal>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { Mo<PERSON>, But<PERSON> as AButton } from '@alive-ui/base';
const {
    Modal: AModal,
    ModalClose: AModalClose,
    ModalTitle: AModalTitle,
    ModalContent: AModalContent,
    ModalFooter: AModalFooter,
} = Modal;

defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    info: {
        type: Object,
        default: () => ({
            title: '',
            desc: '',
            iconUrl: '',
        }),
    },
});

const emit = defineEmits(['close', 'ok', 'update:visible']);

const handleClose = () => {
    emit('close');
    emit('update:visible', false);
};

const handleClick = () => {
    emit('ok');
};
</script>

<style lang="less" scoped>
:deep(.buff-popup-modal) {
    padding-top: 100px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    width: 280px;
    height: 470px;
    background: url(./assets/modal-bg.png) center / 100% no-repeat;

    .buff-popup-gift {
        width: 88px;
        height: 88px;
        margin-top: 60px;
        background-position: center center;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        .img {
            width: 88px;
            height: 88px;
        }
    }

    .buff-popup-gift-desc {
        color: #000;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 12px;
        opacity: 0.6;
        margin-top: 12px;
        height: 34px;
        max-width: 216px;
        margin-bottom: 25px;
        line-height: 17px;
    }
    .btn {
        background: url(./assets/btn-bg.png) center / 100% no-repeat;
    }

    .modal-content {
        margin-top: 0;
        flex: none;
    }
}

.gis-icon-close {
    margin-top: 36px;
}
</style>
