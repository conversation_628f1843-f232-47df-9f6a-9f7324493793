import type { BaseItem } from '@pet/ones-rank.schema/index-home';
/**
 * 请求：区域选择器
 */
export interface ScheduleInfoPostRequest {
    // eg: "liveYearCeremony2024"
    activityBiz: string;
    // 加密的直播间id,不能为0，如果为空表示非直播间入口,返回值就没有主播相关数据
    liveStreamId: string;
    // eg: 931
    scheduleType: number;
    // 主赛事=main，B赛事=sub
    subBiz: 'main' | 'sub';
}

/**
 * 响应：区域选择器
 */
export interface ScheduleInfoPostResponse {
    // 主播赛道
    anchorSchedule?: BaseItem;
    // 赛道
    schedules?: BaseItem[];
}

export interface FilteredSchedule extends BaseItem {
    nameIncludesKeyword: boolean;
    keyword: string;
    preText: string;
    postText: string;
}
