import { request, liveStreamId, activityBiz } from '@alive-ui/actions';
import type { ScheduleInfoPostResponse } from '../schema';

// 新版赛道选择器（平铺--24年度盛典） https://mock.corp.kuaishou.com/project/941/interface/api/1226916
export const scheduleInfo = async (scheduleType: number) => {
    const PATH =
        '/webapi/live/revenue/operation/activity/complex/firstLevelScheduleChoose';
    const res = await request.post<ScheduleInfoPostResponse>(PATH, {
        activityBiz,
        liveStreamId,
        scheduleType,
    });

    return res?.data;
};
