import { ref } from 'vue';
import { defineStore } from 'pinia';
import { Report, getPageStatus } from '@alive-ui/actions';
import { scheduleInfo } from '../services';
import type { BaseItem } from '@pet/ones-rank.schema/index-home';
import type { FilteredSchedule, ScheduleInfoPostResponse } from '../schema';
export default defineStore('schedule-info', () => {
    let clickLoadingFlag = false;
    const anchorScheduleId = ref(0);
    const schedules = ref<BaseItem[]>([]);
    const filteredSchedules = ref<FilteredSchedule[]>([]);
    const status = ref(getPageStatus('init')); // 页面状态数据定义
    const init = async (
        scheduleType: number,
        data: ScheduleInfoPostResponse | null,
    ) => {
        try {
            if (clickLoadingFlag) {
                return;
            }
            clickLoadingFlag = true;
            status.value = getPageStatus('loading');
            // 不传data时，发送请求获取数据
            if (!data) {
                const res = await scheduleInfo(scheduleType);
                status.value = getPageStatus(res ? 'success' : 'nodata');
                anchorScheduleId.value = res?.anchorSchedule?.scheduleId || 0;
                schedules.value = res?.schedules || [];
                return;
            }
            // 传data时，使用data作为数据
            status.value = getPageStatus(data ? 'success' : 'nodata');
            anchorScheduleId.value = data?.anchorSchedule?.scheduleId || 0;
            schedules.value = data?.schedules || [];
        } catch (error) {
            status.value = getPageStatus('error');
            console.log('schedule-info error', error);
            Report.biz.error('城市接口失败', {
                error,
            });
        } finally {
            clickLoadingFlag = false;
        }
    };
    const reset = () => {
        filteredSchedules.value = [];
    };
    const search = (keyword: string) => {
        if (!keyword) {
            filteredSchedules.value = [];
            return;
        }
        const k = keyword.trim().toLowerCase();

        filteredSchedules.value = [];
        for (const item of schedules.value) {
            const filteredItem: FilteredSchedule = {
                ...item,
                nameIncludesKeyword: false,
                keyword: '',
                preText: '',
                postText: '',
            };
            if (item.displayName?.includes(k)) {
                filteredItem.nameIncludesKeyword = true;
                filteredItem.keyword = k;
                filteredItem.preText = item.displayName.slice(
                    0,
                    item.displayName.indexOf(k),
                );
                filteredItem.postText = item.displayName.slice(
                    item.displayName.indexOf(k) + k.length,
                );
                filteredSchedules.value.push(filteredItem);
                continue;
            }

            if (item.pinyin?.includes(k)) {
                filteredItem.nameIncludesKeyword = false;
                filteredSchedules.value.push(filteredItem);
            }
        }
    };
    return {
        status,
        anchorScheduleId,
        schedules,
        filteredSchedules,
        init,
        reset,
        search,
    };
});
