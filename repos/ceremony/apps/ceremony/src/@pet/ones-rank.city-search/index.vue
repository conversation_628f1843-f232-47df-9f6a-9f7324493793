<script lang="ts">
export interface CitySearchRef {
    // eslint-disable-next-line @typescript-eslint/ban-types
    open: Function;
}
</script>

<script lang="ts" setup>
import { ref, watch, type PropType, onBeforeUnmount } from 'vue';
import { storeToRefs } from 'pinia';
import { debounce } from 'lodash-es';
import { useVirtualList } from '@vueuse/core';
import { Popup } from '@lux/sharp-ui-next';
import { ElseStatus } from '@alive-ui/base';
import { stopMove } from '@alive-ui/actions';
import { type ScheduleInfoPostResponse } from './schema';
import useScheduleStore from './models';
import { dataFun } from './data';
import type { BaseItem } from '@pet/ones-rank.schema/index-home';
import type { StopFunItem } from '@alive-ui/actions';
const props = defineProps({
    // provide name
    contextName: {
        type: Symbol,
        default: Symbol.for('ctx'),
    },
    scheduleType: {
        type: Number,
        default: 0,
        required: true,
    },
    data: {
        type: Object as PropType<ScheduleInfoPostResponse | null>,
        default: null,
    },
});
const cityData = dataFun(props.contextName);
const emit = defineEmits(['change']);
const isShowPop = ref(false);

// 避免弹窗滑动透传
let stopMoveObj: StopFunItem;
watch(
    () => isShowPop.value,
    (o) => {
        if (o) {
            stopMoveObj = stopMove();
        } else {
            stopMoveObj?.cancelMove?.();
        }
    },
    { immediate: true },
);
onBeforeUnmount(() => {
    stopMoveObj?.cancelMove?.();
});
const indexMode = ref(true);
const keyword = ref('');

const welcomeTip = '支持汉字、首字母、拼音模糊搜索';
const noDataTip = '无相关搜索结果';
const tip = ref(welcomeTip);
const scheduleStore = useScheduleStore();
const { anchorScheduleId, schedules, filteredSchedules, status } =
    storeToRefs(scheduleStore);

const { list, containerProps, wrapperProps } = useVirtualList(schedules, {
    itemHeight: 38,
    overscan: 10,
});

const {
    list: filterList,
    containerProps: filterContainerProps,
    wrapperProps: filterWrapperProps,
} = useVirtualList(filteredSchedules, {
    itemHeight: 38,
    overscan: 10,
});
const open = () => {
    scheduleStore.init(props.scheduleType, props.data);
    // 保证打开弹窗时展示主页面
    reset();
    isShowPop.value = true;
};

const reset = () => {
    keyword.value = '';
    tip.value = welcomeTip;
    scheduleStore.reset();
    indexMode.value = true;
    isShowPop.value = false;
};

const handleChangeCity = (item: BaseItem) => {
    emit('change', item);
    reset();
};

const handleClickSearchIcon = () => {
    indexMode.value = false;
};

const handleClickBackIcon = () => {
    scheduleStore.reset();
    keyword.value = '';
    tip.value = welcomeTip;
    indexMode.value = true;
};

const handleClickSearchText = () => {
    if (keyword.value.trim() === '') {
        filteredSchedules.value = [];
        tip.value = welcomeTip;
        return;
    }
    // 删除汉字拼字阶段拼音中会出现的分隔符，ios：空白字符，安卓：单引号
    let k = keyword.value.replace(/'/g, '');
    k = k.replace(/\s+/g, '');
    scheduleStore.search(k);
    if (!filteredSchedules.value.length) {
        tip.value = noDataTip;
    }
};

const debouceSearch = debounce(handleClickSearchText, 500);

const handleCompositionUpdate = (e: CompositionEvent) => {
    if (e?.target) {
        keyword.value = e.data;
        debouceSearch();
    }
};

defineExpose({
    open,
});
</script>

<template>
    <Popup
        v-if="isShowPop"
        ref="popupRef"
        v-model="isShowPop"
        position="bottom"
        :transfer-dom="true"
        @hide="reset"
    >
        <div class="card-top-bg" />
        <div class="city-search-popup">
            <div v-if="indexMode" class="city-search">
                <div class="index-header">
                    <div class="a-text-title title">请选择赛道</div>
                    <div
                        v-clickLog="{
                            action: 'OP_ACTIVITY_SELECT_RACETRACK',
                            params: {
                                btn_type: '2',
                            },
                        }"
                        v-showLog="{
                            action: 'OP_ACTIVITY_SELECT_RACETRACK',
                            params: {
                                btn_type: '2',
                            },
                        }"
                        class="search-icon"
                        @click="handleClickSearchIcon"
                    ></div>
                </div>
                <template v-if="status.success">
                    <div class="list a-text-main" v-bind="containerProps">
                        <div v-bind="wrapperProps">
                            <div
                                v-for="{ index, data } in list"
                                :key="index"
                                class="w-full flex justify-between"
                                :style="{ height: '38px' }"
                                @click="handleChangeCity(data)"
                            >
                                <span
                                    class="city-name"
                                    :class="{
                                        'font-semibold':
                                            anchorScheduleId ===
                                            data.scheduleId,
                                    }"
                                    >{{ data.displayName }}</span
                                >
                                <div
                                    v-if="
                                        cityData.headUrl &&
                                        anchorScheduleId === data.scheduleId
                                    "
                                    class="flex font-semibold"
                                >
                                    <div>当前主播</div>
                                    <img
                                        class="avatar"
                                        :src="cityData.headUrl"
                                    />
                                    <div>所在赛道</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <ElseStatus
                    v-else
                    class="else-status"
                    :page-status="status"
                    :is-show-refresh="false"
                    :no-data="false"
                />
            </div>
            <div v-else>
                <div class="search-header flex justify-between">
                    <div class="back-icon" @click="handleClickBackIcon"></div>
                    <form @submit.prevent="debouceSearch">
                        <input
                            v-model="keyword"
                            type="text"
                            class="input a-text-main"
                            placeholder="请输入想查看的地区"
                            @input="debouceSearch"
                            @compositionstart="handleCompositionUpdate"
                            @compositionupdate="handleCompositionUpdate"
                        />
                    </form>
                    <div
                        v-clickLog="{
                            action: 'OP_ACTIVITY_SELECT_RACETRACK',
                            params: {
                                btn_type: '1',
                            },
                        }"
                        v-showLog="{
                            action: 'OP_ACTIVITY_SELECT_RACETRACK',
                            params: {
                                btn_type: '1',
                            },
                        }"
                        class="search-text a-text-main"
                        @click="debouceSearch"
                    >
                        搜索
                    </div>
                </div>
                <template v-if="filteredSchedules?.length > 0">
                    <div class="list a-text-main" v-bind="filterContainerProps">
                        <div v-bind="filterWrapperProps">
                            <div
                                v-for="{ index, data } in filterList"
                                :key="index"
                                v-clickLog="{
                                    action: 'OP_ACTIVITY_SELECT_RACETRACK',
                                    params: {
                                        btn_type: '3',
                                    },
                                }"
                                v-showLog="{
                                    action: 'OP_ACTIVITY_SELECT_RACETRACK',
                                    params: {
                                        btn_type: '3',
                                    },
                                }"
                                class="w-full flex justify-between"
                                :style="{ height: '38px' }"
                                @click="handleChangeCity(data)"
                            >
                                <div v-if="data.nameIncludesKeyword">
                                    <span>{{ data.preText }}</span>
                                    <span class="font-semibold">
                                        {{ data.keyword }}
                                    </span>
                                    <span>{{ data.postText }}</span>
                                </div>
                                <div v-else>
                                    <span>{{ data.displayName }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-else>
                    <div class="tip a-text-main flex-center">
                        {{ tip }}
                    </div>
                </template>
            </div>
        </div>
    </Popup>
</template>

<style lang="less" scoped>
.card-top-bg {
    position: absolute;
    top: 0;
    width: 100%;
    height: 175px;
    margin: 0 auto;
    pointer-events: none;
    background: url('./assets/card-top-bg_2x.png') top / 100% no-repeat;
}
.city-search-popup {
    box-sizing: border-box;
    height: 544px;
    overflow: hidden;
    padding: 20px 17px;
    border-top-right-radius: 16px;
    border-top-left-radius: 16px;
    @apply a-bg-page;
}
.index-header {
    margin-bottom: 26px;
}

.title {
    position: relative;
    font-family: HYYakuHei;
    font-size: 20px;
    line-height: 28px;
    text-align: center;
    vertical-align: middle;
    width: fit-content;
    margin: 0 auto;
}

.search-icon {
    position: absolute;
    top: 17px;
    right: 17px;
    width: 69px;
    height: 32px;
    background: url('./assets/search-icon_2x.png') center / cover no-repeat;
}

.list {
    position: relative;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    line-height: 18px;
    height: 438px;
    z-index: 1;
    overflow-y: scroll;

    &::-webkit-scrollbar {
        display: none;
    }
}
.city-name {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 12em;
}
.avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin: 0 4px;
    &.avatar-default-border {
        border: none;
    }
}

.search-header {
    position: relative;
    margin-bottom: 26px;
}

.back-icon {
    position: relative;
    top: 0;
    left: 0;
    width: 32px;
    height: 32px;
    background: url('./assets/back-icon_2x.png') center / cover no-repeat;
}

.input {
    position: relative;
    width: 276px;
    height: 33px;
    padding: 0 20px;
    font-size: 15px;
    line-height: 21px;
    background: url('./assets/input-bg_2x.png') center / cover no-repeat;
    border: none;
    outline: none;
    &::placeholder {
        color: #ffdfbf;
        opacity: 0.8;
    }
}

.search-text {
    position: relative;
    top: 6px;
    right: 0;
    font-size: 16px;
    font-weight: 600;
    line-height: 22px;
}

.tip {
    position: relative;
    top: 36px;
    font-size: 16px;
    font-weight: 600;
    line-height: 45px;
}

.else-status {
    position: relative;
}
</style>
