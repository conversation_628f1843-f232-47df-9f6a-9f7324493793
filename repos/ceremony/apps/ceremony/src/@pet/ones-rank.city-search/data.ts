import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { RankFrameworkPostResponse } from '@pet/ones-rank.schema/index-home';
export const dataFun = (contextName: symbol) => {
    const homeData = contextName
        ? inject<Ref<RankFrameworkPostResponse>>(contextName)
        : null;
    const data = computed(() => {
        return {
            headUrl: homeData?.value?.currAuthorInfo?.headUrl,
        };
    });
    return data;
};
