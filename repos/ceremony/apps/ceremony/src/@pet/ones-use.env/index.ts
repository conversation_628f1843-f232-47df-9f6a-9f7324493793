// 通过域名判断是否是线上环境 正则匹配
export const isProd = () => {
    return /alive\.kuaishou\.com$/.test(window.location.hostname);
};
// 判断是否是测试环境
export const isStaging = () => {
    // ceremony-kfx.kproxy.corp.kuaishou.com
    return /kproxy\.corp\.kuaishou\.com$/.test(window.location.hostname);
};

//  是否是开发环境
export const isDev = () => {
    return import.meta.env.DEV;
};

// 获取各个环境的 host
export const getHost = (
    port = '5173',
    stg = 'https://annual202409-kfx.kproxy.corp.kuaishou.com',
    prod = 'https://alive.kuaishou.com',
) => {
    if (isDev()) {
        return `http://localhost:${port}`;
    }
    if (isStaging()) {
        return stg;
    }
    return prod;
};
