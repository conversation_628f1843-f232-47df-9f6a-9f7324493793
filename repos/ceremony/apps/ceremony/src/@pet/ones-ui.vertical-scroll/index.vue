<template>
    <div v-if="isIOS()" class="user-card-scroll-ios">
        <slot />
    </div>
    <!-- <div v-else :id="scrollId" class="scroll-wrapper"> -->
    <div v-else :id="scrollId" ref="scrollWrapper" class="scroll-wrapper">
        <div class="scroll-content">
            <slot />
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    defineProps,
    computed,
    onUnmounted,
    ref,
    onUpdated,
    onMounted,
} from 'vue';
import BScroll from '@better-scroll/core';
import { isYodaPCContainer } from '@alive-ui/actions';

const ua = window.navigator.userAgent;

const props = defineProps({
    sId: {
        type: String,
        required: true,
    },
});
const isIOS = (): boolean => {
    return !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
};

const emit = defineEmits(['scrollChange']);
// 使用 ref 来引用 scrollWrapper 元素
const scrollWrapper = ref<HTMLElement | null>(null);
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const bs = ref<any>(null);

const scrollId = computed(() => {
    return `${props.sId}__scroll__wrapper`;
});

const initScroll = () => {
    const wrapper = scrollWrapper.value;

    if (wrapper && !isIOS()) {
        bs.value = new BScroll(wrapper, {
            probeType: 3,
            scrollX: true,
            eventPassthrough: 'vertical',
            freeScroll: true,
        });
        if (isYodaPCContainer) {
            bs.value.on('scroll', (pos: { x: number; y: number }) => {
                emit('scrollChange', pos, {
                    maxScrollX: bs.value.maxScrollX,
                    maxScrollY: bs.value.maxScrollY,
                });
            });
        }
    }
};

onMounted(() => {
    if (!bs.value) {
        initScroll();
    }
});

onUpdated(() => {
    if (!bs.value) {
        initScroll();
    } else {
        bs.value.refresh();
    }
});

onUnmounted(() => {
    if (bs.value) {
        bs.value?.destroy();
    }
});
</script>

<style lang="less" scoped>
.user-card-scroll-ios {
    display: flex;
    overflow-x: scroll;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
        display: none;
        width: 0;
    }
}
.scroll-wrapper {
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
}
.scroll-content {
    position: relative;
    display: flex;
    white-space: nowrap;
}
</style>
