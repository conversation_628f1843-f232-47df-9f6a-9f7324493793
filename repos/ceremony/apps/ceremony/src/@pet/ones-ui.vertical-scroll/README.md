# 垂直滚动组件文档

## 功能概述
垂直滚动组件（`@pet/ones-ui.vertical-scroll`）提供了一个在不同设备上都能良好工作的滚动解决方案。它特别针对iOS设备进行了优化，并支持水平滚动以及滚动事件的监听。

## 属性和方法

### 属性
- **sId** (`String`, 必填)
  - 描述：用于生成滚动容器ID的唯一标识符。
  - 示例：`<VerticalScroll sId=\"uniqueId\" />`

### 方法
- **scrollChange** (`Object`)
  - 描述：当滚动位置发生变化时触发此事件，传递当前滚动位置和最大滚动范围。
  - 参数：
    - `pos` (`{ x: number, y: number }`)：当前滚动位置。
    - `maxScroll` (`{ maxScrollX: number, maxScrollY: number }`)：最大滚动范围。

## 使用示例
```vue
<template>
  <VerticalScroll sId=\"uniqueId\" @scrollChange=\"handleScrollChange\">
    <div class=\"item\">Item 1</div>
    <div class=\"item\">Item 2</div>
    <div class=\"item\">Item 3</div>
  </VerticalScroll>
</template>

<script setup lang=\"ts\">
const handleScrollChange = (pos, maxScroll) => {
  console.log('Current Scroll Position:', pos);
  console.log('Max Scroll Range:', maxScroll);
};
</script>

<style scoped>
.item {
  width: 100px;
  height: 100px;
  background-color: #f0f0f0;
  margin-right: 10px;
}
</style>
```

## 注意事项
- 组件会根据设备类型自动选择合适的滚动方式。对于iOS设备，使用原生滚动；对于其他设备，使用`@better-scroll/core`库。
- 在组件初始化或更新时，会自动调用`initScroll`方法来初始化滚动实例。如果滚动实例已存在，则会调用`refresh`方法来更新滚动内容。
- 组件在卸载时会销毁滚动实例，以避免内存泄漏。

## 依赖项
- `@better-scroll/core`
- `@alive-ui/actions`