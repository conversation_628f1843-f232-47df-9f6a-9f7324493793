<template>
    <div
        v-show="show"
        class="fixed-header a-bg-page"
        :style="{ height: `${headerHeight}px`, opacity }"
    />
    <div
        class="top-header a-bg-page"
        :style="{ height: `${headerHeight}px` }"
    />
</template>

<script lang="ts" setup>
import { onUnmounted, ref, onMounted } from 'vue';
import { invoke } from '@yoda/bridge';

let ticking = false;
let lastScrollPos = 0;
const show = ref(false);
const opacity = ref(0);
const headerHeight = ref(80);

function getHeaderHeight(cb: (footerHeight: number) => void) {
    invoke('webview.getDeviceInfo')
        .then((res) => {
            if (res.data) {
                const dpr = window.devicePixelRatio;
                const { statusBarHeight, titleBarHeight } = res.data;
                // @ts-expect-error
                cb((statusBarHeight + titleBarHeight) / dpr);
            } else {
                cb(80);
            }
        })
        .catch(() => {
            cb(80);
        });
}

let scrollEl: HTMLDivElement | null = null;
onMounted(() => {
    scrollEl = document.querySelector('#app');
});

getHeaderHeight((height) => {
    headerHeight.value = height;
});

const on4TabScroll = () => {
    if (!ticking && window?.requestAnimationFrame) {
        const boundings = scrollEl?.getBoundingClientRect()?.top ?? 0;
        lastScrollPos = boundings === 0 ? boundings : boundings * -1;
        window.requestAnimationFrame(function () {
            if (headerHeight.value > 0) {
                const opNum =
                    Math.abs(lastScrollPos) / Math.abs(headerHeight.value / 2);

                opacity.value = opNum < 1 ? opNum : 1;
            }
            show.value = lastScrollPos > 0;
            ticking = false;
        });
        ticking = true;
    }
};

window.addEventListener('scroll', on4TabScroll);

onUnmounted(() => {
    window.removeEventListener('scroll', on4TabScroll);
});
defineExpose({
    headerHeight,
});
</script>

<style lang="less" scoped>
.fixed-header {
    position: fixed;
    top: -1px;
    right: 0;
    left: 0;
    z-index: 100;
    width: 100%;
}
.top-header {
    width: 100%;
}
</style>
