# 预约功能模块

## 功能概述

本模块提供了预约功能，主要包括发起预约请求、将预约信息写入日历以及相关的UI提示。通过`useReservation`函数，可以方便地在前端应用中集成预约逻辑。

## 属性

### `useReservation`

- **返回值**:
  - `reservation`: 异步函数，用于执行预约操作。

### `addToCalendar`

- **参数**:
  - `item`: 包含预约信息的对象。
- **返回值**:
  - `eventId`: 日历事件ID。
  - `reservationId`: 预约ID。

### `addToCalendarBatch`

- **参数**:
  - `list`: 包含多个预约信息的数组。
- **返回值**:
  - 数组，每个元素包含`eventId`和`reservationId`。

## 使用示例

### 使用 `useReservation`

```javascript
import { useReservation } from '@pet/ones-use.useReservation';

const { reservation } = useReservation();

// 调用预约函数
reservation().then((res) => {
    console.log('预约结果:', res);
}).catch((error) => {
    console.error('预约失败:', error);
});
```

### 使用 `addToCalendar`

```javascript
import { addToCalendar } from '@pet/ones-use.useReservation';

const item = {
    title: '线下晚会',
    subTitle: '2025年1月18日',
    startTime: '2025-01-18T15:00:00',
    endTime: '2025-01-18T18:00:00',
    reservationId: '123456'
};

addToCalendar(item).then((res) => {
    console.log('日历事件添加成功:', res);
}).catch((error) => {
    console.error('日历事件添加失败:', error);
});
```

### 使用 `addToCalendarBatch`

```javascript
import { addToCalendarBatch } from '@pet/ones-use.useReservation';

const list = [
    {
        title: '线下晚会1',
        subTitle: '2025年1月18日',
        startTime: '2025-01-18T15:00:00',
        endTime: '2025-01-18T18:00:00',
        reservationId: '123456'
    },
    {
        title: '线下晚会2',
        subTitle: '2025年1月19日',
        startTime: '2025-01-19T15:00:00',
        endTime: '2025-01-19T18:00:00',
        reservationId: '789012'
    }
];

addToCalendarBatch(list).then((res) => {
    console.log('日历事件批量添加成功:', res);
}).catch((error) => {
    console.error('日历事件批量添加失败:', error);
});
```

## 注意事项

1. `addToCalendar` 和 `addToCalendarBatch` 函数会尝试在3秒内完成操作，如果超时会抛出错误。
2. `addToCalendarBatch` 会先验证权限，如果第一个事件添加失败，则后续事件不会继续添加。
3. `useReservation` 函数在预约成功后会显示一个Toast提示，持续1秒。

## 依赖项

- `@yoda/bridge`
- `@lux/sharp-ui-next`
- `@alive-ui/actions`
