import { invoke } from '@yoda/bridge';
import { Toast } from '@lux/sharp-ui-next';
import { reservation as sendReservation } from './service';

export function useReservation() {
    const reservation = async () => {
        // 发预约请求
        const res = await sendReservation();

        // 写日历
        await addToCalendarBatch(res?.reservationInfos ?? []);

        // 当用户点击预约按钮，预约成功后，弹出toast“恭喜你！已成功预约”，1秒后toast自动消失
        Toast.info('恭喜你！已成功预约', 1000);

        return res;
    };

    return {
        reservation,
    };
}

export async function addToCalendar(item: any) {
    // 防止挂起
    const timer = setTimeout(() => {
        throw new Error('超时错误');
    }, 3000);

    try {
        const res = await invoke('feed.changeEventForCalendar', {
            method: 'add',
            event: {
                type: 0,
                title: item?.title,
                note: item?.subTitle,
                startDay: item?.startTime,
                endDay: item?.endTime,
            },
        });
        clearTimeout(timer);

        return {
            eventId: res?.eventId,
            reservationId: item?.reservationId,
        };
    } catch (e) {
        clearTimeout(timer);
        throw e;
    }
}

export async function addToCalendarBatch(list: any[]) {
    if (!list?.length) {
        return [];
    }
    try {
        // 先写第一个，验证权限
        const firstRes = await addToCalendar(list[0]);
        if (!firstRes?.eventId) {
            return [];
        }
        // 剩余的批量写入
        const secondToLastResList = await Promise.all(
            list.slice(1).map((item: any) => addToCalendar(item)),
        );

        return [firstRes, ...secondToLastResList];
    } catch (e) {
        return [];
    }
}
