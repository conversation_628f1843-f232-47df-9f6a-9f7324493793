<template>
    <!-- 公会榜单头像不能点击 -->
    <RankItemBase
        :item="{
            ...item,
            disableClick: true,
        }"
    >
        <template #tag>
            <div v-if="item?.extraData?.showCpOrgInfoLabel" class="flex mb-4px">
                <div class="group-label-section mt-2px">
                    {{ item?.extraData?.areaInfo }}
                </div>
                <div class="group-label-section mt-2px ml-2px">
                    {{ item?.extraData?.operationsInfo }}
                </div>
            </div>
        </template>

        <template #extra><slot name="extra" /></template>
        <template #right>
            <!-- 定义右侧内容 -->
            <div v-if="item?.sponsors?.length" class="flex-center-center">
                <APAvatar
                    v-for="elem in item?.sponsors"
                    :key="elem?.itemId"
                    class="ml-5px"
                    size="2xs"
                    :user-id="elem?.itemId"
                    :head-url="elem?.headUrl"
                    :live-stream-id="elem?.liveStreamId"
                >
                    <template #bottomInner>
                        <slot name="bottomInner" />
                    </template>
                    <!-- <template #bottomRight>
                        <LiveIcon
                            v-if="elem?.liveStreamId"
                            class="xs-live-icon"
                        />
                    </template> -->
                </APAvatar>
            </div>
            <div v-else></div>
        </template>
    </RankItemBase>
</template>

<script lang="ts" setup>
// import LiveIcon from '@pet/ones-ui.dynamic-living-icon/index.vue';
import RankItemBase from '@pet/ones-rank.base-item/index.vue';
import { Avatar as APAvatar } from '@alive-ui/pro';
import type { PropType } from 'vue';
import type { ItemRankInfo } from '@pet/ones-rank.base-item/schema';
defineProps({
    item: {
        type: Object as PropType<ItemRankInfo>,
        default: () => {
            return {};
        },
    },
});
</script>

<style lang="less" scoped>
.xs-live-icon {
    transform: scale(0.8);
}
.group-label-section {
    width: fit-content;
    border-radius: 2px;
    padding: 1px 3px;
    background: rgba(224, 237, 255, 0.1);
    opacity: 0.9;
    color: #fff;
    font-size: 8px;
}
</style>
