<script lang="ts">
export default {
    name: 'AdaptTransition',
    inheritAttrs: false,
};
</script>

<script setup lang="ts">
import { computed } from 'vue-demi';
import type { AdaptTransitionName } from './types';
interface IDuration {
    enter?: number;
    leave?: number;
}
interface IDelay {
    enter?: number;
    leave?: number;
}

interface Props {
    /**
     * 动画的名称
     */
    name?: AdaptTransitionName;
    /**
     * 动画持续时间
     */
    duration?: IDuration;
    /**
     * 动画延迟时间
     */
    delay?: IDelay;
    /**
     * Transition的appear属性是否开启
     */
    appear?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    name: 'fade',
});

const emit = defineEmits<{
    (event: 'before-enter'): void;
    (event: 'enter'): void;
    (event: 'after-enter'): void;
    (event: 'before-leave'): void;
    (event: 'leave'): void;
    (event: 'after-leave'): void;
}>();

const computedDuration = computed(() => {
    return {
        enter: props.duration?.enter ?? 233,
        leave: props.duration?.leave ?? 150,
    };
});

function setStyle(el: HTMLElement, delay: number, duration: number) {
    const dom = el;
    dom.style.transitionDelay = delay > 0 ? `${delay}ms` : '';
    dom.style.animationDelay = delay > 0 ? `${delay}ms` : '';
    dom.style.transitionDuration = `${duration - delay}ms`;
    dom.style.animationDuration = `${duration - delay}ms`;
}

function beforeEnter(el: Element) {
    if (!(el instanceof HTMLElement)) {
        return;
    }
    setStyle(el, props.delay?.enter ?? 0, computedDuration.value.enter);
    emit('before-enter');
}

function beforeLeave(el: Element) {
    if (!(el instanceof HTMLElement)) {
        return;
    }
    setStyle(el, props.delay?.leave ?? 0, computedDuration.value.leave);
    emit('before-leave');
}

function enter() {
    emit('enter');
}

function afterEnter() {
    emit('after-enter');
}

function leave() {
    emit('leave');
}

function afterLeave() {
    emit('after-leave');
}
</script>

<template>
    <Transition
        v-bind="$attrs"
        :appear="appear"
        :name="name"
        :duration="computedDuration"
        @before-enter="beforeEnter"
        @enter="enter"
        @after-enter="afterEnter"
        @before-leave="beforeLeave"
        @leave="leave"
        @after-leave="afterLeave"
    >
        <slot />
    </Transition>
</template>

<style lang="scss" scoped>
/* stylelint-disable */
$duration-enter: 200ms;
$duration-leave: 150ms;

.fade {
    &-enter-active {
        transition: all $duration-enter linear;
    }

    &-leave-active {
        transition: all $duration-leave linear;
    }

    &-enter-from,
    &-leave-to {
        opacity: 0;
    }
}

$drawer: (
    bottom: translate(0, 100%),
    top: translate(0, -100%),
    left: translate(-100%, 0),
    right: translate(100%, 0),
);

.drawer {
    @each $position, $value in $drawer {
        &-#{$position} {
            &-enter-active {
                transition: all $duration-enter ease-in-out;
            }
            &-leave-active {
                transition: all $duration-leave ease-in-out;
            }

            &-enter-from,
            &-leave-to {
                opacity: 0;
                transform: $value;
            }
        }
    }
}

/* popover animation @cny22 */
.scale-in-out {
    &-enter-active {
        transition: all $duration-enter ease-in-out;
    }

    &-leave-active {
        transition: all $duration-leave ease-in-out;
    }

    &-enter-from,
    &-leave-to {
        opacity: 0;
        transform: scale(0.6);
    }
}

/* red-packet animation @cny23 */
.flip-pop {
    &-enter-from {
        backface-visibility: hidden;
        transform-origin: center center;
    }

    &-enter-active {
        // animation fadeOpacity $duration-enter linear both, flipPopIn $duration-enter linear both
        animation: flipPopIn $duration-enter linear both;
    }

    &-leave-active {
        transition: all $duration-leave linear;
    }

    &-leave-to {
        opacity: 0;
    }
}

@keyframes flipPopIn {
    0% {
        opacity: 0;
        transform: scale(0) rotateY(-180deg);
        animation-timing-function: cubic-bezier(0.35, 0, 0.53, 1);
    }
    23.3% {
        opacity: 1;
        animation-timing-function: linear;
    }
    33.3% {
        transform: scale(1.12) rotateY(-10deg);
        animation-timing-function: cubic-bezier(0.22, 0, 0.67, 1);
    }
    36.7% {
        transform: scale(1.1) rotateY(0deg);
    }
    60% {
        transform: scale(0.9598) rotateY(0deg);
        animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
    }
    83.3% {
        transform: scale(1.02) rotateY(0deg);
    }
    100% {
        transform: scale(1) rotateY(0deg);
    }
}

@keyframes fadeOpacity {
    0% {
        opacity: 0;
    }
    23.3% {
        opacity: 1;
    }
}

.scale-out {
    &-enter-active {
        animation: scaleOutPop $duration-enter cubic-bezier(0.33, 0, 0.67, 1)
            both;
    }

    &-leave-active {
        transition: all $duration-leave ease-in-out;
    }

    &-enter-from,
    &-leave-to {
        opacity: 0;
    }
}

@keyframes scaleOutPop {
    0% {
        opacity: 0;
        transform: scale(0.1);
    }
    25% {
        opacity: 1;
    }
    58.3% {
        transform: scale(1.0506);
    }
    100% {
        transform: scale(1.0016);
        opacity: 1;
    }
}

/* dialog animation @cny23 */
.pop {
    &-enter-active {
        animation: pop $duration-enter linear both;
    }

    &-leave-active {
        animation: pop-out $duration-leave linear both;
    }
}

@keyframes pop {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    23% {
        opacity: 0;
        transform: scale(0);
        animation-timing-function: cubic-bezier(0.17, 0.11, 0.83, 0.9);
    }
    46.19% {
        opacity: 1;
    }
    53.81% {
        transform: scale(1.02);
        animation-timing-function: cubic-bezier(0.17, 2.35, 0.83, 1.06);
    }
    84.53% {
        transform: scale(0.995);
        animation-timing-function: cubic-bezier(0.17, -0.16, 0.83, 0.88);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pop-out {
    57.08%,
    100% {
        opacity: 0;
        transform: scale(0.6);
        animation-timing-function: cubic-bezier(0.17, -0.12, 0.83, 1.07);
    }
}
.flip {
    &-enter-from {
        transform: rotateY(180deg);
    }

    &-enter-active {
        transition: transform $duration-enter linear;
    }

    &-leave-active {
        transition: opacity $duration-leave linear;
    }

    &-leave-to {
        opacity: 0;
    }
}

/* sheet animation @cny23 */
.drawer-debounce-bottom {
    &-enter-active {
        animation: drawer-debounce-bottom $duration-enter linear both;
    }
    &-leave-active {
        transition: transform $duration-leave ease-in-out;
    }

    &-leave-to {
        transform: translateY(100%);
    }
}

@keyframes drawer-debounce-bottom {
    0% {
        animation-timing-function: cubic-bezier(0.17, 0.17, 0.67, 1);
        transform: translateY(100%);
    }
    30% {
        animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
        transform: translateY(-30px);
    }
    70% {
        animation-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
        transform: translateY(10px);
    }
    100% {
        transform: translateY(0);
    }
}

/* popover animation @cny23 */
.scale-in-out-debounce {
    &-enter-active {
        animation: scaleInDebounce $duration-enter linear both;
    }

    &-leave-active {
        transition: all $duration-leave cubic-bezier(0.17, 0, 0.83, 1);
    }

    &-leave-to {
        opacity: 0;
        transform: scale(0.08);
    }
}

@keyframes scaleInDebounce {
    0% {
        opacity: 0;
        transform: scale(0);
        animation-timing-function: cubic-bezier(0.17, 0.12, 0.83, 0.94);
    }
    39.93% {
        transform: scale(1.02);
        animation-timing-function: cubic-bezier(0.17, 2.35, 0.83, 1.06);
    }
    60% {
        opacity: 1;
    }
    79.88% {
        transform: scale(0.995);
        animation-timing-function: cubic-bezier(0.17, -0.16, 0.83, 0.88);
    }
    100% {
        transform: scale(1);
    }
}

/* dialog animation @cny22 */
.pop-crisp {
    &-enter-active {
        animation: pop-crisp $duration-enter linear both;
    }

    &-leave-active {
        transition: all $duration-leave cubic-bezier(0.44, 0.09, 0.14, 1.35);
    }

    &-enter-from,
    &-leave-to {
        opacity: 0;
        transform: scale(0.9);
    }
}

@keyframes pop-crisp {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    35.83% {
        opacity: 1;
    }
    50% {
        opacity: 1;
        transform: scale(1.04);
        transition-timing-function: cubic-bezier(0.33, 0, 0.67, 1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
        transition-timing-function: cubic-bezier(0.11, 0.02, 0.15, 1);
    }
}
</style>
