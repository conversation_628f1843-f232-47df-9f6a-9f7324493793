<script setup lang="ts">
import { ref } from 'vue-demi';
import AdaptTransition from '../index.vue';
import type { AdaptTransitionName } from '../types';

const show = ref(false);

const transType = ref<AdaptTransitionName>('fade');

const transitionTypes: AdaptTransitionName[] = [
    'fade',
    'drawer-top',
    'drawer-left',
    'drawer-right',
    'drawer-bottom',
    'scale-in-out',
    'scale-in-out-debounce',
    'pop',
    'pop-crisp',
    'flip-pop',
    'scale-out',
    'drawer-debounce-bottom',
];

function showTrans() {
    show.value = !show.value;
}

const transDuration = ref({
    enter: 500,
    leave: 200,
});
</script>

<template>
    <div class="demo">
        <article>
            <div class="title">动效类型 {{ transType }}</div>
            <article>
                <div class="title">动效配置</div>
                <div class="row">
                    入场时长<input
                        v-model="transDuration.enter"
                        type="number"
                    />ms
                </div>
                <div class="row">
                    离场时长<input
                        v-model="transDuration.leave"
                        type="number"
                    />ms
                </div>
            </article>
            <section class="all-transitions">
                <label
                    v-for="tType in transitionTypes"
                    :key="tType"
                    class="radioButton"
                >
                    <input
                        v-model="transType"
                        type="radio"
                        name="trans"
                        :value="tType"
                    />{{ tType }}
                </label>
            </section>
            <section>
                <button @click="showTrans">展示动画</button>
            </section>
        </article>
        <section class="playground">
            <AdaptTransition :name="transType" :duration="transDuration">
                <div v-if="show" class="ball"></div>
            </AdaptTransition>
        </section>
    </div>
</template>

<style lang="scss" scoped>
.demo {
    font-size: 14px;
    position: relative;
}
.title {
    margin-bottom: 10px;
    color: #008000;
}
.all-transitions {
    margin: 20px 0;
}
.radioButton {
    margin-right: 5px;
    & > input {
        vertical-align: -2px;
        margin: 0 5px 0;
    }
}
label {
    display: inline-flex;
    width: 150px;
    margin-bottom: 10px;
    font-size: 12px;
    align-items: center;
}
.button {
    margin-top: 20px;
}
.ball {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #fe3666;
}
.row {
    margin-bottom: 10px;
}
</style>
