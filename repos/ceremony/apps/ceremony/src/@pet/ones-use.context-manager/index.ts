/**
 * 可取消的上下文
 */
export type CancelableContext = ReturnType<typeof ContextManager.reset>;

/**
 * 上下文管理器
 *   用于管理和跟踪上下文的状态，以便在上下文失效后，相关的任务可以自行停止。
 *   方案: 如果一个上下文的 round 和它被创建时的不一样, 那它就是被取消的, 业务逻辑应进行停止。
 */
export class ContextManager {
    private static keyToManager: Record<string, ContextManager> = {};

    static reset(key: string) {
        const manager = ContextManager.for(key);
        manager.cancelAllContext();
        return manager.getContext();
    }

    private static for(key: string): ContextManager {
        if (!ContextManager.keyToManager[key]) {
            ContextManager.keyToManager[key] = new ContextManager();
        }
        return ContextManager.keyToManager[key];
    }

    // 管理器实例的 round
    managerRound = 0;

    // 取消当前管理器实例创建的所有上下文
    cancelAllContext() {
        ++this.managerRound;
    }

    /**
     * 获取一个上下文对象
     * @returns 包含用于检测当前上下文是否为取消状态的 isCancellationRequested 方法。
     */
    getContext(this: ContextManager) {
        const ctxRound = this.managerRound;
        return {
            manager: this,
            // 判断当前上下文是否已经被取消
            isCancellationRequested: () => {
                if (ctxRound !== this.managerRound) {
                    return true;
                }
                return false;
            },
            ctxRound,
        };
    }
}
