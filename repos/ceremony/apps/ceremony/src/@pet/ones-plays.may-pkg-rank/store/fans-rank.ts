import { defineStore } from 'pinia';
import {
    formatTime,
    activityBiz,
    getPageStatus,
    Report,
} from '@alive-ui/actions';
import { queryFansRanking } from '../api/query-fans-ranking';
import type { FansRankingResponse, RankList } from '../schemas/index';
import { sendFmp } from '@/common/logger';

export default defineStore('fans', {
    state: () => {
        return {
            pageStatus: getPageStatus('init'),
            isRefetch: false,
            rankDataInfo: {} as FansRankingResponse,
            curPeriod: Date.now(),
            rankType: 'day',
            fansPeriodSwitchOptions: null as any,
            hasPrev: false,
            hasNext: false,
        };
    },
    getters: {
        fansCurrentAuthorInfo: (state) => {
            const { bottomInfo } = state.rankDataInfo;

            if (bottomInfo) {
                return {
                    itemRankInfo: {
                        item: {
                            headUrl: bottomInfo?.userInfo?.headUrl,
                            itemName: bottomInfo?.userInfo?.userName,
                            itemId: bottomInfo?.userInfo?.userId,
                        },
                        displayScore: bottomInfo?.score, // 盛典值
                        h5RankShowIndex: bottomInfo?.rank,
                        liveStreamId: bottomInfo?.liveStreamId, // 跳转用
                        followStatus:
                            bottomInfo?.followingStatus === 2 ? false : true,
                        mysteryMan: false,
                    },
                    displayHint: bottomInfo?.displayHint,
                    hintScore: bottomInfo?.hintScore,
                    showCpOrgInfoLabel: bottomInfo?.showCpOrgInfoLabel,
                    operationsInfo: bottomInfo?.operationsInfo,
                    areaInfo: bottomInfo?.areaInfo,
                };
            }

            return null;
        },
        fansRankArrayList: (state) => {
            return state.rankDataInfo?.rankList?.map((item: RankList) => {
                return {
                    item: {
                        itemId: item?.userInfo?.userId,
                        itemName: item?.userInfo.userName,
                        headUrl: item?.userInfo?.headUrl,
                        liveStreamId: item?.liveStreamId,
                    },
                    score: item?.score,
                    displayScore: item?.score,
                    h5RankShowIndex: item?.rank,
                    liveStreamId: item?.liveStreamId,
                    followStatus: item?.followingStatus === 2 ? false : true,
                    mysteryMan: item.mysteryMan ?? false,
                    showCpOrgInfoLabel: item?.showCpOrgInfoLabel,
                    operationsInfo: item?.operationsInfo,
                    areaInfo: item?.areaInfo,
                };
            });
        },
        liveStreamIdList: (state) => {
            return state.rankDataInfo?.rankList?.map((item) => {
                return item?.liveStreamId;
            });
        },
        awardData: (state) => {
            return {
                rewardTexts: state.rankDataInfo?.rewardTexts,
                rewardPic: state.rankDataInfo?.rewardPic,
            };
        },
    },
    actions: {
        async queryFansRankData() {
            const date = this.rankType === 'day' ? this.curPeriod : undefined;
            const rankName = this.rankType === 'day' ? 'daily' : 'total';

            const queryParams = {
                date,
                biz: activityBiz,
                rankName,
            };

            if (!this.isRefetch) {
                this.pageStatus = getPageStatus('loading');
            }

            try {
                const res = await queryFansRanking(queryParams);
                this.rankDataInfo = res;
                this.pageStatus = !res?.rankList?.length
                    ? getPageStatus('nodata')
                    : getPageStatus('success');
                const { prePeriodTime, currPeriodTime, nextPeriodTime } = res;
                if (this.rankType === 'day') {
                    const periodSwitchOptionsData = [
                        prePeriodTime,
                        currPeriodTime,
                        nextPeriodTime,
                    ]
                        .filter((item) => item)
                        .map((item) => {
                            return {
                                value: item,
                                txt: formatTime(item, 'MM月dd日'),
                            };
                        });
                    this.hasPrev = !!prePeriodTime;
                    this.hasNext = !!nextPeriodTime;

                    if (currPeriodTime) {
                        this.fansPeriodSwitchOptions = {
                            curValue: currPeriodTime,
                            curIndex: periodSwitchOptionsData.findIndex(
                                (item) => item.value === currPeriodTime,
                            ),
                            data: periodSwitchOptionsData,
                        };
                    }
                }
            } catch (error) {
                this.pageStatus = getPageStatus('error');
            } finally {
                try {
                    sendFmp();
                } catch (e) {
                    sendFmp();
                    Report.biz.warning('涨粉榜接口初始化失败', {
                        e,
                    });
                }
            }
        },
        fansHandlePeriodSwitchChange(index: number) {
            this.curPeriod = this.fansPeriodSwitchOptions.data[index].value;
            this.queryFansRankData();
        },
        handleRankTypeChange(type: 'day' | 'all') {
            this.rankType = type;
            this.queryFansRankData();
        },
    },
});
