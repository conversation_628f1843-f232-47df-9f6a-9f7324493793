export type RankType = 'sprint' | 'fans';

export interface DataConfig {
    interfaceConfig: {
        v3RankBiz: string;
    };
    needRank: boolean;
    guildRankKv?: string;
    laneBiz?: string;
    cardTitle?: string;
    tabConfig: {
        actionName: string;
        logName: [string, string];
        rankTypeArray: [RankType, RankType];
        tabList: [
            {
                name: string;
            },
            {
                name: string;
            },
        ];
    };
    rankLabelDesc: {
        desc: string;
        group: string;
        sendGiftTxt: string;
        person: string;
        sprint: string;
        fans: string;
    };
    actGiftId: number;
    sprintRankRewardDesc: {
        currentTxt: string;
        endTxt: string;
    };
    sprintRankNationRewardDesc?: {
        currentTxt: string;
        endTxt: string;
    };
    focusExtraParams: {
        activityId?: string;
    };
    showFansRankRefreshBtn: boolean;
}
