<template>
    <Card>
        <Title
            >{{ dataConfig.cardTitle }} <template #prefix></template>
            <template #suffix></template
        ></Title>
        <Content>
            <FansIndex
                :data-config="dataConfig"
                @refresh="refresh"
                @switch-clicked="handleFansSwitchClicked"
            />
        </Content>
    </Card>
</template>

<script lang="ts" setup>
import {
    computed,
    onMounted,
    ref,
    useCssModule,
    defineAsyncComponent,
    type PropType,
} from 'vue';
import { parseQuery } from '@alive-ui/system';
import { Card as ACard, Tabs as ATabs } from '@alive-ui/base';
import defaultDataConfig from '../variable/config.json';
import FansIndex from './fans-index.vue';
const { Card, Content, Title } = ACard;
const { Tabs, TabList, Tab, TabPanels, TabPanel } = ATabs;

const query = parseQuery();
const { rankTab } = query;
const props = defineProps({
    dataConfig: {
        type: Object,
        default: () => defaultDataConfig,
    },
    fansStyles: {
        type: Object as PropType<Record<string, string>>,
        default: () => ({}),
    },
});
const emits = defineEmits(['refresh', 'fans-switch-clicked']);

const refresh = () => {
    emits('refresh');
};

const handleFansSwitchClicked = (val: string) => {
    emits('fans-switch-clicked', val);
};
</script>

<style lang="less" scoped>
:deep(.my-frame-sm) {
    position: absolute;
    top: 50%;
    left: 50%;
    width: calc(59px);
    height: calc(59px);
    transform: translate(-50%, calc(-50% - 1px));
}

:deep(.my-frame) {
    position: absolute;
    top: 50%;
    left: 50%;
    width: calc(72px);
    height: calc(72px);
    transform: translate(-50%, calc(-50% - 1px));
}
</style>

<style lang="less" module>
.pk-group-content {
    margin-top: 10px;
}
</style>
