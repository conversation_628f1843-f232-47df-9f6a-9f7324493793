<template>
    <div class="relative" :class="stls['fans-rank']">
        <div class="info-height">
            <InfoMain
                v-if="!pageStatus.error && !pageStatus.loading"
                :class="stls['fans-rank-info']"
            >
                <StepperMain
                    v-if="rankType === 'day' && fansPeriodSwitchOptions"
                    :selected-index="fansPeriodSwitchOptions.curIndex"
                    @change="store.fansHandlePeriodSwitchChange"
                >
                    <StepperPrev
                        :class="{
                            'stepper-disabled-o': !hasPrev,
                        }"
                    />
                    <StepperItem
                        v-for="item in fansPeriodSwitchOptions.data"
                        :key="item.value"
                    >
                        {{ item.txt }}
                    </StepperItem>
                    <StepperNext
                        :class="{
                            'stepper-disabled-o': !hasNext,
                        }"
                    />
                </StepperMain>
                <Highlight
                    v-if="rankType === 'all'"
                    text="当前：公会打卡总榜"
                    keywords="公会打卡总榜"
                />
                <template #extra>
                    <div class="ranking-type-switch" @click="handleSwitchClick">
                        <div class="switch-guild-icon" />
                        <div class="switch-text">
                            {{ rankType === 'day' ? '切换总榜' : '切换日榜' }}
                        </div>
                    </div>
                </template>
            </InfoMain>
        </div>

        <RewardCollapse
            :award-data="{
                marqueeList: awardData?.rewardTexts,
                awardURL: awardData?.rewardPic,
                operateText: '查看奖励',
                showAward: true,
            }"
        />
        <template
            v-if="
                !pageStatus.error && !pageStatus.loading && !pageStatus.nodata
            "
        >
            <div class="main-rank-list-container">
                <BaseRankList
                    :log-params="{
                        rankId: '',
                    }"
                >
                    <template v-if="fansCurrentAuthorInfo" #current>
                        <BaseRankItem
                            :order="
                                fansCurrentAuthorInfo.itemRankInfo
                                    .h5RankShowIndex
                            "
                            :log-params="fansCurrentAuthorInfo.itemRankInfo"
                            :is-current="true"
                            :current-label="
                                bolIsAuthor ? '当前公会' : '主播公会'
                            "
                        >
                            <template #left>
                                <Avatar
                                    class="headUrl"
                                    :head-url="
                                        fansCurrentAuthorInfo.itemRankInfo.item
                                            .headUrl
                                    "
                                    :size="'sm'"
                                >
                                    <template #frame>
                                        <div
                                            v-if="
                                                Number(
                                                    fansCurrentAuthorInfo
                                                        .itemRankInfo
                                                        .h5RankShowIndex,
                                                ) <= 3
                                            "
                                            :class="[
                                                'my-frame-sm',
                                                'frame-img' +
                                                    fansCurrentAuthorInfo
                                                        .itemRankInfo
                                                        .h5RankShowIndex,
                                            ]"
                                        ></div> </template
                                ></Avatar>
                            </template>
                            <Username class="a-text-main">
                                {{
                                    nameSlice(
                                        fansCurrentAuthorInfo.itemRankInfo.item
                                            .itemName,
                                    )
                                }}</Username
                            >
                            <InfoItem v-if="fansCurrentAuthorInfo.displayHint">
                                <template #key>
                                    <div>
                                        {{
                                            fansCurrentAuthorInfo.displayHint.replace(
                                                '${hintScore}',
                                                '',
                                            )
                                        }}：
                                    </div>
                                </template>
                                <template #value>
                                    <div class="mt-1px">
                                        {{ fansCurrentAuthorInfo.hintScore }}
                                    </div></template
                                >
                            </InfoItem>
                            <template #right>
                                <div
                                    v-if="
                                        fansCurrentAuthorInfo?.showCpOrgInfoLabel
                                    "
                                    class="info-box"
                                >
                                    <div
                                        v-if="fansCurrentAuthorInfo?.areaInfo"
                                        class="info-area"
                                    >
                                        {{ fansCurrentAuthorInfo?.areaInfo }}
                                    </div>
                                    <div
                                        v-if="
                                            fansCurrentAuthorInfo?.operationsInfo
                                        "
                                        class="info-name"
                                    >
                                        {{
                                            fansCurrentAuthorInfo?.operationsInfo
                                        }}
                                    </div>
                                </div>
                            </template>
                        </BaseRankItem>
                    </template>
                    <template
                        v-for="item in fansRankArrayList"
                        :key="item.item.itemId"
                    >
                        <BaseRankItem
                            :order="item.h5RankShowIndex"
                            :log-params="item"
                        >
                            <template #left>
                                <Avatar
                                    class="headUrl"
                                    :head-url="item.item.headUrl"
                                >
                                    <template #frame>
                                        <div
                                            v-if="
                                                Number(item.h5RankShowIndex) <=
                                                3
                                            "
                                            :class="[
                                                'my-frame',
                                                'frame-img' +
                                                    item.h5RankShowIndex,
                                            ]"
                                        ></div>
                                    </template>
                                </Avatar>
                            </template>
                            <Username>
                                {{ nameSlice(item.item.itemName) }}</Username
                            >
                            <InfoItem>
                                <template #key>
                                    <div>
                                        {{
                                            dataConfig?.rankLabelDesc?.fans ??
                                            '荣耀值'
                                        }}：
                                    </div>
                                </template>
                                <template #value>
                                    <div class="mt-1px">
                                        {{ item?.score }}
                                    </div></template
                                >
                            </InfoItem>
                            <template #right>
                                <div
                                    v-if="item?.showCpOrgInfoLabel"
                                    class="info-box"
                                >
                                    <div
                                        v-if="item?.areaInfo"
                                        class="info-area"
                                    >
                                        {{ item?.areaInfo }}
                                    </div>
                                    <div
                                        v-if="item?.operationsInfo"
                                        class="info-name"
                                    >
                                        {{ item?.operationsInfo }}
                                    </div>
                                </div>
                            </template>
                        </BaseRankItem>
                    </template>
                </BaseRankList>
            </div>
        </template>
        <ElseStatus
            v-else
            :page-status="pageStatus"
            :is-show-refresh="pageStatus.error"
            @refresh="store.queryFansRankData"
        />
        <RefreshIcon
            v-if="dataConfig.showFansRankRefreshBtn"
            :refresh-time="1500"
            @refresh="refreshRankList"
        />
    </div>
</template>

<script lang="ts" setup>
import { computed, useCssModule, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { throttle } from 'lodash-es';
import RewardCollapse from '@pet/ones-ui.rank-award-collapse/index.vue';
import {
    BaseRankList,
    BaseRankItem,
    Avatar,
    FocusButton,
    GiftButton,
} from '@alive-ui/pro';
import {
    Stepper,
    Info,
    ElseStatus,
    RankList,
    RefreshIcon,
} from '@alive-ui/base';
import {
    query,
    formatRealScore,
    newFormatScore,
    authorId,
    nameSlice,
    bolIsAuthor,
} from '@alive-ui/actions';
import defaultDataConfig from '../variable/config.json';
import fansRankStore from '../store/fans-rank';
import type { PropType } from 'vue';
const { StepperItem, Stepper: StepperMain, StepperPrev, StepperNext } = Stepper;
const { Info: InfoMain, InfoHighlight: Highlight } = Info;
const { Username, InfoItem } = RankList;

const props = defineProps({
    dataConfig: {
        type: Object,
        default: () => defaultDataConfig,
    },
    needDealScore: {
        type: Boolean,
        default: () => true,
    },
    // 分数盖帽逻辑需要
    needSeeAllUserRealScore: {
        type: Boolean,
        default: () => false,
    },
    // 分数盖帽逻辑需要
    needSeeRealScore: {
        type: Boolean,
        default: () => false,
    },
    // 关注栈id
    focusStackId: {
        type: Number,
        default: () => {
            return query.focusStackId ?? query.activityId;
        },
    },
});

const emits = defineEmits(['switch-clicked', 'refresh']);

const store = fansRankStore();
const $styles = useCssModule();
const stls = computed(() => {
    return {
        ...$styles,
    };
});

const {
    pageStatus,
    fansRankArrayList,
    liveStreamIdList,
    fansCurrentAuthorInfo,
    rankType,
    fansPeriodSwitchOptions,
    hasPrev,
    hasNext,
    awardData,
} = storeToRefs(store);

store.queryFansRankData();

const handleSwitchClick = throttle(() => {
    store.handleRankTypeChange(rankType.value === 'day' ? 'all' : 'day');

    emits('switch-clicked', rankType.value);
}, 500);

const refreshRankList = () => {
    window.scrollTo({
        top: 0,
    });

    setTimeout(() => {
        store.queryFansRankData();
        emits('refresh');
    }, 200);
};
</script>

<style lang="less" scoped>
.fans-rank {
    min-height: calc(100vh - 210px);
}
:deep(.tag-text-bg) {
    background: linear-gradient(262.85deg, #fbc080 0%, #ffeac5 100%);
    color: rgba(14, 10, 58, 1);
}
:deep(.stepper-icon) {
    width: 16px;
    height: 16px;
    background: url('../variable/assets/stepper-icon_2x.png') center / 100%
        no-repeat;
}
:deep(.rank-award-content) {
    padding-bottom: 0px;
    background: rgba(217, 221, 255, 0.06);
}
:deep(.info-highlight-attr) {
    color: #ffdcb2;
}
// :deep(.line-cut1) {
//     padding-top: 1px;
// }
.fans-rank-info {
    margin-bottom: 10px;
}
.info-height {
    height: 32px;
}
.headUrl {
    border: 1px solid #ffdbf7;
}
.deaultAvatar {
    width: 48px;
    height: 48px;
    border: 1px solid #ffdbf7;
    border-radius: 50%;
    background: url('../variable/assets/smDefaultAvatarIcon.png') center / 100%
        no-repeat;
}
.lgdeaultAvatar {
    width: 60px;
    height: 60px;
    border: 1px solid #ffdbf7;
    border-radius: 50%;
    background: url('../variable/assets/lgDefaultAvatarIcon.png') center / 100%
        no-repeat;
}
.ranking-type-switch {
    display: flex;
    font-family: PingFangSC, PingFangSC-Regular;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    align-items: center;
    .switch-text {
        color: #f3f2ff;
        opacity: 0.6;
    }
    .switch-guild-icon {
        width: 10px;
        height: 10px;
        background: url('../variable/assets/ranking-type-switch-icon_2x.png')
            center / 100% no-repeat;
        margin-right: 4px;
    }
}
.info-box {
    width: 58px;
    height: 28px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: rgba(255, 255, 255, 1);
    opacity: 0.9;
    .info-area {
        height: 12px;
        padding: 0 3px;
        line-height: 14px;
        background-color: rgba(224, 237, 255, 0.1);
        border-radius: 2px;
        margin-bottom: 4px;
    }
    .info-name {
        height: 12px;
        padding: 0 3px;
        line-height: 14px;
        background-color: rgba(224, 237, 255, 0.1);
        border-radius: 2px;
    }
}
// .my-frame-sm {
//     position: absolute;
//     top: 50%;
//     left: 50%;
//     width: calc(59px);
//     height: calc(59px);
//     transform: translate(-50%, calc(-50% - 1px));
// }

// .my-frame {
//     position: absolute;
//     top: 50%;
//     left: 50%;
//     width: calc(72px);
//     height: calc(72px);
//     transform: translate(-50%, calc(-50% - 1px));
// }

// position: absolute;
//   top: 50%;
//   left: 50%;
//   width: 131% !important;
//   height: 131% !important;
//   background: url('../assets/group/gis-avatar-bg-3.png') center / 92.5% no-repeat;
//   transform: translate(-50%, calc(-50% - 1px));
</style>
