import { authorId, request } from '@alive-ui/actions';
import type { FansRankingRequest, FansRankingResponse } from '../schemas/index';

const URL =
    '/webapi/live/revenue/operation/activity/attendanceCpRank/queryRankList';

export const queryFansRanking = async (params: FansRankingRequest) => {
    const res = await request.post<FansRankingResponse>(URL, {
        ...params,
        authorId,
    });

    return res.data;
};
