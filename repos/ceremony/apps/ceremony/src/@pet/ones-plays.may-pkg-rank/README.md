##### 榜单玩法

因为考虑到榜单的特殊性，目前还是收拢在一个玩法里
和扭蛋机的不同点在于，一个活动只会有一种扭蛋机，但榜单可能会存在多种：
比如团战榜，冲刺榜，涨粉榜，如果单独拆开会涉及资源的重叠，但合在一起可能会部分耦合，

目前冲刺榜。涨粉榜，或者其他榜单的组合方式不唯一，故设计传入props为
rankTypeArray:['fans','sprint','group','period']等几种类型自由组合，使得调用方式更加灵活group：分组、period：赛程
或者 rankComponentsList 传入组件路径，如下例子：
``    [{
        componentName: 'sprint',
        componentValue: () => import(`@live-uxd/plays/src/pk-group-rank/views/sprint-index.vue`),
    }, {
        componentName: 'pk',
        componentValue: () => import(`../components/pk-competition/list/index.vue`),
    }, {
        componentName: 'fans',
        componentValue: () => import(`@live-uxd/plays/src/pk-group-rank/views/fans-index.vue`),
    }];``

gis-collapse-rank-reward-2 为冲刺榜奖励图

### 使用说明

-   query参数
    1. rankTab 如果需要定位到非默认tab位置，需要传递这个参数
