export interface FansRankingResponse {
    prePeriodTime: number;
    nextPeriodTime: number;
    currPeriodTime: number;
    rankList: RankList[];
    bottomInfo: BottomInfo;
    rewardTexts: string[];
    rewardPic: string;
}

export interface UserInfo {
    userId: number;
    userName: string;
    headUrl: string;
}

export interface RankList {
    score: string;
    rank: string;
    liveStreamId: string;
    followingStatus: number;
    userInfo: UserInfo;
    mysteryMan: boolean;
    showCpOrgInfoLabel: boolean;
    operationsInfo: string;
    areaInfo: string;
}

export interface BottomInfo {
    score: string;
    rank: string;
    liveStreamId: string;
    followingStatus: number;
    userInfo: UserInfo;
    displayHint: string;
    showCpOrgInfoLabel: boolean;
    operationsInfo: string;
    areaInfo: string;
    hintScore: number;
}

export interface FansRankingRequest {
    /**
     * 	当前时间戳 总榜不传
     */
    date?: number;
    /**
     * 榜单标识，总榜total，日榜daily
     */
    biz: string;
    rankName?: string;
}
