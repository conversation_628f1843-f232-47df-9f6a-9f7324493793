<script setup lang="ts">
import { nextTick, ref } from 'vue';
import { templateRef } from '@vueuse/core';
import Pilot from '@pet/adapt.bezier-path-fly/index.vue';
import { promiseWithResolvers } from '../utils/promise-with-resolvers';

const targetId = ref('');
const pilotRef = templateRef('pilotRef');

const { promise, resolve } = promiseWithResolvers();

defineExpose({
    boot: (id: string) => {
        targetId.value = id;
        nextTick(() => {
            pilotRef.value?.boot();
        });
        return promise;
    },
});
</script>

<template>
    <Pilot ref="pilotRef" :fly-to-target="targetId" @end="resolve">
        <slot></slot>
    </Pilot>
</template>
