export interface RankingView {
    /**
     * 视频ID
     */
    photoId?: string | null | undefined;
    /**
     * 直播ID
     */
    liveId?: string | null | undefined;
    /**
     * 作者ID
     * @format int64
     */
    authorId: number;
    /**
     * 赞助官ID
     * @format int64
     */
    sponsorId: number;
    /**
     * 封面URL
     */
    coverUrl: Array<CdnNodeView>;
    /**
     * 标题
     */
    title: string;
    /**
     * 头像URL
     */
    userAvatarUrl: Array<CdnNodeView>;
    /**
     * 名称
     */
    username: string;
    /**
     * 按钮类型（关注、点赞、参与）
     * @format int32
     */
    buttonType: number;
    /**
     * 按钮文案
     */
    buttonText: string;
    /**
     * 是否直播中
     */
    living?: boolean | null | undefined;
    /**
     * 跳转链接
     */
    jumpUrl?: string | null | undefined;
    /**
     * 预约id
     */
    reservationId?: string | null | undefined;
    /**
     * 创作者奖品id（加密）
     */
    llpeId?: string | null | undefined;
    /**
     * 开奖时间
     * @format int64
     */
    openTime?: number | null | undefined;
    /**
     * 是否强插广告数据
     */
    adMark?: boolean | null | undefined;
    /**
     * 强插位置，1-2 代表从左到右，从上到下，第一个位置开的第2个红包
     */
    position?: string | null | undefined;
    /**
     * 奖品类型，1:现金红包，2:快币红包
     * @format int32
     */
    type: number;
    /**
     * 奖品具体金额，现金(单位分)/快币(个)
     * @format int64
     */
    amount: number;
    /**
     * 数量
     * @format int32
     */
    count: number;
    /**
     * 奖品金额外显，如10.88
     */
    amountStr: string;
    /**
     * 奖品金额外显单位，如万
     */
    unit: string;
    /**
     * 边框类型，0 无，1 cny边框
     * @format int32
     */
    borderType: number;
    openLLpe?: boolean | null | undefined;
    join?: boolean | null | undefined;
}
export interface CdnNodeView {
    cdn?: string | null | undefined;
    url?: string | undefined;
    ip?: string | null | undefined;
    ipList?: Array<string> | null | undefined;
    urlPattern?: string | null | undefined;
    feature?: Array<number> | null | undefined;
    useP2sp?: boolean | null | undefined;
    freeTrafficCdn?: boolean | null | undefined;
}
export interface RoundInfoView {
    /**
     * 轮次
     * @format int32
     */
    round: number;
    /**
     * 提示文案
     */
    note: string;
    /**
     * 开始时间，毫秒时间戳
     * @format int64
     */
    startTime?: number | null | undefined;
    /**
     * 结束时间，毫秒时间戳
     * @format int64
     */
    endTime?: number | null | undefined;
}
export type SpecialTabItem = {
    title: string;
    subTitle?: string;
    value: string | number;
    disabled?: boolean;
    toast?: string;
};
