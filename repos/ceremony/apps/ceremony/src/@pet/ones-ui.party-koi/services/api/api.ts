import { request } from '@gundam/request';
import type { AxiosRequestConfig as RequestConfig } from 'axios';

/**
 * 用户参与抽奖
 *
 * * __method__: POST
 * * __path__: /rest/wd/live/koi/userAttend/attend
 * * __tags__: LiveKoiUserAttendController
 */
export const liveKoiUserAttendControllerAttend = (
    parameters: {
        body: any;
    },
    options: RequestConfig = {},
) =>
    request({
        method: 'post',
        url: '/rest/wd/live/koi/userAttend/attend',
        data: parameters.body,
        ...{ ...options },
    }) as any;
