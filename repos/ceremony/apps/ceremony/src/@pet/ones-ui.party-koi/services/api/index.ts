import { type AxiosResponse } from 'axios';
import { toast } from '@pet/adapt.toast';
import { ERROR_MSG, blackAPIResult } from './const';

// 通用成功处理
export const handleSucc = <T>(res: AxiosResponse<T>): T => {
    return res?.data;
};

// 通用错误处理, 只用于code为200，result非1情况，网络层面的错误直接走兜底页了，不会出现toast
export const handleError = (err: any) => {
    // toast提示能力: 不跳转兜底页code默认就是toast
    if (
        err?.data?.result !== -1 &&
        !blackAPIResult.includes(err?.data?.result)
    ) {
        toast(err.data?.error_msg || ERROR_MSG);
    }
};

// 通用错误日志上报，因底层已经上报，这里预留业务层能力即可
export const logError = (err: any) => {
    console.log(err);
};
