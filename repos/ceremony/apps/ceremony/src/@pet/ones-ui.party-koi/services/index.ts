import { type RankingView as _RankingView } from '../schema/index';
import { handleSucc, handleError, logError } from './api/index';
import { liveKoiUserAttendControllerAttend } from './api/api';

/**
 * 用户参与
 */
export const fetchAttendResult = async (body: any) => {
    try {
        const res = await liveKoiUserAttendControllerAttend({ body });
        return handleSucc(res);
    } catch (err) {
        handleError(err);
        logError(err);
        // @ts-expect-error - 无法为错误指定类型
        if (err?.data?.result !== 1) {
            // 参与失败 向 model 层抛出错误信息
            // @ts-expect-error - 无法为错误指定类型
            throw new Error(`${err?.data?.error_msg}`);
        }
    }
};
