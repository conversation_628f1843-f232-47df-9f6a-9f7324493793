/**
 * 创建一个Promise及其resolve和reject函数
 * @returns 包含Promise及其控制函数的对象
 */
export function promiseWithResolvers<T = void>(): {
    promise: Promise<T>;
    resolve: (value: T | PromiseLike<T>) => void;
    reject: (reason?: unknown) => void;
} {
    let resolve!: (value: T | PromiseLike<T>) => void;
    let reject!: (reason?: unknown) => void;

    // 创建一个新的Promise,并捕获其resolve和reject函数
    const promise = new Promise<T>((res, rej) => {
        resolve = res;
        reject = rej;
    });

    // 返回Promise及其控制函数
    return { promise, resolve, reject };
}
