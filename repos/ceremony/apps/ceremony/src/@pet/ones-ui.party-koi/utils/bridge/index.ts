import {
    type FeedChangeEventForCalendarParams,
    type PostPostVideoParams,
    type ToolLoadUrlOnNewPageParams,
} from '@yoda/bridge-types';
import { invoke as originalInvoke } from '@yoda/bridge';
import { isInWebview, isIOS } from '@pet/yau.core/device';
import { toast as showToast, toastDestroy } from '@pet/adapt.toast';
import { isHalfScreen } from '../../const/index';

/**
 * 打开原生页面
 * @param url
 */
export function openNativePage(url: string) {
    originalInvoke('platform.loadUri', {
        url,
    }).catch((e: any) => {
        window.open(url);
    });
}

// /**
//  * 拉起礼物面板
//  *
//  */
// export const openGiftPanel = () => {
//     originalInvoke('KwaiLive.liveRouter', {
//         url: 'kwailive://giftpanel',
//         dismiss: true,
//     });
// };

// /**
//  * 拉起 krn
//  *
//  */
// export const openKrnPage = (url: string) => {
//     originalInvoke('KwaiLive.liveRouter', {
//         url,
//         dismiss: false,
//     });
// };

/*  @name 登陆
 *  @description checkFromServer:是否进行服务端校验，默认不校验
 */
export const login = async (config: { checkFromServer?: boolean }) => {
    return await originalInvoke('social.login', config, {});
};

/**
 * 打开webview
 * @param url
 */
export function openWebview(params: ToolLoadUrlOnNewPageParams) {
    // ios在直播间通过半屏打开一个新的全屏webview的时候需要传入 ignoreHalfScreenDisplay = 1 否则导航会有问题
    const finalParam = Object.assign(params, {
        url: !/^http(s)?:\/\//g.test(params.url)
            ? window.location.origin + params.url
            : params.url,
    });
    // 是半屏就传这个参数
    if (isIOS() && isHalfScreen) {
        Object.assign(finalParam, {
            ignoreHalfScreenDisplay: 1,
        });
        return originalInvoke('tool.loadUrlOnNewPage', finalParam);
    }
    return originalInvoke('tool.loadUrlOnNewPage', finalParam);
}

/**
 * 触发回退
 *
 */
export function popBack() {
    return originalInvoke('webview.popBack');
}

/**
 * 关闭webview
 */
export function exitWebView() {
    return originalInvoke('webview.exitWebView');
}

/**
 * 设置页面标题
 */
export function setPageTitle(option: {
    title: string;
    titleTextColor?: string;
    titleBackgroundColor?: string;
}) {
    const {
        title,
        titleTextColor = '#222222',
        titleBackgroundColor = '#ffffff',
    } = option;
    document.title = title;
    return originalInvoke('webview.setPageTitle', {
        title,
        titleTextColor,
        titleBackgroundColor,
    });
}

/**
 * 调起快币充值半屏
 * https://yoda.corp.kuaishou.com/docs/bridge/?kpn=KUAISHOU&namespace=pay&name=startHalfScreenRecharge
 */
export function depositCoin(data: {
    targetDou: number | undefined;
    source: string | undefined;
}) {
    originalInvoke('pay.startHalfScreenRecharge', data, (res) => {
        // 这个 callback 会被回调两次
        /**
         * result = 1: 面板拉起
         * result = 2: 面板关闭
         */
        // const result = res?.result;
    });
}

/**
 * 调起现金支付收银台
 * https://yoda.corp.kuaishou.com/docs/bridge/?kpn=KUAISHOU&namespace=pay&name=startKspayOrderPrepay
 */
export function depositLLch(data: { merchantId: string; outOrderNo: string }) {
    return new Promise((resolve, reject) => {
        originalInvoke('pay.startKspayOrderPrepay', data).then(
            (value) => {
                resolve(value);
            },
            (reason) => {
                reject(reason);
            },
        );
    });
}

/**
 * 设置日历
 * @param params
 *
 */
export function changeCalendar(params: FeedChangeEventForCalendarParams) {
    if (import.meta.env.DEV && !isInWebview()) {
        // 解决开发时浏览器环境 Promise 不 resolve 的问题
        return Promise.resolve(true);
    }

    return originalInvoke('feed.changeEventForCalendar', params);
}

/**
 * 判断是否有日历权限
 */
export const authorizationStatusForCalendar = async () => {
    return await originalInvoke('feed.authorizationStatusForCalendar');
};

/**
 * 复制到剪切板
 * @param text
 *
 */
export async function copy(text: string) {
    await originalInvoke('webview.setClipBoard', { text });
    showToast('复制成功');
}

// plc
export async function TunaPLCUpdateServiceLink(params: {
    metaText: string;
    subtype: string;
    extData?: string;
    topics?: string[];
    atFriends?: [
        {
            userName: string;
            userId: string;
        },
    ];
    serviceId: string;
    entryId: string;
}) {
    return new Promise((resolve, reject) => {
        try {
            originalInvoke('TunaPLC.UpdateServiceLink', params)
                .then((res) => {
                    resolve(res);
                })
                .catch((err) => {
                    reject(err);
                });
        } catch {
            reject(new Error('挂载奖品失败'));
        }
    });
}

// 关闭webview
export function closeWebview() {
    originalInvoke('webview.close');
}

/**
 * 获取当前时间
 */
export const getTime = async () => {
    if (import.meta.env.DEV && !isInWebview()) {
        // 解决开发时浏览器环境 Promise 不 resolve 的问题
        return Date.now();
    }

    try {
        const { serverTimeStamp: currentTime } = await originalInvoke(
            'system.getServerTime',
        );
        return currentTime;
    } catch {
        return Date.now();
    }
};

/**
 * 打开电商选择地址页面
 */
export const openMerchantPage = (url: string) => {
    // 半屏打开电商
    originalInvoke('merchant.startRouter', {
        url: decodeURIComponent(url),
        pageId: '',
    });
};

/**
 * 打开本地相册选择照片
 */
export const selectImage = (data: {
    count?: number;
    sourceType?: string[];
    maxFileSize?: number;
    maxWidth?: number;
    minWidth?: number;
    maxHeight?: number;
    minHeight?: number;
    title?: string;
    confirmTitle?: string;
    minFileSize?: number;
    minSizeAlert?: string;
    minHeightWidthAlert?: string;
    selectedList?: string[];
    showNextButtonTakePic?: boolean;
}) => {
    return new Promise((resolve, reject) => {
        originalInvoke('post.selectImage', data, (res) => {
            if (res.result === 1) {
                resolve(res.data);
            } else {
                reject(res);
            }
        });
    });
};

/**
 * 打开loading
 */
export const hideLoading = () => {
    return new Promise((resolve, reject) => {
        originalInvoke('ui.hideLoading').then((res) => {
            if (res.code === 1) {
                resolve(res.data);
            } else {
                reject(res);
            }
        });
    });
};

/**
 * 关闭loading
 */
export const showLoading = () => {
    return new Promise((resolve, reject) => {
        originalInvoke('ui.showLoading').then((res) => {
            if (res.code === 1) {
                resolve(res.data);
            } else {
                reject(res);
            }
        });
    });
};

/**
 * 原生 toast
 */
export const showNativeToast = (text: string) => {
    toastDestroy();
    return originalInvoke('ui.showToast', {
        type: 'normal',
        text,
    });
};

// /**
//  * 关闭直播
//  */
// export const stopLivePlay = () => {
//     originalInvoke('live.stopLivePlay');
// };

/**
 * 发布视频
 */

export const postVideo = (
    param: PostPostVideoParams['param'],
    callback: (arg: any) => void,
) => {
    originalInvoke(
        'post.postVideo',
        {
            param,
        },
        (res) => {
            callback(res);
        },
    );
};

/**
 * 跳转到发布页
 */
export const goPublish = () => {
    openNativePage('kwai://post');
};

/**
 * 跳转到开播页
 */
export const goLive = () => {
    openNativePage('kwai://post?tab=live');
};

/**
 * 跳转个人P页
 */
export const goProfilePage = () => {
    openNativePage('kwai://myprofile');
};

// 调用此方法，使分享海报弹框关闭并销毁
export async function dismissAllShareDialog() {
    try {
        // @ts-expect-error
        await invoke('social.dismissAllShareDialog');
    } catch {}
}

/**
 * 在 iOS 上控制侧滑返回
 */
export async function setSlideBack(enabled: boolean) {
    if (isIOS()) {
        try {
            await originalInvoke('webview.setSlideBack', { enabled });
        } catch (error) {
            console.warn('setSlideBack error', error);
        }
    }
}
