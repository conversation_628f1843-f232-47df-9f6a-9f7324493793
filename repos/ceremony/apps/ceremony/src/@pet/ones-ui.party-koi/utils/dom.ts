/**
 * 判断元素是否可见
 */
export function isElementVisible(element: HTMLElement | null) {
    if (!element || !(element instanceof Element)) {
        return false;
    }

    if (!document.body.contains(element)) {
        return false;
    }

    const style = window.getComputedStyle(element);

    if (
        style.display === 'none' ||
        style.visibility === 'hidden' ||
        style.opacity === '0'
    ) {
        return false;
    }

    const rect = element.getBoundingClientRect();
    if (rect.width === 0 && rect.height === 0) {
        return false;
    }

    let parent = element.parentElement;
    while (parent) {
        const parentStyle = window.getComputedStyle(parent);
        if (
            parentStyle.display === 'none' ||
            parentStyle.visibility === 'hidden' ||
            parentStyle.opacity === '0'
        ) {
            return false;
        }
        parent = parent.parentElement;
    }

    return true;
}
