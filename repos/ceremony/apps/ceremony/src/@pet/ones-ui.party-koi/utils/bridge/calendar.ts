import { isBridgeError } from '@yoda/bridge';
import { nameSlice } from '@alive-ui/actions';
import {
    AUTHOR_LLPE_PREHEAD,
    CALENDAR_EVENTS,
    DENY_CALENDAR_PERMISSION,
} from '../storage-key';
import {
    authorizationStatusForCalendar,
    changeCalendar,
    getTime,
} from './index';
import { isInNebula } from '@/@pet/yau.core';

/** 配置的静态内容 */
interface Config {
    /** 跳转链接 */
    links: {
        /** 预约短链 - 快手 APP */
        reserveLinkKwai: string;
        /** 预约短链 - 极速版 */
        reserveLinkNebula: string;
    };
    /** 日历标题 */
    titleTemplate: string;
    /** 日历备注 */
    noteTemplate: string;
}

/**
 * 将主播开奖时间写入日历
 */
export const reserveAuthorLLpeTime = async (
    params: {
        /** 主播id */
        authorId: number;
        /** 轮次 */
        round: number;
        /** 开奖时间 */
        openTime: number;
        /** 主播用户名 */
        authorName: string;
    },
    config: Config,
) => {
    if (!(await checkPermission())) {
        console.warn('reserveAuthorLLpeTime checkPermission failed');
        return;
    }

    try {
        const authorName = nameSlice(params.authorName, 5);
        // 判断是否是极速版
        const buttonLink = isInNebula()
            ? config.links.reserveLinkNebula
            : config.links.reserveLinkKwai;
        await addCalendar(
            {
                title: config.titleTemplate.replace(
                    '{author}',
                    authorName || '主播',
                ),
                startDay: `${+params.openTime + 2 * 60 * 1000}`,
                duration: `${60 * 60 * 1000}`,
                note: config.noteTemplate.replace('{link}', buttonLink || ''),
                endDay: `${+params.openTime + 60 * 60 * 1000}`,
            },
            `PREHEAD_AUTHOR_${params.authorId}`,
        );
        console.debug('preheadAuthorLLpeTime success');
    } catch (e) {
        // 写日历失败 将信息缓存到本地
        localStorage.setItem(
            AUTHOR_LLPE_PREHEAD,
            JSON.stringify({ ...params }),
        );
        console.warn('preheadAuthorLLpeTime error:', e, JSON.stringify(e));

        if (isBridgeError(e)) {
            // 用户拒绝授权
            localStorage.setItem(DENY_CALENDAR_PERMISSION, 'true');
        }
    }
};

/**
 * 将主播开奖时间写入日历
 * 从本地缓存取参数
 */
export const reserveAuthorLLpeTimeFromCache = async (config: Config) => {
    const params = localStorage.getItem(AUTHOR_LLPE_PREHEAD);
    if (params) {
        localStorage.removeItem(AUTHOR_LLPE_PREHEAD);

        if (!(await checkPermission())) {
            console.warn(
                'reserveAuthorLLpeTimeFromCache checkPermission failed',
            );
            return;
        }

        try {
            const { openTime, authorName, authorId, round } =
                JSON.parse(params);
            const now = await getTime();
            if (+openTime >= now) {
                await reserveAuthorLLpeTime(
                    {
                        authorId,
                        round,
                        openTime,
                        authorName,
                    },
                    config,
                );

                console.debug('reserveAuthorLLpeTime success');
            }
        } catch (e) {
            console.warn('reserveAuthorLLpeTime error:', JSON.stringify(e));
        }
    }
};

/**
 * 检查用户日历授权
 * 如果用户未授权且未拒绝过 则申请授权并尝试写日历
 * 如果用户未授权且拒绝过 则阻止写日历操作
 */
const checkPermission = async (): Promise<boolean> => {
    try {
        // 检查当前是否有日历权限
        await authorizationStatusForCalendar();

        // 用户授予了日历权限
        localStorage.removeItem(DENY_CALENDAR_PERMISSION);
        return true;
    } catch (error) {
        console.warn(
            'authorizationStatusForCalendar error',
            JSON.stringify(error),
        );

        if (isBridgeError(error)) {
            // 用户未授予日历权限
            if (localStorage.getItem(DENY_CALENDAR_PERMISSION) === 'true') {
                // 用户拒绝过 不再申请写日历
                console.debug('calendar permission denied');
                return false;
            }
        }
    }

    return true;
};

/**
 * 添加日历
 * @param CalendarParams
 * @param eventKey 预约标记key，便于记忆
 * @returns
 */
const addCalendar = (
    calendarParams: Omit<Params, 'method'>,
    eventKey: string,
) => {
    return setCalendar(
        {
            ...calendarParams,
            method: 'add',
        },
        eventKey,
    );
};

/**
 * 设置日历
 * @param params 写日历参数
 * @param eventKey 事件的唯一标识
 * @returns error ｜ eventId
 */
const setCalendar = (params: Params, eventKey: string) => {
    return new Promise(async (resolve, reject) => {
        try {
            // 读取本地缓存
            const eventMap = JSON.parse(
                localStorage.getItem(CALENDAR_EVENTS) || '{}',
            );

            if (params.method === 'add') {
                // 新增日程
                const result = await changeCalendar({
                    method: 'add',
                    event: {
                        type: 1,
                        title: params.title,
                        url: params.url,
                        note: params.note,
                        startDay: params.startDay,
                        endDay: params.endDay,
                        duration: params.duration,
                    },
                });

                console.debug('changeCalendar add success', result);

                const { eventId } = result as { eventId: string };

                if (!eventId) {
                    console.warn('invalid eventId', eventId);
                    return;
                }

                // 将事件缓存到本地
                eventMap[eventKey] = eventId;
                localStorage.setItem(CALENDAR_EVENTS, JSON.stringify(eventMap));
                resolve(eventId);
                return;
            }

            // 移除日程
            const eventId = eventMap[eventKey];
            if (!eventId) {
                // 未找到 eventId
                resolve({});
                return;
            }

            const result = await changeCalendar({
                method: 'delete',
                event: {
                    type: 1,
                    eventId,
                },
            });

            const { eventId: resultEventId } = result as { eventId: string };

            if (!resultEventId) {
                delete eventMap?.[eventKey];
                localStorage.setItem(CALENDAR_EVENTS, JSON.stringify(eventMap));
                resolve(result);
            }
        } catch (e) {
            reject(e);
        }
    });
};

interface Params {
    /**
     * 操作类型
     * 写日历 or 移出日历
     */
    method: 'add' | 'delete';
    /**
     * 标题
     */
    title?: string;
    /**
     * 备注内容
     */
    note?: string;
    /**
     * 日程开始时间
     */
    startDay?: string;
    /**
     * 日程结束时间
     */
    endDay?: string;
    /**
     * 跳转链接
     */
    url?: string;
    /**
     * 日程的唯一标识
     */
    eventId?: string;
    /**
     * 日程持续时间
     */
    duration?: string;
}
