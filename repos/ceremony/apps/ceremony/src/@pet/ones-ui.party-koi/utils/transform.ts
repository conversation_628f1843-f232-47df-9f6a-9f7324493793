import dayjs from 'dayjs';
import { TAB_DISABLED_TOAST } from '../const/index';
import { formatSeconds } from './time';
import type { SpecialTabItem, RoundInfoView } from '../schema';

/**
 * 将轮次信息格式化为 Tab 数组
 */
export function transformRoundInfo(
    roundInfo: RoundInfoView[],
    currentTime: number = Date.now(),
) {
    return roundInfo.map((item) => {
        return {
            title: item?.note ?? '第一轮抽奖',
            subTitle: `${dayjs(item?.startTime ?? currentTime).format('MM.DD')}-${dayjs(item?.endTime ?? currentTime).format('MM.DD')}`,
            value: item?.round ?? 1,
            // 加载态或活动未开始时禁用点击
            disabled: currentTime < (item?.startTime ?? currentTime),
            toast: item.round > 1 ? TAB_DISABLED_TOAST : '',
        } satisfies SpecialTabItem;
    });
}

/**
 * 将剩余秒数格式化为倒计时文案
 * @param seconds 剩余的秒数
 * @returns buttonText 按钮文案
 */
export function transformButtonText(seconds: number) {
    if (seconds <= 0) {
        // 更新按钮状态
        return '已开奖';
    }

    if (seconds < 1 * 60) {
        // 小于 1 分钟 展示 `1分钟后开奖`
        return '1分钟后开奖';
    }

    if (seconds < 1 * 60 * 60) {
        // 剩余时间小于 1 小时 展示 `{min}分钟后开奖`
        return `${formatSeconds(seconds, 'm')}分钟后开奖`;
    }

    if (seconds < 24 * 60 * 60) {
        // 剩余时间小于 1 天 展示 `{hour}小时后开奖`
        return `${formatSeconds(seconds, 'H')}小时后开奖`;
    }

    // 剩余时间大于 1 天 展示 `{day}天后开奖`
    return `${formatSeconds(seconds, 'D')}天后开奖`;
}
