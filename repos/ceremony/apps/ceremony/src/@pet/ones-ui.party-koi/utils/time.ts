import duration from 'dayjs/plugin/duration';
import dayjs from 'dayjs';

dayjs.extend(duration);

/**
 * 目标时间到现在的时间差
 * @param targetTime 目标时间
 * @param currentTime 当前时间 (默认使用系统时间)
 * @returns {[number, string]} [时间差(天), '小时:分钟']
 */
export function timeAgo(
    targetTime: string | number | Date,
    currentTime: string | number | Date = new Date(),
): [number, string] {
    const now = dayjs(currentTime);
    const target = dayjs(targetTime);

    // 距此刻的时间差(天)
    const days = target.diff(now, 'day') + 1;
    // 距此刻的时间(小时:分钟)
    const time = target.format('HH:mm');
    return [days, time];
}

/**
 * 将秒数格式化为指定格式
 * @param [format='mm:ss'] 指定格式
 */
export function formatSeconds(seconds: number, format = 'mm:ss') {
    return dayjs.duration(seconds, 'seconds').format(format);
}

/**
 * @description 执行setTimeout后自清除函数
 */
export function mySetTimeout(
    callback: (...args: any[]) => void,
    delay: number,
    ...args: any[]
): number {
    const timeoutId: number = window.setTimeout(() => {
        callback(...args);
        clearTimeout(timeoutId);
    }, delay);
    return timeoutId;
}

/**
 * @description setTimeout模拟setInterval
 */
export class MyInterval {
    private intervalTimer: number | null = null;

    interval(func: () => void, wait: number) {
        const inter = () => {
            this.intervalTimer = window.setTimeout(inter, wait);
            func.call(null);
        };
        this.intervalTimer = window.setTimeout(inter, wait);
    }

    clearTimer() {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        window.clearTimeout(this.intervalTimer!);
        this.intervalTimer = null;
    }
}
