/**
 * 是否低端机
 * @returns true | false
 */
export const isLowDevice = () => {
    const [, ISLP = ''] = window.navigator.userAgent.match(/ISLP\/(\d)/) || [];

    return +ISLP === 1;
};

/**
 * 是否进入Crash兜底策略
 * @returns true | false
 */
export const isICFO = () => {
    const [, ICFO = ''] = window.navigator.userAgent.match(/ICFO\/(\d)/) || [];

    return +ICFO === 1;
};

/**
 * 24小时内崩溃次数
 * @returns number
 */
export const countsCT = () => {
    const [, CT = ''] = window.navigator.userAgent.match(/CT\/(\d)/) || [];

    return +CT;
};

/**
 * 降级判断封装,业务直接调用这个函数就行
 * @returns true | false
 */
export const isDownGrade = () => {
    return countsCT() > 1 || isICFO() || isLowDevice();
};
