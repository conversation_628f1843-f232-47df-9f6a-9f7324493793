/**
 * 关注操作hook
 */

import { invoke, isBridgeError } from '@yoda/bridge';
import { query } from '@alive-ui/actions';
import { getPageName, radarCustomEvent } from '../logger/index';
import { FOCUS_ERROR, FOCUS_SUCC } from '../const/log';
import {
    NORMAL_FOLLOW_REFER,
    UNION_FOLLOW_REFER,
    ACTIVITY_ID,
    NORMAL_FOLLOW_REFER_ACTIVITY_ID,
    UNION_FOLLOW_REFER_ACTIVITY_ID,
    entrySrc,
} from '../const/index';
import type { FocusRankType } from '../const/index';

interface ParamsRaw {
    /** 被关注的用户 id */
    userId: string;
}

interface ContextRaw {
    /** 关注栈 */
    followRefer: number;
    /** 关注需要的活动 id */
    focusActivityId: string;
    /** 锦鲤活动 id */
    activityId: string;
    /** 服务端埋点: 始终传递主播 id */
    fromCreatorId: string;
    /** 服务端埋点: 当前进行到的轮次 */
    round: number;
    /** 服务端埋点: 页面名称 跟随埋点的页面标识 */
    pageName: string;
    /** 服务端埋点: entry_src */
    pageFrom: string;
    /** 服务端埋点: 场景枚举 */
    rankType: FocusRankType;
    /** 服务端埋点: 位置 1-2 2-3 没有则传空 */
    rankIndex: string;
    /** 服务端埋点: 奖品 id */
    prizeId: string;
}

type Params = ParamsRaw & ContextRaw;

export type Context = Pick<
    ContextRaw,
    | 'fromCreatorId'
    | 'round'
    | 'rankType'
    | 'rankIndex'
    | 'prizeId'
    | 'pageName'
    | 'pageFrom'
    | 'activityId'
>;
/**
 * 执行关注
 */
const focus = async (params: Params) => {
    const { userId, followRefer } = params;
    try {
        // 新方式
        const res = await invoke('social.setFollowActivityId', {
            activityId: params.focusActivityId,
        });

        await invoke('social.followUser', {
            userId,
            following: true,
            followRefer,
            info: {
                bizCustomParams: JSON.stringify({
                    round: params.round,
                    fromCreatorId: params.fromCreatorId,
                    activityId: params.activityId,
                    pageName: params.pageName,
                    pageFrom: params.pageFrom,
                    rankType: params.rankType,
                    rankIndex: params.rankIndex,
                    prizeId: params.prizeId,
                }),
            },
            // @ts-expect-error - 隐藏所有端上的 Toast 提示
            showErrorToast: false,
        });

        radarCustomEvent({
            category: FOCUS_SUCC,
            name: '关注bridge调用成功',
            extra_info: JSON.stringify({
                path: location.pathname,
                res,
                ...params,
            }),
        });
    } catch (e) {
        if (isBridgeError(e)) {
            console.warn('focus error', JSON.stringify(e));

            switch (e.code) {
                case 109: // 账号异常 - 登录问题
                case 6002: // 账号异常 - 登录问题
                case 705: // 账号异常 - 登录问题
                case 6001: // token验证失败，一般是黑产
                case 405: // 被关注对象拉黑
                case 305: // 关注拉黑的对象
                case 108: // 账号异常 - 被封禁
                case 64: // 账号异常 - 请到个人主页自助解除
                case 63: // 账号异常 - 需要绑定手机号
                case 115_003: // 移除粉丝次数超限
                case 50: // 验签失败，一般是黑产
                case 15: // uri限流
                case 3: // 异常请求，没有版本号信息
                case -1: // 未知异常
                case 302: // 关注人 visitorId和toUserId相同。自己不能关注自己。
                case 110: // 用户不存在（查toUserId对应的userCache没查到）
                case 21: // 传参异常
                case 125_014: // 非 YODA 环境下调用 Bridge
                    radarCustomEvent({
                        category: FOCUS_ERROR,
                        name: '关注bridge业务原因fail，枚举内原因',
                        extra_info: JSON.stringify({
                            reason: e?.message || '',
                            code: e.code,
                            path: location.pathname,
                            ...params,
                        }),
                    });

                    // 直接抛出默认的错误消息
                    throw new Error('关注失败');

                case 301: // 总关注数超限
                case 304: // 今天关注次数超限
                default:
                    radarCustomEvent({
                        category: FOCUS_ERROR,
                        name: '关注bridge业务原因fail，枚举外的原因',
                        extra_info: JSON.stringify({
                            reason: e?.message || '',
                            code: e.code,
                            path: location.pathname,
                            ...params,
                        }),
                    });

                    const MESSAGES: Record<number, string> = {
                        301: '关注失败，关注数量超限',
                        304: '关注失败，今日关注次数超限',
                    };

                    throw new Error(MESSAGES?.[e.code] ?? '关注失败');
            }
        }
    }
};

/**
 * 普通关注
 */
export const normalFocus = async (userId: string, context: Context) =>
    await focus({
        userId,
        fromCreatorId: context.fromCreatorId,
        round: context.round,
        focusActivityId: NORMAL_FOLLOW_REFER_ACTIVITY_ID,
        followRefer: NORMAL_FOLLOW_REFER,
        activityId: context.activityId,
        pageName: context.pageName,
        pageFrom: query.entry_src,
        rankType: context.rankType,
        rankIndex: context.rankIndex,
        prizeId: context.prizeId,
    });
//

/**
 * 联合关注
 */
export const unionFocus = async (userId: string, context: Context) =>
    await focus({
        userId,
        round: context.round,
        fromCreatorId: context.fromCreatorId,
        focusActivityId: UNION_FOLLOW_REFER_ACTIVITY_ID,
        followRefer: UNION_FOLLOW_REFER,
        activityId: ACTIVITY_ID,
        pageName: getPageName(),
        pageFrom: query.entry_src,
        rankType: context.rankType,
        rankIndex: context.rankIndex,
        prizeId: context.prizeId,
    });
