import { getLocal<PERSON><PERSON><PERSON>Key as withPrefix } from './storage';

/** 保存新手引导状态 */
export const USER_INDEX_GUIDE = withPrefix('USER_INDEX_GUIDE');

/** 主播开奖写日历 */
export const AUTHOR_LLPE_PREHEAD = withPrefix('AUTHOR_LLPE_PREHEAD');

/** 将所有写过的日程缓存在本地 */
export const CALENDAR_EVENTS = withPrefix('CALENDAR_EVENTS');

/** 用户是否拒绝授予日志权限 */
export const DENY_CALENDAR_PERMISSION = withPrefix('DENY_CALENDAR_PERMISSION');

/** 用户挽留弹窗 */
export const USER_RETENTION_DIALOG = withPrefix('USER_RETENTION_DIALOG');

// 强提醒弹窗本地缓存key
export const REMIND_TEXT_LOCAL_STORAGE = withPrefix(
    'REMIND_TEXT_LOCAL_STORAGE',
);

// 宠粉官强提醒弹窗本地缓存key
export const REMIND_SPONSOR_LOCAL_STORAGE = withPrefix(
    'REMIND_SPONSOR_LOCAL_STORAGE',
);

// 发布视频弹窗本地缓存key
export const PUBLISH_VIDEO_DIALOG_LOCAL_STORAGE = withPrefix(
    'PUBLISH_VIDEO_DIALOG_LOCAL_STORAGE',
);

// 轮询查询视频发布状态key
export const POLLING_PUBLISH_VIDEO_STATUS_STORAGE = (str: string) =>
    withPrefix('POLLING_PUBLISH_VIDEO_STATUS_STORAGE') + str;
