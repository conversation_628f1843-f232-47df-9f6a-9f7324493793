<template>
    <div class="party-koi">
        <YodaImage
            v-if="conf4Tab?.partyKoi?.koiBg"
            class="default-bg pos-abs-lt-0"
            :src="conf4Tab?.partyKoi?.koiBg"
            @error="(e: Event) => onYodaImageError(e, 'l1')"
        />
        <VideoPlay
            v-if="!isDowngrade && conf4Tab?.partyKoi?.koiVideo"
            class="pos-abs-lt-0"
            object-fit="fill"
            not-click
            autoplay
            :video-url="[
                {
                    url: conf4Tab?.partyKoi?.koiVideo,
                },
            ]"
        >
        </VideoPlay>
        <div class="content">
            <RedBox :red-packet-info="redPacketInfo" />
            <!-- <RedBox :red-packet-info="redPacketInfo" /> -->
        </div>
        <div
            v-click-log="{
                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                params: { btn_type: 'VIEW_ALL' },
            }"
            v-show-log="{
                action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                params: { btn_type: 'VIEW_ALL' },
            }"
            class="more flex-center"
            @click="goRedPacketPage({ activityId: koiActivityId })"
        >
            <div class="red-packet-icon"></div>
            <span> 抢更多红包 </span>
            <Right class="party-koi_icon" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, provide, ref } from 'vue';
import { storeToRefs } from 'pinia';
import YodaImage from '@pet/yoda.image/img.vue';
import useKconfStore from '@pet/ones-use.useKconf';
import { useDowngrade } from '@pet/ones-use.useDowngrade';
import { is4Tab } from '@pet/ones-use.is4Tab';
import { goRedPacketPage } from '@pet/ones-use.goRedPacketPage';
import VideoPlay from '@pet/ones-ui.video-play/index.vue';
import { Right, Refresh } from '@alive-ui/icon';
import { isOutLiveRoom, Report } from '@alive-ui/actions';
import RedBox from './module-redpacket.vue';
import { baseModel } from './models/base';
import { useAttend } from './hooks/use-attend';
import { SYMBOL_ATTEND, SYMBOL_REDIRECT } from './const/symbols';
import { AttendEntrySrc, FocusRankType, isPLC, round } from './const';
const props = defineProps<{
    koiData: any;
    koiActivityId: string;
}>();

const redPacketInfo = computed(() => {
    // 提取所需数据
    const { commonRankingView, hotRankingView } = props.koiData.userHomeView;

    const mergedRankingView = {
        ...commonRankingView,
        ordinaryRankingList: [
            // hotRankingView 优先级比较高，放在前面
            ...(hotRankingView?.rankingList || []), // 热榜数据
            ...(commonRankingView?.ordinaryRankingList || []), // 普通榜数据
        ],
    };

    return mergedRankingView;
});

const { conf4Tab } = storeToRefs(useKconfStore());

const isDowngrade = useDowngrade();
const roundCur = ref(round); // 轮次信息，接口也可更新
const { jumpKwaiLink } = baseModel();
const { attend } = useAttend({
    round: roundCur,
    // eslint-disable-next-line no-nested-ternary
    entrySrc: is4Tab
        ? 'cny_koi_51'
        : isOutLiveRoom
          ? 'cny_koi_50'
          : 'cny_koi_52',
    rankType: FocusRankType.HalfpageRecommandArea,
    activityId: props.koiActivityId,
});
provide(SYMBOL_ATTEND, attend);
provide(SYMBOL_REDIRECT, jumpKwaiLink);

const onYodaImageError = (e: Event, level: string) => {
    Report.biz.error('锦鲤红包背景yoda-image异常', {
        error: e,
        level,
    });
};
</script>
<style>
@import './style/ceremony/styles.css';
@import './style/ceremony/vars.css';
</style>
<style lang="less" scoped>
.party-koi {
    // 盒子模型为border-box
    width: 414px;
    height: 245px;
    padding: 0 30px;
    position: relative;
    // overflow: hidden;
    &_icon {
        margin-left: 3px;
        width: 10px;
        height: 10px;
    }
}
.content {
    margin-top: 30px;
}
.more {
    width: 128px;
    height: 36px;
    font-size: 12px;
    color: var(--button-text, #4f1800);
    font-family: 'PingFang SC';
    font-weight: 500;
    margin: 0 auto;
    margin-top: 6px;
    position: relative;
    z-index: 2;
    background: url('./assets/more_bg.png') center / 100% no-repeat;
}
.red-packet-icon {
    width: 28px;
    height: 28px;
    flex-shrink: 0;
    background: url(./assets/red-packet.png) center / 100% no-repeat;
    margin-right: 2px;
}
.default-bg {
    --y-img-height: 245px;
    --y-img-width: 414px;
}

.pos-abs-lt-0 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
</style>
