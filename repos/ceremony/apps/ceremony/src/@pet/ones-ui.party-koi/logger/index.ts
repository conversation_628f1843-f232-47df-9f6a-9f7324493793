import {
    updateCurrentUrlPackage,
    sendClickLogImmediately,
    sendShowLogImmediately,
    radar,
    weblog,
} from '@gundam/weblogger';
// import { MODEL_ASSIGNMENT } from '../const/log';
import { getTheme } from '../utils/theme';
import { layoutType } from '../const/index';

// radar 事件维度类型
interface EventDimension {
    name: string;
    category?: string | string[];
    event_type?: string | string[];
    src?: string;
    result_type?: string;
    message?: string;
    extra_info?: object | string;
    yoda_version?: string;
    webview_type?: string;
}

// radar 自定义上报名称
// api 上报包含网络异常情况
export const API_REQUEST = 'api-request';
// error 页面上报
export const ERROR_PAGE = 'error-page-show';
// common error 上报
export const COMMON_ERROR = 'common-error';
// 提现上报
export const LLCH = 'llch-draw-result';
// 分享上报
export const SHARE = 'share-direct-result';
// 转换json失败
export const PARSE_ERROR = 'parse-json-error';

// 活动名称参数
export const activity_name = '25livecnyjinli';

// 盛典还是CNY
export const activity_status = getTheme();

// 公共参数
const commonLogParams = {
    activity_name,
    // 枚举型,区分活动状态（为盛典orcny)0砖石 1盛典 2cny
    activity_status,
};

// 当前 Log 页面名称
let pageName = '';
export const getPageName = () => pageName;
// 初始化页面参数
export const initWebloggerParams = (name: string) => {
    pageName = name || 'NOT_REGISTERED_PAGE';
};

export type LoggerCommonType = {
    [key: string]: string | number;
};

type EventParams = {
    action: string;
    params?: any;
};

// 一：用户/主播主页， 路由：user-index/author-index
export type IndexUrlParamsDefaultType = {
    // 主播侧 ， 用户侧
    view_type: 'anchor' | 'normal_user';
    // 枚举型,区分当前抽奖阶段，activity_status为2时才有值
    cj_stage?: 'first' | 'second';
    // 1活动报名页  2奖品录入页  3录入完成页
    author_page_status?: 1 | 2 | 3;
    // 非枚举文本型,区分活动页面来源，需严格按照对应值上报
    entry_src?: string;
    // 非枚举文本型,plc的id,非必报,entry_src为plc时才有值
    plc_id?: string;
    // 非枚举文本型,视频id,非必报,entry_src为plc时才有值
    photo_id: string;
    // 非枚举文本型,主播id,非必报,entry_src为直播间时才有值
    anchor_id?: string;
    // 非枚举文本型,直播间id,非必报,entry_src为直播间时才有值
    live_room_id?: string;
};

// 二：设置奖励页, 注意entry_src不是来自query里, 路由：author-set-form
export type setFormUrlParamsType = {
    // 枚举型,区分当前页面的打开来源1 //活动主页 2 //视频发布器
    entry_src: 1 | 2;
    // 枚举型,区分当前抽奖阶段
    cj_stage: 'first' | 'second';
};

// 三：用户抽奖记录页, 路由：user-own-record
export type userRecordUrlParamsType = {
    url: string;
    // 枚举型,区分当前抽奖阶段，activity_status为2时才有值
    cj_stage: 'first' | 'second';
};

// 四： 用户开奖页 路由：user-pz
export type pzUrlParamsType = {
    url: string;
    entry_src: string;
    kj_status: 'before' | 'after';
    creator_uid: string;
    zjg_uid: string;
    //   0中奖   1未中奖 2未参与
    open_result: 0 | 1 | 2;
    // 当前轮次
    cj_stage: 'first' | 'second';
};

// 五：钱包页， 路由user-wallet
export type walletUrlParamsType = {
    url: string;
};
// 六：主播抽奖记录页, 路由：author-all-record
export type authorRecordUrlParamsType = {
    // 枚举型,区分当前抽奖阶段，activity_status为2时才有值
    cj_stage: 'first' | 'second';
    user_type: 'AUTHOR' | 'USER';
};

// 手动上报：页面曝光
export const sendPageExposureLog = (urlParams: LoggerCommonType) => {
    // 更新公共参数
    updateCurrentUrlPackage(
        pageName,
        Object.assign({ ...commonLogParams }, { ...urlParams }),
    );
    // 上报 Pv
    weblog?.sendImmediately('PV', {
        coPage: +layoutType === 3,
    });
};

// 手动上报：元素点击事件
export const clickElementLog = (
    elementParams: EventParams,
    urlParams?: LoggerCommonType,
) => {
    if (urlParams)
        updateCurrentUrlPackage(
            pageName,
            Object.assign({ ...commonLogParams }, { ...urlParams }),
        );
    // 上报元素点击埋点
    sendClickLogImmediately(elementParams);
};

// 手动上报：元素曝光事件
export const exposureElementLog = (
    elementParams: EventParams,
    urlParams?: LoggerCommonType,
) => {
    if (urlParams)
        updateCurrentUrlPackage(
            pageName,
            Object.assign({ ...commonLogParams }, { ...urlParams }),
        );
    // 上报元素曝光埋点
    sendShowLogImmediately(elementParams);
};

/**
 * 雷达自定义事件上报
 */
export const radarCustomEvent = (
    dimension: EventDimension,
    values: object = {},
) => {
    radar?.event(dimension, values);
};

// 埋点说明：接口返回后，在model里直接调用sendPageExposureLog进行pv曝光
// 有两个页面没有埋点： user-all-recored/author-relate-video

/**
 *
 * @param message  页面名称+当前函数名
 * @param e 错误对象
 */
// export const logModelJSError = (message: string, e: any) => {
//     radarCustomEvent({
//         name: MODEL_ASSIGNMENT,
//         event_type: 'error',
//         result_type: 'js-error',
//         message,
//         extra_info: JSON.stringify(e),
//     });
// };
