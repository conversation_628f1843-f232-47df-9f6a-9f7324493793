<script lang="ts" setup>
import { nextTick, provide, watch } from 'vue';
import { useServerTimeNow } from '@pet/yau.yoda';
import { transformButtonText } from './utils/transform';
import { useRedpacketModel } from './models/use-redpacket';
import { useTicker } from './hooks/use-ticker';
import { SYMBOL_ADD_TICKER } from './const/symbols';
import RedpacketGroup from './components/redpacket-group.vue';

// 组件属性定义
const props = defineProps<{
    redPacketInfo: any;
}>();
// 模型和基础数据
const { itemTable, onSetup, canAnimate, animate, animationDownGrade, $reset } =
    useRedpacketModel();
onSetup(props.redPacketInfo);
console.log('itemTable', itemTable.value);

// 引入时间服务
const { now: DateNow } = useServerTimeNow();
// 定时器功能
const { add: addTicker, dispose: disposeAllTicker } = useTicker({
    now: () => DateNow.value.getTime(),
});
// 注入全局定时器
provide<ReturnType<typeof useTicker>['add']>(SYMBOL_ADD_TICKER, addTicker);

// 中间卡片大一点，两边卡片小一点
const getSize = (index: number) => {
    if (index === 1) return 'normal';
    return 'small'; // 两边卡片小一点
};

// 尝试消费红包
const onTryConsume = async (
    position: [number, number],
    animating: Promise<void>,
) => {
    const { beforeAnimate, afterAnimate } = animate(position);
    const nextRedpacket = beforeAnimate();

    console.debug('onTryConsume', nextRedpacket);

    if (nextRedpacket) {
        // 有下一张红包 执行动画
        await animating;
        afterAnimate();

        if (nextRedpacket.join && !nextRedpacket.openLLpe) {
            addTicker({
                endTime: nextRedpacket.openTime ?? 0,
                callback: ({ seconds }) => {
                    nextRedpacket.buttonText = transformButtonText(seconds);
                },
            });
        }
    }
};

watch(
    () => itemTable.value.length,
    (newLen, preLen) => {
        if (!newLen) {
            // 针对刷新场景 清空之前的所有定时器
            disposeAllTicker();
        }

        const list = itemTable.value.slice(preLen, newLen);

        list.forEach((row: any[]) => {
            row.forEach((group) => {
                // 取第一个红包 检查当前状态
                const [{ info }] = group;

                if (info && !info.openLLpe && info.join) {
                    // 已参与且未开奖 创建倒计时任务
                    addTicker({
                        endTime: info.openTime ?? 0,
                        callback: ({ seconds }) => {
                            info.buttonText = transformButtonText(seconds);
                        },
                    });
                }
            });
        });
    },
    { immediate: true },
);

watch(
    () => props.redPacketInfo,
    (newVal) => {
        $reset();
        nextTick(() => {
            // 延迟刷新数据
            onSetup(newVal);
        });
    },
    { immediate: true },
);
</script>

<template>
    <div class="content-wrapper">
        <template v-for="(row, rowIdx) of itemTable" :key="rowIdx">
            <RedpacketGroup
                v-for="(group, groupIdx) of row"
                :key="groupIdx"
                :size="getSize(groupIdx)"
                :cards="group"
                :can-animate="
                    group.some((card: any) => !card.info) &&
                    canAnimate(group[0].position)
                "
                :disable-animate="animationDownGrade"
                @animate="onTryConsume"
            />
        </template>
    </div>
</template>

<style lang="less" scoped>
.module-redpacket {
    &[data-no-title='true'] {
        .card-container-heading {
            // 无标题时 不需要内边距
            padding: 0;
        }
    }
}

.status-wrap {
    --adapt-empty-status-icon-color: var(
        --jinli-user-main-color,
        rgba(107, 32, 32, 0.6)
    );

    padding: 30px 0; // 默认的上下 padding 撑开空模块
}

.content-wrapper {
    display: flex;
    &::-webkit-scrollbar {
        display: none; // 隐藏滚动条
    }
}

.redpacket-group {
    @gap: 14px; // 红包在 x 方向上的间距

    height: 134px;
    flex: 0 0 ~'calc((100% - @{gap} * 2) / 3)'; // 使用 ~"" 包裹 calc 表达式
    margin: 0 @gap 20px 0;
    margin-bottom: 0px;

    &:nth-child(3n) {
        // 去除第 3n 个的右边距
        margin-right: 0;
    }
}

:deep(p) {
    // 覆写 LoadMoreObserver 内文案颜色
    color: var(--jinli-user-main60-color, rgba(107, 32, 32, 0.6));
}
</style>
