/* eslint-disable max-lines-per-function */
import { ref, toRaw, watch } from 'vue';
import flattenDepth from 'lodash-es/flattenDepth';
import { radar } from '@gundam/weblogger';
import { type RankingView } from '../schema';
import type { RedPacketItem } from '../models/use-redpacket';
import type { Scene } from '../const';

export interface Context {
    /** 是否还有更多 */
    hasMore?: boolean;
    /** 强插队列 */
    forceRankingList?: RankingView[];
    /** 普通推荐队列 */
    ordinaryRankingList?: RankingView[];
    /** 每组有几个红包 */
    groupSize?: number;
    /**
     * 盛典强插位置
     * 在 loadMore 时需要将其排除
     */
    annualPos?: number;
    /**
     * 场景
     * 在 loadMore 时自动携带
     */
    scene?: Scene;
}

/** 每行的红包个数 固定为 3 */
export const GROUPS_PER_LINE = 3;

interface Options {
    /** 卡片展示 */
    onConsume?: (args: {
        position: [number, number];
        item: RankingView;
    }) => void;
}

export function useRedpacketList(options?: Options) {
    const groupSize = ref(3);
    const annualPos = ref(0);

    /** 用于渲染的红包列表 */
    const itemTable = ref<
        [RedPacketItem[], RedPacketItem[], RedPacketItem[]][]
    >([]);

    /** 强插红包表 */
    const forceMap = new Map<string, RankingView>();

    /** 普通红包队列 */
    const commonQueue = new Array<RankingView>();

    /**
     * 初始化数据 完成首次布局
     */
    const init = (context: Context) => {
        const { forceRankingList = [], ordinaryRankingList = [] } = context;

        forceRankingList.forEach((item) => {
            if (!item.position) {
                console.warn('position not found', item);
                return;
            }

            forceMap.set(item.position, item);
        });
        commonQueue.push(...ordinaryRankingList);

        groupSize.value = context.groupSize ?? 3;
        annualPos.value = context.annualPos ?? 0;

        // 初始完成 3 行的布局
        layout(1);
    };

    /**
     * 加载更多
     * 向普通队列推入更多红包
     */
    const loadMore = (list: RankingView[], loadMoreSize = 3) => {
        commonQueue.push(...list);
        layout(loadMoreSize);
    };

    /**
     * 检查指定位置是否有足够红包补充
     * @param position 指定红包的位置 (索引从 0 开始)
     */
    const canAnimate = (position: [number, number]) => {
        const [groupIdx, idx] = position;
        const id = `${groupIdx + 1}-${idx + 2}`; // 取下一张 并对齐从 1 开始计数的格式
        const target = forceMap.get(id);
        if (target) {
            // 有强插红包
            return true;
        }

        if (commonQueue.length) {
            // 有普通红包
            return true;
        }

        return false;
    };

    /**
     * 获取指定位置的红包
     * @param position 指定红包的位置 (索引从 0 开始)
     */
    const consume = (position: [number, number]) => {
        const [groupIdx, idx] = position; // [第几组, 第几位]

        // 取下一个位置的红包
        const id = `${groupIdx + 1}-${idx + 1}`; // 对齐从 1 开始计数的格式
        console.debug('consume', id);
        const target = forceMap.get(id);

        if (target) {
            // 消费强插红包
            forceMap.delete(id);
            options?.onConsume?.({
                position,
                item: target,
            });
            return target;
        }

        const normal = commonQueue.shift();
        if (normal) {
            // 消费普通红包
            options?.onConsume?.({
                position,
                item: normal,
            });
            return normal;
        }

        return null;
    };

    /**
     * 动画回调
     * @param nextPosition 下一张红包的位置
     */
    const animate = (nextPosition: [number, number]) => {
        console.debug('animate', nextPosition);
        const [groupIdx, idx] = nextPosition;
        const rowIdx = Math.floor(groupIdx / GROUPS_PER_LINE);
        const row = itemTable.value[rowIdx];

        return {
            /**
             * 动效开始前
             * 更新下一张牌的信息
             */
            beforeAnimate: () => {
                radar?.action.start({
                    name: '翻红包',
                });

                // 检查下一张牌是否为空
                if (idx + 1 > groupSize.value) {
                    // 翻到了最后一张牌
                    console.warn('last card', idx);
                    radar?.action.end({
                        name: '翻红包',
                        result_type: '翻到了最后一张牌',
                    });
                    return false;
                }

                if (rowIdx + 1 > itemTable.value.length) {
                    // 已经是最后一行
                    console.warn('last row', rowIdx);
                    radar?.action.end({
                        name: '翻红包',
                        result_type: '翻到了最后一行',
                    });
                    return false;
                }

                if (!canAnimate([groupIdx, idx])) {
                    // 剩余的红包不足
                    console.warn('next card not found', groupIdx, idx);
                    radar?.action.end({
                        name: '翻红包',
                        result_type: '剩余的红包不足',
                    });
                    return false;
                }

                // 更新下一张牌的信息
                const nextIdx = idx + 1;

                const item = consume([groupIdx, nextIdx]);

                if (!item) {
                    // 没有下一张牌了
                    console.warn('consume not found', groupIdx, nextIdx);
                    radar?.action.end({
                        name: '翻红包',
                        result_type: '没有下一张牌了',
                    });
                    return false;
                }

                row[groupIdx % GROUPS_PER_LINE][1].info = item;

                return item;
            },
            /**
             * 动效结束
             * 将当前牌置底
             */
            afterAnimate: () => {
                const newGroup = [...row[groupIdx % GROUPS_PER_LINE]];
                newGroup.push(newGroup.splice(0, 1)[0]);
                row[groupIdx % GROUPS_PER_LINE] = newGroup;

                radar?.action.end({
                    name: '翻红包',
                    result_type: '成功',
                });
            },
        };
    };

    /**
     * 从队列中取出红包填充到布局中
     * @param lines 完成指定行数的布局
     */
    const layout = (lines: number) => {
        console.debug('layout start', lines);

        if (itemTable.value.length) {
            // 优先填充最后一行完全为空的 group
            const lastRowIndex = itemTable.value.length - 1;
            const lastRow = itemTable.value[lastRowIndex];

            const emptyGroups = lastRow.filter((group) =>
                group.every((item) => !item.info),
            );
            if (emptyGroups.length) {
                // 给所有空的 group 填充第一个红包
                for (let i = 0; i < emptyGroups.length; i++) {
                    console.debug('layout fill empty group', i);
                    const group = emptyGroups[i];
                    const item = consume([
                        lastRowIndex * GROUPS_PER_LINE + i,
                        0,
                    ]);
                    if (item) {
                        group[0].info = item;
                    }
                }
            }
        }

        // 开始布局新的 lines
        const len = itemTable.value.length;
        for (let line = 0; line < lines; line++) {
            const row: [RedPacketItem[], RedPacketItem[], RedPacketItem[]] = [
                [],
                [],
                [],
            ];
            for (let groupIdx = 0; groupIdx < GROUPS_PER_LINE; groupIdx++) {
                // 每行有 3 列
                row[groupIdx] = new Array(groupSize.value)
                    .fill(null)
                    .map((_, idx) => {
                        // 根据当前列表的长度 计算出新红包的位置信息
                        const position: [number, number] = [
                            (len + line) * GROUPS_PER_LINE + groupIdx,
                            idx,
                        ];
                        const item: RedPacketItem = {
                            position,
                        };

                        if (idx === 0) {
                            // 给每组第一个红包填充数据
                            const info = consume(position);

                            if (info) {
                                item.info = info;
                            }
                        }

                        return item;
                    });
            }

            // 完全为空的 row 不要渲染
            const isRowEmpty = row.every((group) => !group[0].info);
            if (isRowEmpty) {
                console.debug('layout skip empty row', line);
                continue;
            }

            // 将新数据添加到列表中
            itemTable.value.push(row);
        }

        console.debug('layout done', itemTable.value, itemTable.value.length);
    };

    /**
     * 获取已缓存列表的主播 id
     * 排除了强插队列
     */
    const getExcludeIds = (): number[] => {
        itemTable.value.length; // 显式追踪列表的长度

        return [
            ...Array.from(forceMap)
                // 只上报盛典的强插红包
                .filter(([key]) => key.split('-')[0] === `${annualPos.value}`)
                .map(([, item]) => item.authorId),
            ...commonQueue.map((item) => item.authorId),
            ...flattenDepth(toRaw(itemTable.value), 2)
                .filter((item) => item.info?.authorId)
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                .map((item) => item.info!.authorId),
        ];
    };

    /**
     * 清空并重置所有状态
     */
    const reset = () => {
        itemTable.value = [];
        forceMap.clear();
        commonQueue.splice(0, commonQueue.length);
    };

    return {
        itemTable,
        groupSize,
        canAnimate,
        animate,
        init,
        loadMore,
        getExcludeIds,
        reset,
    };
}
