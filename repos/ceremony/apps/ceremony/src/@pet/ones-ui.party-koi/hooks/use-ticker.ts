/* eslint-disable @typescript-eslint/no-invalid-void-type */
import { onScopeDispose, ref, toRef, type MaybeRefOrGetter } from 'vue';
import { useIntervalFn } from '@vueuse/core';

interface Context<T> {
    /** 剩余时间 (s) */
    seconds: number;
    /** 当前 ticker */
    ticker: Ticker<T>;
}

interface Ticker<T> {
    /** 截止时间 */
    endTime: number;
    /** 每一 tick 执行的回调 */
    callback: (context: Context<T>) => void | Promise<void>;
    /** 是否立即执行一次 tick 更新 @default true */
    immediate?: boolean;
    /** 用于标识 Ticker 的额外参数 */
    extraInfo?: T;
}

interface Options<T> {
    /** 当前时间 (ms) */
    now: MaybeRefOrGetter<number>;
    /** tick 执行前回调 可以拦截 callback 执行 */
    onBeforeTick?: (
        context: Context<T>,
    ) => Promise<void> | Promise<boolean> | boolean | void;
}

/**
 * 用一个定时器同时管理多个回调
 * 支持添加、移除和批处理回调
 */
export function useTicker<T extends Record<string, unknown>>(
    options?: Options<T>,
) {
    const tickers: Ticker<T>[] = [];

    const now = toRef(options?.now ?? (() => Date.now()));

    /** 每 tick 间隔时间 */
    const timeout = ref(1000);

    useIntervalFn(() => {
        tickers.forEach((ticker) => tick(ticker));
    }, timeout);

    onScopeDispose(() => {
        dispose();
    });

    const tick = async (
        ticker: Ticker<T>,
        _options?: { immediate?: boolean },
    ) => {
        const { immediate = false } = _options ?? {};
        const seconds = Math.max(
            0,
            Math.floor((ticker.endTime - now.value) / 1000),
        );

        if (seconds === 0) {
            // 倒计时结束
            remove(ticker);

            // 执行一次 seconds 为 0 的回调
            ticker.callback({
                seconds,
                ticker,
            });
            return;
        }

        try {
            const res = await options?.onBeforeTick?.({ seconds, ticker });
            if (!immediate && res === false) {
                // 显式返回了 false: 跳过此 tick 执行
                return;
            }

            ticker.callback({
                seconds,
                ticker,
            });
        } catch (error) {
            console.warn('onBeforeTick error:', ticker, error);
        }
    };

    const add = (ticker: Ticker<T>) => {
        if (!ticker.endTime) {
            console.warn('invalid ticker endTime', ticker);
            return () => null;
        }

        if (ticker?.immediate ?? true) {
            // 立即触发一次 tick 更新
            tick(ticker, { immediate: true });
        }

        tickers.push(ticker);

        return () => remove(ticker);
    };

    const remove = (ticker: Ticker<T>) => {
        const index = tickers.indexOf(ticker);
        if (index > -1) {
            tickers.splice(index, 1);
        }
    };

    const dispose = () => {
        // 倒序清空 tickers
        for (let i = tickers.length - 1; i >= 0; i--) {
            remove(tickers[i]);
        }
    };

    return {
        add,
        dispose,
    };
}
