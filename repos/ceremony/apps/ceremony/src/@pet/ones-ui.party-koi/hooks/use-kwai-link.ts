import cookie from 'js-cookie';

const KUAISHOU_KPN = 'KUAISHOU';

/**
 * 在 iframe 中打开指定链接
 */
function openUrlInIframe(link: string) {
    const iframe = document.createElement('iframe');
    iframe.setAttribute('src', link);
    iframe.setAttribute('style', 'display:none');
    document.body.appendChild(iframe);
    setTimeout(() => document.body.removeChild(iframe), 100);
}

/**
 * 打开 kwai 链
 */
export function useKwaiLink() {
    /**
     * 打开 kwai 链
     * 支持 `kwai://{url}` `ksnebula://{string}`
     * @param url 要跳转的链接
     */
    const open = (url: string | string) => {
        const isKwai = url.startsWith('kwai') || url.startsWith('ksnebula');

        if (!isKwai) {
            throw new Error('invalid kwai link');
        }

        const { href: _url } = new URL(url, window.location.origin);

        const kpn = cookie.get('kpn') ?? KUAISHOU_KPN;
        const app = kpn === KUAISHOU_KPN ? 'kwai' : 'ksnebula';
        const urlWithoutProtocol = _url
            .replace(/^kwai:\/\//u, '')
            .replace(/^ksnebula:\/\//u, '');
        openUrlInIframe(`${app}://${urlWithoutProtocol}`);
    };

    return {
        open,
    };
}
