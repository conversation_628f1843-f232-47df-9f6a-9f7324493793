import { toRef, type MaybeRefOrGetter } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { Report } from '@alive-ui/actions';
import { normalFocus, unionFocus, type Context } from '../utils/focus';
import { fetchAttendResult } from '../services';
import { getPageName } from '../logger';
import {
    entrySrc,
    FocusRankType,
    type Round,
    AttendEntrySrc,
    ButtonType,
} from '../const';
import { ACTIVITY_ID, entryType, userId } from '../const';
import { useCalendar } from './use-calendar';
import { useServerTimeNow } from '@/@pet/yau.yoda';
import { isInWebview } from '@/@pet/yau.core';
import { is4Tab } from '@/@pet/ones-use.is4Tab';

export type AttendSuccessCb = (
    res: Awaited<ReturnType<typeof fetchAttendResult>>,
) => void;

interface Options {
    /**
     * 当前轮次
     */
    round: MaybeRefOrGetter<Round>;
    /**
     * 服务端 attend 接口埋点 入口来源
     * @default 1
     */
    entrySrc: string;
    /**
     * 服务端关注埋点参数 入口来源
     * @default 1
     */
    rankType?: FocusRankType;

    activityId: string;
}

// eslint-disable-next-line max-lines-per-function
export function useAttend(options: Options) {
    const round = toRef(options.round);
    //
    const { reserveAuthorLLpeTime } = useCalendar();
    const { now: DateNow } = useServerTimeNow();

    /**
     * 参与抽奖
     * 不会向上抛出错误 无需 try-catch
     * @returns {Promise<boolean>} 是否参与成功
     */
    const attend = async (
        info: {
            /** 开奖时间 */
            openTime?: number | null;
            /** 奖品 id */
            llpeId?: string | null;
            /** 主播 id */
            authorId: number;
            /** 宠粉官 id */
            sponsorId: number;
            /** 主播名称 */
            username: string;
            /** 按钮类型 */
            buttonType: ButtonType;
        },
        _options?: {
            /** 已过开奖时间 */
            onExpired?: () => void;
            /** 参与成功 */
            onSuccess?: AttendSuccessCb;
            /** 红包位置 如 1-1 3-2 */
            position?: string;
        },
    ): Promise<boolean> => {
        console.debug('attend', info);

        if (!info.openTime || !info.llpeId) {
            Toast.info('奖品参数有误');
            console.warn('attend invalid argument', info.authorId);
            return false;
        }

        if (`${info.authorId}` === userId) {
            // 拦截主播参与自己的抽奖
            Toast.info('不能参加自己的抽奖');
            console.warn('attend self', info.authorId);
            return false;
        }

        // 处理边界 case: 页面很久未刷新
        const serverTime = DateNow.value.getTime();
        if (serverTime >= info.openTime) {
            // 当前已过开奖时间
            Toast.info('状态已更新，以页面结果为准');
            _options?.onExpired?.();
            console.warn('attend expired', info.openTime, serverTime);
            return false;
        }

        console.debug('attend start focus');

        // 执行关注
        const { buttonType, authorId, sponsorId } = info;
        const context: Context = {
            round: 1,
            fromCreatorId: `${authorId}`,
            rankType: options?.rankType ?? FocusRankType.HomepageTopArea,
            rankIndex: _options?.position ?? '',
            prizeId: info.llpeId,
            pageFrom: options.entrySrc,
            activityId: options.activityId,
            // 不区分页面，直接写死就好
            pageName: 'OP_ACTIVITY_CERE_CONTENT_H5',
        };

        try {
            switch (buttonType) {
                case ButtonType.FollowAuthor:
                    await normalFocus(`${authorId}`, context);
                    break;

                case ButtonType.FollowAuthorAndSponsor:
                    // 不关心关注宠粉官成功与否
                    sponsorId && unionFocus(`${sponsorId}`, context);
                    await unionFocus(`${authorId}`, context);
                    break;

                case ButtonType.FollowNone:
                    break;

                case ButtonType.FollowSponsor:
                    sponsorId && normalFocus(`${sponsorId}`, context);
                    break;

                default:
                    console.warn(
                        'focus ignored',
                        'unknown button type',
                        buttonType,
                    );
                    break;
            }
        } catch (error) {
            console.warn('attend focus error', JSON.stringify(error));
            if (error instanceof Error) {
                Toast.info(error.message ?? '关注失败');

                Report.biz.warning('关注失败', {
                    message: error.message,
                    rawError: JSON.stringify(error),
                    options,
                    info,
                });
            }

            // 关注失败 直接阻塞参与
            return false;
        }

        console.debug('attend focus success');

        try {
            // 参与抽奖
            const res = await fetchAttendResult({
                activityId: options.activityId,
                round: 1,
                creatorLLpeId: info.llpeId,
                entryType: is4Tab ? 3 : 2,
                entrySrc: 4,
                pageFrom: options.entrySrc,
                // 不区分页面，直接写死就好
                pageName: 'OP_ACTIVITY_CERE_CONTENT_H5',
            });

            _options?.onSuccess?.(res);
        } catch (error) {
            if (error instanceof Error) {
                // 参与失败
                Toast.info(error.message ?? '参与失败');
                console.warn('attend failed', error);

                Report.biz.warning('参与抽奖失败', {
                    message: error.message,
                    options,
                    info,
                });
            }

            return false;
        }

        try {
            // 参与成功 写日历
            if (import.meta.env.DEV && !isInWebview()) {
                // 开发环境下 直接返回
                return true;
            }

            await reserveAuthorLLpeTime({
                authorId: info.authorId,
                authorName: info.username,
                openTime: info.openTime,
                round: round.value,
            });
        } catch (error) {
            // 写日历失败不阻塞参与成功
            console.warn('attend effect error:', error);
        }

        return true;
    };

    return {
        attend,
    };
}
