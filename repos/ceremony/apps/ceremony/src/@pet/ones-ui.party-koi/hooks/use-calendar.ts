import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import useKConf from '@pet/ones-use.useKconf';
import {
    reserveAuthorLLpeTime as _reserveAuthorLLpeTime,
    reserveAuthorLLpeTimeFromCache as _reserveAuthorLLpeTimeFromCache,
} from '../utils/bridge/calendar';

/**
 * 二次封装日历相关方法
 * 提供 KConf 配置的响应式更新
 */
export function useCalendar() {
    const { conf4Tab: kconf } = storeToRefs(useKConf());

    const config = computed(() => ({
        links: kconf.value.appointment.appointmentLinks,
        titleTemplate: kconf.value.appointment.titleTemplate,
        noteTemplate: kconf.value.appointment.noteTemplate,
    }));

    const reserveAuthorLLpeTime = async (
        params: Parameters<typeof _reserveAuthorLLpeTime>[0],
    ) => {
        return _reserveAuthorLLpeTime(params, config.value);
    };

    const reserveAuthorLLpeTimeFromCache = async () => {
        return _reserveAuthorLLpeTimeFromCache(config.value);
    };

    return {
        reserveAuthorLLpeTime,
        reserveAuthorLLpeTimeFromCache,
    };
}
