import { computed, ref, toRef, type MaybeRefOrGetter } from 'vue';
import { unreachable } from '../utils/unreachable';
import { isElementVisible } from '../utils/dom';
import { ID_RECORD_FIXED, ID_RECORD_FOLD } from '../const/id';
import type { RedPacketItem } from '../models/use-redpacket';
import { sleep } from '@/@pet/yau.core';

interface Options {
    /** 当前组的所有红包 */
    redpackets: MaybeRefOrGetter<RedPacketItem[]>;
    /** 是否禁用动效 */
    disabled: MaybeRefOrGetter<boolean>;
    /** 动画启动函数 */
    handler: MaybeRefOrGetter<{
        boot: (id: string) => void;
    }>;
}

/** 动画帧 */
type Phase = 0 | 1 | 2 | 3;

export function useRedpacketAnimate(options: Options) {
    const redpackets = toRef(options.redpackets);
    const disabled = toRef(options.disabled);
    const handler = toRef(options.handler);

    /** 正在动画的红包 */
    const animatingRedpacket = ref<RedPacketItem | null>(null);

    /** 当前动画进行到的动画帧 */
    const currentPhase = ref<Phase>(0);

    /** 正在动画的红包索引 */
    const animatingRedpacketIndex = computed(() => {
        if (animatingRedpacket.value) {
            return redpackets.value.findIndex(
                (redpacket) => redpacket === animatingRedpacket.value,
            );
        }
        return -1;
    });

    const targets = [ID_RECORD_FOLD, ID_RECORD_FIXED];

    /** 飞行动画的目标 DOM id */
    const bootTarget = () => {
        for (const id of targets) {
            const el = document.getElementById(id);

            console.debug('el', el);

            if (isElementVisible(el)) {
                // 如果可见 则直接使用
                return id;
            }
        }

        // 如果都不可见 使用最后一个
        return targets[targets.length - 1];
    };

    // @ts-expect-error - 能够保证 pointerEvents 值为限定值
    const styles = computed<Partial<CSSStyleDeclaration>>(() => {
        return redpackets.value
            .map((_, index) => {
                // 计算每个卡片的样式
                const isAnimatingCard = index === animatingRedpacketIndex.value;
                const zIndex = isAnimatingCard ? 99999 : index + 1; // 动画中的卡片置于最上层
                const pointerEvents = isAnimatingCard ? 'none' : 'auto';

                // 下一张牌的索引
                const isAnimatingNextCardIndex =
                    animatingRedpacketIndex.value - 1;

                // bounce 效果 100% -> 110% -> 95% -> 102% -> 100%
                if (index === isAnimatingNextCardIndex) {
                    switch (currentPhase.value) {
                        case 0:
                            return {
                                transform: 'scale(1)',
                                zIndex,
                                pointerEvents,
                            };

                        case 1:
                            return {
                                transform: 'scale(1.1)',
                                zIndex,
                                pointerEvents,
                            };

                        case 2:
                            return {
                                transform: 'scale(.95)',
                                zIndex,
                                pointerEvents,
                            };

                        case 3:
                            return {
                                transform: 'scale(1.02)',
                                zIndex,
                                pointerEvents,
                            };

                        default:
                            unreachable(currentPhase.value);
                    }
                }

                return {
                    zIndex,
                    pointerEvents,
                };
            })
            .reverse(); // 反转数组，使卡片从上到下排列
    });

    /**
     * 执行动画
     */
    const animate = (indexReversed = 0) => {
        // 将反转后的索引转换为正常索引
        const index = redpackets.value.length - 1 - indexReversed;

        if (animatingRedpacket.value !== null) {
            // 动画进行中，不执行新的动画
            console.warn('animation is in progress');
            return;
        }

        const { info, position } = redpackets.value[indexReversed];

        if (disabled.value) {
            // 动效被关闭
            console.warn('animate skip: disableAnimate', disabled.value);

            return {
                position,
                promise: Promise.resolve(),
            };
        }

        animatingRedpacket.value = redpackets.value[index];
        currentPhase.value = 1;

        const doAnimate = async () => {
            // 按照动效参数执行动画
            handler.value.boot(bootTarget());

            await sleep(100);
            currentPhase.value = 1;
            await sleep(200);
            currentPhase.value = 2;
            await sleep(200);
            currentPhase.value = 3;
            await sleep(200);
            currentPhase.value = 0;

            animatingRedpacket.value = null;
        };

        // 调用动画函数
        const promise: Promise<void> = doAnimate();

        if (!info) {
            console.warn('invalid info', info);
            return;
        }

        return { promise, position };
    };

    return {
        animatingRedpacket,
        styles,
        animate,
        bootTarget,
    };
}
