import type { InjectionKey, Ref } from 'vue';
import type { baseModel } from '../models/base';
import type { useAttend } from '../hooks/use-attend';

/**
 * 用户参与抽奖
 */
export const SYMBOL_ATTEND = Symbol('SYMBOL_ATTEND') as InjectionKey<
    ReturnType<typeof useAttend>['attend']
>;

/**
 * 打开 kwai 链
 * 用于跳转直播间/上下滑
 */
export const SYMBOL_REDIRECT = Symbol('SYMBOL_REDIRECT') as InjectionKey<
    ReturnType<typeof baseModel>['jumpKwaiLink']
>;

/**
 * 已存储的 id
 */
export const SYMBOL_EXCLUDE_IDS = Symbol('SYMBOL_EXCLUDE_IDS') as InjectionKey<
    Ref<number[]>
>;

/**
 * 注册定时器
 */
export const SYMBOL_ADD_TICKER = Symbol('SYMBOL_ADD_TICKER');
