import { getTheme, THEME } from '../utils/theme';
import { getCookie } from '../utils/cookie';

export const getSearchParam = <T = string>(key: string, url?: string) => {
    const u = new URL(url ?? location.href);
    const { searchParams } = u;
    return (searchParams.get(key) as T) || '';
};

// 当前用户id:userId
export const userId =
    getSearchParam('userId') ||
    getSearchParam('nativeCurrentUserId') ||
    getCookie('ud') ||
    getCookie('userId');

// 当前主播id: authorId
export const authorId =
    getSearchParam('authorId') ||
    getSearchParam('nativeAuthorId') ||
    getSearchParam('nativeUserId');

// 当前用户是不是主播
export const bolIsAuthor = authorId && authorId === userId;

// 当前直播间id
export const liveStreamId =
    getSearchParam('liveStreamId') || getSearchParam('nativeLiveStreamId');

// 活动id,必须要，用来唯一标识当前活动
export const ACTIVITY_ID = getSearchParam('activityId');

// 锦鲤关注抽奖：212： 普通的锦鲤抽奖，未关用户抽奖时会关注作
export const NORMAL_FOLLOW_REFER = 149;
export const NORMAL_FOLLOW_REFER_ACTIVITY_ID = 'activity-auto-25482875e';

// 联合关注抽奖：221： 如果是联合抽奖（作者+宠粉官），则用户参与抽奖，需要关注作者与宠粉官
export const UNION_FOLLOW_REFER = 221;
export const UNION_FOLLOW_REFER_ACTIVITY_ID = 'activity-auto-261c53e29';

// 是否在直播间内
export const isInLive = !!liveStreamId;

// 此刻是主播正在开播状态，不能离开直播间或者用全屏盖住直播间
export const isAuthorAndLiving = bolIsAuthor && isInLive;

// 容器样式类型
export const layoutType = +getSearchParam('layoutType');

// 全屏
export const isFullScreen = layoutType === 4;

// 半屏
export const isHalfScreen = layoutType === 3;

// plc bizType 产品申请的业务值,用在打开plc容器的参数里，项目里用不到
// export const plcBizType = 79;

// 角色 1：主播  2：用户
export const role = getSearchParam('role');

// plc id
export const plcId = getSearchParam('plcId');

// plc 场景下的创作者 id
export const creatorId = getSearchParam('creatorId');

// 视频id
export const photoId = getSearchParam('photoId');

// 锦鲤入口的场景 1PLC 2直播间 3全屏
export const entryType = getSearchParam('entryType');

export const entryTypePLC = '1';
export const entryTypeLive = '2';
export const entryTypeFullScreen = '3';

// 是否是 plc 场景
export const isPLC = entryType === entryTypePLC;

export const entrySrcInner = getSearchParam('entry_src');

// 埋点所需的来源+前缀, 兼容外部传入的不是数字
export const entrySrc =
    entrySrcInner.indexOf('cny_koi') > -1
        ? entrySrcInner
        : `cny_koi_${entrySrcInner.toString()}`;

// 当前轮次
export const round = +getSearchParam('round');

/** 点赞视频时需要传递的 biz_type */
export const likePhotoBizType = 'cny2025.koi';

export const baseRoute = `/live/act/cny25-koi/${import.meta.env.VITE_THEME}`;

// 导出常量
export const IS_CNY_THEME = getTheme() === THEME.CNY_THEME;
export const IS_CEREMONY_THEME = getTheme() === THEME.CEREMONY_THEME;
export const IS_DIAMOND_THEME = getTheme() === THEME.DIAMOND_THEME;
export const THEME_NAME = getTheme();

export const ERROR_MSG = '系统繁忙，请稍后重试';

export enum LlpeType {
    /** 现金 */
    Money = 1,
    /** 快币 */
    KCoin = 2,
}

export enum Round {
    /** 第一轮 */
    First = 1,
    /** 第二轮 */
    Second = 2,
}

export enum ButtonType {
    /**
     * 未知状态
     */
    Unknown = 0,
    /**
     * 仅关注主播
     * '关注并抽奖'
     */
    FollowAuthor = 1,
    /**
     * 关注主播和宠粉官
     * '关注并抽奖'
     */
    FollowAuthorAndSponsor = 2,
    /**
     * 都不关注 仅参与
     * '点赞并抽奖'
     */
    FollowNone = 3,
    /**
     * 仅关注宠粉官
     * '关注并抽奖'
     */
    FollowSponsor = 4,
    /**
     * 开奖前 & 早鸟期间，不跳活动主页
     * '参与更多抽奖'
     */
    WAITING = 5,
    /**
     * 开奖前，跳转活动主页
     * '待开奖，点击参与更多'
     */
    More = 7,
    /**
     * 页面状态为开奖，但是按钮显示已开奖？特殊 case？
     * '参与更多抽奖'
     */
    Finished = 6,
    /**
     * 开奖后，跳转活动主页
     * '点击参与更多'
     */
    MORE_AFTER = 8, // 对应 server MORE_LOTTERY_AFTER（敏感词）
}

/**
 * 为你推荐模块的场景枚举
 */
export enum Scene {
    /** 用户主页 */
    UserHome = 2,
    /** 用户半屏页 */
    UserHalf = 3,
}

/** 参与抽奖服务端埋点 */
export enum AttendEntrySrc {
    UserIndex = 1, // 活动主页
    PzPLC = 2, // plc 半屏
    PzLive = 3, // 直播半屏
}

/** 关注/抽奖埋点 tabName 枚举 */
export enum FocusTabName {
    Suggest = '为你推荐',
    NewYear = '新春好礼',
    AuthorRwd = '主播奖品',
}

/** 关注 服务端埋点参数 */
export enum FocusRankType {
    /** 未知 */
    Unknown = 0,
    /** 用户主页新春好礼模块 */
    HomepageTopArea = 1,
    /** 用户主页为你推荐模块 */
    HomepageBottomArea = 2,
    /** 用户半屏推荐模块 */
    HalfpageRecommandArea = 3,
    /** 用户主页导流卡片 */
    HomepageSpotlightCard = 4,
    /** 用户半屏主按钮参与 */
    HalfpageTopArea = 5,
}
// 切换轮次tab不可点击提示
export const TAB_DISABLED_TOAST = '当前轮次未开始';
