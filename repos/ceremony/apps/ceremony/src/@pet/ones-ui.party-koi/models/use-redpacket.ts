import { computed, ref } from 'vue';
import { createUseModel } from '@gundam/model';
import { isDownGrade } from '../utils/down-grade';
import { type RankingView } from '../schema';
import {
    GROUPS_PER_LINE,
    useRedpacketList,
    type Context,
} from '../hooks/use-redpacket-list';
import { ACTIVITY_ID, entryType, Scene } from '../const';
// import useKConf from '@/models/kconf-theme';

export interface RedPacketItem {
    /**
     * 红包位置 全局唯一
     * [第几组, 第几位]
     */
    position: [number, number];
    /**
     * 红包的信息
     * 为空时表示尚未被曝光
     */
    info?: RankingView;
}

export const useRedpacketModel = createUseModel(() => {
    const noMore = ref(false);
    const loading = ref(true); // 默认为 true 待 onSetup 执行完毕后更新

    /** 用于标识 model 最新状态 */
    let counter = 0;

    const scene = ref(Scene.UserHome);

    // const { kconfData: kconf } = useKConf();
    const animationDownGrade = computed(
        () => isDownGrade(),
        //     kconf.value.userIndex.redpacketAnimationDownGrade || isDownGrade(),
    );

    const {
        init,
        loadMore: _loadMore,
        itemTable,
        groupSize,
        canAnimate,
        animate,
        getExcludeIds,
        reset,
    } = useRedpacketList();

    /** 初始化数据 */
    const onSetup = (context: Context) => {
        console.log('context', context);

        init(context);

        if (context.scene) {
            scene.value = context.scene;
        }

        if (!context.hasMore) {
            noMore.value = true;
        }

        loading.value = false;
    };

    /** 加载更多红包 */
    // const loadMore = async (round: number, excludeIds: number[] = []) => {
    //     if (noMore.value) {
    //         return;
    //     }

    //     if (loading.value) {
    //         return;
    //     }

    //     loading.value = true;

    //     // 缓存 model 当前的状态
    //     const currentCount = counter;

    //     /** 每次请求 3 行的数据 */
    //     const LOAD_MORE_SIZE = 3;

    //     try {
    //         const res = await fetchMoreRecommendList({
    //             round,
    //             cursor: `${getExcludeIds().length}`, // 传递红包数量
    //             size: LOAD_MORE_SIZE * GROUPS_PER_LINE * groupSize.value, // 一次请求三行的数据
    //             entryType: +entryType,
    //             excludeIdSet: excludeIds,
    //             activityId: ACTIVITY_ID,
    //             scene: scene.value,
    //         });

    //         if (currentCount !== counter) {
    //             // model 状态已更新
    //             console.warn('model state changed', currentCount, counter);
    //             return;
    //         }

    //         if (!res?.data?.hasMore) {
    //             // 没有更多了
    //             noMore.value = true;
    //         }

    //         if (res?.data?.rankingList?.length) {
    //             // 加载更多红包
    //             _loadMore(res.data.rankingList, LOAD_MORE_SIZE);
    //         }
    //     } catch (err) {
    //         // logModelJSError('user-index-redpacket-loadmore', err);
    //     } finally {
    //         loading.value = false;
    //     }
    // };

    /** 将 Model 中的状态恢复到初始态 */
    const $reset = () => {
        counter++;
        noMore.value = false;
        loading.value = true;
        reset();
    };

    return {
        onSetup,
        noMore,
        loading,
        itemTable,
        canAnimate,
        animate,
        getExcludeIds,
        $reset,
        animationDownGrade,
    };
});
