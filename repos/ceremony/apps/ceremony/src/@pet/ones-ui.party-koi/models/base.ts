import { debounce } from '@pet/yau.core';
import { toast } from '@pet/adapt.toast';
import { createUseModel } from '@gundam/model';
import { useKwaiLink } from '../hooks/use-kwai-link';
import {
    BUTTON_BLOCK_COMMON_TOAST,
    BUTTON_BLOCK_LIVE_TOAST,
} from '../const/text';
import { isHalfScreen, isAuthorAndLiving, liveStreamId } from '../const/index';

// eslint-disable-next-line max-lines-per-function
export const baseModel = createUseModel(() => {
    // 针对直播会场半屏内的返回

    const { open: openKwaiLink } = useKwaiLink();
    //  分享降级
    // 客服链接
    // 规则页链接

    /**
     * 直播状态下阻断用户行为
     * @param callback
     */
    const beforeAction = (
        callback: any,
        textType: 'common' | 'live' = 'common',
    ) => {
        if (isHalfScreen && isAuthorAndLiving) {
            const textMap = {
                common: BUTTON_BLOCK_COMMON_TOAST,
                live: BUTTON_BLOCK_LIVE_TOAST,
            };
            toast(textMap[textType ?? 'common']);
        } else {
            callback();
        }
    };

    /**
     * 跳转 kwai 链
     * @param kwaiLink 要跳转的 kwai 链
     * @param _liveStreamId 目标直播间 id
     */
    const jumpKwaiLink = debounce(
        (kwaiLink: string, _liveStreamId?: string) => {
            if (!kwaiLink) {
                console.warn('invalid kwaiLink', kwaiLink);
                return;
            }

            if (_liveStreamId && liveStreamId === _liveStreamId) {
                console.warn('already in liveroom', liveStreamId);
                toast('已在当前主播直播间');
                return;
            }

            beforeAction(() => {
                openKwaiLink(kwaiLink);
            });
        },
    );

    return {
        beforeAction,
        jumpKwaiLink,
    };
});
