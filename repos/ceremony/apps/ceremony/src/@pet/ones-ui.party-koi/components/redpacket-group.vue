<script setup lang="ts">
import { ref, inject } from 'vue';
import { storeToRefs } from 'pinia';
import { useTemplateRefsList } from '@vueuse/core';
import { usePartySwitch } from '@pet/ones-use.usePartySwitch';
import useKconf from '@pet/ones-use.useKconf/index';
import { Toast } from '@lux/sharp-ui-next';
import { Report } from '@alive-ui/actions';
import { transformButtonText } from '../utils/transform';
import { useRedpacketAnimate } from '../hooks/use-redpacket-animate';
import {
    SYMBOL_ADD_TICKER,
    SYMBOL_ATTEND,
    SYMBOL_REDIRECT,
} from '../const/symbols';
import Pilot from '../bezier/index.vue';
import RedpacketItem from './redpacket-item.vue';
import type { RankingView } from '../schema/index';
import type { RedPacketItem } from '../models/use-redpacket';
import type { useTicker } from '../hooks/use-ticker';
import { sendShow, sendClick } from '@/common/logger';
import { debounce } from '@/@pet/yau.core';
import { toast } from '@/@pet/adapt.toast';

const props = defineProps<{
    /* 卡片数据 */
    cards: RedPacketItem[];
    /* 是否可以执行动画 */
    canAnimate: boolean;
    /** 跳过动画执行 */
    disableAnimate: boolean;
    size: 'small' | 'normal';
}>();

const emit = defineEmits<{
    /** 开始执行动画 */
    animate: [position: [number, number], animating: Promise<void>];
}>();

const refs = useTemplateRefsList<InstanceType<typeof Pilot>>();
const { conf4Tab } = storeToRefs(useKconf());
const moduleSwitch = usePartySwitch();

const { animatingRedpacket, styles, animate } = useRedpacketAnimate({
    redpackets: () => props.cards,
    disabled: () => props.disableAnimate,
    handler: () => ({ boot: refs.value?.[0].boot }),
});

const attend = inject(SYMBOL_ATTEND);
const redirect = inject(SYMBOL_REDIRECT);
const addTicker =
    inject<ReturnType<typeof useTicker>['add']>(SYMBOL_ADD_TICKER);
const loading = ref(false);

const onClick = debounce(
    async (info: RankingView, position: [number, number]) => {
        if (moduleSwitch.value.jinLiApiDowngradeSwitch) {
            Toast.info(conf4Tab.value.jinLiApiToast ?? '网络异常。请稍后重试~');
        }

        if (animatingRedpacket.value) {
            // 有正在进行的动画
            console.warn('animation is in progress');
            return;
        }

        if (!info) {
            console.warn('info is null');
            return;
        }

        const params = createParams(info, position);
        logClick('OP_ACTIVITY_CNY24_JINLI_MAIN_JOIN_BUTTON', params);

        loading.value = true;

        /** 参与副作用 */
        const attendEffect = async () => {
            return new Promise((resolve) => {
                setTimeout(() => {
                    // 延迟一会后开始动效 优化用户体验
                    if (!props.canAnimate) {
                        // 当前牌组已经被抽完
                        console.warn(
                            'animate skip: canAnimate',
                            props.canAnimate,
                        );
                        resolve(false);
                        return;
                    }

                    const result = animate();
                    if (result) {
                        emit('animate', result.position, result.promise);
                    }

                    resolve(true);
                }, 300);
            });
        };

        try {
            const res = await attend?.(info, {
                onExpired: () => {
                    // 已开奖 更新按钮状态与文案
                    info.buttonText = '已开奖';
                    info.openLLpe = true;
                    attendEffect();
                },
                position: position.map((item) => item + 1).join('-'),
            });

            loading.value = false;

            if (!res) {
                // 参与失败 不做任何处理
                return;
            }

            // 参与成功 更新状态
            info.join = true;

            const dispose = addTicker?.({
                endTime: info.openTime ?? 0,
                callback: ({ seconds }) => {
                    info.buttonText = transformButtonText(seconds);
                },
                extraInfo: { position },
            });

            const effectResult = await attendEffect();

            if (effectResult) {
                // 有下一张牌补位 将前一张牌的定时器移除
                dispose?.();
            }
        } catch (error) {
            Report.biz.error('【锦鲤服务调用异常】', {
                error,
            });
        }
    },
);

/** 点击跳转直播间/视频流 */
const onClickRedirect = debounce(
    (info: RankingView, position: [number, number]) => {
        if (loading.value) {
            // 参与过程中 禁用跳转
            console.warn(
                'redirect is disabled cause by loading',
                info.authorId,
            );
            return;
        }

        // sendRedpacketClickLog(info);
        const params = createParams(info, position);
        logClick('OP_ACTIVITY_CNY24_JINLI_MAIN_JOIN_BUTTON', params);
        redirect?.(
            info.jumpUrl ?? '',
            info.living && info.liveId ? info.liveId : void 0,
        );
    },
);

/** 卡片曝光 */
const onRedpacketItemShow = (info: RankingView, position: [number, number]) => {
    const params = createParams(info, position);

    sendShow({
        action: 'OP_ACTIVITY_CNY24_JINLI_MAIN_JOIN_BUTTON',
        params,
    });
};

/** 按钮禁用态点击 */
const onDisabledClick = debounce((info: RankingView) => {
    if (info?.join) {
        if (props.canAnimate) {
            // 还有下一张卡片可以抽取 不弹出 toast
            return;
        }

        toast('本叠已抽完');
        return;
    }
});
const logClick = (action: string, params: Record<string, any>) => {
    sendClick({
        action,
        params,
    });
};

// 封装埋点参数生成逻辑
const createParams = (info: RankingView, position: [number, number]) => ({
    // 锦鲤主播id
    anchor_id: info.authorId,
    // 宠粉官id
    cfg_id: info.sponsorId,
    // 奖池id
    price_id: info.llpeId,
    // 视频id
    content_id: info.photoId,
    index: position[0] + 1,
    button_name: info.buttonText,
});
</script>

<template>
    <div
        v-if="cards.some((data) => data.info)"
        :class="size"
        class="redpacket-group"
    >
        <div
            v-for="(data, index) of cards"
            :key="data.position.join('-')"
            :style="[styles[index]]"
            class="redpacket-item-wrapper"
        >
            <!-- 只渲染第一张 仅在动画进行中渲染第二张 -->
            <Pilot
                v-if="
                    data.info &&
                    (index === 0 || (animatingRedpacket && index === 1))
                "
                :ref="refs.set"
            >
                <RedpacketItem
                    :info="data.info"
                    :loading="loading"
                    @attend="onClick(data.info, data.position)"
                    @redirect="onClickRedirect(data.info, data.position)"
                    @show="onRedpacketItemShow(data.info, data.position)"
                    @disable-click="onDisabledClick(data.info)"
                />
            </Pilot>
        </div>
        <!-- 只渲染最后一张 作为背景图 -->
        <div class="background jinli-choujiang-stackbg"></div>
    </div>
</template>

<style lang="less" scoped>
.redpacket-group {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    height: fit-content;
}

.redpacket-item-wrapper {
    position: absolute;
    transition: transform 0.2s ease-in-out;
    will-change: transform;
}
.small {
    transform-origin: bottom center;
    transform: scale(0.85); /* 中间卡片正常大小 */
}
.normal {
    transform: scale(1); /* 中间卡片正常大小 */
}
.background {
    transform: translateY(-4px);
}
</style>
