<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { debounce } from 'lodash-es';
import { templateRef, useIntersectionObserver } from '@vueuse/core';
import Button from '@pet/adapt.button/index.vue';
import { Toast } from '@lux/sharp-ui-next';
import { Avatar } from '@alive-ui/base';
import { nameSlice } from '@alive-ui/actions';
import { LlpeType } from '../const';
import type { RankingView } from '../schema';

const { Avatar: AAvatar } = Avatar;
const props = defineProps<{
    info: RankingView;
    loading: boolean;
}>();

const emit = defineEmits<{
    redirect: [];
    attend: [];
    show: [];
}>();

// 广告标识
const showAd = computed(() => props.info.adMark);
// 用户头像（优先从 info 获取，否则使用默认头像）
const avatar = computed(() => props.info.userAvatarUrl[0]?.url);
// 用户名截取
const name = computed(() => nameSlice(props.info.username, 4));
// 瓜分人数
const pzCount = computed(() => props.info.count);
// 金额字符串
const pzAmountStr = computed(() => props.info.amountStr);
// 单位
const pzUnit = computed(() => props.info.unit);
// 类型
const pzType = computed(() => props.info.type);
// 按钮是否禁用
const disabled = computed(() => !!(props.info.openLLpe || props.info.join));
// 按钮文字
const buttonText = computed(() => props.info.buttonText);
// 用于引用红包条目的 DOM 节点
const redpacketItemRef = templateRef<HTMLElement | null>('redpacketItemRef');

// 是否可见的状态
const isVisible = ref(false);

const { stop } = useIntersectionObserver(redpacketItemRef, ([entry]) => {
    isVisible.value = entry?.isIntersecting || false;
});

watch(isVisible, (visible) => {
    if (visible) {
        emit('show');
        stop(); // 中止监听
    }
});

/** 按钮禁用态点击 */
const onDisabledClick = debounce((info: RankingView) => {
    if (info?.join) {
        Toast.info('本叠已抽完');
        return;
    }
});
</script>

<template>
    <div
        ref="redpacketItemRef"
        class="redpacket-item jinli-choujiang-leafletbg"
        @click="$emit('redirect')"
    >
        <!-- <div v-if="showAd" class="commercial-tag jinli-guanggao-label"></div> -->
        <div class="info-container">
            <div class="pz-container">
                <span
                    class="pz-icon"
                    :class="[
                        pzType === LlpeType.Money
                            ? 'jinli-xianjin-icon'
                            : 'jinli-kuaibi40-icon',
                    ]"
                ></span>
                <div class="pz">
                    <span class="font-din1451 amount">
                        {{ pzAmountStr }}
                    </span>
                    <span class="unit">
                        {{ pzUnit
                        }}{{ pzType === LlpeType.Money ? '元' : '快币' }}
                    </span>
                </div>
            </div>
            <div class="user">{{ pzCount }}人瓜分</div>
        </div>
        <div class="author-info">
            <AAvatar class="avatar" :src="avatar" />
            <span class="name">{{ name }}</span>
        </div>
        <Button
            class="attend jinli-xsbuttonbg-gold"
            :disabled="disabled"
            :looks-like-disabled="!!info.join"
            :loading="loading"
            :debounce="0"
            :has-substrate="true"
            @click.stop="$emit('attend')"
            @disabled-click.stop="onDisabledClick(info)"
        >
            {{ buttonText }}
        </Button>
    </div>
</template>

<style lang="less" scoped>
.redpacket-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 110px;
    height: 126px;
    user-select: none;
    cursor: pointer;
}

.commercial-tag {
    position: absolute;
    top: -3px;
    right: 3px;
    border-top-right-radius: 9px;
}

.info-container {
    position: absolute;
    top: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .pz-container {
        display: flex;
        margin-bottom: -2px;
    }

    .pz-icon {
        display: flex;
        width: 24px;
        height: 24px;
        margin-right: 2px;
    }

    .pz {
        color: var(--jinli-modal-text, #500);

        .amount {
            font-size: 20px;
            margin-right: 2px;
        }

        .unit {
            font-size: 10px;
            font-weight: var(--font-weight-500);
            line-height: 14px /* 140% */;
        }
    }

    .user {
        display: flex;
        width: fit-content;
        border-radius: 2px;
        padding: 0 3px;
        background: var(--jinli-LightFixedLining-color, rgba(200, 69, 36, 0.1));
        color: var(--jinli-modal-text, #6b2020);
        font-size: 10px;
        line-height: 14px /* 140% */;
    }
}

.author-info {
    position: absolute;
    bottom: 56px;
    display: flex;
    align-items: center;

    .avatar {
        --y-img-width: 16px;
        --y-img-height: 16px;
        --y-img-border-radius: 50%;
        --y-img-border: 0.5px solid var(--jinli-main-stroke, #ffffff);

        display: block;
        width: 16px;
        height: 16px;
        margin-right: 3px;
    }

    .name {
        color: var(--jinli-modal-text, #6b2020);
        font-size: 10px;
        line-height: 10px;
    }
}

.attend {
    --adapt-button-primary-font-color: var(--jinli-highlight-color, #f72b2b);
    --adapt-button-primary-font-linear: var(--jinli-highlight-color, #f72b2b);
    --adapt-button-primary-background-image: url('../assets/jinli-xsbuttonbg-gold_2x.png');
    --adapt-button-substrate-background: rgba(244, 255, 184, 0.5);
    --font-weight-500: 500;
    --adapt-button-main-font-family: 'PingFang SC';
    position: absolute;
    bottom: 8px;
    width: 90px;
    min-width: unset;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(.btn-main-box) {
        background-color: unset;
    }

    :deep(.btn-words) {
        line-height: 16.8px;
        font-size: 12px;
        font-weight: var(--font-weight-500);
    }
}
.font-din1451 {
    font-family: 'Alte DIN Mittelschrift';
}
</style>
