# 奖励轮播组件 Award Marquee Component

## 功能描述
该组件用于展示奖励信息的滚动条，包括奖励提示文案、奖励图片和额外的奖励信息。用户可以通过点击按钮展开或折叠奖励详情。

## Props 提取
该组件接收以下 props：

| Prop Name     | Type     | Default Value | Description                               |
|---------------|----------|---------------|-------------------------------------------|
| data          | Object   | AwardSchema   | 奖励数据，包括滚动列表、奖励图片等信息 |
| useProps      | Boolean  | false         | 是否使用 props 作为数据源               |
| contextName   | String   | ''            | 上下文名称，用于数据注入                |

## Vue 使用代码 Demo
以下是如何在 Vue 组件中使用 `AwardMarquee` 组件的示例：

```vue
<script lang="ts" setup>
import { provide } from 'vue';
import AwardMarquee from './AwardMarquee.vue';
import { dataFun } from './data'; // 假设 data.ts 中有处理数据的逻辑

const contextName = 'yourContextName'; // 替换为实际的上下文名称
const awardData = dataFun(contextName); // 获取奖励数据

provide(contextName, awardData); // 注入数据

</script>

<template>
  <div>
    <AwardMarquee :contextName="contextName" />
  </div>
</template>

<style lang="less" scoped>
/* 添加样式 */
</style>
```
## 数据处理方式
组件通过 contextName 注入上下文数据，使用 dataFun 方法处理数据。确保在使用组件之前，相关的数据已经通过 provide 注入。
你也可以通过props的方式注入
```typescript
import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { AwardSchema } from './schema';

export const dataFun = (contextName: string) => {
    const awardInfo = contextName
        ? inject<Ref<AwardSchema>>(contextName)
        : null;

    const data = computed(() => {
        return {
            marqueeList: awardInfo?.value?.extraData?.privilegeDesc?.split('$')?.filter((i) => i) || [],
            awardURL: awardInfo?.value?.extraData?.privilegeIconUrl || '',
            awardList: awardInfo?.value?.extraData?.privilegeOverview?.championAwards || [],
            showAward: true,
            operateText: '奖励',
            topAwards: awardInfo?.value?.extraData?.privilegeOverview?.topAwards || [],
            isFinals: awardInfo?.value?.extraData?.ctivityFinalSchedule || false,
        };
    });

    return data;
};
