<template>
    <div>
        <div
            :class="[
                'rank-award-content',
                'flex',
                'items-center',
                'mx-auto',
                'pb-16px',
                'a-bg-substrate',
                'rounded-6px',
                getLengthClass(awardLength),
            ]"
        >
            <div
                v-pcDirectives:scroll="handleScroll"
                class="rank-award-scfollList"
                @scroll="handleScroll"
            >
                <div
                    v-for="(award, index) in awardList"
                    :key="award.awardId"
                    class="rank-award-item"
                    @click="
                        () => {
                            handleAwardClick(award, index);
                        }
                    "
                >
                    <img :src="award.awardIcon" alt="活动奖励说明" />
                    <div class="rank-award-text">
                        {{ award.awardName }}
                    </div>
                </div>
            </div>
            <div
                v-show="leftMaskShow && awardLength > 4"
                class="rank-award-scfollList-left-mask"
            ></div>
            <div
                v-show="rightMaskShow && awardLength > 4"
                class="rank-award-scfollList-right-mask"
            ></div>
        </div>
        <!-- 弹窗组件 -->
        <Popup
            v-if="showPrizeDetail"
            v-model="showPrizeDetail"
            :show-mask="true"
            position="bottom"
            popup-class="prize-popup-wrap"
            :mask-closeable="true"
            @hide="showPrizeDetail = false"
        >
            <slot
                name="award-scroll-popup-content"
                :top-awards="topAwards"
                :popup-title="popupTitle"
                :show-prize-detail="showPrizeDetail"
            >
                <awardPopupContent
                    v-model:swiper-index="swiperIndex"
                    :top-awards="topAwards"
                    :popup-title="popupTitle"
                    :show-prize-detail="showPrizeDetail"
                />
            </slot>
        </Popup>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onBeforeUnmount } from 'vue';
import { Popup } from '@lux/sharp-ui-next';
import {
    stopMove,
    isYodaPCContainer,
    type StopFunItem,
} from '@alive-ui/actions';
import awardPopupContent from './award-popup-content.vue';
import type { PropType } from 'vue';
import type { ChampionAward, TopAward } from '../schema';
const props = defineProps({
    awardList: {
        type: Array as PropType<ChampionAward[]>,
        default: () => [],
    },
    topAwards: {
        type: Array as PropType<TopAward[]>,
        default: () => [],
    },
    isFinals: {
        type: Boolean,
        default: false,
    },
    popTitle: {
        type: String,
        default: '',
    },
});
const leftMaskShow = ref(false);
const rightMaskShow = ref(true);
const awardLength = computed(() => {
    return props.awardList?.length || 0;
});
const popupTitle = computed(() => {
    //  2025夏季盛典不区分决赛和非决赛阶段，但是保留这个判断逻辑吧，以免后续又要区分
    return props.isFinals ? '比赛奖励' : '比赛奖励';
});
const swiperIndex = ref(0);

const getLengthClass = (length: number) => {
    if (!length) return '';
    return length > 4 ? 'length-more-than-4' : `length-${length}`;
};

const handleScroll = (e: any) => {
    const scrollRef = e.target;
    if (!scrollRef) return;
    if (scrollRef.scrollLeft === 0) {
        leftMaskShow.value = false;
    } else if (
        scrollRef.scrollLeft + scrollRef.offsetWidth + 0.5 >=
        scrollRef.scrollWidth
    ) {
        rightMaskShow.value = false;
    } else {
        leftMaskShow.value = true;
        rightMaskShow.value = true;
    }
};
const scrollChange = (
    pos: { x: number; y: number },
    max: { maxScrollX: number; maxScrollY: number },
) => {
    if (!isYodaPCContainer) return;
    if (pos.x >= 0) {
        leftMaskShow.value = false;
    } else if (pos.x <= max.maxScrollX) {
        rightMaskShow.value = false;
    } else {
        leftMaskShow.value = true;
        rightMaskShow.value = true;
    }
};
const handleAwardClick = (award: ChampionAward, index: number) => {
    showPrizeDetail.value = true;
    swiperIndex.value = index;
};

const showPrizeDetail = ref(false);

// 处理ios弹窗溢出滚动
let stopMoveObj: StopFunItem;

watch(
    () => showPrizeDetail.value,
    (o) => {
        if (o) {
            stopMoveObj = stopMove();
        } else {
            stopMoveObj?.cancelMove?.();
        }
    },
    { immediate: true },
);
onBeforeUnmount(() => {
    stopMoveObj?.cancelMove?.();
});
</script>

<style lang="less" scoped>
.prize-popup-wrap {
    :deep(.prize-popup) {
        @apply a-bg-page;
    }
}
.rank-award-content {
    width: 358px;
    min-height: 100px;
    font-size: 0;
    padding: 19px 12px 16px;
    position: relative;
    overflow: hidden;
    img {
        width: 100%;
    }
}
.rank-award-scfollList {
    width: 100%;
    height: 100%;
    overflow-x: scroll;
    overflow-y: hidden;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
        display: none;
        width: 0;
    }
    &-left-mask {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 32px;
        height: 126px;
        background: url(../rank-award-scroll-mask_2x.png) center / 100%
            no-repeat;
        transform: scaleX(-1);
    }

    &-right-mask {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 32px;
        height: 126px;
        background: url(../rank-award-scroll-mask_2x.png) center / 100%
            no-repeat;
    }
}
.rank-award-text {
    font-size: 10px;
    line-height: 14px;
    opacity: 0.6;
    text-align: center;
    color: #ffdfbf;
}
.rank-award-item {
    width: 84px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:last-child {
        margin-right: 0 !important;
    }
    img {
        width: 72px;
        height: 72px;
        margin-bottom: 4px;
    }
}
// item长度为1的样式
.length-1 {
    padding: 8px 0 16px;
    .rank-award-scfollList {
        justify-content: center;
    }
}
// item长度为2的样式
.length-2 {
    padding: 8px 0 16px;
    .rank-award-item {
        margin-right: 32px;
    }
    .rank-award-scfollList {
        justify-content: center;
    }
}
// item长度为3的样式
.length-3 {
    padding: 8px 0 16px;
    .rank-award-item {
        margin-right: 20px;
    }
    .rank-award-scfollList {
        justify-content: center;
    }
}
// item长度为4的样式
.length-4 {
    padding: 8px 0 16px;
    .rank-award-scfollList {
        justify-content: center;
    }
}
// item长度大于4
.length-more-than-4 {
    padding: 8px 12px 16px;
    .rank-award-item {
        margin-right: 12px;
    }
    .rank-award-scfollList {
        justify-content: flex-start;
    }
}
</style>
