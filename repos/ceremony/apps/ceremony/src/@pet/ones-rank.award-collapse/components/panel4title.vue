<template>
    <div class="panel4-title">
        <div v-if="isShowArrow && !subTitle" class="lt-wrap">
            <slot v-if="$slots.lt" name="lt" />
            <div v-else class="btn gis-lt-arrow" />
        </div>
        <div class="title flex-center-center">
            <div class="card-title-icon card-title-icon-attrs"></div>
            <span class="title-span">{{ title }}</span>
            <div v-if="subTitle" :class="['tag', getSubtitleSize()]">
                <span class="tag-desc">{{ subTitle }}</span>
            </div>
            <div
                class="card-title-icon card-title-icon-attrs card-title-icon-right"
            ></div>
        </div>
        <div v-if="isShowArrow && !subTitle" class="rt-wrap">
            <slot v-if="$slots.rt" name="rt" />
            <div v-else class="btn gis-rt-arrow" />
        </div>
    </div>
</template>

<script lang="ts" setup>
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    isShowArrow: {
        type: Boolean,
        default: true,
    },
    subTitle: {
        type: String,
        default: '',
    },
});

const getSubtitleSize = () => {
    return props.subTitle.length >= 6 ? 'large' : 'sm';
};
</script>

<style lang="less" scoped>
.panel4-title {
    display: flex;
    justify-content: center;
    align-items: center;
}

.title {
    position: relative;
    margin: 0 4px;
    .title-span {
        // font-family: var(--titleFontFamily);
        // font-family: FZRuiZhengHeiS-EB-GB;
        font-family: HYYakuHei;
        font-size: 20px;
        font-weight: 400;

        @apply a-text-title;

        background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .tag {
        position: absolute;
        top: 6px;
        display: inline-flex;
        height: 20px;
        font-family: PingFangSC, PingFangSC-Medium;
        font-size: 0.2rem;
        font-weight: 500;
        line-height: 0.34rem;
        color: #fff;
        background: linear-gradient(262.85deg, #ff5eb5 29.69%, #ff84a1 100%);
        border-radius: 0.08rem;
        align-items: center;
        justify-content: center;
        // .font-10();

        .tag-desc {
            position: relative;
            top: -1px;
        }
        &.sm {
            width: 48px;
        }
        &.large {
            width: 55px;
        }
    }
}
</style>
