<template>
    <div class="prize-popup">
        <div class="top-bg card-bg-img card-bg-img-attrs"></div>
        <Panel4Title :title="popupTitle" :is-show-arrow="false" />
        <ATabs
            v-if="topAwards?.length > 1"
            class="pop-tab"
            type="solid"
            :default-index="popTabIndex"
            @change="handleTabChange"
        >
            <ATabList>
                <ATab v-for="(item, index) in topAwards" :key="index">
                    {{ item.rankName }}
                </ATab>
            </ATabList>
        </ATabs>
        <div v-if="showPrizeDetail" class="glory">
            <Swiper
                ref="swiper"
                :key="popTabIndex"
                :loop="true"
                :init-index="swiperIndex"
                :indicator-show="false"
                :is-overflow-hidden="false"
                :follow-config="{
                    isFollow: true,
                    scale: {
                        minScale: 0.7,
                    },
                }"
                @change="onChange"
            >
                <div
                    v-for="item in currentRewardList || []"
                    :key="item.awardId"
                    class="award-swiper-img"
                >
                    <YodaImage
                        :src="item.awardImg"
                        @error="onError"
                    ></YodaImage>
                </div>
            </Swiper>
        </div>

        <div
            :key="currentRewardList?.[swiperIndex]?.awardId"
            class="panel-prize-desc a-text-main"
        >
            <h3>
                {{ currentRewardList?.[swiperIndex]?.awardName }}
            </h3>
            <p v-html="currentRewardList?.[swiperIndex]?.awardDesc" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import YodaImage from '@pet/yoda.image/img.vue';
import { Swiper } from '@lux/sharp-ui-next';
import { ATabs, ATabList, ATab } from '@alive-ui/base';
import { type TopAward } from '../schema';
import Panel4Title from './panel4title.vue';
import type { PropType } from 'vue';
const props = defineProps({
    topAwards: {
        type: Array as PropType<TopAward[]>,
        default: () => [],
    },
    popupTitle: {
        type: String,
        default: '',
    },
    showPrizeDetail: {
        type: Boolean,
        default: false,
    },
    swiperIndex: {
        type: Number,
        default: 0,
    },
});

const popTabIndex = ref(0);
const emit = defineEmits(['update:swiperIndex']);

const handleTabChange = (index: number) => {
    popTabIndex.value = index;
    emit('update:swiperIndex', 0);
};
const onChange = (index: number) => {
    emit('update:swiperIndex', index);
};
const currentRewardList = computed(() => {
    return props.topAwards?.[popTabIndex.value]?.awardItemIcons;
});
const onError = (e: Event) => {
    console.log(e);
};
</script>

<style lang="less" scoped>
.prize-popup {
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
    width: 100%;
    padding: 20px 0 30px;
    overflow: hidden;
    border-radius: 18px 18px 0 0;
    box-sizing: border-box;
    .top-bg {
        position: absolute;
        top: 0;
        width: 100%;
        height: 176px;
    }

    .panel-prize-desc {
        padding: 0 17px;
        margin-top: 14px;

        h3 {
            font-family: 'PingFang SC';
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            text-align: center;
        }

        p {
            height: 36px;
            margin-top: 8px;
            font-size: 12px;
            line-height: 18px;
            text-align: center;
            word-wrap: break-word;
            white-space: normal;
            opacity: 0.6;
            overflow-wrap: break-word;
        }
    }
}
.pop-tab {
    margin-top: 16px;

    .tabs-tab-attrs {
        line-height: 30px;
    }
}
.glory {
    width: 50%;
    height: 200px;
    margin: 12px auto 0;

    .award-swiper-img {
        --y-img-height: 200px;
        --y-img-width: 200px;
    }
}
</style>
