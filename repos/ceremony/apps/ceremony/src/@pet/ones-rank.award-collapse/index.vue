<template>
    <div
        v-if="
            newData?.marqueeList?.length &&
            (newData.awardURL || newData.awardList?.length)
        "
        :key="newData.randomKey"
        class="mt-14px mb-14px"
    >
        <AInfo type="solid">
            <AMarquee class="a-text-main opacity-60">
                <AInfoHighlight
                    v-for="(item, index) in newData.marqueeList"
                    :key="index"
                    :text="item"
                />
            </AMarquee>
            <template #extra>
                <AInfoButton
                    v-clickLog="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: '奖励',
                        },
                    }"
                    v-showLog="{
                        action: 'OP_ACTIVITY_FUNCTION_BUTTON',
                        params: {
                            btn_type: '奖励',
                        },
                    }"
                    class="rank-award-button"
                    @click="foldCollapse"
                >
                    <span class="inline-block mr-4px">
                        {{ newData.operateText }}</span
                    >
                    <template #suffix>
                        <Up :class="{ 'rotate-180': !foldStatus }"
                    /></template>
                </AInfoButton>
            </template>
        </AInfo>
        <div v-show="foldStatus" class="mt-14px">
            <img
                v-if="newData.awardURL"
                class="w-auto h-full rank-award-url"
                :src="newData.awardURL"
                alt="活动奖励说明"
            />
            <slot
                v-else
                name="extra"
                :award-list="newData.awardList"
                :top-awards="newData.topAwards"
                :is-finals="newData.isFinals"
            >
                <AwardScroll
                    :award-list="newData.awardList"
                    :pop-title="popTitle"
                    :top-awards="newData.topAwards"
                    :is-finals="newData.isFinals"
                />
            </slot>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { Up } from '@alive-ui/icon';
import { Info, Marquee as AMarquee } from '@alive-ui/base';
import { dataFun } from './data';
import AwardScroll from './components/award-scroll.vue';
import type { AwardSchema, PropsCommonParams } from './schema';
const {
    Info: AInfo,
    InfoHighlight: AInfoHighlight,
    InfoButton: AInfoButton,
} = Info;

interface AwardData extends PropsCommonParams {
    data?: AwardSchema;
    popTitle?: string;
}
const props = withDefaults(defineProps<AwardData>(), {
    data: () => {
        return {
            randomKey: 0,
            marqueeList: [],
            awardURL: '',
            awardList: [],
            showAward: true,
            operateText: '奖励',
            topAwards: [],
        };
    },
    useProps: false,
    contextName: Symbol.for('ctx'),
});

const newData = props.useProps
    ? computed(() => props.data)
    : dataFun(props.contextName);
const foldStatus = ref(false);

watch(
    () => newData?.value.showAward,
    (val, old) => {
        if (val && val !== old) {
            foldStatus.value = val;
        }
    },
    { immediate: true },
);

const foldCollapse = () => {
    foldStatus.value = !foldStatus.value;
};
</script>
<style lang="less" scoped>
.rank-award-button {
    height: 100%;
    white-space: nowrap;
}
.rank-award-url {
    width: 100%;
}
</style>
