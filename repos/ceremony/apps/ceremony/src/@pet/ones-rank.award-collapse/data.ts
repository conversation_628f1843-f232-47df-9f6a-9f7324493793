import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';
import type { AwardSchema } from './schema';
export const dataFun = (contextName: symbol) => {
    const rankInfo = contextName
        ? inject<Ref<QueryRankPostResponse>>(contextName)
        : null;
    const data = computed<AwardSchema>(() => {
        return {
            randomKey: +new Date(),
            // 奖励提示文案
            marqueeList:
                rankInfo?.value?.extraData?.privilegeDesc
                    ?.split('$')
                    ?.filter((i) => i) || [],
            //  奖励图片
            awardURL: rankInfo?.value?.extraData?.privilegeIconUrl || '',
            // 荣耀奖励列表
            awardList:
                rankInfo?.value?.extraData?.privilegeOverview?.championAwards ||
                [],
            showAward: true,
            operateText: '奖励',
            // 冠军亚军、十强奖励tab切换
            topAwards:
                rankInfo?.value?.extraData?.privilegeOverview?.topAwards || [],
            isFinals:
                rankInfo?.value?.extraData?.isActivityFinalSchedule || false,
        };
    });

    return data;
};
