<template>
    <div
        v-clickLog="{
            action: 'OP_ACTIVITY_FUNCTION_BUTTON',
            params: {
                btn_type: '完整榜单',
            },
        }"
        class="view-more a-text-main text-14px"
        @click="viewRank"
    >
        完整榜单<Right class="a-text-main ml-1px w-10px h-10px" />
    </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { getCurrentInstance } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { Right } from '@alive-ui/icon';
const { kconfData } = storeToRefs(useKconfStore());
const props = withDefaults(defineProps<{ stageType: number }>(), {
    stageType: 0,
});
const router = useRouter();
const viewRank = async () => {
    const link =
        kconfData.value?.common?.stageGroup?.linkGroup?.[props.stageType];
    await router.replace({
        query: {
            ...router.currentRoute.value.query,
            cardGroup: undefined,
        },
    });
    router
        .push({
            name: link || 'rank',
        })
        .catch(console.error);
};
</script>
<style lang="less" scoped>
.view-more {
    white-space: nowrap;
}
</style>
