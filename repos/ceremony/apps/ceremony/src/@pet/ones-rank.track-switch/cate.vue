<template>
    <div v-if="dynCityName" class="daily-category-switch">
        <div class="flex align-center" @click="switchCategory">
            <div class="current-city-name">
                {{ nameSlice(dynCityName, 6) }}赛道
            </div>
            <div class="gis-daily-switch-icon" />
        </div>
        <Popup
            v-model="isShowPop"
            :mask-closeable="true"
            position="bottom"
            title="cdc"
        >
            <div class="category-select-content">
                <div class="category-selector-header flex align-center">
                    <div class="left-opertate-text pop-text" @click="onHide">
                        取消
                    </div>
                    <div class="select-title-text">
                        {{ cateGoryData.title }}
                    </div>
                    <div
                        class="right-opertate-text pop-text"
                        @click="onComplete"
                    >
                        完成
                    </div>
                </div>
                <div
                    v-if="!isOutLiveRoom"
                    class="current-author-lane flex align-center"
                >
                    当前主播所属赛道是: {{ cateGoryData.authorCityName }}赛道
                    <div class="back-self-button" @click="goBackSelfLane">
                        返回当前赛道
                    </div>
                </div>
                <div
                    class="select-data-main flex"
                    :class="{ 'ios-bottom': osName === 'ios' }"
                >
                    <div
                        v-for="(elem, index) in cateGoryData.options"
                        :key="index"
                        class="select-elem-component"
                        :class="{
                            'active-select-elem': currentActive === index,
                        }"
                        @click="handleCategorySelect(elem, index)"
                    >
                        {{ elem.label }}
                    </div>
                    <!-- <Selector
                        v-if="options?.length"
                        v-model="selected"
                        :options="options"
                        :multiple="false"
                        @change="handleCategorySelect"
                    ></Selector> -->
                </div>
            </div>
        </Popup>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Popup } from '@lux/sharp-ui-next';
import { osName } from '@alive-ui/system';
import { nameSlice, isOutLiveRoom } from '@alive-ui/actions';
import type { PropType } from 'vue';

const props = defineProps({
    cateGoryData: {
        type: Object as PropType<{
            authorCityName: string;
            currentCityName: string;
            anchorId: string;
            options: any[];
            title: string;
        }>,
        required: true,
        default: () => {
            return {};
        },
    },
});
const emits = defineEmits(['category-select']);

const currentActive = ref(0);
const initAcitve = ref(0);
const authorAcitve = ref(0);
const isShowPop = ref(false);
const dynCityName = ref('');
watch(
    () => props.cateGoryData.currentCityName,
    (newVal) => {
        console.log('newVal', newVal);
        dynCityName.value = newVal;
    },
    {
        immediate: true,
    },
);
watch(
    () => props.cateGoryData.anchorId,
    (newVal) => {
        const index = props.cateGoryData?.options?.findIndex((elem, index) => {
            return elem.anchorId === props.cateGoryData?.anchorId;
        });
        authorAcitve.value = index;
        initAcitve.value = index;
        currentActive.value = props.cateGoryData.options?.findIndex(
            (item, index) => {
                return item.displayName === dynCityName.value;
            },
        );
    },
    {
        immediate: true,
    },
);
const switchCategory = () => {
    isShowPop.value = true;
};

const onHide = () => {
    // currentActive.value = initAcitve.value;
    selectItem.value = props.cateGoryData?.options?.[initAcitve.value];
    isShowPop.value = false;
};

const goBackSelfLane = () => {
    currentActive.value = authorAcitve.value;
    selectItem.value = props.cateGoryData?.options?.[authorAcitve.value];
    emits('category-select', selectItem.value?.rankId);
    isShowPop.value = false;
};
const selectItem = ref();
const handleCategorySelect = (item: any, index: number) => {
    currentActive.value = index;
    selectItem.value = item;
};
const onComplete = () => {
    isShowPop.value = false;
    if (!selectItem.value) {
        return;
    }
    initAcitve.value = currentActive.value;
    // selected.value = [selectItem.value.anchorId];
    emits('category-select', selectItem.value?.rankId);
};
</script>

<style lang="less" scoped>
.daily-category-switch {
    margin-left: -70px;
    margin-top: 2px;
    white-space: nowrap;
    .current-city-name {
        color: var(--main-color);
        font-size: 12px;
        font-family: PingFang SC;
        line-height: 18px;
        text-align: left;
        vertical-align: top;
        font-size: 12px;
        margin-right: 2px;
    }
    .gis-daily-switch-icon {
        width: 10px;
        height: 10px;
        background: url('./switchIcon.png') center / 100% no-repeat;
    }
}
.current-author-lane {
    font-size: 17px;
    padding: 10px;
    justify-content: space-between;
}
.back-self-button {
    width: 116px;
    height: 40px;
    border-radius: 28px;
    background-color: #fe3666;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}
.right-opertate-text {
    font-size: 16px;
    color: #ff5477;
}
.select-data-main {
    max-width: 100%;
    max-height: 200px;
    overflow-y: scroll;
    padding: 0 5px 35px;
    flex-wrap: wrap;
}
.ios-bottom {
    padding-bottom: calc(
        35px + constant(safe-area-inset-bottom)
    ); /* 兼容 iOS < 11.2 */
    padding-bottom: calc(
        35px + env(safe-area-inset-bottom)
    ); /* 兼容 iOS >= 11.2 */
}
.select-elem-component {
    // background: #fff;
    // border: 0.5px solid #eaeaea;
    color: #222;
    margin: 10px 7px;
    width: 120px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    background: rgba(#d9d9d9, 0.2);
}
.active-select-elem {
    background: #ffeef2;
    border: 0.5px solid rgb(254 54 102 / 30%);
    color: #fe3666;
    opacity: 1;
}
.category-select-content {
    position: relative;
    box-sizing: border-box;
    height: 300px;
    overflow: hidden;
    width: 100%;
    background-color: #fff;
    color: #666666;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    .category-selector-header {
        position: relative;
        position: sticky;
        height: 56px;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        border-bottom: 1px solid #eaeaea;
    }
    .left-opertate-text {
        font-family: PingFang SC;
        font-size: 16px;
        font-weight: 500;
        text-align: left;
        color: #666666;
    }
    .select-title-text {
        font-family: PingFang SC;
        font-size: 17px;
        font-weight: 500;
        text-align: center;
        color: #000000;
    }
}
</style>
