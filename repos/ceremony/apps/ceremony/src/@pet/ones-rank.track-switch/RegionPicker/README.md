# 地区选择器

## 使用方法

### props

```typescript
const props = withDefaults(
    defineProps<{
        show?: boolean;
        height?: number;
        pickerTitle?: string;
        recommendTitle?: string;
        showRecommend?: boolean;
    }>(),
    {
        show: false,
        height: 500,
        pickerTitle: "请选择地区赛道",
        recommendTitle: "推荐赛道",
        showRecommend: true,
    },
);
```

### emit

```typescript
const emit = defineEmits(["select-recommend", "select-region", "update:show"]);
```

### 示例

```vue
<template>
    <RegionPickerPopup
        :show.sync="isShowPicker"
        @select-recommend="handleRecommendSelect"
        @select-region="handleRegionSelect"
    />
</template>

<script setup lang="ts">
import { ref } from "vue";
import RegionPickerPopup from "../../components/RegionPicker/RegionPickerPopup.vue";

const isShowPicker = ref(false);

const handleRecommendSelect = (item: any) => {
    console.log(item);
    isShowPicker.value = false;
};

const handleRegionSelect = (item: any) => {
    console.log(item);
    isShowPicker.value = false;
};
</script>

<style scoped lang="less"></style>
```
