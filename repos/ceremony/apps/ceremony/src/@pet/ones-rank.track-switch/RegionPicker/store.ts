import { ref } from 'vue';
import { defineStore } from 'pinia';
import { activityBiz, liveStreamId, getPageStatus } from '@alive-ui/actions';
import { fetchRegion } from './services/region-picker';
import type {
    ResidentSchedule,
    RecommendSchedule,
    ScheduleChooseMap,
} from './schemas/scheduleChoose';
import type {
    RecommendItem,
    RegionMap,
    RegionItem,
    RecommendType,
} from './schema';

const useRegionPickerStore = defineStore('store_region_picker', () => {
    // eslint-disable-next-line no-undefined
    const regionData = ref<RegionMap | undefined>(undefined);
    const recommendData = ref<RecommendItem[]>([]);
    const pickerStatus = ref(getPageStatus('loading'));

    const transformRecommendData = (
        data: ResidentSchedule[] | RecommendSchedule[],
        type: RecommendType,
        tag: string,
    ) => {
        return data.map((item) => {
            const { displayName } = item;
            const recommendItem: RecommendItem = {
                label: displayName,
                tag,
                value: item,
                type,
            };

            return recommendItem;
        });
    };

    const transformRegionData = (data: ScheduleChooseMap) => {
        const regionMap: RegionMap = {};

        Object.entries(data).forEach((entry) => {
            const [groupName, items] = entry;

            regionMap[groupName] = items.map((item) => {
                const { displayName, subScheduleNodes } = item;
                const regionItem: RegionItem = {
                    label: displayName,
                    value: item,
                };

                if (subScheduleNodes) {
                    regionItem.children = transformRegionData(subScheduleNodes);
                }

                return regionItem;
            });
        });

        return regionMap;
    };

    const getRegionData = async (scheduleType: number) => {
        try {
            pickerStatus.value = getPageStatus('loading');

            const { recommendSchedules, residentSchedule, scheduleChooseMap } =
                await fetchRegion({
                    activityBiz,
                    liveStreamId,
                    scheduleType,
                });

            recommendData.value = [
                ...transformRecommendData(recommendSchedules, 'hot', '热门'),
                ...transformRecommendData(
                    residentSchedule ? [residentSchedule] : [],
                    'resident',
                    '常驻',
                ),
            ];
            regionData.value = transformRegionData(scheduleChooseMap);

            pickerStatus.value = getPageStatus('success');
        } catch (err) {
            console.error(err);
            pickerStatus.value = getPageStatus('error');
        }
    };

    return {
        regionData,
        recommendData,
        getRegionData,
        pickerStatus,
    };
});

export default useRegionPickerStore;
