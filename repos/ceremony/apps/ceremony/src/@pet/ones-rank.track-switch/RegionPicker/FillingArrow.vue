<template>
    <svg
        t="1698547732171"
        class="filling-arrow"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="3842"
    >
        <path
            v-if="direction === 'down'"
            d="M482.133333 738.133333L136.533333 392.533333c-17.066667-17.066667-17.066667-42.666667 0-59.733333
         8.533333-8.533333 19.2-12.8 29.866667-12.8h689.066667c23.466667 0 42.666667 19.2 42.666666 42.666667
         0 10.666667-4.266667 21.333333-12.8 29.866666L541.866667 738.133333c-17.066667 17.066667-42.666667 17.066667-59.733334 0z"
            :fill="active ? activeColor : inActiveColor"
            p-id="3843"
        />
        <path
            v-else
            d="M541.866667 285.866667l345.6 345.6c17.066667 17.066667 17.066667 42.666667 0 59.733333-8.533333
         8.533333-19.2 12.8-29.866667 12.8H168.533333c-23.466667 0-42.666667-19.2-42.666666-42.666667
         0-10.666667 4.266667-21.333333 12.8-29.866666l343.466666-345.6c17.066667-17.066667 42.666667-17.066667 59.733334 0z"
            :fill="active ? activeColor : inActiveColor"
            p-id="3991"
        />
    </svg>
</template>

<script setup lang="ts">
withDefaults(
    defineProps<{
        direction?: 'up' | 'down';
        active?: boolean;
        activeColor?: string;
        inActiveColor?: string;
    }>(),
    {
        direction: 'up',
        active: true,
        activeColor: '#363636',
        inActiveColor: '#bdbdbd',
    },
);
</script>

<style scoped lang="less">
.filling-arrow {
    width: 14px;
    height: 14px;
}
</style>
