<template>
    <Popup
        v-model="isShowPicker"
        :show-mask="true"
        :mask-closeable="true"
        :closeable="true"
        :disable-scroll="true"
        position="bottom"
        popup-class="region-picker-popup"
    >
        <RegionPicker
            ref="regionPickerRef"
            :show="isShowPicker"
            :height="height"
            :picker-title="pickerTitle"
            :recommend-title="recommendTitle"
            :show-recommend="showRecommend"
            :schedule-type="scheduleType"
            :cur-region-key="curRegionKey"
            :cur-region-val="curRegionVal"
            :exclude-recommend-key="excludeRecommendKey"
            :exclude-recommend-val="excludeRecommendVal"
            @select-recommend="handleRecommendSelect"
            @select-region="handleRegionSelect"
        />
    </Popup>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue';
import { Popup } from '@lux/sharp-ui-next';
import '@lux/sharp-ui-next/themes/default/popup';
import { stopMove } from '@alive-ui/system';
import useStore from './store';
import RegionPicker from './RegionPicker.vue';
import type { StopFunItem } from '@alive-ui/system';

const props = withDefaults(
    defineProps<{
        show?: boolean;
        height?: number;
        pickerTitle?: string;
        recommendTitle?: string;
        showRecommend?: boolean;
        scheduleType?: number;
        curRegionKey?: string;
        curRegionVal?: string | number;
        excludeRecommendKey?: string;
        excludeRecommendVal?: (string | number)[];
    }>(),
    {
        show: false,
        height: 500,
        pickerTitle: '请选择地区赛道',
        recommendTitle: '推荐赛道',
        showRecommend: true,
        // eslint-disable-next-line no-undefined
        scheduleType: undefined,
        curRegionKey: 'anchorId',
        // eslint-disable-next-line no-undefined
        curRegionVal: undefined,
        excludeRecommendKey: 'anchorId',
        // eslint-disable-next-line no-undefined
        excludeRecommendVal: undefined,
    },
);
const emit = defineEmits<{
    (e: 'select', value: any): void;
    (e: 'update:show', value: boolean): void;
}>();

const store = useStore();
// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
const regionPickerRef = ref<null | InstanceType<typeof RegionPicker>>(null);

const isShowPicker = computed<boolean>({
    get() {
        return props.show;
    },
    set(newVal) {
        emit('update:show', newVal);
    },
});

let stopMoveObj: StopFunItem;

watch(
    () => isShowPicker.value,
    (o) => {
        if (o) {
            stopMoveObj = stopMove();
            regionPickerRef.value?.refresh();
            regionPickerRef.value?.resetPosition();
        } else {
            stopMoveObj?.cancelMove?.();
        }
    },
    { immediate: true },
);

watch(
    () => store.pickerStatus,
    async (newVal) => {
        if (newVal.success && isShowPicker.value) {
            await nextTick();
            regionPickerRef.value?.refresh();
            regionPickerRef.value?.resetPosition();
        }
    },
);

const handleRecommendSelect = (item: any) => {
    emit('select', item);
};

const handleRegionSelect = (item: any) => {
    emit('select', item);
};
</script>

<style scoped lang="less">
.region-picker-popup {
    max-width: 414px;
    margin: 0 auto;
    :deep(.spu-popup__box__close) {
        top: 25px;
        right: 20px;
        font-size: 24px !important;
    }
}
</style>
