<template>
    <div class="region-recommend">
        <div class="region-recommend__title">
            {{ title }}
        </div>
        <div class="region-recommend__item-wrap">
            <div
                v-for="item in list"
                :key="item.label"
                class="region-recommend__item"
            >
                <div
                    class="region-recommend__item-label"
                    @click="handleItemClick(item.value)"
                >
                    {{ item.label }}
                </div>
                <div class="region-recommend__item-tag">
                    {{ item.tag }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { RecommendItem } from './schema';

withDefaults(
    defineProps<{
        title?: string;
        list?: RecommendItem[];
    }>(),
    {
        title: '',
        list: () => [],
    },
);
const emit = defineEmits<{
    (e: 'select', value: any): void;
}>();

const handleItemClick = (val: any) => {
    emit('select', val);
};
</script>

<style scoped lang="less">
.region-recommend {
    font-size: 17px;
    font-weight: 500;
    color: #000;
    &__title {
        margin-bottom: 15px;
        font-size: 12px;
        font-weight: 500;
        color: #363636;
        opacity: 0.5;
    }
    &__item {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        margin-right: 30px;
        margin-bottom: 15px;
        &-wrap {
            display: flex;
            flex-wrap: wrap;
            margin-right: -30px;
            margin-bottom: -15px;
        }
        &-label {
            margin-right: 11px;
            font-size: 17px;
            font-weight: 700;
            color: #000;
        }
        &-tag {
            height: 17px;
            padding: 1px 4px;
            font-size: 10px;
            font-weight: 600;
            line-height: 15px;
            color: #fff;
            background: linear-gradient(263deg, #ff586d 0%, #ff4594 100%);
            border-radius: 2px;
        }
    }
}
</style>
