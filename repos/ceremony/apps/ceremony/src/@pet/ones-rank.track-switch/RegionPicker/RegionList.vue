<template>
    <div class="region-list">
        <div class="region-list__tab">
            <div
                v-for="(item, index) in regionMapList"
                :key="index"
                class="region-list__tab-item"
                :class="{
                    'region-list__tab-item--active':
                        index === curRegionMapIndex,
                }"
                @click="handleTabClick(index)"
            >
                {{ nameSlice(item.cur || '请选择') }}
            </div>
        </div>
        <div class="region-list__divider" />
        <div class="region-list__main">
            <div ref="listWrapEl" class="region-list__group-list-wrap">
                <div class="region-list__group-list">
                    <div
                        v-for="groupName in Object.keys(curRegionMap)"
                        ref="groupEls"
                        :key="groupName"
                        class="region-list__group"
                        :data-group="groupName"
                    >
                        <div class="region-list__group-label">
                            {{ groupName }}
                        </div>
                        <div
                            v-for="(item, index) in curRegionMap[groupName]"
                            ref="regionItemEls"
                            :key="item.label + index"
                            class="region-list__group-item"
                            :class="{
                                'region-list__group-item--active':
                                    item.label === curRegionLabel,
                            }"
                            :data-region-item="item.label"
                            @click="handleItemClick(item)"
                        >
                            {{ item.label }}
                        </div>
                    </div>
                    <div
                        class="region-list__empty"
                        :style="{ height: `${emptyHeight}px` }"
                    />
                </div>
            </div>
            <div class="region-list__index-container">
                <div
                    ref="indexWrapEl"
                    class="region-list__index-wrap"
                    :class="{
                        'region-list__index-wrap--arrow-up': showIndexArrowUp,
                        'region-list__index-wrap--arrow-down':
                            showIndexArrowDown,
                    }"
                >
                    <div ref="indexListEl" class="region-list__index">
                        <div
                            v-for="groupName in Object.keys(curRegionMap)"
                            ref="indexEls"
                            :key="groupName"
                            class="region-list__index-item"
                            :class="{
                                'region-list__index-item--active':
                                    groupName === curGroup,
                            }"
                            :data-group="groupName"
                            @click="jumpGroup(groupName)"
                        >
                            {{ groupName }}
                        </div>
                    </div>
                </div>
                <div
                    ref="indicatorEl"
                    class="region-list__index-indicator"
                    :style="{ transform: `translateY(${indicatorOffsetY}px)` }"
                    :class="{
                        'region-list__index-indicator--visible': showIndicator,
                    }"
                >
                    {{ curGroup }}
                </div>
                <FillingArrow
                    v-if="showIndexArrowUp"
                    ref="indexArrowUpEl"
                    class="region-list__index-arrow-up"
                    direction="up"
                    :active="isIndexArrowUpActive"
                    @click="scrollIndex('up')"
                />
                <FillingArrow
                    v-if="showIndexArrowDown"
                    class="region-list__index-arrow-down"
                    direction="down"
                    :active="isIndexArrowDownActive"
                    @click="scrollIndex('down')"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch, onBeforeUnmount } from 'vue';
import { cloneDeep } from 'lodash-es';
import BScroll from '@better-scroll/core';
import { nameSlice } from '@alive-ui/actions';
import { TouchHandler } from './touch';
import FillingArrow from './FillingArrow.vue';
import type {
    RegionMap,
    RegionItem,
    RegionMapListItem,
    RegionInfoMap,
} from './schema';

const props = withDefaults(
    defineProps<{
        data?: RegionMap;
        curRegionKey?: string;
        curRegionVal?: string | number;
        addEmpty?: boolean;
    }>(),
    {
        data: () => ({}),
        // eslint-disable-next-line no-undefined
        curRegionKey: undefined,
        // eslint-disable-next-line no-undefined
        curRegionVal: undefined,
        addEmpty: false,
    },
);
const emit = defineEmits<{
    (e: 'select', value: any): void;
}>();

const regionMapList = ref<RegionMapListItem[]>([
    {
        cur: '',
        map: props.data,
    },
]);
const curRegionMapIndex = ref(0);

const listWrapEl = ref<HTMLElement | null>(null);
const groupEls = ref<HTMLElement[] | null>(null);
const indexEls = ref<HTMLElement[] | null>(null);
const regionItemEls = ref<HTMLElement[] | null>(null);
const indexWrapEl = ref<HTMLElement | null>(null);
const indexListEl = ref<HTMLElement | null>(null);
const indicatorEl = ref<HTMLElement | null>(null);
// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
const indexArrowUpEl = ref<InstanceType<typeof FillingArrow> | null>(null);
const listBs = ref<BScroll | null>(null);
const indexBs = ref<BScroll | null>(null);
const listTouch = ref<TouchHandler | null>(null);
const indexTouch = ref<TouchHandler | null>(null);
const indicatorOffsetY = ref(0);
const showIndicator = ref(false);
const emptyHeight = ref(0);
const showIndexArrowUp = ref(false);
const showIndexArrowDown = ref(false);
const isIndexArrowUpActive = ref(false);
const isIndexArrowDownActive = ref(false);

const SCROLL_DURATION = 500;
let scrollIndexUpTimer: number | null = null;
let scrollIndexDownTimer: number | null = null;

const curRegionMap = computed(
    () => regionMapList.value[curRegionMapIndex.value].map,
);
const curRegionLabel = computed(
    () => regionMapList.value[curRegionMapIndex.value].cur ?? '',
);

const curGroup = ref(Object.keys(curRegionMap.value)[0]);
const curRegionGroup = ref(curGroup.value);

const updateCurRegionInfoMap = (
    infoMap: RegionInfoMap,
    mapList: RegionMapListItem[],
    index: number,
    data: RegionMap,
    curKey?: string,
) => {
    Object.entries(data).forEach((group) => {
        const [groupName, groupItems] = group;

        groupItems.forEach((item) => {
            const { label, children, value } = item;
            const mapListCopy = [
                ...mapList.slice(0, mapList.length - 1),
                {
                    cur: label,
                    map: mapList?.slice()?.pop()?.map || {},
                },
            ];

            if (curKey && value[curKey]) {
                infoMap.set(value[curKey], {
                    curRegionMapIndex: index,
                    regionMapList: mapListCopy,
                    groupName,
                });
            }

            if (children && Object.keys(children).length) {
                updateCurRegionInfoMap(
                    infoMap,
                    [...mapListCopy, { cur: '', map: children }],
                    index + 1,
                    children,
                    curKey,
                );
            }
        });
    });

    return infoMap;
};

const curRegionInfoMap = computed<RegionInfoMap>(() => {
    const infoMap: RegionInfoMap = new Map();

    return updateCurRegionInfoMap(
        infoMap,
        [
            {
                cur: '',
                map: props.data,
            },
        ],
        0,
        props.data,
        props.curRegionKey,
    );
});

const groupOffsetMap = computed(() => {
    const map = new Map<string, number>();

    if (groupEls.value) {
        groupEls.value.forEach((item) => {
            map.set(String(item.dataset.group), item.offsetTop);
        });
    }

    return map;
});

const regionItemOffsetMap = computed(() => {
    const map = new Map<string, number>();

    if (regionItemEls.value) {
        regionItemEls.value.forEach((item) => {
            const halfRegionWrapHeight =
                (listWrapEl.value?.getBoundingClientRect().height ?? 0) / 2;
            const halfRegionItemHeight = regionItemEls.value
                ? regionItemEls.value[0].clientHeight / 2
                : 0;
            const centerOffsetTop =
                item.offsetTop - halfRegionWrapHeight + halfRegionItemHeight;
            map.set(String(item.dataset.regionItem), centerOffsetTop);
        });
    }

    return map;
});

const getIndexOffsetTopMap = () => {
    const map = new Map<string, [number, number]>();

    if (indexEls.value) {
        indexEls.value.forEach((item) => {
            map.set(String(item.dataset.group), [
                item.getBoundingClientRect().top,
                item.getBoundingClientRect().top + item.clientHeight,
            ]);
        });
    }

    return map;
};

const jumpRegionItem = (region: string) => {
    const scrollHeight = -(regionItemOffsetMap.value.get(region) ?? 0);
    listBs.value?.scrollTo(
        0,
        scrollHeight > 0 ? 0 : scrollHeight,
        SCROLL_DURATION,
    );
};

const updateIndexArrow = async () => {
    await nextTick();

    if (!indexBs.value) {
        return;
    }
    const curScrollY = Math.abs(indexBs.value.y);
    const maxScrollY = Math.abs(indexBs.value.maxScrollY);

    if (!maxScrollY) {
        showIndexArrowUp.value = false;
        showIndexArrowDown.value = false;
        isIndexArrowUpActive.value = false;
        isIndexArrowDownActive.value = false;

        return;
    }

    showIndexArrowUp.value = true;
    showIndexArrowDown.value = true;
    isIndexArrowUpActive.value = true;
    isIndexArrowDownActive.value = true;

    if (!curScrollY) {
        isIndexArrowUpActive.value = false;
    } else if (curScrollY >= maxScrollY) {
        isIndexArrowDownActive.value = false;
    }
};

const jumpGroup = (group: string) => {
    listBs.value?.scrollTo(
        0,
        -(groupOffsetMap.value.get(group) ?? 0),
        SCROLL_DURATION,
    );
    updateIndexArrow();
};

const jumpIndex = async (group: string) => {
    indexBs.value?.refresh();
    const indexWrapHeight =
        indexWrapEl.value?.getBoundingClientRect().height ?? 0;
    const indexListHeight =
        indexListEl.value?.getBoundingClientRect().height ?? 0;
    const halfIndexWrapHeight = indexWrapHeight / 2;
    const index = Object.keys(curRegionMap.value).findIndex(
        (item) => item === group,
    );
    const indexItemHeight = indexEls.value ? indexEls.value[0].clientHeight : 0;
    const targetCenterOffsetY =
        index === -1 ? 0 : index * indexItemHeight + indexItemHeight / 2;

    if (targetCenterOffsetY <= halfIndexWrapHeight) {
        indexBs.value?.scrollTo(0, 0);

        return;
    }

    if (indexListHeight - targetCenterOffsetY <= halfIndexWrapHeight) {
        indexBs.value?.scrollTo(0, indexBs.value.maxScrollY);

        return;
    }

    indexBs.value?.scrollTo(0, halfIndexWrapHeight - targetCenterOffsetY);
    await nextTick();
};

const scrollIndex = (direction: 'up' | 'down') => {
    if (!indexBs.value) {
        return;
    }

    const curScrollY = Math.abs(indexBs.value.y);
    const maxScrollY = Math.abs(indexBs.value.maxScrollY);

    if (
        (direction === 'down' && curScrollY >= maxScrollY) ||
        (direction === 'up' && indexBs.value.y >= 0)
    ) {
        return;
    }

    let scrollHeight: number;
    const indexItemHeight = indexEls.value ? indexEls.value[0].clientHeight : 0;

    if (direction === 'up') {
        scrollHeight =
            curScrollY - indexItemHeight < 0 ? curScrollY : indexItemHeight;
    } else {
        scrollHeight =
            curScrollY + indexItemHeight > maxScrollY
                ? curScrollY - maxScrollY
                : -indexItemHeight;
    }

    if (indexEls.value) {
        indexBs.value?.scrollBy(0, scrollHeight, SCROLL_DURATION);
        updateIndexArrow();
    }
};

const clearScrollIndexTimer = () => {
    if (scrollIndexUpTimer) {
        window.clearInterval(scrollIndexUpTimer);
    }

    if (scrollIndexDownTimer) {
        window.clearInterval(scrollIndexDownTimer);
    }

    scrollIndexUpTimer = null;
    scrollIndexDownTimer = null;
};

const getTouchGroup = (pageY: number) => {
    const indexOffsetTopMap = getIndexOffsetTopMap();

    for (const entry of Array.from(indexOffsetTopMap.entries())) {
        const [group, [startY, endY]] = entry;

        if (pageY >= startY && pageY <= endY) {
            return {
                name: group,
                offsetY: startY + (endY - startY) / 2,
            };
        }
    }
};

const updateIndicator = (pageY: number) => {
    const group = getTouchGroup(pageY);

    if (!group) {
        return;
    }
    const { offsetY: groupOffsetY, name } = group;
    const indexWrapOffsetY =
        indexWrapEl.value?.getBoundingClientRect().top ?? 0;
    const indexWrapHeight =
        indexWrapEl.value?.getBoundingClientRect().height ?? 0;
    const indicatorRadius = (indicatorEl.value?.clientHeight ?? 0) / 2;
    const indexArrowUpHeight =
        indexArrowUpEl.value?.$el.getBoundingClientRect().height ?? 0;

    const offsetY = groupOffsetY - indexWrapOffsetY - indicatorRadius;

    if (offsetY < 0) {
        indicatorOffsetY.value = indexArrowUpHeight;
    } else if (offsetY + 2 * indicatorRadius > indexWrapHeight) {
        indicatorOffsetY.value =
            indexWrapHeight - 2 * indicatorRadius + indexArrowUpHeight;
    } else {
        indicatorOffsetY.value = offsetY + indexArrowUpHeight;
    }
    curGroup.value = name;
};

const updateIndex = (touch: Touch) => {
    const { pageY: touchOffsetY } = touch;
    const indexWrapHeight =
        indexWrapEl.value?.getBoundingClientRect().height ?? 0;
    const indexWrapOffsetY =
        indexWrapEl.value?.getBoundingClientRect().top ?? 0;

    const isBeyondWrapBottom =
        touchOffsetY - indexWrapOffsetY > indexWrapHeight;
    const isBeyondWrapTop = touchOffsetY - indexWrapOffsetY < 0;

    if (isBeyondWrapBottom) {
        clearScrollIndexTimer();

        scrollIndexDownTimer = window.setInterval(() => {
            scrollIndex('down');
            updateIndicator(indexWrapOffsetY + indexWrapHeight);
        }, 50);
    } else if (isBeyondWrapTop) {
        clearScrollIndexTimer();

        scrollIndexUpTimer = window.setInterval(() => {
            scrollIndex('up');
            updateIndicator(indexWrapOffsetY);
        }, 50);
    } else {
        clearScrollIndexTimer();
    }

    updateIndicator(touchOffsetY);
};

const handleTouchIndexStart = (touch: Touch) => {
    updateIndex(touch);
};

const handleTouchIndexMove = (touch: Touch) => {
    updateIndex(touch);
    showIndicator.value = true;
};

const handleTouchIndexEnd = () => {
    showIndicator.value = false;

    if (curGroup.value) {
        jumpGroup(curGroup.value);
        jumpIndex(curGroup.value);
    }

    clearScrollIndexTimer();
};

const handleTabClick = async (index: number) => {
    if (index < 0 || index > regionMapList.value.length - 1) {
        return;
    }
    curRegionMapIndex.value = index;

    const hasGroup = Object.entries(curRegionMap.value).some((item) => {
        const [groupName, groupItems] = item;

        return groupItems.some((i) => {
            if (i.label === curRegionLabel.value) {
                curGroup.value = groupName;
                curRegionGroup.value = groupName;

                return true;
            }
        });
    });

    if (!hasGroup) {
        curGroup.value = '';
        curRegionGroup.value = '';
    }

    await nextTick();
    await updateIndexArrow();
    jumpRegionItem(curRegionLabel.value);
    await jumpIndex(curGroup.value);
    updateIndexArrow();
};

const initBScroll = () => {
    nextTick(() => {
        if (listWrapEl.value) {
            listBs.value = new BScroll(listWrapEl.value, {
                scrollY: true,
                click: true,
            });

            listTouch.value = new TouchHandler({
                target: listWrapEl.value,
                isPreventDefault: true,
                leftCallback: () => handleTabClick(curRegionMapIndex.value + 1),
                rightCallback: () =>
                    handleTabClick(curRegionMapIndex.value - 1),
                thresholdX: 20,
            });
        }

        if (indexWrapEl.value) {
            indexBs.value = new BScroll(indexWrapEl.value, {
                scrollY: true,
                click: true,
                disableTouch: true,
            });

            indexTouch.value = new TouchHandler({
                target: indexWrapEl.value,
                touchStart: handleTouchIndexStart,
                touchMove: handleTouchIndexMove,
                touchEnd: handleTouchIndexEnd,
            });
        }
        updateIndexArrow();
    });
};

const refreshBScroll = () => {
    nextTick(() => {
        listBs.value?.refresh();
        indexBs.value?.refresh();
    });
};

const handleItemClick = (item: RegionItem) => {
    if (item.children) {
        regionMapList.value = [
            ...regionMapList.value.slice(0, curRegionMapIndex.value),
            {
                ...regionMapList.value[curRegionMapIndex.value],
                cur: item.label,
            },
            {
                cur: '',
                map: item.children,
            },
        ];
        curRegionMapIndex.value += 1;
        curGroup.value = '';
        curRegionGroup.value = '';
    } else {
        regionMapList.value[curRegionMapIndex.value].cur = item.label;
        emit('select', item.value);
        refreshBScroll();
    }

    nextTick(() => {
        updateIndexArrow();
        jumpGroup(curGroup.value);
        jumpIndex(curGroup.value);
    });
};

watch(() => props.data, initBScroll, {
    immediate: true,
});
watch(curRegionMapIndex, refreshBScroll);

const initData = (curRegionVal?: string | number) => {
    // eslint-disable-next-line no-undefined
    if (curRegionVal === undefined) {
        return;
    }

    const rst = cloneDeep(curRegionInfoMap.value.get(curRegionVal));

    if (!rst) {
        return;
    }
    const { curRegionMapIndex: index, regionMapList: mapList, groupName } = rst;
    regionMapList.value = mapList;
    curRegionMapIndex.value = index;
    curGroup.value = groupName;
    curRegionGroup.value = groupName;
};

watch(
    [() => props.curRegionKey, () => props.curRegionVal],
    (newVal) => {
        const [newCurRegionKey, newCurRegionVal] = newVal;

        if (!newCurRegionKey || !newCurRegionVal) {
            return;
        }

        initData(newCurRegionVal);
    },
    {
        immediate: true,
    },
);

watch(curRegionInfoMap, () => {
    initData(props.curRegionVal);
});

const updateEmptyHeight = () => {
    const regionWrapHeight =
        listWrapEl.value?.getBoundingClientRect().height ?? 0;
    const regionItemHeight = regionItemEls.value
        ? regionItemEls.value[0].clientHeight
        : 0;
    emptyHeight.value = (regionWrapHeight - regionItemHeight) / 2;
    refreshBScroll();
};

defineExpose({
    refresh: refreshBScroll,
    resetPosition: () => {
        nextTick(() => {
            props.addEmpty && updateEmptyHeight();
            updateIndexArrow();
            curGroup.value = curRegionGroup.value;
            jumpRegionItem(curRegionLabel.value);
            jumpIndex(curGroup.value);
        });
    },
});

onBeforeUnmount(() => {
    listBs.value?.destroy();
    indexBs.value?.destroy();
    listTouch.value?.destroy();
    indexTouch.value?.destroy();
});
</script>

<style scoped lang="less">
.region-list {
    position: relative;
    height: 100%;
    &__tab {
        display: flex;
        height: 30px;
        margin-right: -38px;
        &-item {
            margin-right: 38px;
            font-size: 16px;
            font-weight: 500;
            color: #363636;
            opacity: 0.5;
            &--active {
                font-weight: 700;
                color: #222;
                opacity: 1;
            }
        }
    }
    &__divider {
        height: 1px;
        margin: 4px 0;
        background-color: #eaeaea;
    }
    &__main {
        position: absolute;
        top: 39px;
        bottom: 0;
        width: 100%;
        overflow: hidden;
    }
    &__empty {
        margin-top: -10px;
    }
    &__group {
        display: flex;
        flex-direction: column;
        padding-right: 44px;
        margin-bottom: 10px;
        &-label {
            font-size: 15px;
            font-weight: 500;
            color: #363636;
        }
        &-item {
            margin-top: 10px;
            font-size: 16px;
            font-weight: 400;
            color: #aaa;
            &--active {
                color: #222;
            }
        }
        &-list {
            display: flex;
            margin-bottom: -10px;
            overflow-y: scroll;
            flex-direction: column;
            &-wrap {
                height: 100%;
                margin-right: -20px;
                overflow: hidden;
            }
        }
    }
    &__index {
        display: flex;
        flex-direction: column;
        &-container {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 24px;
        }
        &-wrap {
            position: absolute;
            top: 0;
            right: 0;
            width: 24px;
            max-height: 100%;
            overflow: hidden;
            background-color: #f5f5f5;
            border-radius: 12px;
            &--arrow-up {
                top: 14px;
            }
            &--arrow-down {
                bottom: 14px;
            }
        }
        &-item {
            height: 25px;
            font-size: 15px;
            font-weight: 500;
            line-height: 25px;
            color: #bdbdbd;
            text-align: center;
            &--active {
                color: #363636;
            }
        }
        &-indicator {
            position: absolute;
            top: 0;
            left: -40px;
            width: 24px;
            height: 24px;
            font-size: 15px;
            font-weight: 500;
            line-height: 24px;
            color: #363636;
            text-align: center;
            background-color: #eee;
            border-radius: 50%;
            opacity: 0;
            transition: all ease 0.25s;
            &--visible {
                opacity: 1;
            }
        }
        &-arrow {
            &-up {
                position: absolute;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
            }
            &-down {
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
            }
        }
    }
}
</style>
