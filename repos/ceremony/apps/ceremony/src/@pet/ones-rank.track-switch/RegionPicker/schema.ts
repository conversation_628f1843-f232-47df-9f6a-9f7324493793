export type RecommendType = 'hot' | 'resident';

export interface RecommendItem {
    type?: RecommendType;
    label: string;
    tag: string;
    value: any;
}

export interface ListTab {
    label: string;
    value: string | number;
}

export interface RegionItem {
    label: string;
    value?: any;
    children?: RegionMap;
}

export interface RegionMap {
    [key: string]: RegionItem[];
}

export interface RegionMapListItem {
    cur?: string;
    map: RegionMap;
}

export type RegionInfoMap = Map<
    any,
    {
        regionMapList: RegionMapListItem[];
        curRegionMapIndex: number;
        groupName: string;
    }
>;
