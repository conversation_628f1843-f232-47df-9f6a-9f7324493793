import { request } from '@alive-ui/actions';
import type {
    ScheduleChoosePostRequest,
    ScheduleChoosePostResponse,
} from '../schemas/scheduleChoose';
// 赛道选择器： https://mock.corp.kuaishou.com/project/941/interface/api/1138259

// 打榜赛事玩法-榜单页： https://mock.corp.kuaishou.com/project/941/interface/api/828812
export const fetchRegion = async (params: ScheduleChoosePostRequest) => {
    const res = await request.post<ScheduleChoosePostResponse>(
        '/webapi/live/revenue/operation/activity/complex/scheduleChoose',
        params,
    );

    return res?.data;
};
