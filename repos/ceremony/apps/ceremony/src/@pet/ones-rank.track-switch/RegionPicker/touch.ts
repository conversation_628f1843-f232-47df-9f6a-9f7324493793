// 参考：https://www.jianshu.com/p/84d0d6793546

type HandlerFunc = (e: TouchEvent) => void;

type Callback = (...args: any) => void;

type TouchCallback = (t: Touch) => void;

type TargetElement = (HTMLElement | Document) & {
    attachEvent?: (type: string, handler: HandlerFunc) => void;
    detachEvent?: (type: string, handler: HandlerFunc) => void;
};

interface Options {
    /**
     * 绑定监听的目标元素
     */
    target: TargetElement;
    /**
     * 判断水平方向滑动的阈值（单位：px）
     */
    thresholdX?: number;
    /**
     * 判断垂直方向滑动的阈值（单位：px）
     */
    thresholdY?: number;
    /**
     * 是否屏蔽掉触摸滑动的默认行为（例如页面的上下滚动，缩放等）
     */
    isPreventDefault?: boolean;
    /**
     * 向上滑动的监听回调
     */
    upCallback?: Callback;
    /**
     * 向右滑动的监听回调
     */
    rightCallback?: Callback;
    /**
     * 向下滑动的监听回调
     */
    downCallback?: Callback;
    /**
     * 向左滑动的监听回调
     */
    leftCallback?: Callback;
    touchStart?: TouchCallback;
    touchMove?: TouchCallback;
    touchEnd?: TouchCallback;
}

export class TouchHandler {
    constructor(public options: Options) {
        this.init();
    }
    private startX?: number;
    private startY?: number;
    private init() {
        const { target } = this.options;
        this.addHandler(target, 'touchstart', (e) => this.handleTouchEvent(e));
        this.addHandler(target, 'touchend', (e) => this.handleTouchEvent(e));
        this.addHandler(target, 'touchmove', (e) => this.handleTouchEvent(e));
    }
    public destroy() {
        const { target } = this.options;
        this.removeHandler(target, 'touchstart', (e) =>
            this.handleTouchEvent(e),
        );
        this.removeHandler(target, 'touchend', (e) => this.handleTouchEvent(e));
        this.removeHandler(target, 'touchmove', (e) =>
            this.handleTouchEvent(e),
        );
    }
    private handleTouchEvent(event: TouchEvent) {
        const {
            downCallback,
            isPreventDefault,
            leftCallback,
            rightCallback,
            thresholdX = 30,
            thresholdY = 30,
            upCallback,
            touchStart,
            touchMove,
            touchEnd,
        } = this.options;

        switch (event.type) {
            case 'touchstart':
                this.startX = event.touches[0].pageX;
                this.startY = event.touches[0].pageY;

                if (touchStart) {
                    touchStart(event.touches[0]);
                }
                break;
            case 'touchend':
                const spanX =
                    event.changedTouches[0].pageX - (this.startX ?? 0);
                const spanY =
                    event.changedTouches[0].pageY - (this.startY ?? 0);

                const isVerticalSlide = Math.abs(spanY) > Math.abs(spanX);

                if (isVerticalSlide) {
                    const isUpSlide = spanY > thresholdY;
                    const isDownSlide = spanY < -thresholdY;

                    isUpSlide && upCallback && upCallback();
                    isDownSlide && downCallback && downCallback();
                } else {
                    const isRightSlide = spanX > thresholdX;
                    const isLeftSlide = spanX < -thresholdX;

                    isRightSlide && rightCallback && rightCallback();
                    isLeftSlide && leftCallback && leftCallback();
                }

                if (touchEnd) {
                    touchEnd(event.touches[0]);
                }
                break;
            case 'touchmove':
                isPreventDefault && event.preventDefault();

                if (touchMove) {
                    touchMove(event.touches[0]);
                }
                break;
            default:
                break;
        }
    }
    private addHandler(
        element: TargetElement,
        type: string,
        handler: HandlerFunc,
    ) {
        if (element.addEventListener) {
            element.addEventListener(type, handler as any, false);
        } else if (element.attachEvent) {
            element.attachEvent(`on${type}`, handler);
        } else {
            (element as any)[`on${type}`] = handler;
        }
    }
    private removeHandler(
        element: TargetElement,
        type: string,
        handler: HandlerFunc,
    ) {
        if (element.removeEventListener) {
            element.removeEventListener(type, handler as any, false);
        } else if (element.detachEvent) {
            element.detachEvent(`on${type}`, handler);
        } else {
            (element as any)[`on${type}`] = handler;
        }
    }
}
