<template>
    <div class="region-picker" :style="{ height: `${height * 0.02}rem` }">
        <RegionTitle
            v-if="pickerTitle"
            class="region-picker__title"
            :text="pickerTitle"
        />
        <template v-if="store.pickerStatus.success">
            <RegionRecommend
                v-if="showRecommend && recommendList.length"
                class="region-picker__recommend"
                :title="recommendTitle"
                :list="recommendList"
                @select="handleRecommendItemClick"
            />
            <RegionList
                v-if="store.regionData"
                ref="regionListRef"
                :data="store.regionData"
                :cur-region-key="curRegionKey"
                :cur-region-val="curRegionVal"
                class="region-picker__list"
                @select="handleRegionClick"
            />
        </template>
        <ElseStatus
            v-else
            :page-status="store.pickerStatus"
            :is-show-refresh="true"
            :no-data="false"
            class="mt-20px"
            @on-refresh="store.getRegionData"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { debounce } from 'lodash-es';
import { ElseStatus } from '@alive-ui/base';
import useStore from './store';
import RegionTitle from './RegionTitle.vue';
import RegionRecommend from './RegionRecommend.vue';
import RegionList from './RegionList.vue';
import type { RecommendItem } from './schema';

const props = withDefaults(
    defineProps<{
        height?: number;
        pickerTitle?: string;
        recommendTitle?: string;
        showRecommend?: boolean;
        scheduleType?: number;
        curRegionKey?: string;
        curRegionVal?: string | number;
        excludeRecommendKey?: string;
        excludeRecommendVal?: (string | number)[];
        maxRecommendHot?: number;
        maxRecommendResident?: number;
    }>(),
    {
        height: 500,
        pickerTitle: '',
        recommendTitle: '',
        showRecommend: true,
        // eslint-disable-next-line no-undefined
        scheduleType: undefined,
        // eslint-disable-next-line no-undefined
        curRegionKey: undefined,
        // eslint-disable-next-line no-undefined
        curRegionVal: undefined,
        // eslint-disable-next-line no-undefined
        excludeRecommendKey: undefined,
        // eslint-disable-next-line no-undefined
        excludeRecommendVal: undefined,
        maxRecommendHot: 4,
        maxRecommendResident: 1,
    },
);
const emit = defineEmits<{
    (e: 'select-recommend' | 'select-region', value: any): void;
}>();

const store = useStore();

// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
const regionListRef = ref<InstanceType<typeof RegionList> | null>(null);

const { recommendData } = storeToRefs(store);

const limitRecommend = (list: RecommendItem[]) => {
    let hotCount = 0;
    let residentCount = 0;
    const rst: RecommendItem[] = [];

    list.some((item) => {
        if (item.type === 'hot' && hotCount < props.maxRecommendHot) {
            rst.push(item);
            hotCount++;
        } else if (
            item.type === 'resident' &&
            residentCount < props.maxRecommendResident
        ) {
            rst.push(item);
            residentCount++;
        }

        if (
            hotCount === props.maxRecommendHot &&
            residentCount === props.maxRecommendResident
        ) {
            return true;
        }
    });

    return rst;
};

const recommendList = computed(() => {
    if (!props.excludeRecommendKey || !props.excludeRecommendVal) {
        return limitRecommend(recommendData.value);
    }

    return limitRecommend(
        recommendData.value.filter((item) => {
            return !props?.excludeRecommendVal?.includes(
                item.value[props.excludeRecommendKey || ''],
            );
        }),
    );
});

const handleRecommendItemClick = (item: any) => {
    emit('select-recommend', item);
};

const handleRegionClick = (item: any) => {
    emit('select-region', item);
};

const loadData = debounce((scheduleType: number) => {
    store.getRegionData(scheduleType);
}, 500);

watch(
    () => props.scheduleType,
    (newVal) => {
        newVal && loadData(newVal);
    },
    {
        immediate: true,
    },
);

defineExpose({
    refresh: () => {
        regionListRef.value?.refresh();
    },
    resetPosition: () => {
        regionListRef.value?.resetPosition();
    },
});
</script>

<style scoped lang="less">
.region-picker {
    display: flex;
    padding: 24px;
    overflow: hidden;
    background-color: #fff;
    flex-direction: column;
    border-top-right-radius: 16px;
    border-top-left-radius: 16px;
    &__title {
        margin-bottom: 15px;
    }
    &__recommend {
        margin-bottom: 15px;
    }
    &__list {
        flex-grow: 1;
        overflow: hidden;
    }
}
</style>
