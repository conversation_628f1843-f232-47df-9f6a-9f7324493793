/**
 * 请求: 赛道选择器
 */
export interface ScheduleChoosePostRequest {
    activityBiz: string;
    liveStreamId: string;
    scheduleType: number;
}

/**
 * 响应: 赛道选择器
 */
export interface ScheduleChoosePostResponse {
    recommendSchedules: RecommendSchedule[];
    residentSchedule: ResidentSchedule;
    scheduleChooseMap: ScheduleChooseMap;
}

export interface RecommendSchedule {
    classifyId: number;
    displayName: string;
    rankId: number;
}

export interface ResidentSchedule {
    classifyId: number;
    displayName: string;
    rankId: number;
}

export interface ScheduleChooseMap {
    [key: string]: ScheduleChooseMapItem[];
}

export interface ScheduleChooseMapItem {
    displayName: string;
    classifyId?: number;
    rankId?: number;
    subScheduleNodes?: ScheduleChooseMap;
}
