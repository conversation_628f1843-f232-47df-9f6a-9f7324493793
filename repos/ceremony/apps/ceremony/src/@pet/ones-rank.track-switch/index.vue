<template>
    <div v-if="tabData?.tabList?.length" :key="tabData?.scheduleType">
        <div
            class="tab-switch"
            :class="{ 'suit-tab-width': tabData?.tabList.length < 4 }"
        >
            <!-- <div v-if="newTabList && newTabList.length > 5 && (curTabIndex >1)" class="left-mask mask-line-gradient" /> -->
            <div
                v-if="
                    tabData?.tabType === 'normal' &&
                    newTabList &&
                    newTabList.length > 5 &&
                    curTabIndex !== newTabList.length - 1
                "
                class="right-mask mask-line-gradient"
            />
            <div class="tab-scroll-area">
                <div
                    class="first-tab-active"
                    :style="{
                        width: `${tabData?.tabList.length < 4 ? 1.8 : 1.4}rem`,
                        transition: '0.3s ease-out',
                        transform: `translateX(${curTabIndex * (tabData?.tabList.length < 4 ? 1.8 : 1.4)}rem`,
                    }"
                />
                <div
                    v-for="(item, index) in tabData?.tabType === 'normal'
                        ? newTabList
                        : newTabList.slice(0, 4)"
                    :key="index"
                >
                    <div
                        v-if="item.displayName"
                        :style="{
                            width: `${tabData?.tabList.length < 4 ? 1.8 : 1.4}rem`,
                        }"
                        class="tab-item a-text-main"
                        :class="{
                            'tab-active-style': currentTabInfo?.anchorId
                                ? currentTabInfo?.anchorId === item?.anchorId
                                : currentTabInfo?.rankId === item?.rankId,
                        }"
                        @click="tabChange(item, index)"
                    >
                        {{
                            nameSlice(
                                item.displayName,
                                tabData?.tabType === 'city' ? 5 : 6,
                            )
                        }}
                    </div>
                </div>
            </div>
            <div
                v-if="tabData?.tabType === 'city'"
                class="switch-area-arrow a-text-main"
                @click="switchArea"
            >
                {{ tabData?.switchText }}
                <svg
                    width="10"
                    height="10"
                    viewBox="0 0 10 10"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <g opacity="0.8">
                        <path
                            d="M8.5 3.5L5 7L1.5 3.5"
                            stroke="#ffdfbf"
                            stroke-linecap="square"
                        />
                    </g>
                </svg>
            </div>
        </div>
        <div
            v-if="
                tabData?.tabType !== 'city' &&
                currentTabInfo?.subLaneViews?.length
            "
            class="sub-child-tab"
        >
            <Tabs
                v-if="currentTabInfo?.subLaneViews"
                v-model="curTabRankId"
                class="tabs"
                :type="'primary'"
                @tab-select="subTabChange"
            >
                <TabPanel
                    v-for="subTab in currentTabInfo.subLaneViews"
                    :id="subTab?.rankId"
                    :key="subTab?.rankId"
                >
                    <template #label>
                        <div class="custom-label">
                            <div class="sub-label-name">
                                {{ subTab.displayName }}
                            </div>
                            <div v-if="subTab.desc" class="sub-label-desc">
                                {{ subTab.desc }}
                            </div>
                        </div>
                    </template>
                </TabPanel>
            </Tabs>
        </div>
        <RegionPickerPopup
            v-if="tabData?.tabType == 'city'"
            v-model:show="isShowPicker"
            :picker-title="tabData?.pickerTitle"
            :show-recommend="tabData?.showRecommend"
            :cur-region-val="currentTabInfo.anchorId"
            :exclude-recommend-val="filterAnchorId"
            :schedule-type="tabData?.scheduleType"
            @select="handleRegionSelect"
        />
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, nextTick } from 'vue';
import { debounce } from 'lodash-es';
import { Tabs, TabPanel } from '@lux/sharp-ui-next';
import { nameSlice } from '@alive-ui/actions';
import RegionPickerPopup from './RegionPicker/RegionPickerPopup.vue';
import type { PropType } from 'vue';
interface BaseItem {
    scheduleId?: number;
    classifyId?: number;
    displayName?: string;
    rankId: number;
    desc?: string;
    order?: number;
    startTime?: number;
    rankAliasName: string;
    anchorId?: number;
}

interface TabItem extends BaseItem {
    subLaneViews: BaseItem[];
}
const props = defineProps({
    tabData: {
        type: Object as PropType<{
            scheduleType: number;
            showRecommend: boolean;
            pickerTitle: string;
            switchText: string;
            tabType: string; // 'normal' | 'city'
            defaultTab: BaseItem;
            tabList: TabItem[];
        }>,
        required: true,
        default: () => {
            return {
                scheduleType: 0,
                showRecommend: true,
                pickerTitle: '请选择地区赛道',
                switchText: '切换地区',
                tabType: 'normal',
                defaultTab: {},
                tabList: [],
            };
        },
    },
});
const emits = defineEmits(['change', 'sub-change', 'disabled-tab-clicked']);

const newTabList = ref([] as TabItem[]);
const compIndex = computed(() => {
    return props.tabData?.tabList.findIndex((elem: TabItem) => {
        return props.tabData?.defaultTab?.anchorId === elem?.anchorId;
    });
});
const curTabRankId = ref(0);
const curTabIndex = ref(0);
// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
const currentTabInfo = ref({} as TabItem);
let domList: Element[] = [];
let scrollDom: Element;
let domCenterX: number;

const scrollDomLeft = () => {
    domList = Array.from(document.querySelectorAll('.tab-item'));
    const domCenter = domList?.[2] as HTMLElement;

    if (domCenter) {
        const { x: x2 } = domCenter?.getBoundingClientRect();
        domCenterX = x2;
    }
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    scrollDom = document.querySelector('.tab-scroll-area')!;
    const rest = curTabIndex.value - 2;

    if (scrollDom && curTabIndex.value > 2) {
        scrollDom.scrollLeft = 70 * rest + scrollDom?.scrollLeft;
    }
};

watch(
    () => props.tabData?.scheduleType,
    (val, old) => {
        if (val !== old) {
            nextTick(() => {
                scrollDomLeft();
            });
        }
    },
    {
        immediate: true,
    },
);

watch(
    () => props.tabData?.tabList,
    (data) => {
        newTabList.value = [...props.tabData?.tabList];
        const initIndex = compIndex.value < 0 ? 0 : compIndex.value;
        currentTabInfo.value = data?.[initIndex];
        curTabIndex.value = initIndex;
        curTabRankId.value = props.tabData?.defaultTab?.rankId || 0;
    },
    {
        immediate: true,
        deep: true,
    },
);

const filterAnchorId = computed(() => {
    return newTabList.value?.map((elem) => {
        return elem.anchorId || '';
    });
});

const tabChange = debounce((item: TabItem, index: number) => {
    if (curTabIndex.value === index) {
        return;
    }

    const dom = domList?.[index] as HTMLElement;
    if (scrollDom && dom) {
        nextTick(() => {
            const { x } = dom?.getBoundingClientRect();
            const diffVal = x - domCenterX;
            scrollDom.scrollLeft = scrollDom.scrollLeft + diffVal;
        });
    }
    curTabIndex.value = index;
    currentTabInfo.value = item;

    if (!item.rankId) {
        curTabRankId.value = item.subLaneViews?.[0]?.rankId || 0;
        emits('change', curTabRankId.value, item.subLaneViews?.[0]);
    } else {
        curTabRankId.value = item.rankId;
        emits('change', item.rankId, index);
    }
}, 300);

const subTabChange = (id: number) => {
    curTabRankId.value = id;
    const targetSubTabItem = currentTabInfo.value?.subLaneViews?.filter(
        (item) => item.rankId === id,
    );
    emits('change', id, targetSubTabItem?.[0]);
};

const isShowPicker = ref(false);

const switchArea = () => {
    isShowPicker.value = true;
};

const handleRegionSelect = (item: any) => {
    const find = newTabList.value.findIndex((elem: any) => {
        return elem.anchorId === item.anchorId;
    });
    currentTabInfo.value = item;

    if (find === -1) {
        curTabIndex.value = 0;
        newTabList.value?.splice(3, 1);
        newTabList.value?.unshift(item);
    } else {
        curTabIndex.value = find;
    }

    isShowPicker.value = false;
    emits('change', item?.rankId);
};
</script>

<style lang="less" scoped>
.tabs {
    --sp-tab-color-nav-bg: transparent;
    --sp-tab-size-nav-text-primary: 12px;

    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 500;
}

// sharp-ui 组件样式覆盖
:deep(.spu-tab-bar-item) {
    color: rgba(100, 34, 65, 1);
    opacity: 0.8;
}
:deep(.spu-tab-bar-item-active) {
    color: var(--tabs-tab-active-color, #000);
}
:deep(.spu-tab-bar__slider) {
    bottom: 6px !important;
    width: 24px;
    height: 3px;
    // background: rgba(255, 84, 119, 100%);
    background-color: var(--tabs-tab-active-color, #000);
    border-radius: 10px;
}
.left-mask {
    left: -1px;
    transform: rotate(180deg);
}
.right-mask {
    right: -0.5px;
}
.mask-line-gradient {
    position: absolute;
    top: 0;
    z-index: 10;
    width: 40px;
    height: 30px;
    pointer-events: none;
    background: linear-gradient(-90deg, #38266b 0%, #38266b00 95.31%);
    flex-shrink: 0;
    border-top-right-radius: 18px;
    border-bottom-right-radius: 18px;
}
.suit-tab-width {
    width: fit-content;
}
.switch-area-arrow {
    display: flex;
    width: 71px;
    height: 30px;
    padding: 0 4px;
    margin-left: 7px;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 14px; /* 116.667% */
    text-align: center;
    white-space: nowrap;
    background: var(--substrate-color, rgba(170, 140, 255, 0.1));
    border-radius: 18px;
    opacity: 0.8;
    align-items: center;
    justify-content: center;
    svg {
        width: 10px;
        height: 10px;
        margin-left: 2px;
    }
}
.sub-child-tab {
    margin-bottom: -5px;
    font-family: 'PingFang SC';
}
.sub-label-name {
    font-size: 12px;
    font-weight: 500;
    line-height: 14px;
    text-align: center;
}
.sub-label-desc {
    margin-top: 6px;
    font-size: 7px;
    font-weight: 400;
    line-height: 10px; /* 142.857% */
    color: var(--main-color, rgba(85, 0, 0, 100%));
    text-align: center;
    opacity: 0.6;
}
.tab-switch {
    position: relative;
    display: flex;
    max-width: 358px;
    margin: 0 auto;
    margin-bottom: 4px;
    overflow: hidden;
    justify-content: center;
    .tab-item {
        position: relative;
        display: flex;
        height: 30px;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 30px; /* 116.667% */
        text-align: center;
        opacity: 0.8;
        align-items: center;
        justify-content: center;
    }
    .first-tab-active {
        position: absolute;
        top: 0;
        bottom: 0.5px; // 解决iPhone 14 pro
        background: url('./tab-active-bg_2x.png') center / 100% no-repeat;
        border-radius: 15px;
    }
    .tab-active-style {
        font-weight: bold;
        color: var(--tab-active-text, #fff);
        opacity: 1;
    }
    .tab-scroll-area {
        position: relative;
        display: flex;
        overflow-x: auto;
        // overflow-y: hidden;
        // white-space: nowrap;
        background: var(--substrate-color, rgba(170, 140, 255, 0.1));
        border-radius: 15px;
        // -ms-overflow-style: none;
        &::-webkit-scrollbar {
            display: none;
        }
    }
}
</style>
