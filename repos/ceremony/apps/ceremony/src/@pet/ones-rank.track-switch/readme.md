### 全局tab切换组件

-   传参：

    -   defaultTab： 默认tab的信息
        数据格式参考

    ```json
    {
        //"scheduleType": 1001, //当前赛程，可以不传
        "anchorId": "460422", // 第一层tab选中
        "rankId": 6529340 // 第一层rankId或者第二层
    }
    ```

    -   tabList: tab列表
        数据格式参考

    ```json
    [
        {
            "classifyId": 460422,
            "scheduleId": 0,
            "rankId": 0,
            "order": 1,
            "displayName": "脱口秀男主播",
            "desc": null,
            "subLaneViews": [
                {
                    "classifyId": 460422,
                    "scheduleId": 6259206,
                    "rankId": 6529340,
                    "order": 1,
                    "displayName": "王牌",
                    "desc": "800万粉主播",
                    "subLaneViews": null
                },
                {
                    "classifyId": 460422,
                    "scheduleId": 6259197,
                    "rankId": 6529326,
                    "order": 2,
                    "displayName": "巨星",
                    "desc": "800万粉主播",
                    "subLaneViews": null
                }
            ]
        }
    ]
    ```

-   第一层大tab和第二层子tab是分开的
-   第一层 tab emit出去的是item对象
-   第二层小tab emit出去的rankId

```vue
<TrackTabSwitch
    v-if="
        indexInfoData?.displayLaneViewList &&
        currentAnchorStageType !== TWELVEHOURS
    "
    :key="currentScheduleType"
    :defaultTab="indexInfoData?.anchorDisplayScheduleAndLane"
    :tabList="indexInfoData?.displayLaneViewList"
    :pickerTitle="
        '请选择' + (areaCityMap?.[currentAnchorStageType] || '') + '赛道'
    "
    :tabType="
        [RACE_TYPE_CITY, RACE_TYPE_AREA, COUNTRY].includes(
            currentAnchorStageType,
        )
            ? 'city'
            : 'normal'
    "
    :switch-text="'切换' + (areaCityMap?.[currentAnchorStageType] || '')"
    :schedule-type="indexStore.currentScheduleType"
    @change="tabChange"
    @sub-change="subChange"
/>
```
