// # if (target === '4tab')
import { ref, onBeforeUnmount, onMounted } from 'vue';
import { Container } from 'typedi';
import {
    isGteVersion,
    osName,
    addListener,
    removeListener,
} from '@alive-ui/actions';

function callFns(key: string) {
    try {
        const fnList = Container.getMany(key) ?? [];
        console.log('fn1 List', key, fnList);
        fnList.forEach((fn) => {
            if (fn && typeof fn === 'function') {
                // console.log('fn1 !', fn);
                fn();
            }
        });
    } catch (e) {
        console.error(e);
    }
}

/**
 * 可见性
 */
export function useNativeVisible() {
    // FIXME: 去掉测试代码
    // setTimeout(() => {
    //     callFns('on_native_visible_show');
    // }, 3000);

    const isAndroid = osName === 'android';
    const isIOS = osName === 'ios';

    const supportNativeVisible =
        (isAndroid && isGteVersion('11.4.40')) ||
        (isIOS && isGteVersion('11.10.20'));
    const isVisible = ref(supportNativeVisible ? false : true);

    const hide = () => {
        isVisible.value = false;
        console.log(Date.now(), '[native visible hide()]:', isVisible.value);
        callFns('on_native_visible_hide');
    };

    const show = () => {
        isVisible.value = true;
        console.log(Date.now(), '[native visible show()]:', isVisible.value);
        callFns('on_native_visible_show');
    };

    const handleNativeVisible = (data: { visible: boolean }) => {
        if (data.visible) {
            show();
        } else {
            hide();
        }
    };

    onMounted(() => {
        if (supportNativeVisible) {
            addListener('native_visible', handleNativeVisible).catch(
                console.error,
            );
        } else {
            // 有点击则在前台
            window.document.addEventListener('touchstart', show);
            addListener('native_leave', hide).catch(console.error);
            addListener('native_reentry', show).catch(console.error);
            // 兼容性处理，上面的native_reentry 在ios端，webview切换以及app后台都会执行，所以native_foreground只用在android端就可以了。
            if (isAndroid) {
                // app显示，锁定屏幕，再打开，重新请求列表数据
                addListener('native_background', hide).catch(console.error);
                addListener('native_foreground', show).catch(console.error);
            }
        }
    });

    onBeforeUnmount(() => {
        if (supportNativeVisible) {
            removeListener('native_visible', handleNativeVisible).catch(
                console.error,
            );
        } else {
            window.document.removeEventListener('touchstart', show);
            removeListener('native_leave', hide).catch(console.error);
            removeListener('native_reentry', show).catch(console.error);
            if (isAndroid) {
                removeListener('native_foreground', show).catch(console.error);
                removeListener('native_background', hide).catch(console.error);
            }
        }
    });

    return isVisible;
}

// # endif
