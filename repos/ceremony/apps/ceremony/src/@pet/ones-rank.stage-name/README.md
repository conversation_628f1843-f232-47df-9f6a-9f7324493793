# 赛程名称组件

## 功能概述
此组件用于显示当前赛程的名称，并根据赛程名称的长度动态调整背景图的大小。如果是在回看模式下，还会显示额外的子赛程列表，用户可以点击切换不同的子赛程。

## 属性和方法

### Props
- **contextName** (String): 提供赛程上下文名称，默认为空字符串。

### Emit Events
- **change** (item): 当用户点击子赛程列表中的某个赛程时触发，传递被点击赛程的信息。

## 使用示例

```vue
<template>
  <StageName contextName="main" @change="handleStageChange" />
</template>

<script setup>
import { ref } from 'vue';
import StageName from './components/StageName.vue';

const handleStageChange = (item) => {
  console.log('Selected stage:', item);
};
</script>
```

## 注意事项
- 组件内部会根据赛程名称的长度自动调整背景图的大小。
- 子赛程列表仅在回看模式下且存在多个子赛程时显示。
- 点击子赛程列表中的项目将触发 `change` 事件。

## 依赖项
- Vue 3.x
- `./data` 模块中的 `stageNameFun` 函数

## 样式说明
- **main-stage-bg-1**: 赛程名称长度为5及以下时使用的背景图。
- **main-stage-bg-2**: 赛程名称长度为6或7时使用的背景图。
- **main-stage-bg-3**: 赛程名称长度大于等于8时使用的背景图。
- **sub-stage-text**: 子赛程文本样式。
- **current**: 当前选中的子赛程样式。