<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { stageNameFun } from './data';
const props = defineProps({
    // provide name
    contextName: {
        type: Symbol,
        default: Symbol.for('ctx'),
    },
});
const data = stageNameFun(props.contextName);
const emit = defineEmits(['change', 'stage-refresh-task']);
const name = computed(() => {
    // 回看使用stageName，非回看使用stageDesc
    return data.value.lookingBack
        ? data.value.subView?.stageName
        : data.value.subView?.stageDesc;
});
const status = ref(3);
// 根据赛程名称长度来展示不同长度的背景图
const handleStatus = () => {
    const length = name.value?.length;
    if (!length) {
        return;
    }
    status.value = 3; // 赛程名称长度大于等于8
    if (length < 8 && length >= 6) {
        status.value = 2; // 6或7
    } else if (length < 6) {
        status.value = 1; // 5及以下
    }
};

const handleClick = (item: any) => {
    emit('change', item);
    emit('stage-refresh-task', item);
};

const hasSub = computed(() => {
    return (
        data.value.lookingBack &&
        data.value?.subView?.subStageTableViewList &&
        data.value?.subView?.subStageTableViewList?.length > 1
    );
});

watch(data, handleStatus, { immediate: true });
</script>

<template>
    <div class="stage-name flex-center flex-col" :class="{ 'has-sub': hasSub }">
        <div class="flex-center" :class="`main-stage-bg-${status}`">
            <div class="main-stage-text a-text-main flex-center">
                {{ name }}
            </div>
        </div>
        <template v-if="hasSub">
            <div class="flex-center sub-stage">
                <div
                    v-for="item in data.subView?.subStageTableViewList"
                    :key="item.stageType"
                    class="a-text-main sub-stage-text"
                    :class="{
                        current: item.stageType === data.anchorStageType,
                    }"
                    @click="($event) => handleClick(item)"
                >
                    <span>{{ item.stageName }}</span>
                </div>
            </div>
        </template>
    </div>
</template>

<style lang="less" scoped>
.stage-name {
    font-family: HYYakuHei;
    white-space: nowrap;
}
.has-sub {
    margin-bottom: 20px;
}
.main-stage-bg-1 {
    width: 144px;
    height: 40px;
    background: url('./bg-1_2x.png') center / 100% no-repeat;
}
.main-stage-bg-2 {
    width: 188px;
    height: 40px;
    background: url('./bg-2_2x.png') center / 100% no-repeat;
}
.main-stage-bg-3 {
    width: 202px;
    height: 40px;
    background: url('./bg-3_2x.png') center / 100% no-repeat;
}
.main-stage-text {
    height: 30px;
    font-size: 20px;
    // font-weight: 400;
    // line-height: 30px;
    // text-align: center;
    color: #813418;
    // margin-top: -2px;
}
.sub-stage {
    margin-top: 64px;
}
.sub-stage-text {
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    opacity: 0.8;
    position: relative;
    &:not(:last-child) {
        margin-right: 16px;
    }
    &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: -9px;
        top: 6px;
        width: 2px;
        height: 20px;
        background: url('./border.png') center / 100% no-repeat;
    }
}
.current {
    font-size: 20px;
    line-height: 30px;
    opacity: 1;
}
</style>
