import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { RankFrameworkPostResponse } from '@pet/ones-rank.schema/index-home';
export const stageNameFun = (contextName: symbol) => {
    const homeData = inject<Ref<RankFrameworkPostResponse>>(contextName);
    const data = computed(() => {
        return {
            anchorStageType: homeData?.value?.anchorStageType,
            subView: homeData?.value?.secondLevelStageTableView,
            lookingBack: homeData?.value?.lookingBack,
        };
    });
    return data;
};
