import { isOutLiveRoom, entry_src } from '@alive-ui/actions';

export const is4Tab = entry_src === 'HOME_FOUR_TAB';

// -1 tab
export const isSearchTab = entry_src === 'SEARCH_BANNER';

//  全局挂件
export const isAllApp = entry_src === 'ALL_APP_PENDANT';

// 间外搜索 banner 位置
export const isSeachGuessList = entry_src === 'SEARCH_GUESS_LIST';

// 分享场景
export const isShare = entry_src === 'SHARE_BACK';

// 间内场景
export const isLive = !isOutLiveRoom;
