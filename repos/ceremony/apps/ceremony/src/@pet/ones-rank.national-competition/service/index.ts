import {
    request,
    activityBiz,
    authorId,
    getQuery,
    Report,
} from '@alive-ui/actions';
import type { Data } from '../scheme';

// 全国赛主接口
export const getNationalData = async () => {
    const PATH = '/webapi/live/revenue/operation/activity/ceremony/countryRank';
    const res = await request.post<Data>(PATH, {
        activityBiz,
        authorId,
        timestamp: 0,
        rankActivityAlias: getQuery('rankAliasName'),
    });

    return res?.data;
};
