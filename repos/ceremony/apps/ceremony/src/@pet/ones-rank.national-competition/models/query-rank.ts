/**
 * 榜单列表 store
 */
import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import { Toast } from '@lux/sharp-ui-next';
import { getPageStatus, Report } from '@alive-ui/actions';
import { getNationalData } from '../service';
import { focusActivityId } from '@/const';

// eslint-disable-next-line max-lines-per-function
export default defineStore('national-rank', () => {
    const dyncData = ref({
        rankId: 0,
        periodId: 0,
        bizName: '',
    });
    const laneIndex = ref(0);
    const allLanesRank = ref();
    const status = ref(getPageStatus('init'));
    const rankInfo = ref();
    // 直播间id列表,点击头像上下滑直播间所需
    const liveStreamIdList = computed(() => {
        return (
            rankInfo.value?.rankList?.map((item: any) => {
                return item?.liveStreamId;
            }) || []
        );
    });

    // 榜单主体数据
    const rankData = computed(() => {
        return {
            ...rankInfo.value, // 榜单接口返回的所有数据
            liveStreamIdList: liveStreamIdList.value,
            status: status.value, // 榜单接口状态
            isClearing: rankInfo.value?.extraData?.isClearing,
            dyncData: dyncData.value, // 榜单的请求参数,从这里拿到当前榜单rankId的好处在于不用等榜单接口返回
            configData: {
                list: rankInfo.value?.rankList || [],
                scheduleId: rankInfo.value?.scheduleId,
                liveStreamIdList: liveStreamIdList.value,
                currentRankId: rankInfo.value?.rankId || 0,
                // 榜单分值标签名称，如盛典值
                scoreLabel: '热度值',
                // 埋点额外参数
                logParams: {
                    scheduleId: rankInfo.value?.scheduleId,
                    rankId: rankInfo.value?.rankId,
                },
                focusActivityId, // 全局的constant文件
                isNeedDivider: true,
                isNeedTraffic: true,
            },
        };
    });
    const awardInfo = computed(() => {
        return {
            randomKey: new Date().getTime(),
            marqueeList:
                rankInfo?.value?.extraData?.privilegeDesc
                    ?.split('$')
                    ?.filter((i: any) => i) || [],
            awardURL: rankInfo?.value?.extraData?.privilegeIconUrl || '',
            awardList:
                rankInfo.value?.extraData?.privilegeOverview?.championAwards ||
                [],
            showAward: true,
            operateText: '奖励',
            topAwards:
                rankInfo.value?.extraData?.privilegeOverview?.topAwards || [],
        };
    });

    let clickLoadingFlag = false;

    const tabList = computed(() => {
        // 从allLanesRank.value 数组中的每一项，选出laneName, 作为一个新数组返回

        return allLanesRank.value?.reduce(
            (acc: any[], cur: { laneName: any }) => {
                console.log('cur', cur);
                acc.push(cur.laneName);
                return acc;
            },
            [],
        );
    });
    // 榜单获取接口数据
    const init = async (isTabChange?: boolean) => {
        try {
            if (isTabChange) {
                Toast.loading('正在加载', 0, true);
            } else {
                status.value = getPageStatus('loading');
            }

            const res = await getNationalData();
            allLanesRank.value = res?.allLanesRank || [];
            rankInfo.value = allLanesRank.value?.[laneIndex.value];
            status.value = getPageStatus(
                rankInfo.value?.extraData?.isClearing ||
                    rankInfo.value?.rankList?.length
                    ? 'success'
                    : 'nodata',
            );
            if (!rankInfo.value) {
                Report.biz.error('全国榜单接口错误', {
                    error: '无数据',
                });
            }
            if (status.value.nodata) {
                Report.biz.error('全国榜单接口错误', {
                    error: '无榜单数据',
                    laneIndex: laneIndex.value,
                });
            }
        } catch (err) {
            status.value = getPageStatus('error');
            Report.biz.error('全国榜单接口错误', {
                err,
            });
            rankInfo.value = undefined;
            allLanesRank.value = [];
        } finally {
            Toast.hide();
            clickLoadingFlag = false;
        }
    };
    const displayHintArr = computed(() => {
        const bottomInfo = rankInfo.value?.bottomInfo;

        if (bottomInfo?.displayHint && bottomInfo?.h5ShowHintScore) {
            return bottomInfo.displayHint.split('${hintScore}');
        }

        return []; // 如果 bottomInfo 不存在，返回空字符串或其他默认值
    });
    // 榜单刷新
    const refresh = () => {
        init();
    };
    const tabChange = (index: number) => {
        console.log(index);
        laneIndex.value = index;
        init(true);
        // rankInfo.value = allLanesRank.value?.[index];
        // status.value = getPageStatus(
        //     rankInfo.value?.extraData?.isClearing ||
        //         rankInfo.value?.rankList?.length
        //         ? 'success'
        //         : 'nodata',
        // );
        // if (status.value.nodata) {
        //     Report.biz.error('全国榜单接口错误', {
        //         error: '无榜单数据',
        //         laneIndex: laneIndex.value,
        //     });
        // }
    };
    const currAuthorInfo = computed(() => {
        return rankInfo.value?.bottomInfo?.itemRankInfo || null;
    });
    return {
        liveStreamIdList,
        status,
        rankData,
        rankInfo,
        tabList,
        awardInfo,
        displayHintArr,
        currAuthorInfo,
        tabChange,
        init,
        refresh,
        dyncData,
        laneIndex,
        allLanesRank,
    };
});
