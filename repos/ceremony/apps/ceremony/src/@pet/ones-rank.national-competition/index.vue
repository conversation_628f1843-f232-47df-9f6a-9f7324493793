<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { defineAsyncComponent, onUnmounted } from 'vue';
import { storeToRefs } from 'pinia';
import PageFrame from '@pet/ones-ui.page-frame/index.vue';
import TrackTab from '@pet/ones-rank.track-tab/index.vue';
import RankList from '@pet/ones-rank.common-list/index.vue';
import Clearing from '@pet/ones-rank.clear/index.vue';
import { Avatar as APAvatar } from '@alive-ui/pro';
import {
    ACard,
    ACardContent,
    ACardSubtitle,
    ACardTitle,
    ACardTitleTag,
    ATabs,
    ATabList,
    ATab,
    ElseStatus,
} from '@alive-ui/base';
import { getPageStatus } from '@alive-ui/actions';
import useRank from './models/query-rank';
import useKConfStore from '@/@pet/ones-use.useKconfBatch/index';

const AwardCollapse = defineAsyncComponent(() => {
    return import('@pet/ones-rank.award-collapse/index.vue');
});
const router = useRouter();
const { kconfData } = storeToRefs(useKConfStore());
const store = useRank();
store.init();
const goHome = () => {
    router.push({
        name: 'home',
    });
};
const {
    tabList,
    rankInfo,
    awardInfo,
    liveStreamIdList,
    displayHintArr,
    currAuthorInfo,
    status,
    rankData,
} = storeToRefs(store);

onUnmounted(() => {
    store.dyncData = {
        rankId: 0,
        periodId: 0,
        bizName: '',
    };
    store.laneIndex = 0;
    store.allLanesRank = undefined;
    store.status = getPageStatus('init');
    store.rankInfo = undefined;
});
</script>

<template>
    <PageFrame
        :data="{
            image:
                kconfData?.mainPage?.nationalCompetition?.kvImg ||
                'https://p4-live.wskwai.com/kos/nlav12706/2025-summer/assets/auto_2x.dee20692ad10d3ee.png',
            rule: 'growFans',
            showBack: true,
            homeStatus: {
                success: true,
            },
            backFunc: goHome,
        }"
        class="national-competition-page"
        @refresh="store.init"
    >
        <template #main-container>
            <ACard v-if="currAuthorInfo && !rankInfo?.extraData?.isClearing">
                <ACardContent>
                    <div class="profile-frame a-text-main">
                        <div class="flex-start-center">
                            <APAvatar
                                :class="{
                                    'error-avatar': !currAuthorInfo?.headUrl,
                                }"
                                size="xs"
                                :user-id="currAuthorInfo?.itemId"
                                :head-url="
                                    !currAuthorInfo?.headUrl
                                        ? 'https://p4-live.wskwai.com/kos/nlav12706/2025-summer/error-default.png'
                                        : currAuthorInfo?.headUrl
                                "
                                :live-stream-id-list="liveStreamIdList"
                                :live-stream-id="currAuthorInfo?.liveStreamId"
                                :is-mystery-man="currAuthorInfo?.mysteryMan"
                            ></APAvatar>
                            <div
                                v-if="currAuthorInfo.h5RankShowIndex"
                                class="profile-info"
                            >
                                第<span>{{
                                    currAuthorInfo.h5RankShowIndex
                                }}</span
                                >名
                            </div>
                            <div v-else class="profile-info">主播未上榜</div>
                        </div>
                        <div
                            v-if="displayHintArr?.length"
                            class="additional-data"
                        >
                            {{ displayHintArr[0]
                            }}<span>{{
                                rankInfo?.bottomInfo?.h5ShowHintScore
                            }}</span
                            >{{ displayHintArr[1] }}
                        </div>
                    </div>
                </ACardContent>
            </ACard>
            <ACard>
                <ACardContent>
                    <ATabs
                        v-if="tabList?.length"
                        size="xs"
                        type="solid"
                        @change="store.tabChange"
                    >
                        <ATabList>
                            <ATab v-for="item in tabList" :key="item">{{
                                item
                            }}</ATab>
                        </ATabList>
                    </ATabs>

                    <Clearing
                        v-if="rankInfo?.extraData?.isClearing"
                        class="mt-35px mb-35px"
                        use-props
                        :is-absolute="false"
                        :data="{
                            endTime: rankInfo?.extraData?.clearingEndTime || 0,
                            text: '已定榜，数据结算中',
                        }"
                        @end="store.init"
                    />
                    <template v-else>
                        <AwardCollapse
                            v-if="status.success"
                            :data="awardInfo"
                            use-props
                        ></AwardCollapse>
                        <RankList
                            v-if="status.success"
                            class="rank-list"
                            :data="{
                                list: rankInfo?.rankList,
                                liveStreamIdList,
                                logParams: {
                                    rankId: 124,
                                },
                                scoreLabel: '热度值',
                                focusActivityId:
                                    rankData?.configData?.focusActivityId,
                            }"
                        >
                        </RankList>
                        <ElseStatus
                            v-else
                            class="error-page-status"
                            :page-status="status"
                            :is-show-refresh="!status.nodata"
                            @refresh="store.init"
                        >
                        </ElseStatus>
                    </template>
                </ACardContent>
            </ACard>
        </template>
    </PageFrame>
</template>

<style lang="less" scoped>
.national-competition-page {
    --marginTop: -90px;
    --yImgWidth: 100%;
    --yImgHeight: 390px;
}
.profile-frame {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;
}
.profile-info {
    font-size: 16px;
    font-weight: 500;
    font-family: PingFang SC;
    margin-left: 10px;
}
.additional-data {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    text-align: right;
    opacity: 0.6;
    font-size: 12px;
}
.rank-list {
    margin-top: 14px;
}
</style>
