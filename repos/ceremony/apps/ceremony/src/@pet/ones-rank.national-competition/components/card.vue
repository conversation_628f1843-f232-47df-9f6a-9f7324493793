<template>
    <ACard
        v-show-log="{
            action: 'OP_ACTIVITY_FANS_FIGHT_CARD',
        }"
        class="national-competition-card"
    >
        <ACardContent class="national-competition-content">
            <div class="activity-title">
                <div class="name-section">
                    <!-- 使用 alive-ui 的 Avatar 组件 -->
                    <img
                        :src="growFansGame.privilegeIcon"
                        :size="48"
                        class="privilege-icon"
                        fall-back-src="https://placeholder.com/48x48"
                    />

                    <div class="national-content">
                        <div class="title-row">
                            <!-- 活动名称使用渐变文字 -->
                            <span class="activity-name a-text-title">{{
                                growFansGame.title
                            }}</span>

                            <!-- 状态标签 -->
                            <div class="status-tag">
                                <span>{{ growFansGame.joinTip }}</span>
                            </div>

                            <!-- 查看按钮区域 -->
                            <div
                                v-click-log="{
                                    action: 'OP_ACTIVITY_FANS_FIGHT_CARD',
                                    params: {
                                        btn_type: 'VIEW',
                                    },
                                }"
                                class="view-section flex-end a-text-main-o2"
                                @click="nationalCompetitionPage"
                            >
                                <span class="view-text">查看全国榜</span>
                                <Right class="title-arrow ml-2px" />
                            </div>
                        </div>

                        <!-- 底部描述文本 -->
                        <div class="description">
                            {{ growFansGame.rewardTip }}
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div
                v-if="growFansGame.clearing"
                class="clearing-box a-text-main-o2"
            >
                已定榜请稍后，数据结算中
                <span class="time-countdown">
                    {{ useCountdownText?.text }}
                </span>
            </div> -->
            <div
                v-if="!growFansGame.clearing && growFansGame.rankList?.length"
                v-pcDirectives:scroll="handleScroll"
                class="user-management"
            >
                <div
                    v-for="(user, index) in growFansGame.rankList"
                    :key="index"
                    class="user-profile"
                    :class="{ 'cur-user-profile': authorId === user.itemId }"
                >
                    <!-- 使用 alive-ui 的 Avatar 组件
                        :live-stream-id-list="liveStreamIdList"
					   -->
                    <APAvatar
                        v-click-log="{
                            action: 'OP_ACTIVITY_FANS_FIGHT_CARD',
                            params: {
                                btn_type: 'VIEW',
                                author_id: user.itemId,
                                live_stream_id: user.liveStreamId,
                            },
                        }"
                        :user-id="user.itemId"
                        :head-url="
                            !user.headUrl
                                ? 'https://p4-live.wskwai.com/kos/nlav12706/2025-summer/error-default.png'
                                : user.headUrl
                        "
                        :live-stream-id="user.liveStreamId"
                        :is-mystery-man="user.mysteryMan"
                        :live-stream-id-list="liveStreamIdList"
                        size="2xs"
                    />

                    <div class="user-data">
                        <div class="process-data">
                            <!-- 图标容器 -->
                            <div
                                class="data-frame"
                                :class="`data-frame-${user.rankShowIndex}`"
                            >
                                <span
                                    v-if="1 * user.rankShowIndex > 3"
                                    class="data-frame-number a-text-main"
                                    >{{ user.h5RankShowIndex }}</span
                                >
                                <!-- <img :src="user.icon" alt="icon" /> -->
                            </div>

                            <!-- 地区标签 -->
                            <div class="data-frame-secondary">
                                <span class="location">{{
                                    growFansGame.author2LaneNameMap[user.itemId]
                                }}</span>
                            </div>
                        </div>

                        <!-- 描述文本 -->
                        <div class="additional-data">
                            <span
                                v-if="authorId === user.itemId"
                                class="data-item"
                                >当前主播</span
                            >
                            <span v-else class="data-item">{{
                                nameSlice(user.itemName, 6)
                            }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </ACardContent>
    </ACard>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { computed, ref, type Ref } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { Avatar as APAvatar } from '@alive-ui/pro';
import { Right, Refresh } from '@alive-ui/icon';
import { ACard, ACardContent, ACardTitle, ACardSubtitle } from '@alive-ui/base';
import {
    nameSlice,
    authorId,
    useCountdownTo,
    convertSToMinusSecond,
} from '@alive-ui/actions';
import type { GrowFansGame } from '@pet/ones-rank.schema/query-rank';

// 定义组件props
const props = withDefaults(
    defineProps<{
        growFansGame: Omit<GrowFansGame, 'showConfig'>;
        rankAliasName: string;
    }>(),
    {
        growFansGame: () => {
            return {
                privilegeIcon: '',
                title: '涨粉榜争夺赛',
                joinTip: '每个赛道Top1争夺中',
                rewardTip: '前三名瓜分百万流量包',
                rankList: [],
                author2LaneNameMap: {},
            };
        },
    },
);
const liveStreamIdList = computed(() => {
    return props.growFansGame?.rankList?.map((item: any) => item.liveStreamId);
});
const router = useRouter();
const nationalCompetitionPage = () => {
    router.push({
        name: 'national-competition',
        query: {
            rankAliasName: props.rankAliasName,
        },
    });
};
// 处理滚动事件
const handleScroll = (e: Event) => {
    // 处理横向滚动逻辑
};
// const useCountdownText = ref<{
//     text: Ref<string>;
//     reset: (_t: number) => void;
//     leftMs: Ref<number>;
// }>();
const emits = defineEmits(['refresh-task']);

// useCountdownText.value = useCountdownTo(props.growFansGame.clearingEndTime, {
//     immediateEmit: true,
//     useServerTime: true,
//     transformFn: (t: number) => convertSToMinusSecond(t / 1000),
//     onEnd: () => {
//         console.log('倒计时结束');
//         emits('refresh-task', {
//             extraCardKey: 'growFansGame',
//             showLoading: false,
//             isSingle: true,
//         });
//     },
// });
</script>

<style lang="less" scoped>
.national-competition-card {
    padding: 12px 0;
    margin: 14px 0 !important;
}
.activity-title {
    box-sizing: border-box;
    width: 382px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
}

.name-section {
    flex: 1;
    display: flex;
    align-items: center;
    .privilege-icon {
        width: 48px;
        height: 48px;
        margin-right: 4px;
    }
}

.national-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.title-row {
    display: flex;
    align-items: center;
}

.activity-name {
    min-width: 84px;
    height: 20px;
    font-size: 14px;
    line-height: 20px;
    font-family: HYYakuHei;
}

.status-tag {
    height: 16px;
    border-radius: 8px;
    background: #ffd40019;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 4px;
    margin-left: 4px;
}

.status-tag span {
    font-size: 10px;
    line-height: 16px;
    color: #ffd400;
}

.view-section {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    font-family: 'PingFang SC';
    font-size: 0.24rem;
    font-weight: 400;
    line-height: 0.36rem;
    display: flex;
    align-items: center;
}

.view-text {
    opacity: 0.6;
    font-size: 12px;
    line-height: 18px;
    color: #ffdfbf;
}

.arrow-icon {
    width: 10px;
    height: 10px;
}

.description {
    opacity: 0.6;
    font-size: 12px;
    line-height: 18px;
    color: #ffdfbf;
}

.national-competition-rank {
    display: flex;
}
.rank-item {
    margin-right: 10px;
    display: flex;
    width: 104px;
    height: 48px;
    background-color: #f2f2ff0f;
    border-radius: 8px;
}
.user-management {
    width: 382px;
    min-height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: flex-start;
    padding-left: 12px;
    overflow-x: auto;
    overflow-y: hidden;
    margin-top: 8px;
    &:after {
        content: '';
        display: block;
        min-width: 12px;
        height: 1px;
    }
}

.user-profile {
    width: 122px;
    min-height: 48px;
    border-radius: 8px;
    background: #f1f1ff0f;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 8px;
    flex-shrink: 0;
    & + .user-profile {
        margin-left: 8px;
    }
}
.cur-user-profile {
    background: rgba(241, 241, 255, 0.16);
}

.user-data {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    margin-top: 2px;
    margin-left: 8px;
}

.additional-data {
    margin-top: 2px;
    height: 14px;
    line-height: 14px;
}

.process-data {
    display: flex;
    align-items: center;
}

.data-frame {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    background: #9abdff0f;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: 100%;
    flex-shrink: 0;
    &.data-frame-1 {
        background-image: url('../assets/rank1-icon_2x.png');
    }
    &.data-frame-2 {
        background-image: url('../assets/rank2-icon_2x.png');
    }
    &.data-frame-3 {
        background-image: url('../assets/rank3-icon_2x.png');
    }
    .data-frame-number {
        font-size: 12px;
        font-family: Alte DIN Mittelschrift;
    }
    img {
        width: 100%;
        height: 100%;
    }
}

.data-frame-secondary {
    height: 16px;
    border-radius: 3px;
    background: #9abdff0f;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 4px;
    margin-left: 2px;
}

.location {
    font-size: 10px;
    line-height: 14px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #ffdfbf;
}

.data-item {
    opacity: 0.6;
    font-size: 10px;
    line-height: 14px;
    font-family: PingFang SC;
    color: #ffdfbf;
}
.clearing-box {
    background: #f2f2ff0f;
    width: 358px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    border-radius: 8px;
    font-size: 12px;
    margin-top: 2px;
    .time-countdown {
        font-family: Alte DIN Mittelschrift;
        margin-left: 2px;
    }
}
</style>
