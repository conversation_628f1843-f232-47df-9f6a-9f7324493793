export interface RankListItem {
    itemId: number;
    itemName: string;
    headUrl: string;
    liveStreamId: any; // 根据实际情况调整类型
    mysteryMan: boolean;
    rankShowIndex: number;
    h5ShowScore: string;
    followStatus: boolean;
}

export interface BottomInfo {
    itemRankInfo: {
        item: {
            itemId: number;
            itemName: string;
            headUrl: string;
            liveStreamId: any; // 根据实际情况调整类型
            mysteryMan: boolean;
        };
        score: number;
        expScore: number;
        rankShowIndex: number;
        liveStreamId: string;
        followStatus: boolean;
        h5ShowScore: string;
        mysteryMan: boolean;
        h5RankShowIndex: string;
        itemId: number;
        itemName: string;
        displayScore: string;
        headUrl: string;
    };
    displayHint: string;
    hintScore: number;
    h5ShowHintScore: string;
    showBottom: boolean;
    showPromotion: boolean;
    cpPrivateCpOrgInfo: any; // 根据实际情况调整类型
}

export interface ChampionAward {
    awardId: number;
    awardIcon: string;
    awardImg: string;
    awardName: string;
    awardMinScore: number;
}

export interface TopAward {
    rankName: string;
    awardItemIcons: ChampionAward[];
}

export interface PrivilegeOverview {
    championAwards: ChampionAward[];
    topAwards: TopAward[];
}

export interface Lane {
    classifyName: string;
    laneName: string;
    order: number;
    rankList: RankListItem[];
    bottomInfo: BottomInfo | null;
    privilegeOverview: PrivilegeOverview;
}

export interface Data {
    title: string;
    joinTip: string;
    allLanesRank: Lane[];
}

export interface Response {
    result: number;
    hostName: string;
    ktraceId: string;
    data: Data;
}
