## 使用

App.vue 中初始化，根据选择模式和 kconf key 一次性拉取配置

```typescript
import useKConf from '@pet/ones-use.useKconf/index';

const { initData } = useKConf();
initData({
    // 模式，normal_only 只获取普通业务配置 confBiz（别名 kconfData）和 kv 配置 confKv；4tab_only 只获取 4Tab 配置 conf4Tab；both 这几个配置都有
    mode: 'both',
    // kconf key，注意都用 frontend.activity.{}，否则活动后端接口调不通
    keys: {
        biz: 'frontend.activity.ceremony-2024-09', // 业务配置 kconf key
        kv: 'frontend.activity.ceremony-2024-09-kv', // kv 配置 kconf key
        conf4tab: 'frontend.activity.ceremony-2024-4tab', // 4Tab 配置 kconf key
    },
});
```

配置消费

```typescript
import useKConf from '@pet/ones-use.useKconf/index';

const conf = useKconf(); // 直接获取配置

// 业务配置消费：conf.confBiz?.foo?.bar
// kv 消费：conf.confKv?.foo?.bar
// 4Tab 消费：conf.conf4Tab?.foo?.bar

// 也可以通过 pinia storeToRefs 解构成 ref使用
const { confBiz, confKv } = storeToRefs(useKconf);
// confBiz?.foo?.bar
```
