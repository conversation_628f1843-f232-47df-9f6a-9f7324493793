import { ref } from 'vue';
import { defineStore } from 'pinia';
import { getPageStatus, getKConf, Report } from '@alive-ui/actions';
import type { CONFIG } from './schemas';

// normal_only 只有普通业务配置 kconfData；4tab_only 只有 conf4Tab；both 两个配置都有
type Mode = 'normal_only' | '4tab_only' | 'both';

interface Options {
    // 模式，见 type Mode 注释说明
    mode?: Mode;
    // kconf key，注意都用 frontend.activity.{}，否则活动后端接口调不通
    keys: {
        biz?: string; // 主业务 kconf key，e.g. frontend.activity.ceremony-2024-09
        kv?: string; // kv kconf key，e.g. frontend.activity.ceremony-2024-09-kv
        conf4tab?: string; // 4tab kconf key，e.g. frontend.activity.ceremony-2024-4tab
    };
}

export default defineStore('stores_kconf', () => {
    const pageStatus = ref(getPageStatus('init'));
    const pageStatusKv = ref(getPageStatus('init'));
    const pageStatus4Tab = ref(getPageStatus('init'));
    // TODO: 拉取远端 kconf、生成类型
    const kconfData = ref({} as unknown as CONFIG);
    const confKv = ref<any>({});
    const conf4Tab = ref<any>({});
    const lock = ref(false);

    async function initData(options: Options) {
        const { mode = 'normal_only', keys } = options;
        pageStatus.value = getPageStatus('loading');
        pageStatusKv.value = getPageStatus('loading');
        const injectData = window.__GUNDAM_KCONF_INJECT_DATA__;

        const keyList4Tab = keys.conf4tab ? keys.conf4tab.split('.') : [];
        const params4Tab = {
            key1: keyList4Tab[0],
            key2: keyList4Tab[1],
        };

        // 一、4tab，走接口、只取 4tab 专有配置
        if (mode === '4tab_only') {
            if (pageStatus4Tab.value?.loading || lock.value) {
                return;
            }
            pageStatus4Tab.value = getPageStatus('loading');
            try {
                lock.value = true;
                const { data } = await getKConf(keyList4Tab[2], params4Tab);
                conf4Tab.value = data || {};
                pageStatus4Tab.value = getPageStatus(
                    data ? 'success' : 'nodata',
                );
                setTimeout(() => {
                    lock.value = false;
                }, 1000);
            } catch (error) {
                pageStatus4Tab.value = getPageStatus('error');
                Report.biz.error('【4tab-kconf】请求异常', {
                    error,
                });
                lock.value = false;
            }
        } else {
            // 二、非 4tab h5，消费普通业务配置，优先读 KFX 注入
            if (keys.biz && injectData?.[keys.biz]) {
                kconfData.value = injectData[keys.biz];
                pageStatus.value = getPageStatus('success');
                keys.kv && (confKv.value = injectData[keys.kv]);
                pageStatusKv.value = getPageStatus('success');
            } else {
                try {
                    if (keys.biz) {
                        const { data } = await getKConf(keys.biz.split('.')[2]);
                        kconfData.value = data || {};
                        pageStatus.value = getPageStatus(
                            data ? 'success' : 'nodata',
                        );
                    }
                } catch (error) {
                    pageStatus.value = getPageStatus('error');
                }
                try {
                    if (keys.kv) {
                        const { data } = await getKConf(keys.kv.split('.')[2]);
                        confKv.value = data || {};
                        pageStatusKv.value = getPageStatus(
                            data ? 'success' : 'nodata',
                        );
                    }
                } catch (error) {
                    pageStatusKv.value = getPageStatus('error');
                }
            }

            // 三、非 4tab h5，4tab 页面投放 h5 仍然消费专有配置、优先读 KFX 注入
            if (mode === 'both') {
                pageStatus4Tab.value = getPageStatus('loading');
                if (keys.conf4tab && injectData?.[keys.conf4tab]) {
                    conf4Tab.value = injectData[keys.conf4tab];
                    pageStatus4Tab.value = getPageStatus('success');
                } else {
                    try {
                        const { data } = await getKConf(
                            keyList4Tab[2],
                            params4Tab,
                        );
                        conf4Tab.value = data || {};
                        pageStatus4Tab.value = getPageStatus(
                            data ? 'success' : 'nodata',
                        );
                    } catch (error) {
                        pageStatus4Tab.value = getPageStatus('error');
                    }
                }
            }
        }
    }

    return {
        pageStatus4Tab,
        pageStatus,
        kconfData, // 旧消费命名
        confBiz: kconfData, // 未来消费命名
        confKv,
        conf4Tab,
        initData,
    };
});
