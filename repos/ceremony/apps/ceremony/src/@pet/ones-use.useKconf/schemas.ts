export type CONFIG = {
    // [key: string]: string | number | boolean | string[];
    // 榜单配置
    kingKv: string;
    kingNormalKv: string;
    competitionBgUrlMap: any;
    competitionBgUrlDefault: any;
    mainRank: MainRank;
    // 巅峰对决配置
    peak: Peak;
    acceptRewardAddress: string;
    lotteryResources: LotteryResources; //  @wenxin 定义消费资源切换的数据类型
    lotterySwitch: boolean; // 全局控制总开关，用来控制视频是否播放
    lotteryToast: string; // kconf远端配置toast文案
    lotteryConfigs: lotteryConfigs;
    // 季度之星
    star: Star;
    throughFinals: ThroughFinals;
    restDay: REST_DAY;
    // 用户身份加成
    cBuffCardRules: any;
    noblePage: any;
    // 规则字段
    rule: RuleConfig;
    // 荣耀神器
    glory: GloryConfigs;
    // 年度回忆录
    recall: RecallConfig;
    // 暂时为any
    newAnchorCard: any;
    // 凝聚力挑战
    cohesion: Cohesion;
    competitionB: {
        stageMap: Record<string, EStageKeyB>; // [stageType, stageKeyB]
        resourceMap: Record<
            EStageKeyB,
            {
                ruleKey: string;
            }
        >;
    };
    newStormConfig: {
        previewPrize: any;
        scheduleConfig: any;
        previewBottom: any;
        titleList: any;
        previewUserText: any;
        kvImg: string;
        moduleList: any;
        previewUserImg: string;
        answer: any;
        answerPop: {
            noDataText: string;
            noRewardText: string;
        };
    };
    myProp: {
        kv: string;
        strategyC: string;
    };
    answerSessionConfig: {
        ruleUrl: string;
        kvImage: string;
    };
    preview: {
        authorLaneTips: string;
    };
    hotListConfig: {
        kvImage: string;
    };
    fixedBottomConfig: {
        config: any;
        tabs: any;
        stageMap: object;
        outLiveRoomDel: any;
        competitionGroup: any;
    };
    //  身份加成
    authorAddition: {
        kv: string;
        ruleIcon: string;
        ruleUrl: string;
    };
    // 规则页链接
    ruleURL: string;
    guildCash: {
        KV: string;
        ruleUrl: string;
        divideImg: string;
        popularContest: number[];
    };
    // 荣誉殿堂
    honorHallConfigs: HonorHallConfigs;
    addressConfig: {
        anchorAddress: string;
        userAddress: string;
    };
    gameRewardsText: string;
    bufferModal: {
        title: string;
        desc: string;
    };
    appointment: any;
    /**
     * 线下邀请赛相关配置
     */
    inviteConfigs: {
        timeRange: number[]; //  时间区间，用于判断底 tab 展示
        kv: string;
        ruleUrl: string;
        fnalNavMap: any[];
        dailyNavMap: any[];
        giftNavMap: any[];
        giftFinalNavMap: any[];
        kvTitle: string;
    };
    offlineConfigs: {
        kv: string;
        offlineGame: string;
    };
    memoryEntry: {
        show: boolean;
        icon: string;
        link: string;
    };
};

export type RecallConfig = {
    showMemoirs: boolean; // 是否展示回忆录入口
    memoirsStartTime: Date; // 开始时间展示回忆录的时间 2024-12-10 00:00:00
    link: string; // 入口跳转回忆录地址
    icon: string; // 图片地址
};

export interface RuleConfig extends Record<string, string> {
    // 用户身份加成
    userStanding: string;
}

export enum EStageKeyB {
    STAR = 'star', // 新星赛
    PK = 'pk', // 盛典PK赛
    FANS = 'fans', // 超级粉丝赛
    GUILD = 'guild', // 公会地区赛
}

export interface REST_DAY {
    titie: string;
    restText: string;
    honorImg: string;
    futureImg: string;
}

export type ThroughFinals = {
    title: string; // 直通决赛名额标题
    subTitle: string; // 日榜TOP1直通决赛副标题
    Kv: string; // Kv图片链接
    defaultAvatar: string; // 默认头像图片链接
    rewardImg: string; // 奖励图片链接
};
export type MainRank = {
    cityType: number[]; // 城市类型
    focusActivityId: string; // 关注活动id
    rankType: { [key: number]: string }; //  榜单类型
    kvRule: string; // kv规则
    reviveRule: string; // 复活规则
};

export type Star = {
    kv?: {
        kvImg?: string;
        subtitle: string;
        signText: string;
        signUrl: string;
    };
    rule: {
        link: string;
        icon: string;
    };
    error: {
        forbiddenText: string;
        errorText: string;
        emptyText: string;
        kv: string;
    };
    task: {
        totalStarTitle: string;
        curStarTitle: string;
        taskTitle: string;
        taskDesc: string;
        addTeam: {
            title: string;
        };
        disbandTeamTxt: {
            title: string;
            content: string;
            cancelBtn: string;
            confirmBtn: string;
        };
        addTeamTxt: {
            title: string;
            placeholder: string;
            confirmBtn: string;
        };
        taskOutTeamInfo: {
            add: string;
            remind: string;
            copy: string;
        };
        taskInTeamInfo: {
            remove: string;
        };
        starUnit: string;
        record: string;
    };
    starEffect: {
        [key: number]: {
            [key: string]: string;
            YELLOW: string;
            PURPLE: string;
        };
    };
    award: {
        cupVideoSDKPlayer: boolean;
        cupMap: {
            [key: number]: {
                img: string;
                video: string;
            };
        };
        section: {
            disAbledExchange: string;
            emitAuthor: string;
            exchange: string;
            unExchange: string;
            exchanged: string;
            cupExchange: string;
            cupExchanged: string;
            cupUnExchangeStart: string;
            cupUnExchangeEnd: string;
            cupUnExchangeEnd2: string;
            cupEmitAuthor: string;
            cupDisabledExchange: string;
            disabledExchangeToast: string;
            userExchangeToast: string;
            userDisabledExchangeToast: string;
        };
        redeem: {
            submitTitle: string;
            submitContent: string;
            submitAction: string;
            cancelAction: string;
            failTitle: string;
            failContent: string;
            failCancel: string;
            failSubmit: string;
            success: string;
        };
        history: {
            title: string;
            star: string;
            redeem: string;
            format: string;
        };
    };
};

export type Peak = {
    headKv: string;
    headKvRule: string;
    headKvRuleUrl: string;
    overview: {
        tabTitle: string;
        tabIcon: string;
        notStartText4p: string;
        notStartText2p: string;
        inProgressText: string;
        notParticipatedText: string;
    };
    wonderful: {
        tabTitle: string;
        tabIcon: string;
        tipStart: string;
        tipActive: string;
        tipEnd: string;
        elseStatus: {
            title: string;
            text: string;
        };
    };
};

export type LotteryContentConfigs = {
    audio: string[];
    general: any[];
    transparent: any[];
};
export type LotteryItemConfigs = {
    audio?: any[] | undefined;
    poster: string;
    oneLottery: LotteryContentConfigs;
    tenLottery: LotteryContentConfigs;
};
export type LotteryResources = {
    blindBox: LotteryItemConfigs;
    scratchOff: LotteryItemConfigs;
    marbles: LotteryItemConfigs;
};

export type lotteryConfigs = {
    cardTitle: string;
    cardSubTitle: string;
    coinName: string;
    getCionLabel: string;
    acceptRewardAddress: string;
    lotteryOnceImg: string;
    lotteryRuleUrl: string;
    isDoublePanel: boolean;
    notParallelTab: boolean;
    onceModal: boolean;
};

export type GloryConfigs = {
    topBg: string;
    giftIcon: string;
    ruleBgTop: string;
    ruleBgMiddle: string;
    ruleBgBottom: string;
};

export type Cohesion = {
    kv: string;
    rule: string;
    guideAward: string;
    guideAuthor1: string;
    guideAuthor2: string;
    guideUser: string;
};

export type HonorHallConfigs = {
    titleKv: {
        share: string;
        rule: string;
        ruleUrl: string;
    };
    competitionHonors: string;
    titleIcon: {
        left: string;
        right: string;
    };
    noDataImage: string;
    noDataText: string;
    honorBackground: {
        video: string;
        image: string;
    };
    honorNumberBackground: string;
    honorNumberWithoutBackground: string;
    isUsePreload: boolean;
    isShowShare: boolean;
};
