<script lang="ts" setup>
import { useRouter, type LocationQueryValueRaw } from 'vue-router';
import { computed, ref, defineAsyncComponent, onMounted, watch } from 'vue';
import MultPk from '@pet/ones-rank.mult-pk/index.vue';
import { Toast } from '@lux/sharp-ui-next';
import { sendClickLogImmediately as sendClick } from '@gundam/weblogger';
import { ACard, ACardContent } from '@alive-ui/base';
import { liveStreamId } from '@alive-ui/actions';
// import PropCard from '@pet/ones-rank.prop-card/card/index.vue';
import { BoostCardType, BoostCardStatus, PeakBattelSignType } from './schema';
import useSignUpStore from './card/store/signUp';
import NotOpen from './card/not-open/index.vue';
import BranchCardHead from './card/head/index.vue';
import DoublePk from './card/double-pk/index.vue';
import type { PropsCard, ExtraData } from '@pet/ones-rank.schema/query-rank';
import BufferCard from '@/modules/main/components/buffer-card/index.vue';

const signUpStore = useSignUpStore();

const router = useRouter();
// const MultPk = defineAsyncComponent(() => {
//     return import('@pet/ones-rank.mult-pk/index.vue');
// });

// const BufferCard = defineAsyncComponent(() => {
//     return import('@/modules/main/components/buffer-card/index.vue');
// });

const props = withDefaults(
    defineProps<{
        // propsCard?: PropsCard;
        pk?: ExtraData;
        stageType: number;
        rankActivityId: number;
        extraCardKey?: string;
    }>(),
    {
        // propsCard: () => ({}),
        pk: () => ({}) as ExtraData,
        extraCardKey: '',
    },
);

const emits = defineEmits(['refresh-task']);

const logs = (logTitle = '', extra = {}) => {
    return {
        action: 'OP_ACTIVITY_MORE_PLAY_CARD',
        params: {
            type: logTitle,
            live_stream_id: liveStreamId,
            ...extra,
        },
    };
};

const activeAdditionInfo = computed(() => {
    const { defaultSelectedAdditionInfo } = props.pk || {};
    return defaultSelectedAdditionInfo;
});

const signStatus = computed(() => {
    return props.pk?.peakBattleSignStatus;
});

const onTimeover = () => {
    emits('refresh-task', {
        extraCardKey: props.extraCardKey,
        showLoading: false,
    });
};

const goPage = (item: { source: BoostCardType }) => {
    // 小时榜
    // 车轮战
    if (
        item.source === BoostCardType.hourRank ||
        item.source === BoostCardType.carousel
    ) {
        router.push({
            name: 'peak-battle',
            query: {
                peakType: 0,
                stageType: props.stageType,
            },
        });
        const logTitle =
            item.source === BoostCardType.hourRank
                ? '四人巅峰对决'
                : '双人巅峰对决';

        // 点击事件埋点
        sendClick(
            logs(logTitle, {
                btn_type: 'OTHER',
            }),
        );
    }

    // 本次没有凝聚力暂时注释
    // if (item.source === BoostCardType.aggregation) {
    //     router.push({
    //         name: 'cohesion',
    //         query: {
    //             showBack: 'true',
    //             stageType: props.stageType,
    //         },
    //     });
    // }
};

const signUp = async () => {
    const res: any = await signUpStore.signUp(props.rankActivityId);

    if (res.success) {
        emits('refresh-task', {
            extraCardKey: props.extraCardKey,
            showLoading: true,
        });
    } else {
        Toast.info(res?.data?.data?.msg);
    }
};
</script>

<template>
    <div class="answer-card-wrapper">
        <ACard>
            <ACardContent class="card-content">
                <!-- 头部内容 -->
                <BranchCardHead
                    :data="props.pk"
                    :branch-data="activeAdditionInfo"
                    @go-branch-page="goPage"
                    @timeover="onTimeover"
                />
                <!-- 车轮战或者小时榜在未报名或者（未报名&报名时间已结束）时展示 -->
                <NotOpen
                    v-if="
                        (activeAdditionInfo?.source ===
                            BoostCardType.carousel ||
                            activeAdditionInfo?.source ===
                                BoostCardType.hourRank) &&
                        (signStatus === PeakBattelSignType.notRegistered ||
                            signStatus ===
                                PeakBattelSignType.notRegisteredAndEnd)
                    "
                    :data="activeAdditionInfo"
                    :sign-status="signStatus"
                    @sign-up="signUp"
                />
                <!-- 未报名 || 未报名&最后一天的加成卡展示时间-->
                <template
                    v-if="
                        signStatus === PeakBattelSignType.registered ||
                        signStatus ===
                            PeakBattelSignType.notRegisteredAndLastDay
                    "
                >
                    <!-- 车轮战 1v1 -->
                    <DoublePk
                        v-if="
                            activeAdditionInfo?.source ===
                                BoostCardType.carousel &&
                            activeAdditionInfo?.status !==
                                BoostCardStatus.obtained
                        "
                        v-show-log="logs('双人巅峰对决')"
                        v-click-log="logs('双人巅峰对决')"
                        :stage-type="stageType"
                        :data="activeAdditionInfo"
                    />
                    <!-- 小时榜 多人对决-->
                    <MultPk
                        v-if="
                            activeAdditionInfo?.source ===
                            BoostCardType.hourRank
                        "
                        v-show-log="logs('四人巅峰对决')"
                        v-click-log="logs('四人巅峰对决')"
                        :source-data="activeAdditionInfo"
                    />
                    <!-- 加成卡 -->
                    <BufferCard
                        v-if="
                            activeAdditionInfo?.status ===
                                BoostCardStatus.obtained &&
                            activeAdditionInfo?.source ===
                                BoostCardType.carousel
                        "
                        v-show-log="logs('加成卡')"
                        v-click-log="logs('加成卡')"
                        :item="activeAdditionInfo?.peakBattleInfo?.additionInfo"
                        @end-time="onTimeover"
                        @use-buff-card="onTimeover"
                    />
                </template>
                <!-- 道具卡 -->
                <!-- <PropCard :props-card="propsCard" /> -->
            </ACardContent>
        </ACard>
    </div>
</template>

<style lang="less" scoped>
.answer-card-wrapper {
    width: 382px;
    // min-height: 282px;
    margin: 0 auto;

    .card-content {
        padding: 0 12px;
    }
}
</style>
