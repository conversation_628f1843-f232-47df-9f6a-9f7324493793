export enum BoostCardStatus {
    // 1错失,2获得,3对决中,4未开始,5结束
    notGet = 1,
    obtained = 2,
    underWay = 3,
    notOpen = 4,
    finished = 5,
}

export enum BoostCardType {
    // 1凝聚力,2小时榜,3,车轮战
    aggregation = 1,
    hourRank = 2,
    carousel = 3,
}

export enum PeakBattelSignType {
    // 巅峰对决报名状态 1-未报名 2-已报名 3-未报名&报名时间已结束 4-未报名&最后一天的加成卡展示时间
    notRegistered = 1,
    registered = 2,
    notRegisteredAndEnd = 3,
    notRegisteredAndLastDay = 4,
}

export interface AuthorAdditionInfo {
    additionInfo: {
        showAdditionRate: string;
        additionRate: string;
        additionFactor: string;
    };
    state: BoostState;
    tips: string;
    prefixText: string;
}

export enum BoostState {
    // 0 待生效 1 进行中 2加成中
    notEffective = 0,
    underWay = 1,
    boosting = 2,
}
// export enum BoostCardStatusClass {
//     // 1错失,2获得,3对决中,4未开始
//      = 'not-get',
// }

export enum StageTypes {
    // 公会赛
    guild = 90,
    // 公会赛弹幕赛道
    guildBullet = 91,
    // 战队赛
    team = 170,
}
