<template>
    <div class="peak-battle-wonderful">
        <!-- 当前处于二人对决时间 -->
        <template v-if="isDoubleModel && isOnPK">
            <div class="peak-battle-wonderful-tip">
                <div class="peak-battle-wonderful-tip-icon"></div>
                <div class="peak-battle-wonderful-tip-text">
                    {{ kconf?.wonderful?.tipStart
                    }}<span v-if="kconf?.wonderful?.tipActive">{{
                        kconf?.wonderful?.tipActive
                    }}</span
                    >{{ kconf?.wonderful?.tipEnd }}
                </div>
            </div>
            <template v-if="hotRaceList.length > 0">
                <PeakCard
                    v-for="(item, index) in hotRaceList"
                    :key="index"
                    class="peak-battle-wonderful-item"
                    :pk-info="item"
                    :header-text="`TOP${index + 1}`"
                    use-active-tip
                    :show-crites="false"
                />
            </template>
            <ElseStatus
                v-else
                :page-status="{ nodata: true }"
                :is-show-refresh="false"
            ></ElseStatus>
        </template>
        <!-- 非二人对决时间 -->
        <ElseStatus
            v-else
            class="peak-battle-wonderful-else-status"
            :is-show-refresh="false"
        >
            <template #icon>
                <div class="settlement-icon"></div>
            </template>
            <template #title>
                <div class="mt-4px">{{ elseStatusTitle }}</div>
            </template>
            <template #text>
                {{ kconf?.wonderful?.elseStatus?.text }}
            </template>
        </ElseStatus>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import PeakCard from '@pet/ones-rank.card-peak-battle/components/peak-card/index.vue';
import { ElseStatus } from '@alive-ui/base';
import usePeakBattleStore from '../../store/peak-battle';

const peakBattleStore = usePeakBattleStore();
const { hotRaceList, kconf, isDoubleModel, isOnPK, doublePKText } =
    storeToRefs(peakBattleStore);

const elseStatusTitle = computed(() => {
    return isDoubleModel.value && !isOnPK.value
        ? doublePKText.value
        : kconf.value?.wonderful?.elseStatus?.title;
});
</script>

<style lang="less" scoped>
.peak-battle-wonderful {
    width: 100%;
    padding: 16px 12px;
    &-tip {
        width: 100%;
        padding: 8px 0;
        display: flex;
        align-items: center;
        @apply a-bg-substrate;
        @apply a-text-main;
        margin-bottom: 12px;
        border-radius: 8px;
        &-icon {
            width: 40px;
            height: 40px;
            background-image: url('../../assets/tip-icon.png');
            background-size: cover;
            background-repeat: no-repeat;
            margin-right: 8px;
            margin-left: 10px;
        }
        &-text {
            font-size: 14px;
            font-weight: bold;
            span {
                margin: 0 2px;
                @apply a-text-highlight;
                font-weight: bold;
            }
        }
    }
    &-item {
        margin-bottom: 12px;
    }
    &-else-status {
        height: 400px;
    }
}
</style>
