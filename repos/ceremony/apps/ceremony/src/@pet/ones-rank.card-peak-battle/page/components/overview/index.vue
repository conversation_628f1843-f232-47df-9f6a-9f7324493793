<template>
    <div class="peak-battle-overview">
        <div
            v-for="cardItem in bufferCardList?.allCardList"
            :key="cardItem.cardId"
            class="buff-card"
        >
            <BufferCard
                :item="cardItem"
                @end-time="bufferCardStore.initBufferCard"
                @use-buff-card="bufferCardStore.initBufferCard"
            ></BufferCard>
        </div>
        <div
            class="buff-all flex align-center a-text-main a-bg-substrate text-bold"
        >
            <div v-if="hasParticipatedRaces && totalAdditionRatio">
                <span class="prefix">
                    已参与{{ hasParticipatedRaces }}场对决，获得
                </span>
                <span
                    class="buff-number a-text-highlight text-din text-regular"
                >
                    {{ totalAdditionRatioStr }}
                </span>
                <span class="suffix">加成</span>
            </div>
            <div v-else>参与挑战赢加成</div>
        </div>
        <div class="all-peak-battle">
            <Timeline v-if="allRaceList?.length > 0">
                <TimelineItem
                    v-for="item in allRaceList"
                    :key="
                        item.timelineInfo.timestamp +
                        item.timelineInfo.result +
                        item.timelineInfo.indexText
                    "
                    v-bind="item.timelineInfo"
                >
                    <template v-if="item.timelineInfo.hasParticipated">
                        <PKList
                            v-if="
                                item.inPk4Race &&
                                item.timelineInfo.itemStatus === RaceStatus.END
                            "
                            :list="item.adaptedHourlyRankListRoundView"
                        ></PKList>
                        <!-- 结束时和进行中结算时都要展示 -->
                        <PeakCard
                            v-else-if="
                                !item.inPk4Race &&
                                (item.timelineInfo.itemStatus ===
                                    RaceStatus.END ||
                                    (item.timelineInfo.itemStatus ===
                                        RaceStatus.IN_PROGRESS &&
                                        item.timelineInfo.settle))
                            "
                            :pk-info="item.adaptedPeakBattleHomeQueryApiView"
                            :show-crites="false"
                            :show-header="false"
                            class="mt-8px"
                            size="small"
                        ></PeakCard>
                    </template>
                </TimelineItem>
            </Timeline>
            <ElseStatus
                v-else
                :page-status="{ nodata: true }"
                :is-show-refresh="false"
            ></ElseStatus>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import PKList from '@pet/ones-rank.mult-pk/components/pk-list.vue';
import PeakCard from '@pet/ones-rank.card-peak-battle/components/peak-card/index.vue';
import { ElseStatus } from '@alive-ui/base';
import useBufferCardStore from '../../store/peak-buffer-card';
import usePeakBattleStore from '../../store/peak-battle';
import { RaceStatus } from '../../schemas';
import TimelineItem from './timeline-item/index.vue';
import Timeline from './timeline/index.vue';
import BufferCard from '@/modules/main/components/buffer-card/index.vue';

const peakBattleStore = usePeakBattleStore();
const bufferCardStore = useBufferCardStore();
const {
    hasParticipatedRaces,
    totalAdditionRatioStr,
    allRaceList,
    totalAdditionRatio,
} = storeToRefs(peakBattleStore);
const { bufferCardList } = storeToRefs(bufferCardStore);
</script>

<style lang="less" scoped>
.peak-battle-overview {
    width: 100%;
    padding-top: 16px;
    .buff-card {
        height: 84px;
        margin-bottom: 12px;
    }

    .buff-all {
        height: 45px;
        margin: 0 12px 12px;
        padding: 12px;
        border-radius: 8px;
    }

    .prefix,
    .buff-number,
    .suffix {
        height: 21px;
        line-height: 21px;
        display: inline-block;
    }

    .buff-number {
        font-size: 16px;
        height: 20px;
        line-height: 20px;
        text-align: left;
        margin: 0 4px;
    }

    .pk-list {
        margin-top: 8px;
    }

    :deep(.pk-list__item) {
        width: 80px;
        height: 80px;
        &:not(:first-child) {
            margin-left: 6px;
        }
    }
}
</style>
