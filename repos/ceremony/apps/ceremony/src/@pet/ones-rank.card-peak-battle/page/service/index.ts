import {
    authorId,
    activityBiz,
    request,
    liveStreamId,
    getQuery,
} from '@alive-ui/actions';
// import { StageType } from '../utils/const';
import type { RequestBody, Response } from '../schemas/index';

const PATH = {
    home: '/webapi/live/revenue/operation/activity/pkHourlyRank/panorama',
    // 加成卡
    addCard:
        '/webapi/live/revenue/operation/activity/addcard/queryAuthorAdditionCard',
};

export const queryPeakBattleHome = async () => {
    const stageType = getQuery('stageType');
    const params: RequestBody = {
        authorId: +authorId,
        stageType: +(stageType || 0),
        biz: activityBiz,
        appointTime: Date.now(),
    };
    const res = await request.post<Response>(PATH.home, params);
    return res?.data;
};

export const getBufferCard = async () => {
    const params = {
        liveStreamId,
        biz: activityBiz,
    };
    const res = await request.post<Response>(PATH.addCard, params);
    return res?.data;
};
