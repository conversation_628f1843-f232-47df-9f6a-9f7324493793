export interface RequestBody {
    biz: string;
    stageType: number;
    authorId: number;
    appointTime: number;
}
export interface Response {
    hasParticipatedRaces: number;
    totalAdditionRatio: number;
    totalAdditionRatioStr: string;
    // 是否车轮战时段
    wheelPeakBattleActive: boolean;
    onPK: boolean;
    hotRacePreview: string;
    peakBattleViews: PeakBattleView[];
    hotRaces: HotRace[];
}

export enum RaceStatus {
    NOT_START = 1,
    IN_PROGRESS = 2,
    END = 3,
}

export interface PeakBattleView {
    hasParticipated: boolean;
    raceStartTime: number;
    racePkEndTime: number; // pk结束时间，结束后开始结算，比如58分结束
    raceEndTime: number; // 当前场次PK结束时间，一般为 00 整点
    raceStatus: number; // 1未开始，2进行中，3已结束
    additionRatioDesc: string;
    inPk4Race: boolean; // 是否是4人对决
    hourlyRankListRoundView: HourlyRankListRoundView;
    wheelPeakBattleRaceView: BattleInfo;
    raceDesc: string; // 第x场
    settle: boolean; // 是否结算中
}

export interface HourlyRankListRoundView {
    rankList: Rank[];
    curAuthorInfo: CurAuthorInfo;
    privilegeDesc: string;
}

interface Rank {
    userId: number; // 参赛者ID
    userName: string; // 参赛者名称
    headUrl: string; // 头像
    liveStreamIdStr: string; // 直播ID，加密，str类型
    score: number; // 实际分数
    h5ShowScore: string; // 展示分数
    scoreDesc: string; // 分数描述（盛典值、热度值）
    rankIndex: number; // 排名
    followStatus: boolean; // 是否关注主播
    additionRatio: number; // 加成
    additionRatioStr: string; // 加成文本
}

interface CurAuthorInfo {
    userId: number;
    userName: string;
    headUrl: string; // 头像
    liveStreamIdStr: string; // 直播ID，加密，str类型
    followStatus: boolean;
    score: number; // 实际分数
    h5ShowScore: string; // 展示分数
    rankIndex: number;
}

interface HeadUrl {
    cdn: string;
    url: string;
}

interface AuthorInfo {
    eid: string;
    user_sex: string;
    headurl: string;
    headurls: HeadUrl[];
    user_name: string;
    user_id: number;
}

interface MaxScoreAudienceInfo {
    userId: number;
    userName: string;
    headUrl: string;
    liveStreamId: string | null;
    mysteryMan: boolean;
}

export interface BattleInfo {
    activityId: number;
    roundId: number;
    raceId: number;
    version: number;
    authorInfo: AuthorInfo;
    opponentInfo: AuthorInfo;
    authorTeamPartnerInfo: null;
    opponentPartnerInfo: null;
    authorMaxScoreAudienceInfo: MaxScoreAudienceInfo | null;
    opponentMaxScoreAudienceInfo: MaxScoreAudienceInfo | null;
    raceStartTime: number;
    raceEndTime: number;
    authorScore: number;
    authorScoreH5Show: string;
    opponentScore: number;
    opponentScoreH5Show: string;
    authorRedPack: number;
    opponentRedPack: number;
    redPackThreshold: number;
    raceChangeScore: number;
    raceChangeScoreH5Show: string;
    authorLiveStreamId: string;
    opponentLiveStreamId: string;
    status: number;
    raceStatus: number;
    authorUseFogCard: boolean;
    sendFogCardUserId: number;
    opponentUseFogCard: boolean;
    opponentSendFogCardUserId: number;
    matching: boolean;
    redPackAmount: number;
    privilegeDescList: string[];
    battlePrivilegeDesc: string;
    matchEndTime: number;
    pkEndTime: number;
    stolenScoreTime: boolean;
    extraScore: number;
    extraScoreH5Show: string;
    addCardTimes: number | null;
    winRaceAddCardTimes: number | null;
    personalTips: string | null;
    currrentRaceTips: string | null;
}

export interface HotRace {
    authorInfo: AuthorInfo;
    opponentInfo: AuthorInfo;
    authorScore: number;
    authorScoreH5Show: string; // 盖帽
    opponentScore: number;
    opponentScoreH5Show: string; // 盖帽
    authorRedPack: number; // 主播A对应的红包金额：分
    opponentRedPack: number; // 对手主播对应的红包金额：分
    authorMaxScoreAudienceInfo: MaxScoreAudienceInfo; // 主播的大哥
    opponentMaxScoreAudienceInfo: MaxScoreAudienceInfo; // 对手主播的大哥
    authorLiveStreamId: string;
    opponentLiveStreamId: string;
    authorTeamPartnerInfo: null;
    opponentPartnerInfo: null;
    authorUseFogCard: boolean;
    sendFogCardUserId: number;
    opponentUseFogCard: boolean;
    opponentSendFogCardUserId: number;
    scoreType: number; // 巅峰对决积分类型 0.单积分(默认)，1.多积分
    multipleScoreList: MultipleScore[]; // 石头剪刀布三种积分
    authorWinCount: number; // 主播预计胜利的猜拳次数 (基于当前比分情况)
    opponentWinCount: number; // 对手预计胜利的猜拳次数 (基于当前比分情况)
    redPackTotalFlow: string; // 总红包流量
}

interface AuthorInfo {
    following: boolean;
    profilePagePrefetchInfo: ProfilePagePrefetchInfo;
    eid: string;
    user_sex: string;
    headurl: string;
    visitorBeFollowed: boolean;
    headurls: HeadUrl[];
    user_id: number;
    user_name: string;
}

interface ProfilePagePrefetchInfo {
    profilePageType: number;
}

interface HeadUrl {
    cdn: string;
    url: string;
}

interface MaxScoreAudienceInfo {
    userId: number;
    userName: string;
    headurl: string; // 观众头像地址
}

interface MultipleScore {
    name: string; // 名称
    displayName: string; // 外显名称
    iconUrl: IconUrl[]; // icon
    authorScore: number; // 主播实际分数
    authorScoreH5Show: string; // 盖帽
    opponentScore: number; // 对手实际分数
    opponentScoreH5Show: string; // 盖帽
    scoreDiff: number; // 双方差值绝对值 (authorScore-opponentScore)
    scoreDiffH5Show: string; // 盖帽
}

interface IconUrl {
    cdn: string;
    url: string;
}
