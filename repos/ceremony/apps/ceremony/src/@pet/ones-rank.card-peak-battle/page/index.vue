<template>
    <PageAdaptor class="peak-battle">
        <PageHeader class="peak-battle-header">
            <template #leftTop>
                <!-- 返回按钮 -->
                <BackStatic v-pcDirectives:hide @click="handleBack" />
            </template>
            <template #rightTop>
                <!-- 规则按钮 -->
                <RuleIcon :rule-key="'peakBattle'" />
            </template>
            <YodaImage
                class="peak-battle-header-image"
                :src="kconf?.headKv"
                @error="(e: Event) => onYodaImageError(e, 'l1')"
            ></YodaImage>
        </PageHeader>
        <div class="peak-battle-content">
            <ATabcard v-model:active-index="activeTab">
                <ATabcardItem
                    v-click-log="{
                        action: 'OP_ACTIVITY_TOP_TAB',
                        params: {
                            tab_name: kconf?.overview?.tabTitle,
                        },
                    }"
                >
                    {{ kconf?.overview?.tabTitle }}
                    <template #extra>
                        <ATabcardIcon :src="kconf?.overview?.tabIcon" />
                    </template>
                </ATabcardItem>
                <ATabcardItem
                    v-click-log="{
                        action: 'OP_ACTIVITY_TOP_TAB',
                        params: {
                            tab_name: kconf?.wonderful?.tabTitle,
                        },
                    }"
                >
                    {{ kconf?.wonderful?.tabTitle }}
                    <template #extra>
                        <ATabcardIcon :src="kconf?.wonderful?.tabIcon" />
                    </template>
                </ATabcardItem>
            </ATabcard>
            <div class="peak-battle-content-inner a-bg-part">
                <template v-if="status.success">
                    <AsyncOverview v-show="activeTab === TabType.OVERVIEW" />
                    <AsyncWonderful v-show="activeTab === TabType.WONDERFUL" />
                </template>
                <ElseStatus
                    v-else
                    :page-status="status"
                    :is-show-refresh="status.error"
                    @refresh="homeStore.initData"
                />
            </div>
        </div>
    </PageAdaptor>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { defineAsyncComponent, ref } from 'vue';
import { storeToRefs } from 'pinia';
import YodaImage from '@pet/yoda.image/img.vue';
import RuleIcon from '@pet/ones-ui.rule-icon/index.vue';
import { parseQuery } from '@alive-ui/system';
import { BackStatic } from '@alive-ui/icon';
import {
    PageAdaptor,
    Tabcard,
    AKvTextIcon,
    PageHeader,
    ElseStatus,
} from '@alive-ui/base';
import { Report } from '@alive-ui/actions';
import useBufferCardStore from './store/peak-buffer-card';
import useHomeStore from './store/peak-battle';
import { TabType } from './schemas/common';

const {
    Tabcard: ATabcard,
    TabcardIcon: ATabcardIcon,
    TabcardItem: ATabcardItem,
} = Tabcard;
const homeStore = useHomeStore();
const bufferCardStore = useBufferCardStore();
const { kconf, status } = storeToRefs(homeStore);
// action 中的query执行一次后会永久缓存
const query = parseQuery();
const defaultType = query.peakType ? Number(query.peakType) : TabType.OVERVIEW;
// 当前 tab
const activeTab = ref<number | TabType>(defaultType);
const router = useRouter();
const AsyncWonderful = defineAsyncComponent(
    () =>
        import(
            /* webpackChunkName:"peak-battle-wonderful" */ './components/wonderful/index.vue'
        ),
);
const AsyncOverview = defineAsyncComponent(
    () =>
        import(
            /* webpackChunkName:"peak-battle-overview" */ './components/overview/index.vue'
        ),
);

const onYodaImageError = (e: Event, level: string) => {
    Report.biz.error('kv区yoda-image异常', {
        error: e,
        level,
    });
};

const handleBack = () => {
    router.back();
};

homeStore.initData();
bufferCardStore.initBufferCard();
</script>

<style lang="less" scoped>
.peak-battle {
    &-header {
        height: 250px;
        --y-img-height: 250px;
        --y-img-width: 414px;
    }
    &-content {
        margin: -40px 16px 0;
        overflow: hidden;
        &-inner {
            min-height: 228px;
            border-radius: 0 0 16px 16px;
            overflow: hidden;
            position: relative;
            margin-bottom: 32px;
        }
    }

    :deep(.rule-icon) {
        width: 32px;
        height: 32px;
    }
}
</style>
