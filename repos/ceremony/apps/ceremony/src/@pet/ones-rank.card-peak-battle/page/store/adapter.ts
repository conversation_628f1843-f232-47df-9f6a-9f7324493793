import { WIN_LOSS_STATUS } from '@pet/ones-rank.card-peak-battle/components/peak-card/constants';
import {
    type PeakBattleView,
    type HourlyRankListRoundView,
    type BattleInfo,
    RaceStatus,
} from '../schemas';
import type { ComputedRef, Ref } from 'vue';
import type { IPkItem } from '@pet/ones-rank.mult-pk/components/interface';
import type {
    TimeLineItemInfo,
    TimelineItemData,
} from '../components/overview/timeline-item/schema';

export type Peak = {
    headKv: string;
    headKvRule: string;
    headKvRuleUrl: string;
    overview: {
        tabTitle: string;
        tabIcon: string;
        notStartText4p: string;
        notStartText2p: string;
        inProgressText: string;
        notParticipatedText: string;
    };
    wonderful: {
        tabTitle: string;
        tabIcon: string;
        tipStart: string;
        tipActive: string;
        tipEnd: string;
        elseStatus: {
            title: string;
            text: string;
        };
    };
};
// 四人对决接口数据转组件数据
function hourlyRankListRoundViewAdapter(
    hourlyRankListRoundView: HourlyRankListRoundView,
) {
    const pkList: IPkItem[] = []; // 组件数据
    const { rankList, curAuthorInfo } = hourlyRankListRoundView;
    for (const rank of rankList) {
        const pkItem: IPkItem = {
            userInfo: {
                userId: rank.userId,
                userName: rank.userName,
                headUrl: rank.headUrl,
            },
            isCurrAuthor: rank.userId === curAuthorInfo.userId,
            liveStreamId: rank.liveStreamIdStr,
            scoreH5Show: `${rank.h5ShowScore || '0'}${rank.scoreDesc}`,
            additionRatio: rank.additionRatio ? rank.additionRatioStr : '',
            additionDesc: rank.additionRatio ? '加成' : '无加成',
        };
        pkList.push(pkItem);
    }
    return pkList;
}

// 二人对决接口数据转组件数据
function peakBattleHomeQueryApiViewAdapter(
    peakBattleHomeQueryApiView: BattleInfo,
    settle: boolean,
) {
    // 进行中且到结算时间了，展示结果态
    const computeStatus =
        peakBattleHomeQueryApiView.raceStatus === RaceStatus.IN_PROGRESS &&
        settle
            ? RaceStatus.END
            : peakBattleHomeQueryApiView.raceStatus;
    return {
        ...peakBattleHomeQueryApiView,
        raceStatus: computeStatus,
    };
}

// 四人对决场次后展示的标题，eg：第x名
function getHourlyRankListTitle(
    hourlyRankListRoundView: HourlyRankListRoundView,
) {
    const { rankIndex } = hourlyRankListRoundView.curAuthorInfo;
    if (!rankIndex) {
        return '';
    }
    return `第${rankIndex}名`;
}

// 二人对决场次后展示的标题，eg：胜利，失败
function getPeakBattleTitle(battleInfo: BattleInfo) {
    switch (battleInfo.status) {
        case WIN_LOSS_STATUS.TIE:
            return '平局';
        case WIN_LOSS_STATUS.WIN:
            return '胜利';
        case WIN_LOSS_STATUS.LOSS:
        case WIN_LOSS_STATUS.ALL_LOSS:
            return '失败';
    }
    return '';
}

// 时间展示逻辑
function getTimestamp(peakBattleView: PeakBattleView, status: RaceStatus) {
    // 未开始状态展示开始时间
    if (status === RaceStatus.NOT_START) {
        return peakBattleView.raceStartTime;
    }
    // 进行中状态展示结算时间
    if (status === RaceStatus.IN_PROGRESS) {
        return peakBattleView.racePkEndTime;
    }
    // 已结束状态&兜底展示结束时间
    return peakBattleView.raceEndTime;
}

function getTitle(
    peakBattleView: PeakBattleView,
    inPk4Race: boolean,
    peakKconf: ComputedRef<Peak>,
) {
    if (peakBattleView.raceStatus === RaceStatus.IN_PROGRESS) {
        // 结算时也要展示结果
        if (peakBattleView.settle) {
            return getPeakBattleTitle(peakBattleView.wheelPeakBattleRaceView);
        }
        return '进行中';
    }
    if (peakBattleView.raceStatus === RaceStatus.NOT_START) {
        return inPk4Race
            ? peakKconf.value?.overview?.notStartText4p || '巅峰大乱斗'
            : peakKconf.value?.overview?.notStartText2p || '巅峰对决';
    }
    if (!peakBattleView.hasParticipated) {
        return '未参与';
    }
    if (inPk4Race) {
        return getHourlyRankListTitle(peakBattleView.hourlyRankListRoundView);
    }
    return getPeakBattleTitle(peakBattleView.wheelPeakBattleRaceView);
}

// 接口数据转组件数据
export function peakBattleViewsAdapter(
    allRaceList: Ref<PeakBattleView[] | undefined>,
    peakKconf: ComputedRef<Peak>,
) {
    const timelineList: TimelineItemData[] = [];
    if (!allRaceList.value) {
        return timelineList;
    }
    for (let i = 0; i < allRaceList.value.length; i++) {
        const timelineItem: TimelineItemData = {
            inPk4Race: false,
            timelineInfo: {
                indexText: '',
                timestamp: 0,
                title: '',
                result: '',
                itemStatus: RaceStatus.NOT_START,
                isLastItem: false,
                hasParticipated: false,
                settle: false,
            },
            adaptedHourlyRankListRoundView: [],
            adaptedPeakBattleHomeQueryApiView: [],
        };
        const peakBattleView = allRaceList.value[i];

        // 4人pk or 2人pk
        const inPk4Race = !!peakBattleView?.inPk4Race;

        // 4人pk数据
        const adaptedHourlyRankListRoundView = inPk4Race
            ? hourlyRankListRoundViewAdapter(
                  peakBattleView.hourlyRankListRoundView,
              )
            : [];

        // 2人pk数据
        const adaptedPeakBattleHomeQueryApiView = inPk4Race
            ? []
            : peakBattleHomeQueryApiViewAdapter(
                  peakBattleView.wheelPeakBattleRaceView,
                  peakBattleView.settle,
              );

        // 时间线展示信息
        const timelineInfo: TimeLineItemInfo = {
            indexText: peakBattleView.raceDesc,
            timestamp: getTimestamp(peakBattleView, peakBattleView.raceStatus),
            title: getTitle(peakBattleView, inPk4Race, peakKconf),
            result: peakBattleView.additionRatioDesc,
            itemStatus: peakBattleView.raceStatus,
            isLastItem: i === allRaceList.value.length - 1,
            hasParticipated: peakBattleView.hasParticipated,
            settle: peakBattleView.settle,
        };

        timelineItem.inPk4Race = inPk4Race;
        timelineItem.adaptedHourlyRankListRoundView =
            adaptedHourlyRankListRoundView;
        timelineItem.adaptedPeakBattleHomeQueryApiView =
            adaptedPeakBattleHomeQueryApiView;
        timelineItem.timelineInfo = timelineInfo;

        timelineList.push(timelineItem);
    }
    return timelineList;
}
