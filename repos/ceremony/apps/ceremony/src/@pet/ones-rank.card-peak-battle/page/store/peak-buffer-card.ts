import { ref } from 'vue';
import { defineStore } from 'pinia';
import { Report } from '@alive-ui/actions';
import { getBufferCard } from '../service/index';

export default defineStore('peak-buffer-card-yihang', () => {
    const bufferCardList = ref<any>([]);
    async function initBufferCard() {
        try {
            const res = await getBufferCard();
            bufferCardList.value = res || [];
        } catch (error) {
            Report.biz.error('加成卡初始化失败', {
                error,
            });
            console.log(error);
        }
    }

    return {
        initBufferCard,
        bufferCardList,
    };
});
