/* eslint-disable max-lines-per-function */
import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import useKconfBatch from '@pet/ones-use.useKconfBatch/index';
import { sendFmp } from '@gundam/weblogger';
import { getPageStatus, Report } from '@alive-ui/actions';
import { queryPeakBattleHome } from '../service/index';
import {
    type HotRace,
    type PeakBattleView,
    type Response,
} from '../schemas/index';
import { PeakType } from '../schemas/common';
import { peakBattleViewsAdapter } from './adapter';
import awaitWrap from '@/utils/awaitWrap';
export default defineStore('peak-battle', () => {
    // 页面整体状态
    const status = ref(getPageStatus('init'));
    // 参加场次
    const hasParticipatedRaces = ref<number>(0);
    // 总加成
    const totalAdditionRatioStr = ref<string>('');
    const totalAdditionRatio = ref<number>(0);
    // 精彩对决
    const hotRaceList = ref<HotRace[]>([]);
    // 当前对决类型
    const currentPeakType = ref<PeakType>(PeakType.MULTIPLE);
    // 是否双人对决
    const isDoubleModel = computed(() => {
        return currentPeakType.value === PeakType.DOUBLE;
    });
    // 是否PK中
    const isOnPK = ref<boolean>(false);
    // 二人PK预告文案
    const doublePKText = ref<string>('');
    const kconfStore = useKconfBatch();

    const kconf = computed(() => {
        return kconfStore.kconfData?.peakBattle?.peak;
    });
    const peakBattleViews = ref<PeakBattleView[]>();
    // 对决场次
    const allRaceList = computed(() => {
        return peakBattleViewsAdapter(peakBattleViews, kconf);
    });
    const pageStatus = computed(() => {
        if (kconfStore.pageStatus.success) {
            return status.value;
        }

        return kconfStore.pageStatus;
    });

    // 对决数据初始化
    const initHomeData = async () => {
        const [err, res] = await awaitWrap<Response>(queryPeakBattleHome());

        const setData = () => {
            peakBattleViews.value = res?.peakBattleViews || [];
            hasParticipatedRaces.value = res?.hasParticipatedRaces || 0;
            totalAdditionRatioStr.value = res?.totalAdditionRatioStr || '';
            totalAdditionRatio.value = res?.totalAdditionRatio || 0;
            hotRaceList.value = res?.hotRaces || [];
            currentPeakType.value = res?.wheelPeakBattleActive
                ? PeakType.DOUBLE
                : PeakType.MULTIPLE;
            isOnPK.value = !!res?.onPK;
            doublePKText.value = res?.hotRacePreview || '';
        };

        return [err, res, setData];
    };

    async function initData() {
        status.value = getPageStatus('loading');
        const [err, res, setData] = await initHomeData();
        try {
            sendFmp();
        } catch {
            console.log('sendFmp 出错');
        }

        if (err || !res) {
            status.value = getPageStatus('error');
            return;
        }

        try {
            setData();
            status.value = getPageStatus('success');
        } catch (error: any) {
            Report.biz.error('巅峰对决初始化失败', {
                error: {
                    message: error?.message,
                    stack: error?.stack,
                },
            });
        }
    }

    return {
        initData,
        kconf,
        status: pageStatus,
        isOnPK,
        doublePKText,
        currentPeakType,
        hasParticipatedRaces,
        totalAdditionRatioStr,
        totalAdditionRatio,
        allRaceList,
        hotRaceList,
        isDoubleModel,
    };
});
