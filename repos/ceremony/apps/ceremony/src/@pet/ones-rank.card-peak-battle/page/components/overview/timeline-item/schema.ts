import type { IPkItem } from '@pet/ones-rank.mult-pk/components/interface';
import type { RaceStatus } from '../../../schemas/index';

// 时间轴信息
export type TimeLineItemInfo = {
    indexText: string; // 第x场
    timestamp: number; // 时间戳
    title: string; // 标题
    result: string; // 结果
    itemStatus: RaceStatus; // 状态
    isLastItem: boolean; // 是否是最后一个item
    hasParticipated: boolean; // 是否参与
    settle: boolean; // 是否结算中
};

// 时间轴数据
export type TimelineItemData = {
    inPk4Race: boolean; // 是否为4人对决
    timelineInfo: TimeLineItemInfo; // 时间轴信息
    adaptedHourlyRankListRoundView: IPkItem[]; // 4人对决数据
    adaptedPeakBattleHomeQueryApiView: any; // TODO: 2人对决数据
};
