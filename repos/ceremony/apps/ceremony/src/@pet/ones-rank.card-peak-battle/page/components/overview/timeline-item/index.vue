<template>
    <div class="timeline-item-container flex">
        <div
            class="timeline-item-border"
            :class="{ o3: styleCtrl.isIconAndTailLowerOpacity }"
        >
            <div
                class="timeline-item-tail"
                :class="{
                    'hidden-tail': isLastItem,
                }"
                :style="{ borderLeft: '1px dashed #ffdfbf' }"
            ></div>
            <div
                class="timeline-item-icon"
                :class="{
                    'active-icon': itemStatus === RaceStatus.IN_PROGRESS,
                }"
            ></div>
        </div>
        <div class="timeline-item-content">
            <div class="flex justify-between a-text-main">
                <div class="flex flex-col">
                    <div
                        class="timeline-item-title text-bold"
                        :class="{
                            'a-text-main-o2': styleCtrl.isTitleLowerOpacity,
                            'a-text-highlight': styleCtrl.isTitleHighlight,
                        }"
                    >
                        {{ indexText }} {{ title }}
                    </div>
                    <div
                        class="timeline-item-time a-text-main-o2"
                        :class="{
                            'a-text-main-o3': styleCtrl.isTitleLowerOpacity,
                            'a-text-highlight': styleCtrl.isTitleHighlight,
                        }"
                    >
                        {{ timeText }} {{ styleCtrl.text }}
                    </div>
                </div>
                <div class="flex-center">
                    <div
                        class="timeline-item-result"
                        :class="{
                            'a-text-highlight': styleCtrl.isResultHighlight,
                        }"
                    >
                        {{ result }}
                    </div>
                </div>
            </div>
            <div class="slot"><slot></slot></div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { timestampToText } from '../utils';
import { RaceStatus } from '../../../schemas/index';
import type { TimeLineItemInfo } from './schema';

const props = defineProps<TimeLineItemInfo>();

const styleCtrlMap = {
    [RaceStatus.NOT_START]: {
        text: '开始', // 展示在时间后的状态文案
        isTitleHighlight: false, // 控制左侧标题颜色
        isResultHighlight: false, // 控制右侧结果颜色
        isTitleLowerOpacity: false, // 控制左侧标题透明度
        isIconAndTailLowerOpacity: false, // 控制左侧icon和tail透明度
    },
    [RaceStatus.IN_PROGRESS]: {
        text: '结算',
        isTitleHighlight: true,
        isResultHighlight: false,
        isTitleLowerOpacity: false,
        isIconAndTailLowerOpacity: false,
    },
    // PK结束结算中
    settle: {
        text: '已结束',
        isTitleHighlight: true,
        isResultHighlight: false,
        isTitleLowerOpacity: false,
        isIconAndTailLowerOpacity: false,
    },
    [RaceStatus.END]: {
        text: '已结束',
        isTitleHighlight: false,
        isResultHighlight: true,
        isTitleLowerOpacity: true,
        isIconAndTailLowerOpacity: true,
    },
    default: {
        text: '已结束',
        isTitleHighlight: false,
        isResultHighlight: true,
        isTitleLowerOpacity: true,
        isIconAndTailLowerOpacity: true,
    },
};
const timeText = computed(() => {
    return timestampToText(props.timestamp);
});
const styleCtrl = computed(() => {
    if (props.settle && props.itemStatus === RaceStatus.IN_PROGRESS) {
        return styleCtrlMap.settle;
    }
    return styleCtrlMap[props.itemStatus] || styleCtrlMap.default;
});
</script>

<style lang="less" scoped>
.timeline-item-container {
    position: relative;
    width: 382px;
    padding-right: 12px;
    padding-left: 8px;
    .timeline-item-border {
        position: relative;
        left: 0;
        width: 20px;
    }
    .timeline-item-tail {
        position: absolute;
        height: calc(100% - 14px);
        top: 18px;
        left: 8px;
        margin-right: 4px;
    }
    .timeline-item-icon {
        position: absolute;
        top: 5px;
        left: 2.5px;
        width: 12px;
        height: 12px;
        background: url(./assets/icon.svg) center / 100% no-repeat;
    }
    .active-icon {
        background: url(./assets/icon-active.svg) center / 100% no-repeat;
    }
    .timeline-item-content {
        position: relative;
        left: 4px;
        width: 338px;
        margin-bottom: 24px;
    }

    .timeline-item-title {
        position: relative;
        font-size: 14px;
        line-height: 21px;
        text-align: left;
        margin-bottom: 4px;
    }

    .timeline-item-time {
        position: relative;
        font-size: 12px;
        line-height: 18px;
        text-align: left;
    }

    .timeline-item-result {
        font-size: 12px;
    }

    .o3 {
        opacity: 0.3;
    }

    .hidden-tail {
        display: none;
    }
}
</style>
