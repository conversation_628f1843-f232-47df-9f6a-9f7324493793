<template>
    <div
        class="peak-battle-card"
        :class="{
            [cardType]: true,
            'crits-status': isInCrites && showCrites,
            'peak-battle-card-center':
                !showHeader || !(headerText || isInCrites),
        }"
    >
        <div
            v-if="(headerText || isInCrites) && showHeader"
            class="peak-battle-card-header"
            :class="{
                'crits-status': isInCrites || useActiveTip,
                'peak-top-count': useActiveTip,
            }"
        >
            {{ headerContent }}
            <template v-if="isInCrites && showCrites">
                <span v-if="countDownStart" class="mr-2px">{{
                    countDownStart
                }}</span>
                <Countdown
                    class="count-down-time"
                    immediate-emit
                    :to="countDownTime"
                    :transform="countdownFn"
                    @end="onCountDownEnd"
                ></Countdown>
                <span v-if="countDownEnd">{{ countDownEnd }}</span>
            </template>
        </div>
        <div class="peak-battle-card-content">
            <div class="peak-battle-card-content-author">
                <Avatar
                    :key="pkInfo.authorInfo.user_id"
                    v-click-log="{
                        action: 'OP_ACTIVITY_MORE_PLAY_CARD',
                        params: {
                            type: '四人巅峰对决',
                            btn_type: 'AUTHOR_HEAD',
                            author_id: pkInfo.authorInfo.user_id,
                        },
                    }"
                    :head-url="pkInfo.authorInfo.headurl"
                    :user-id="pkInfo.authorInfo.user_id"
                    :live-stream-id="authorLiveStreamId"
                    live-corner-class=""
                    size="xs"
                />
                <div
                    v-if="isRaceEnd"
                    :class="`peak-battle-card-icon ${getTagClass(true)}`"
                />
            </div>
            <div class="peak-battle-card-content-center">
                <div class="peak-battle-card-content-center-author-name">
                    {{ nameSlice(pkInfo.authorInfo.user_name, 5) }}
                </div>
                <div class="peak-battle-card-content-center-opponent-name">
                    {{ nameSlice(pkInfo.opponentInfo.user_name, 5) }}
                </div>
                <div class="peak-battle-card-content-center-pk-bar">
                    <div
                        class="peak-battle-card-content-center-pk-bar-left"
                        :style="{
                            'flex-grow': growData.growLeft,
                        }"
                    >
                        {{ pkInfo.authorScoreH5Show }}
                    </div>
                    <div
                        class="peak-battle-card-content-center-pk-bar-right"
                        :style="{
                            'flex-grow': growData.growRight,
                        }"
                    >
                        {{ pkInfo.opponentScoreH5Show }}
                    </div>
                    <div
                        class="peak-battle-card-content-center-pk-bar-mask"
                    ></div>
                </div>
            </div>
            <div class="peak-battle-card-content-opponent">
                <Avatar
                    :head-url="pkInfo.opponentInfo.headurl"
                    :user-id="pkInfo.opponentInfo.user_id"
                    :live-stream-id="opponentLiveStreamId"
                    live-corner-class=""
                    size="xs"
                />
                <div
                    v-if="isRaceEnd"
                    :class="`peak-battle-card-icon ${getTagClass(false)}`"
                />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import Countdown from '@pet/ones-ui.countdown/index.vue';
import { Avatar } from '@alive-ui/pro';
import { nameSlice } from '@alive-ui/actions';
import { useCrites } from './hooks/use-crites';
import { WIN_LOSS_STATUS } from './constants/index';

const props = defineProps({
    pkInfo: {
        type: Object,
        default: () => ({}),
    },
    size: {
        type: String,
        default: 'normal',
    },
    showHeader: {
        type: Boolean,
        default: true,
    },
    useActiveTip: {
        type: Boolean,
        default: false,
    },
    headerText: {
        type: String,
        default: '',
    },
    showCrites: {
        type: Boolean,
        default: true,
    },
});
const emits = defineEmits(['onCountDownEnd', 'onPkEnd']);

const {
    isInCrites,
    isRaceEnd,
    countDownTime,
    countDownStart,
    countDownEnd,
    countdownFn,
    onCountDownEnd,
} = useCrites({
    props,
    emits,
});

const cardType = computed(() => {
    return `${props.size}-card`;
});

const headerContent = computed(() => {
    if (isInCrites.value && props.showCrites) {
        return '';
    }
    return props.headerText;
});

const getTagClass = (isAuthor: boolean) => {
    const winClass = isAuthor
        ? 'peak-battle-card-icon-winner'
        : 'peak-battle-card-icon-loser';
    const loseClass = isAuthor
        ? 'peak-battle-card-icon-loser'
        : 'peak-battle-card-icon-winner';
    const drawClass = 'peak-battle-card-icon-draw';
    const allLoseClass = 'peak-battle-card-icon-loser';
    switch (props.pkInfo?.status) {
        case WIN_LOSS_STATUS.WIN:
            return winClass;
        case WIN_LOSS_STATUS.LOSS:
            return loseClass;
        case WIN_LOSS_STATUS.TIE:
            return drawClass;
        case WIN_LOSS_STATUS.ALL_LOSS:
            return allLoseClass;
        default:
            return '';
    }
};

const getStreamId = (id: number) => {
    const isShowLive = !isRaceEnd.value;
    if (isShowLive) {
        return id;
    }
    return '';
};
const opponentLiveStreamId = computed(() => {
    return getStreamId(props.pkInfo?.opponentLiveStreamId);
});

const authorLiveStreamId = computed(() => {
    return getStreamId(props.pkInfo?.authorLiveStreamId);
});

// 25夏季因为包含减分卡 最新逻辑如下：
// a、b 为正 与正数处理一样
// a为正、b<=0，b是负数时都当0处理
// a、b为负，与正数处理一样（但相反）
// a0，b为负，与正数处理一样（但相反）
const growData = computed(() => {
    let growLeft = props?.pkInfo?.authorScore;
    let growRight = props?.pkInfo?.opponentScore;

    // if (
    //     (growLeft >= 0 && growRight >= 0) ||
    //     (growLeft > 0 && growRight <= 0) ||
    //     (growLeft <= 0 && growRight > 0)
    // ) {
    //     growLeft = Math.max(growLeft, 1);
    //     growRight = Math.max(growRight, 1);
    // } else if (
    //     (growLeft < 0 && growRight < 0) ||
    //     (growLeft === 0 && growRight < 0) ||
    //     (growLeft < 0 && growRight === 0)
    // ) {
    //     [growLeft, growRight] = [
    //         Math.max(Math.abs(growRight), 1),
    //         Math.max(Math.abs(growLeft), 1),
    //     ];
    // }

    if (growLeft >= 0 && growRight >= 0) {
        growLeft = Math.max(growLeft, 1);
        growRight = Math.max(growRight, 1);
    }

    if ((growLeft > 0 && growRight <= 0) || (growLeft <= 0 && growRight > 0)) {
        growLeft = Math.max(growLeft, 1);
        growRight = Math.max(growRight, 1);
    }

    if (growLeft < 0 && growRight < 0) {
        [growLeft, growRight] = [
            Math.max(Math.abs(growRight), 1),
            Math.max(Math.abs(growLeft), 1),
        ];
    }

    if (
        (growLeft === 0 && growRight < 0) ||
        (growLeft < 0 && growRight === 0)
    ) {
        [growLeft, growRight] = [
            Math.max(Math.abs(growRight), 1),
            Math.max(Math.abs(growLeft), 1),
        ];
    }

    return {
        growLeft,
        growRight,
    };
});
</script>

<style lang="less" scoped>
.peak-battle-card {
    width: 100%;
    border-radius: 8px;
    position: relative;
    display: flex;
    align-items: center;
    flex-direction: column;
    @apply a-bg-substrate;
    padding: 0 12px;
    &.normal-card {
        height: 82px;
    }
    &.small-card {
        height: 80px;
    }
    &.big-card {
        height: 84px;
        .peak-battle-card-header {
            margin-bottom: 10px;
        }
    }
    &.peak-battle-card-center {
        justify-content: center;
    }
    &.crits-status {
        background-color: rgba(255, 212, 0, 0.1);
    }
    &-header {
        padding: 3px 7px;
        margin-bottom: 8px;
        background-size: 100% 100%;
        background-image: url('./assets/header-bar.png');
        background-repeat: no-repeat;
        font-size: 10px;
        @apply a-text-main;
        height: 20px;
        line-height: 1;
        display: flex;
        align-items: center;
        &.crits-status {
            display: flex;
            align-items: center;
            color: rgba(255, 212, 0, 1);
            padding: 3px 13.5px 3px 14.5px;
            background-image: url('./assets/header-bar-crits.png');
            .count-down-time {
                font-size: 12px;
                @apply text-bold;
            }
        }
        &.peak-top-count {
            width: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            text-align: center;
            background-image: url('./assets/header-bar-top.png');
        }
    }
    &-content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        &-author {
            width: 36px;
            height: 36px;
            position: relative;
        }
        .peak-battle-card-icon {
            position: absolute;
            top: 26px;
            left: 0;
            right: 0;
            margin: auto;
            width: 32px;
            height: 16px;
            background-size: cover;
            &-winner {
                background-image: url('./assets/win-icon.png');
            }
            &-loser {
                background-image: url('./assets/fail-icon.png');
            }
            &-draw {
                background-image: url('./assets/draw-icon.png');
            }
        }
        &-opponent {
            width: 36px;
            height: 36px;
            position: relative;
        }
        &-center {
            flex: 1;
            display: flex;
            justify-content: space-between;
            margin: 0 8px;
            height: 38px;
            position: relative;
            @apply a-text-main;
            font-size: 12px;
            &-author-name {
                height: 16px;
                display: flex;
                align-items: center;
            }
            &-opponent-name {
                height: 16px;
                display: flex;
                align-items: center;
            }
            &-pk-bar {
                position: absolute;
                bottom: 0;
                width: 100%;
                height: 14px;
                display: flex;
                border-radius: 8px;
                overflow: hidden;
                color: #ffffff;
                &-left {
                    position: relative;
                    z-index: 1;
                    height: 100%;
                    background: #ffab38;
                    padding-left: 10px;
                    display: flex;
                    align-items: center;
                }
                &-right {
                    position: relative;
                    z-index: 1;
                    height: 100%;
                    background: #1e93ff;
                    padding-right: 10px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                }
                &-mask {
                    position: absolute;
                    z-index: 2;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    background-image: url('./assets/pk-bar-bg.png');
                    background-size: cover;
                }
            }
        }
    }
}
</style>
