import {
    UNDERWAY_STATUS,
    CRITICAL_HIT_GROUP,
    LIVE_STREAM_ID,
} from '../constants/index';
export const getChDetail = (
    nowTime: number,
    pkInfo: any,
    fpretellTime = 2000 * 1000,
) => {
    const {
        baoJiTime = [],
        raceEndTime,
        raceStartTime,
        matchEndTime,
        pkEndTime,
    } = pkInfo;
    let criticalHitType = UNDERWAY_STATUS.PK_NO_START;
    let countDownTime = 0;
    let addCardTimes = '';

    for (const element of baoJiTime) {
        const { startTime, endTime, baoJiCoefficient } = element;
        const offetTime = startTime - nowTime;
        if (startTime <= nowTime && endTime > nowTime) {
            criticalHitType = UNDERWAY_STATUS.CH_ING;
            countDownTime = endTime;
            addCardTimes = baoJiCoefficient;
            break;
        } else if (offetTime > 0 && offetTime <= fpretellTime) {
            criticalHitType = UNDERWAY_STATUS.CH_FPRETELL;
            countDownTime = startTime;
            break;
        }
    }
    if (!CRITICAL_HIT_GROUP.includes(criticalHitType)) {
        if (raceStartTime <= nowTime && nowTime < matchEndTime) {
            criticalHitType = UNDERWAY_STATUS.PK_NO_START;
        } else if (nowTime >= pkEndTime && nowTime < raceEndTime) {
            criticalHitType = UNDERWAY_STATUS.SETTL_ING;
        } else {
            criticalHitType = UNDERWAY_STATUS.PK_ING;
            countDownTime = pkEndTime;
        }
    }

    return {
        hitType: criticalHitType,
        time: countDownTime,
        addCardTimes,
    };
};

export const getMockData = (nowTime: number, liveStreamId: string | number) => {
    const raceTimes = {
        raceStartTime: nowTime - 1000 * 60 * 10,
        matchEndTime: nowTime - 1000 * 60 * 9,
        pkEndTime: nowTime + 1000 * 72,
        raceEndTime: nowTime + 1000 * 60 * 20,
    };
    const mock = {
        // pk中倒计时
        [LIVE_STREAM_ID.PK_ING]: {
            ...raceTimes,
            baoJiTime: [
                {
                    startTime: nowTime + 1000 * 19,
                    endTime: nowTime + 1000 * 29,
                },
                {
                    startTime: nowTime + 1000 * 41,
                    endTime: nowTime + 1000 * 52,
                },
            ],
        },
        // 暴击时刻预告
        [LIVE_STREAM_ID.CH_FPRETELL]: {
            ...raceTimes,
            baoJiTime: [
                {
                    startTime: nowTime + 1000 * 19,
                    endTime: nowTime + 1000 * 29,
                },
                {
                    startTime: nowTime + 1000 * 41,
                    endTime: nowTime + 1000 * 52,
                },
            ],
        },
        // 暴击时刻中
        [LIVE_STREAM_ID.CH_ING]: {
            ...raceTimes,
            baoJiTime: [
                {
                    startTime: nowTime - 1000 * 60 * 1,
                    endTime: nowTime + 1000 * 60 * 2,
                },
                {
                    startTime: nowTime + 1000 * 60 * 7,
                    endTime: nowTime + 1000 * 60 * 8,
                },
            ],
        },
    };
    return mock[liveStreamId as keyof typeof mock];
};
