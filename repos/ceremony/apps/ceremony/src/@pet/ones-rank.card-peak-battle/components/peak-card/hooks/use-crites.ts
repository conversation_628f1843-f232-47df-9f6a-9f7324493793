import { ref, computed, watch } from 'vue';
import { isYodaPCContainer, useCountdownTo } from '@alive-ui/system';
import { getNowTime } from '../utils/time';
import { getChDetail, getMockData } from '../utils/get-ch-detail';
import {
    UNDERWAY_STATUS,
    CRITICAL_HIT_GROUP,
    STATUS,
} from '../constants/index';

export const useCrites = ({ props, emits }: any) => {
    // const mockData = getMockData(Date.now(), LIVE_STREAM_ID.CH_ING)
    const criticalHitType = ref(UNDERWAY_STATUS.CH_FPRETELL);
    const countDownTime = ref(0);
    const countDownStart = ref('');
    const countDownEnd = ref('');
    const addCardTimes = ref('');
    const isCrites = computed(() => {
        return CRITICAL_HIT_GROUP.includes(criticalHitType.value);
    });
    const isInCrites = computed(() => {
        return criticalHitType.value === UNDERWAY_STATUS.CH_ING;
    });

    const setTypeAndCountDownTime = async (props: any) => {
        const nowTime = await getNowTime();
        const {
            hitType,
            time,
            addCardTimes: times,
        } = getChDetail(nowTime, props.pkInfo);
        // mock 数据
        // const { type, time } = getChDetail(nowTime, mockData);
        criticalHitType.value = hitType;
        countDownTime.value = time;
        // 当前加成倍数
        addCardTimes.value = times;
    };

    const isRaceEnd = computed(() => {
        setTypeAndCountDownTime(props);
        return props.pkInfo.raceStatus === STATUS.FINISHED;
    });

    const onCountDownEnd = () => {
        setTypeAndCountDownTime(props);

        emits('onCountDownEnd');
    };

    const countdownFn = (val: number) => {
        // 毫秒转为秒
        const s = Math.floor(val / 1000);
        onCountdownChange(val);
        const time = `${s}s`;
        return time;
    };

    const countDownFnForChangeStatus = () => {
        let contentStart = '';
        const contentEnd = '';
        if (criticalHitType.value === UNDERWAY_STATUS.CH_ING) {
            const times = addCardTimes.value ? `${addCardTimes.value}倍` : '';
            contentStart = `PK${times}暴击时刻 `;
        }
        countDownStart.value = contentStart;
        countDownEnd.value = contentEnd;
        return '';
    };
    const onCountdownChange = (nowTime: any) => {
        // mock 数据
        // const { type } = getChDetail(countDownTime.value - nowTime, mockData);

        // 实时判断是否进入暴击时刻
        const { hitType } = getChDetail(
            countDownTime.value - nowTime,
            props.pkInfo,
        );

        if (hitType !== criticalHitType.value) {
            onCountDownEnd();
        }
    };

    const { reset } = useCountdownTo(countDownTime.value, {
        useServerTime: !isYodaPCContainer,
        transformFn: countDownFnForChangeStatus,
        immediateEmit: true,
        onEnd: () => {
            onCountDownEnd();
        },
    });

    watch(countDownTime, (t) => {
        reset(t);
    });

    return {
        isCrites,
        isInCrites,
        isRaceEnd,
        addCardTimes,
        countDownTime,
        countDownStart,
        countDownEnd,
        criticalHitType,
        onCountDownEnd,
        countdownFn,
    };
};
