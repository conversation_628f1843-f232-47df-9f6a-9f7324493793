/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> liu<PERSON>@kuaishou.com
 * @Date: 2024-01-19 00:12:11
 * @LastEditors: l<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-02-05 16:56:08
 */

// 对决结果状态
export enum WIN_LOSS_STATUS {
    NOT_START = 0,
    // 胜利
    WIN = 1,
    // 失败
    LOSS = 2,
    // 平局
    TIE = 3,
    // 未参赛
    NOT_PARTICIPATE = 4,
    // 都是失败，双方都是0分
    ALL_LOSS = 5,
}

// 对决状态
export enum STATUS {
    // 未知
    UNKNOWN = 0,
    // 未开始
    NOT_START = 1,
    // 进行中
    PROCESSING = 2,
    // 已结束
    FINISHED = 3,
}

// 进行中的状态 pk中，暴击时刻预告，暴击时刻
export enum UNDERWAY_STATUS {
    // 未开始
    PK_NO_START = 'PK_NO_START',
    // pk中
    PK_ING = 'PK_ING',
    // 暴击时刻预告
    CH_FPRETELL = 'CH_FPRETELL',
    // 暴击时刻
    CH_ING = 'CH_ING',
    // 结算中
    SETTL_ING = 'SETTL_ING',
}

// 暴击状态集合
export const CRITICAL_HIT_GROUP = [
    UNDERWAY_STATUS.CH_FPRETELL,
    UNDERWAY_STATUS.CH_ING,
];

export enum LIVE_STREAM_ID {
    PK_ING = 132,
    CH_FPRETELL = 152,
    CH_ING = 154,
}
