<script lang="ts" setup>
import { AButton } from '@alive-ui/base';
import { bolIsAuthor } from '@alive-ui/actions';
import { PeakBattelSignType } from '../../schema';
import type { DefaultSelectedAdditionInfo } from '@pet/ones-rank.schema/query-rank';

const emits = defineEmits(['signUp']);

const props = defineProps<{
    data?: DefaultSelectedAdditionInfo | undefined;
    signStatus?: number;
}>();

const hanleSignUp = () => {
    emits('signUp');
};
</script>

<template>
    <div class="not-open a-bg-substrate">
        <img class="not-open-image" src="./not-open.png" />
        <div class="not-open-content">
            <div>
                <div class="title a-text-main">
                    {{ data?.peakBattleInfo?.desc }}
                </div>
                <div class="desc a-text-main opacity-60">
                    {{ data?.peakBattleInfo?.supplementDesc }}
                </div>
            </div>
            <div v-if="signStatus === PeakBattelSignType.notRegistered">
                <AButton
                    v-if="bolIsAuthor"
                    v-show-log="{
                        action: 'OP_ACTIVITY_SIGN_UP_BUTTON',
                        params: {},
                    }"
                    v-click-log="{
                        action: 'OP_ACTIVITY_SIGN_UP_BUTTON',
                        params: {},
                    }"
                    size="xs"
                    type="primary"
                    class="prop-card-btn"
                    @click="hanleSignUp"
                >
                    立即报名
                </AButton>
                <span v-else class="a-text-main-o2 resgist-text">
                    主播未报名
                </span>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.not-open {
    display: flex;
    align-items: center;
    height: 84px;
    border-radius: 8px;
    justify-content: flex-start;
    padding: 0 12px;
    .not-open-content {
        display: flex;
        align-items: center;
        flex: 1;
        justify-content: space-between;
        .title {
            font-family: PingFang SC;
            font-size: 14px;
            font-weight: 500;
            line-height: 21px;
            text-align: left;
        }
        .desc {
            font-family: PingFang SC;
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            text-align: left;
            opacity: 0.6;
        }

        .resgist-text {
            font-size: 12px;
            font-weight: 400;
        }
    }
    .not-open-image {
        width: 56px;
        height: 56px;
        margin-right: 10px;
    }
}
</style>
