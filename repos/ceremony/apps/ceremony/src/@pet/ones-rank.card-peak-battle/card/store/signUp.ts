import { computed, ref } from 'vue';
import { defineStore } from 'pinia';
import { Report } from '@alive-ui/actions';
import { getSignUp } from '../service/index';

export default defineStore('peak-buffer-card-signUp', () => {
    async function signUp(id: number) {
        try {
            const res = await getSignUp(id);
            return { success: true, data: res }; // 成功时返回数据
        } catch (error) {
            Report.biz.error('巅峰对决报名失败', {
                error,
            });
            return { success: false, data: error }; // 失败时返回错误
        }
    }

    return {
        signUp,
    };
});
