import { authorId, activityBiz, request, getQuery } from '@alive-ui/actions';

const PATH = {
    signUp: '/webapi/live/revenue/operation/activity/peakBattleGame/signUp',
};

export const getSignUp = async (activityId: number) => {
    const params = {
        authorId: +authorId,
        activityBiz,
        activityId,
    };
    const res = await request.post<Response>(PATH.signUp, params);
    return res?.data;
};
