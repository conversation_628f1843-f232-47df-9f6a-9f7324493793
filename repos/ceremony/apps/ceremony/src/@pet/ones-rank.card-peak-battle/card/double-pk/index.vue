<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { uniqueId } from 'lodash-es';
import PeakCard from '@pet/ones-rank.card-peak-battle/components/peak-card/index.vue';
import { sendClickLogImmediately as sendClick } from '@gundam/weblogger';
import { Right } from '@alive-ui/icon';
import { Marquee as AMarquee } from '@alive-ui/base';
import { authorId, liveStreamId } from '@alive-ui/actions';
import NotOpen from '../not-open/index.vue';
import { BoostCardType, BoostCardStatus } from '../../schema';
import { homePkDataFormat, homePkDataFormat2 } from '../../format/index';
import type { DefaultSelectedAdditionInfo } from '@pet/ones-rank.schema/query-rank';

const props = defineProps<{
    data: DefaultSelectedAdditionInfo;
    stageType: number;
}>();
const router = useRouter();
const logs = (logTitle = '', extra = {}) => {
    return {
        action: 'OP_ACTIVITY_MORE_PLAY_CARD',
        params: {
            type: logTitle,
            live_stream_id: liveStreamId,
            author_id: authorId,
            ...extra,
        },
    };
};
const goHotPeakBattle = (btn_type = '') => {
    // 小时榜
    // 车轮战
    const { stageType } = props;

    router.push({
        name: 'peak-battle',
        query: {
            peakType: 1,
            stageType,
        },
    });
    sendClick(logs('热门对决', { btn_type }));
};
</script>

<template>
    <div class="attrition-warfare">
        <!-- 未开始 || 未参与 -->
        <NotOpen
            v-if="
                data?.status === BoostCardStatus.notOpen ||
                !data?.peakBattleInfo?.join
            "
            :data="data"
        />
        <!-- 双人对决 -->
        <PeakCard
            v-if="
                (data?.status === BoostCardStatus.finished ||
                    data?.status === BoostCardStatus.underWay) &&
                data?.peakBattleInfo?.join
            "
            :pk-info="homePkDataFormat(data)"
            :header-text="data?.peakBattleInfo?.desc"
        />
        <!-- 热门对决 -->
        <div
            v-if="data?.peakBattleInfo?.hotPeakBattle"
            v-show-log="
                logs('精彩对决', {
                    btn_type: 'CURRENT_RANK',
                })
            "
        >
            <div class="hotPkBattle__header">
                <div class="header__left">
                    <div class="top-title">精彩对决</div>
                </div>
                <div
                    class="header__right"
                    @click="goHotPeakBattle('CURRENT_RANK')"
                >
                    <AMarquee>
                        <div
                            v-for="item in data.peakBattleInfo.hotPeakBattle
                                .authorHotDesc"
                            :key="uniqueId(item)"
                            class="tip-text a-text-main"
                        >
                            {{ item }}
                            <Right class="title-arrow" />
                        </div>
                    </AMarquee>
                </div>
            </div>
            <div class="attrition-warfare-content">
                <PeakCard
                    v-if="data?.source === BoostCardType.carousel"
                    :pk-info="homePkDataFormat2(data)"
                    @click="goHotPeakBattle"
                />
                <div
                    class="see-more a-bg-substrate a-text-main opacity-60"
                    @click="goHotPeakBattle('MORE_PK_BUTTON')"
                >
                    更多对决
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="less" scoped>
.attrition-warfare {
    .attrition-warfare-content {
        display: flex;
    }
    .see-more {
        width: 40px;
        height: 84px;
        line-height: 40px;
        border-radius: 8px;
        font-family: PingFang SC;
        font-size: 12px;
        font-weight: 400;
        vertical-align: middle;
        display: inline-block;
        text-align: center;
        margin-left: 10px;
        /*文字居中*/

        /*文字竖排*/
        writing-mode: vertical-lr; /*从左向右 从右向左是 writing-mode: vertical-rl;*/
    }
}
.not-open {
    display: flex;
    align-items: center;
    height: 84px;
    border-radius: 8px;
    justify-content: flex-start;
    padding: 0 12px;
    .not-open-content {
        .title {
            //styleName: 中黑体 bold/描述文本 font-bold-14;
            font-family: PingFang SC;
            font-size: 14px;
            font-weight: 500;
            line-height: 21px;
            text-align: left;
        }
        .desc {
            //styleName: 常规体 regular/辅助文本 font-regular-12;
            font-family: PingFang SC;
            font-size: 12px;
            font-weight: 400;
            line-height: 18px;
            text-align: left;
            opacity: 0.6;
        }
    }
    .not-open-image {
        width: 56px;
        height: 56px;
        margin-right: 10px;
    }
}
.hotPkBattle__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0 12px;
    .header__left {
        display: flex;
        align-items: center;
        .top-title {
            flex-shrink: 0;
            height: 20px;
            font-family: HYYakuHei;
            font-size: 16px;
            line-height: 20px;
            background: linear-gradient(
                89.98deg,
                #ffffff 0.03%,
                #ffc4a3 95.69%
            );
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }
    .header__right {
        height: 18px;
        overflow: hidden;
        font-size: 12px;
        opacity: 0.6;
    }
    .tip-text {
        display: flex;
        align-items: center;
    }
    .title-arrow {
        font-size: 10px;
    }
}
</style>
