<script lang="ts" setup>
import { ref, watch } from 'vue';
import { uniqueId } from 'lodash-es';
import { Right } from '@alive-ui/icon';
import { Marquee as AMarquee } from '@alive-ui/base';
import { BoostCardType, BoostCardStatus } from '../../schema';

// 公会赛集合
const emits = defineEmits<{
    (e: 'timeover'): void;
    (e: 'goBranchPage', item: any): void;
}>();
const props = withDefaults(
    defineProps<{
        data: any;
        branchData: any;
    }>(),
    {
        data: {},
        branchData: {},
    },
);

const countTime = ref(0); // 倒计时
let timer: any = null;
const getTimeDetail = (item: {
    status?: BoostCardStatus;
    source?: BoostCardType;
    peakBattleInfo?: any;
}) => {
    const { pkEndTime } = item?.peakBattleInfo || {};
    // console.log(pkEndTime, 'pkEndTimepkEndTimepkEndTime')
    const nowTime = Date.now();
    const time = pkEndTime - nowTime;
    return {
        time,
        isVacancyTime: nowTime > pkEndTime,
    };
};
const clearTimer = () => {
    if (timer) {
        clearTimeout(timer);
        timer = null;
    }
};

const handleCountDown = (branchData: any) => {
    // 倒计时
    // const { additionCardShowRefreshTime } = props.data || {};
    if (!branchData?.peakBattleInfo) {
        return;
    }
    const { time } = getTimeDetail(branchData);
    // 时间差超过1s就定义为超时了
    if (time < -1000) {
        return;
    }
    // 时间差在0-1s氛围内定义为结束
    if (
        time <= 0 &&
        time > -1000 &&
        branchData?.status === BoostCardStatus.underWay
    ) {
        // 扔出事件,停止计时，本次外面做打散，内部暂时不做处理
        // setTimeout(() => {
        emits('timeover');
        // }, additionCardShowRefreshTime || 0);
        clearTimer();
        return;
    }
    clearTimer();
    countTime.value = time;
    timer = setTimeout(() => {
        handleCountDown(branchData);
    }, 1000);
};

watch(
    () => props.branchData,
    (val) => {
        if (val?.peakBattleInfo) {
            handleCountDown(val);
        }
    },
    { deep: true, immediate: true },
);
function formatTime(ms: number) {
    const totalSeconds = Math.floor(ms / 1000); // 将毫秒转换为秒
    const minutes = Math.floor(totalSeconds / 60); // 计算分钟
    const seconds = totalSeconds % 60; // 计算剩余秒数

    // 使用 padStart 补零，确保两位数格式
    const formattedMinutes = String(minutes).padStart(2, '0');
    const formattedSeconds = String(seconds).padStart(2, '0');

    return `${formattedMinutes}:${formattedSeconds}`;
}
const showLoopTime = (item: {
    status: BoostCardStatus;
    source: BoostCardType;
}) => {
    const { isVacancyTime } = getTimeDetail(item);
    if (isVacancyTime) {
        return false;
    }
    if (
        item?.status === BoostCardStatus.underWay &&
        (item?.source === BoostCardType.hourRank ||
            item?.source === BoostCardType.carousel)
    ) {
        return true;
    }
    return false;
};
const goPage = (item: { source: BoostCardType }) => {
    // 小时榜
    // 车轮战
    emits('goBranchPage', item);
};

// const showTag = (item: {
//     [
//         x: string
//     ]: // / <reference types="../../../node_modules/.vue-global-types/vue_3.4_false.d.ts" />
//     any;
//     source: any;
//     status: any;
// }) => {
//     // 凝聚力不显示
//     if (item?.source === BoostCardType.aggregation) {
//         return false;
//     }
//     // 车轮战显示逻辑
//     if (
//         item?.source === BoostCardType.carousel &&
//         (item?.status === BoostCardStatus.finished ||
//             item?.status === BoostCardStatus.obtained)
//     ) {
//         return false;
//     }
//     // 小时榜显示逻辑
//     if (item?.source === BoostCardType.hourRank) {
//         return !!item?.smallTitle;
//     }
//     return !!item?.smallTitle;
// };

// 是否是公会赛
</script>

<template>
    <div class="content-card-box__header">
        <div class="content-card-box__header__left">
            <div class="top-title">{{ branchData?.title }}</div>
            <!-- 小时榜标签 -->
            <!-- showTag(branchData) -->
            <div
                v-if="branchData?.smallTitle"
                class="mult-pk-tag a-text-highlight"
            >
                {{ branchData?.smallTitle }}
            </div>
            <!-- 倒计时 -->
            <div v-if="showLoopTime(branchData)" class="time">
                {{ formatTime(countTime) }}
            </div>
        </div>
        <div
            class="content-card-box__header__right opacity-60"
            @click="() => goPage(branchData)"
        >
            <AMarquee>
                <div
                    v-for="item in branchData?.desc"
                    :key="uniqueId(item)"
                    class="tip-text a-text-main"
                >
                    {{ item }}
                    <Right class="title-arrow" />
                </div>
            </AMarquee>
        </div>
    </div>
</template>

<style lang="less" scoped>
.content-card-box {
    padding: 20px 0px 0 0;
    background: #141522;
    &__header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        &__left {
            display: flex;
            align-items: center;
            .top-title {
                flex-shrink: 0;
                height: 20px;
                font-family: HYYakuHei;
                font-size: 16px;
                line-height: 20px;
                background: linear-gradient(
                    89.98deg,
                    #ffffff 0.03%,
                    #ffc4a3 95.69%
                );
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            .time {
                margin-left: 4px;
                height: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 2px 6px;
                font-size: 12px;
                font-weight: 500;
                line-height: 16px;
                border-radius: 10px;
                text-align: center;
                color: #ffd400;
                background: rgba(255, 212, 0, 0.1);
            }
        }
        &__right {
            height: 18px;
            overflow: hidden;
            font-size: 12px;
            .tip-text {
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }
            .entry-icon {
                width: 10px;
                height: 10px;
            }
        }
    }
}
.content-card-box__header__right {
    font-size: 12px;
}

.mult-pk-tag {
    background: #ff54771a;
    margin-left: 4px;
    height: 20px;
    flex-shrink: 0;
    padding: 2px 6px 2px 6px;
    border-radius: 10px;
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: 500;
    line-height: 16px;
    text-align: center;
    color: #ff5477;
}
</style>
