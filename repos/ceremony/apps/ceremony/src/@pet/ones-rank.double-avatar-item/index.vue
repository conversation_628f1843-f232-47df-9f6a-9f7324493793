<!-- 双头像榜单单项组件 -->
<template>
    <RankItemBase :item="item" :current-label="currentLabel">
        <template #left>
            <ADoubleAvatar :size="item.avatarSize">
                <APAvatar
                    :size="item.avatarSize"
                    :user-id="item.itemId"
                    :head-url="item.headUrl"
                    :live-stream-id-list="item?.liveStreamIdList"
                    :live-stream-id="item.liveStreamId"
                    :is-mystery-man="item.mysteryMan"
                >
                    <template #bottomRight><span></span></template>
                    <template #bottomInner>
                        <!-- 可扩展头像底部信息：如+号关注等，需alive-ui prop确认插槽能力  -->
                        <!-- <slot name="bottomInner"> xxx </slot> -->
                    </template>
                    <template #topInner>
                        <img
                            v-if="item.liveStreamId"
                            src="./new-living_2x.png"
                            class="w-full h-full"
                        />
                    </template>
                </APAvatar>
                <APAvatar
                    :size="item.avatarSize"
                    :user-id="item?.sponsors?.[0]?.itemId"
                    :head-url="item?.sponsors?.[0]?.headUrl"
                    :is-mystery-man="item?.sponsors?.[0]?.mysteryMan"
                >
                    <template #frame><span></span></template>
                </APAvatar>
            </ADoubleAvatar>
        </template>
        <template #info>
            <!-- 放榜单项主播或xx名称下的信息内容 todo：自定义 -->
            <slot name="info" />
        </template>
        <template #right>
            <!-- 定义右侧内容 -->
            <div v-if="!needShowRight" />
            <slot v-else name="right" />
        </template>
    </RankItemBase>
</template>

<script lang="ts" setup>
import RankItemBase from '@pet/ones-rank.base-item/index.vue';
import { Avatar as APAvatar } from '@alive-ui/pro';
import { Avatar } from '@alive-ui/base';
const { DoubleAvatar: ADoubleAvatar } = Avatar;
import type { PropType } from 'vue';
import type { ItemRankInfo } from '@pet/ones-rank.base-item/schema';
defineProps({
    item: {
        type: Object as PropType<
            ItemRankInfo & {
                avatarSize: 'lg' | '2xs' | 'xs' | 'sm';
                liveStreamIdList?: string[];
                activityId?: string;
                rankId?: number;
                scoreLabel: string;
            }
        >,
        default: () => {
            return {};
        },
    },
    needShowRight: {
        type: Boolean,
        default: true,
    },
    isCurrent: {
        type: Boolean,
        default: false,
    },
    currentLabel: {
        type: String,
        default: '',
    },
});
</script>

<style lang="less" scoped>
.gis-new-living {
    width: 42px;
    height: 13px;
    background: url('./new-living_2x.png') center / 100% no-repeat;
}
</style>
