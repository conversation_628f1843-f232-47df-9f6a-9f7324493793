# 横向赛程切换组件

## 功能概述

允许用户选择不同的赛程。每个赛程项可以显示赛程名称和开始时间。当用户选择不同的赛程时，会触发 `change` 事件。

## 属性和方法

### Props

| 名称 | 类型 | 是否必填 | 默认值 | 描述 |
|------|------|----------|--------|------|
| data | `Object` | 是 | `{ defaultIndex: 0, list: [] }` | 包含赛程信息的对象，其中 `defaultIndex` 表示默认选中的赛程索引，`list` 是赛程列表 |

### Events

| 名称 | 参数 | 描述 |
|------|------|------|
| change | `(item: object, index: number)` | 当用户选择不同的赛程时触发，返回选中的赛程对象和索引 |

## 使用示例

```vue
<template>
    <div v-if="data?.list?.length" class="schedule-demo flex-center-center">
        <ASchedule :active-index="activeIndex" @change="scheduleChange">
            <AScheduleItem
                v-for="(item, index) in data?.list"
                :key="index"
                class="schedule-item-font"
            >
                <span>{{ item.scheduleName }}</span>
                <template #extra>
                    <span class="position-relative">
                        {{ timestampToTime(item.scheduleStartTime) }}
                    </span>
                </template>
            </AScheduleItem>
        </ASchedule>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { timestampToTime } from '@alive-ui/actions';

const emits = defineEmits(['change']);
const props = defineProps({
    data: {
        type: Object as PropType<{
            defaultIndex: number;
            list: any[];
        }>,
        required: true,
        default: () => ({
            defaultIndex: 0,
            list: [],
        }),
    },
});

const activeIndex = ref(props.data.defaultIndex);

const scheduleChange = (index: number) => {
    const item = props.data?.list?.[index];
    if (item.showStageType === 1) {
        Toast.info('赛程未开始');
        return;
    }
    activeIndex.value = index;
    emits('change', item, index);
};
</script>

<style lang="less">
.schedule-demo {
    max-width: 414px;
    height: 48px;
    position: relative;
    z-index: 1;
    overflow: hidden;
    margin-top: -96px;
}
.schedule-item-font {
    font-size: 12px;
    position: relative;
    font-weight: bold;
}
</style>
```

## 注意事项

- 确保 `data` 对象中的 `list` 数组包含有效的赛程信息。
- `data.defaultIndex` 应该是一个有效的索引值，对应于 `list` 中的某个赛程。
- 如果 `item.showStageType` 为 1，表示赛程未开始，此时会显示提示信息并阻止切换。

## 依赖项

- `@lux/sharp-ui-next`
- `@alive-ui/base`
- `@alive-ui/actions`