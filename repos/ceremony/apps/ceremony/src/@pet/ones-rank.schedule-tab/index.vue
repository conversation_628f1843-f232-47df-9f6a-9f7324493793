<template>
    <div v-if="data?.list?.length" class="schedule-demo flex-center-center">
        <ASchedule :active-index="activeIndex" @change="scheduleChange">
            <AScheduleItem
                v-for="(item, index) in data?.list"
                :key="index"
                class="schedule-item-font"
            >
                <span>{{ item.scheduleName }}</span>
                <template #extra>
                    <span class="position-relative">
                        {{ timestampToTime(item.scheduleStartTime) }}
                    </span>
                </template>
            </AScheduleItem>
        </ASchedule>
    </div>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { Schedule } from '@alive-ui/base';
import { timestampToTime } from '@alive-ui/actions';
import type { PropType } from 'vue';
const { ScheduleItem: AScheduleItem, Schedule: ASchedule } = Schedule;

const emits = defineEmits(['change']);
const props = defineProps({
    data: {
        type: Object as PropType<{
            defaultIndex: number;
            list: any[];
        }>,
        require: true,
        default: () => {
            return {
                defaultIndex: 0,
                list: [],
            };
        },
    },
});

const activeIndex = ref(0);
watch(
    () => props.data.defaultIndex,
    (val, old) => {
        if (val !== old) {
            activeIndex.value = val;
        }
    },
    {
        immediate: true,
    },
);
const scheduleChange = (index: number) => {
    const item = props.data?.list?.[index];
    if (item.showStageType === 1) {
        Toast.info('赛程未开始');
        return;
    }
    activeIndex.value = index;
    emits('change', item, index);
};
</script>

<style lang="less">
.schedule-demo {
    max-width: 414px;
    height: 48px;
    position: relative;
    z-index: 1;
    overflow: hidden;
    margin-top: -96px;
}
.schedule-item-font {
    font-size: 12px;
    position: relative;
    font-weight: bold;
}
</style>
