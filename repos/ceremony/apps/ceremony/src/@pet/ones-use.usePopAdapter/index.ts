import { ref } from 'vue';
import { throttle } from 'lodash-es';

export function usePopAdapter(movePos: 'left' | 'right' | 'bottom' | 'top') {
    const boundings = ref<DOMRect>();
    const initialPos = ref();
    const { body } = document;
    const moveDistance = ref(0);

    const isBigScreen = ref();

    const appEl = document.querySelector('#app');

    //  实际的一般情况下，resize 不会被频繁触发，因此就不加 throttle 了
    const adapter = () => {
        boundings.value = appEl?.getBoundingClientRect();
        initialPos.value = body?.getBoundingClientRect()?.[movePos];
        const docEl = document.documentElement;
        const aspectRatio = docEl.clientWidth / docEl.clientHeight;

        isBigScreen.value = aspectRatio >= 0.8 && aspectRatio <= 1.2;
        moveDistance.value =
            (docEl.clientWidth - (boundings.value?.width ?? 0)) / 2;
        body.style[movePos] = `${moveDistance.value}px`;
    };

    const recover = () => {
        body.style[movePos] = `${initialPos.value}px`;
        window.removeEventListener('resize', adapter);
    };

    window.addEventListener('resize', adapter);

    return {
        boundings,
        initialPos,
        adapter,
        recover,
        isBigScreen,
        moveDistance,
    };
}
