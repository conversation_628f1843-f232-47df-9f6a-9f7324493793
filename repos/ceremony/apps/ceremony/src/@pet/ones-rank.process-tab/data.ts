import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { RankFrameworkPostResponse } from '@pet/ones-rank.schema/index-home';
export const dataFun = (contextName: symbol) => {
    const homeData = contextName
        ? inject<Ref<RankFrameworkPostResponse>>(contextName)
        : null;
    const data = computed(() => {
        const { scheduleViewList, anchorDisplayScheduleAndLane } =
            homeData?.value || {};
        const { scheduleType } = anchorDisplayScheduleAndLane || {};
        if (!scheduleType) {
            return {
                defaultIndex: 0,
                list: [],
            };
        }
        return {
            // 默认选中的赛程
            defaultIndex:
                scheduleViewList?.findIndex((elem: any) => {
                    return elem?.scheduleType === scheduleType;
                }) || 0,
            // 赛程列表
            list: scheduleViewList,
        };
    });
    return data;
};
