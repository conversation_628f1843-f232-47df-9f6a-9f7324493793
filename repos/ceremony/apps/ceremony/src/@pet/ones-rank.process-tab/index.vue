<template>
    <div v-if="newData?.list?.length" class="process-period-list">
        <div class="process-scroll-area">
            <div
                v-for="(item, index) in newData.list"
                :key="item.scheduleType"
                class="flex align-center"
                @click="change(item, index)"
            >
                <div
                    :style="{
                        width: `${curActive == index ? adaptorWdith[newData.list.length] : ''}`,
                    }"
                    class="every-process-item text-12px a-text-main flex-center"
                    :class="
                        curActive == index
                            ? 'active-process-style a-text-main'
                            : ''
                    "
                >
                    {{ item?.promotionDesc }}
                </div>
                <div
                    v-if="showIcon(item, index)"
                    :style="{
                        margin: `0 ${adaptorMargin[newData.list.length]}`,
                    }"
                    :class="
                        curActive == index
                            ? 'gis-process-active-icon'
                            : 'gis-process-active-icon'
                    "
                />
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { Toast } from '@lux/sharp-ui-next';
import { dataFun } from './data';
import type { ScheduleViewList } from '@pet/ones-rank.schema/index-home';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
interface ProcessData extends PropsCommonParams {
    data?: {
        defaultIndex: number;
        list: ScheduleViewList[];
    };
}
const props = withDefaults(defineProps<ProcessData>(), {
    data: () => {
        return {
            defaultIndex: 0,
            list: [],
        };
    },
    useProps: false,
    contextName: Symbol.for('ctx'),
});
const newData = props.useProps
    ? computed(() => props.data)
    : dataFun(props.contextName);
const emits = defineEmits(['change']);
const curActive = ref(0);

watch(
    () => newData.value.defaultIndex,
    (val, old) => {
        if (val !== old) {
            curActive.value = val;
        }
    },
    {
        immediate: true,
    },
);
const tabChange = ref(false);

const change = (item: any, index: number) => {
    if (index === curActive.value) {
        return;
    }
    enum typeEnum {
        unStart = 1,
        inTime = 2,
        timeEnd = 3,
    }

    if (item.status === typeEnum.unStart) {
        Toast.info('未开始', 1000, false);

        return;
    }
    tabChange.value = true;
    curActive.value = index;
    emits('change', item, index);
};

const showIcon = (item: any, index: number) => {
    if (newData?.value?.list && index === newData.value.list.length - 1) {
        return false;
    }
    // 把后面的数组部分拿出来
    const rightArray = newData?.value?.list?.slice(index + 1);

    // 只要后面的还有，就要显示右箭头，否则不显示
    return rightArray?.some((elem: ScheduleViewList) => elem.promotionDesc);
};
const adaptorWdith: { [key: number]: string } = {
    5: '1.12rem',
    4: '1.28rem',
    3: '1.44rem',
    2: '1.44rem',
    1: '1.44rem',
};
const adaptorMargin: { [key: number]: string } = {
    5: '0.16rem',
    4: '0.2rem',
    3: '0.4rem',
    2: '0.4rem',
};
</script>

<style lang="less" scoped>
.process-period-list {
    position: relative;
    display: flex;
    margin: 0;
    justify-content: center;
    align-items: center;
    .gis-process-active-icon {
        width: 10px;
        height: 10px;
        background: url('./process-active-icon_2x.png') center / 100% no-repeat;
    }
    .gis-process-unactive-icon {
        width: 10px;
        height: 10px;
        background: url('./process-unactive-icon_2x.png') center / 100%
            no-repeat;
    }
    .every-process-item {
        position: relative;
        height: 30px;
        font-weight: bold;
        line-height: 30px;
        white-space: nowrap;
        opacity: 0.8;
    }
    .active-process-style {
        min-width: 56px;
        font-family: 'PingFang SC';
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        opacity: 1;
    }
}
.process-scroll-area {
    position: relative;
    display: flex;
    border-radius: 15px;
    justify-content: center;
}
</style>
