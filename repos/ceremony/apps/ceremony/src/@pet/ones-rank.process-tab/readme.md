# 赛程进制 组件

## 功能描述

该组件用于展示一个可点击的赛程列表，用户可以选择不同的赛程进行操作。组件支持动态样式变化和状态提示，适用于活动流程展示场景。

## Props

| 属性名          | 类型                     | 默认值          | 描述                                     |
|-----------------|--------------------------|------------------|------------------------------------------|
| `data`          | `{ defaultIndex: number; list: ScheduleViewList[]; }` | `{ defaultIndex: 0, list: [] }` | 赛程数据，包括默认索引和赛程列表       |
| `useProps`      | `boolean`                | `false`          | 是否使用 props 传入的数据               |
| `contextName`   | `Symbol`                 | `Symbol.for('ctx')` | 上下文名称，用于数据传递               |

## 使用方式

```vue
<template>
  <ProcessPeriodList
    :data="{ defaultIndex: 0, list: scheduleList }"
    :useProps="true"
    :contextName="Symbol.for('ctx')"
    @change="handleChange"
  />
</template>

<script setup>
import ProcessPeriodList from '@pet/ones-rank.process-tab/index.vue';

const scheduleList = [
  { scheduleType: 1, promotionDesc: '赛程1', status: 2 },
  { scheduleType: 2, promotionDesc: '赛程2', status: 1 },
  { scheduleType: 3, promotionDesc: '赛程3', status: 2 },
  { scheduleType: 4, promotionDesc: '赛程4', status: 3 },
  // 更多赛程数据
];

const handleChange = (item, index) => {
  console.log('Selected Schedule:', item, index);
};
</script>
```
## Mock 数据
以下是一个示例的 mock 数据，用于展示组件的使用：

```javascript
const mockData = {
  defaultIndex: 0,
  list: [
    { scheduleType: 1, promotionDesc: '赛程1', status: 2 },
    { scheduleType: 2, promotionDesc: '赛程2', status: 1 },
    { scheduleType: 3, promotionDesc: '赛程3', status: 2 },
    { scheduleType: 4, promotionDesc: '赛程4', status: 3 },
  ],
};
```
