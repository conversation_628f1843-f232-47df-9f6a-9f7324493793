<template>
    <!-- 刷新按钮组件 -->
    <div
        class="refresh-icon"
        :class="{
            'refresh-icon-animation': isRefreshing,
        }"
        @click="handleRefresh"
    ></div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { throttle } from 'lodash-es';
import { isOutLiveRoom, layoutType } from '@alive-ui/actions';
import { is4Tab, isSearchTab } from '../ones-use.is4Tab';

const emit = defineEmits<{
    (e: 'refresh'): void;
}>();

const props = defineProps<{
    height: number; // 外部传入的高度参数
}>();

const DEFAULT_TOP = 0; // 默认顶部距离

// 顶部动态 CSS 值
const topCSSValue = ref(DEFAULT_TOP);
// 刷新状态
const isRefreshing = ref(false);
const handleRefresh = throttle(() => {
    if (isRefreshing.value) return;

    isRefreshing.value = true;
    emit('refresh');
    setTimeout(() => {
        isRefreshing.value = false;
    }, 1000);
}, 500);

const updateTopCSSValue = () => {
    if (is4Tab) {
        // 4tab动态改变
        topCSSValue.value = props.height;
    } else if (isOutLiveRoom && !isSearchTab) {
        // 间外(非-1tab)顶部高度会增加30
        topCSSValue.value = 30;
    }
    // -1tab 和 间内 不增加高度

    // 设置 CSS 变量
    document.documentElement.style.setProperty(
        '--topCSS',
        `${topCSSValue.value}px`,
    );
};

// 监听 props.height 的变化
watch(() => props.height, updateTopCSSValue, { immediate: true });
</script>

<style lang="less" scoped>
.refresh-icon {
    width: 32px;
    height: 32px;
    position: fixed;
    // 默认的顶部高度是20px，使用rem动态计算
    top: calc(var(--topCSS) + 20px);
    left: 50%;
    transform: translateX(154px);
    z-index: 10;
    background: url('./assets/refresh.png') no-repeat;
    background-size: 100% 100%;
}

.refresh-icon-animation {
    animation: spin 600ms linear;
}

@keyframes spin {
    0% {
        transform: translateX(154px) rotateZ(0deg);
    }

    100% {
        transform: translateX(154px) rotateZ(-720deg);
    }
}
</style>
