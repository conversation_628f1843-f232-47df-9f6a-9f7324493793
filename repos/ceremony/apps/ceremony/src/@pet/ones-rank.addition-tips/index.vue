<template>
    <div class="addition-tips a-text-main">
        <div class="progress-text text-din">
            <slot name="text">
                {{ text }}
            </slot>
        </div>
        <progress :value="value" :max="max" />
    </div>
</template>

<script setup lang="ts">
interface AdditionSchema {
    text?: string;
    value: number;
    max: number;
}
const props = withDefaults(defineProps<AdditionSchema>(), {
    text: '',
    value: 0,
    max: 100,
});
</script>

<style lang="less" scoped>
.addition-tips {
    --radius: 9px;
    position: relative;
    width: 56px;
    height: 14px;
    line-height: 14px;
    font-size: 9px;
    font-weight: 400;
    font-family: PingFang SC;
    text-align: center;
    border-radius: var(--radius);
    overflow: hidden;
    .progress-text {
        position: absolute;
        left: 0;
        right: 0;
        margin: 0 auto;
    }
}
progress {
    --progressHeight: 14px;
    --allColor: rgba(255, 84, 119, 0.2);
    --perColor: rgba(255, 84, 119, 40%);
    width: 100%;
    height: var(--progressHeight, 14px);
    appearance: none;
}
progress::-webkit-progress-bar {
    background: var(--allColor);
    border-radius: var(--radius);
}
progress::-webkit-progress-value {
    background: var(--perColor);
    border-radius: var(--radius);
}
progress::-moz-progress-bar {
    background: var(--allColor);
    border-radius: var(--radius);
}
</style>
