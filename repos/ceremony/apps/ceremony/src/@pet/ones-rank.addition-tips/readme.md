README
### 组件功能描述
该组件是应用在榜单场景的一个加成进度条，实现了一个进度条和提示文本的组合，允许用户通过进度条直观地查看某个值的进度。组件支持通过插槽自定义提示文本，并且可以设置进度条的当前值和最大值。默认情况下，组件会显示一个默认文本和进度条。

### 主要功能
-  显示一个可自定义的提示文本。
-  显示一个进度条，表示当前进度。
-  允许通过插槽自定义提示文本内容。

### Props 提取
该组件接受以下 props：

| Prop Name | Type   | Description                                      |
|-----------|--------|--------------------------------------------------|
| text      | String | 提示文本，默认为 'xxx'。                         |
| value     | Number | 当前进度值，必传。                              |
| max       | Number | 进度条的最大值，必传。                          |

---

Props 示例
```typescript
interface AdditionSchema {
    text?: string;
    value: number;
    max: number;
}
const props = withDefaults(defineProps<AdditionSchema>(), {
    text: 'xxx',
    value: 0,
    max: 100,
});
```
### Vue 使用代码 Demo

- 以下是如何在 Vue 组件中使用该进度条组件的示例：

```vue
<template>
    <div>
        <AdditionTips
            :text="customText"
            :value="currentValue"
            :max="maxValue"
        >
            <template #text>
                <strong>{{ customText }}</strong>
            </template>
        </AdditionTips>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import AdditionTips from './AdditionTips.vue'; // 假设组件文件名为 AdditionTips.vue

const customText = ref('当前进度');
const currentValue = ref(50); // 当前进度值
const maxValue = ref(100); // 最大值
</script>
```

