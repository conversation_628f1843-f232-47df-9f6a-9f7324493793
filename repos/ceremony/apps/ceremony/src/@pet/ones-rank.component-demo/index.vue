<template>
    <div class="component-demo">组件demo {{ newData.text }}</div>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { dataFun } from './data';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
interface ComponentSchema extends PropsCommonParams {
    data: {
        // todo: 需要哪些数据，示例
        text: string;
    };
}
const props = withDefaults(defineProps<ComponentSchema>(), {
    data: () => {
        return {
            text: '',
        };
    },
    useProps: false,
    // 组件的上下文名称,在页面容器层
    contextName: Symbol.for('ctx'),
});
// 组件提供两种消费数据方式，一种是可以通过props传入，一种是可以通过data.ts，消费组件的页面级别provideroot数据 inject给组件
const newData = props.useProps
    ? computed(() => props.data)
    : dataFun(props.contextName);
</script>
<style lang="less" scoped></style>
