<template>
    <div
        class="ones-float-button"
        :class="`float-${pos}`"
        :style="{ backgroundImage: `url(${props.bgImg})` }"
        @click="emits('float-click')"
    ></div>
</template>

<script lang="ts" setup>
const props = withDefaults(
    defineProps<{
        bgImg: string;
        pos: 'left'; //  后续可以扩展位置枚举
    }>(),
    {
        bgImg: '',
        pos: 'left',
    },
);
const emits = defineEmits(['float-click']);
</script>

<style lang="less" scoped>
// 目前只有左下角的位置，后续可以根据业务扩展其他位置的悬浮
.ones-float-button {
    position: fixed;
    z-index: 11;
    width: 56px;
    height: 56px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    &.float-left {
        bottom: 100px;
        left: 8px;
    }
}
</style>
