<template>
    <div class="rest-day">
        <!-- 标题和副标题 -->
        <div class="text-20 mx-auto a-text-title text-center rest-title">
            {{ kconfData?.mainPage?.restDay?.titie }}
        </div>
        <div
            class="text-14 mx-auto a-text-title text-center mt-[6px] mb-[22px] rest-title"
        >
            {{ kconfData?.mainPage?.restDay?.restText }}
        </div>
        <!-- 后续赛程奖励 -->
        <YodaImage
            v-if="kconfData?.mainPage?.restDay?.honorImg"
            class="w-[382px] h-[306px] mx-[16px] honor-img"
            :src="kconfData?.mainPage?.restDay?.honorImg"
            @error="(e: Event) => onYodaImageError(e, 'l1')"
        ></YodaImage>
        <!-- 后续赛程安排 -->
        <YodaImage
            v-if="kconfData?.mainPage?.restDay?.futureImg"
            class="w-[382px] h-[908px] mt-[14px] mx-[16px] future-img"
            :src="kconfData?.mainPage?.restDay?.futureImg"
            @error="(e: Event) => onYodaImageError(e, 'l1')"
        ></YodaImage>
    </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import YodaImage from '@pet/yoda.image/img.vue';
import useKconf from '@pet/ones-use.useKconfBatch';
import { Report } from '@alive-ui/actions';

const { kconfData } = storeToRefs(useKconf());

const onYodaImageError = (e: Event, level: string) => {
    Report.biz.error('kv区yoda-image异常', {
        error: e,
        level,
    });
};
</script>

<style lang="less" scoped>
.rest-day {
    margin-top: -70px;
}
.rest-title {
    font-family: HYYakuHei;
    width: fit-content;
}
.honor-img {
    --y-img-height: 306px;
    --y-img-width: 382px;
}
.future-img {
    --y-img-height: 1232px;
    --y-img-width: 382px;
}
</style>
