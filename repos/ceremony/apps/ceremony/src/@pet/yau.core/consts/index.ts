/**
 * 是否在客户端环境下
 */
export const isBrowser =
    typeof window !== 'undefined' && typeof document !== 'undefined';

/**
 * 是否在客户端环境下（同isBrowser）
 */
export const isClient = isBrowser;

/**
 * 是否在生产环境
 */
export const isProduction = !isBrowser && process.env.NODE_ENV === 'production';
/**
 * 是否在开发环境
 */
export const isDev = !isBrowser && process.env.NODE_ENV === 'development';
/**
 * 是否在测试环境
 */
export const isTest = !isBrowser && process.env.VUE_APP_ENV === 'test';
/**
 * 是否在staging环境
 */
export const isStaging = !isBrowser && process.env.VUE_APP_ENV === 'staging';
/**
 * 是否不在生产环境
 */
export const isNotProd = isDev || isTest || isStaging;

/**
 * 默认window
 */
export const defaultWindow = /* #__PURE__*/ isBrowser ? window : undefined;
/**
 * 默认document
 */
export const defaultDocument = /* #__PURE__*/ isBrowser
    ? window.document
    : undefined;
/**
 * 默认navigater
 */
export const defaultNavigator = /* #__PURE__*/ isBrowser
    ? window.navigator
    : undefined;
/**
 * 默认location
 */
export const defaultLocation = /* #__PURE__*/ isBrowser
    ? window.location
    : undefined;
