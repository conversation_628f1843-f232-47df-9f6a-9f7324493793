import { isBrowser } from '../consts';

/**
 * @description 是否处于低端设备环境
 */
export const islp = isBrowser ? !!window.ENV_INFO?.islp : false;
/**
 * @description 是否处于低配置设备环境
 */
export const icfo = isBrowser ? !!window.ENV_INFO?.icfo : false;
/**
 * @description 获取当前页面url查询参数
 */
const searchParams = isBrowser
    ? new URLSearchParams(window.location.search)
    : undefined;
/**
 * @description 表示查询参数中是否包含名为"islp=1“的参数
 */
export const queryIslp = searchParams?.get('islp') === '1';
/**
 * @description 表示是否在低端设备
 */
export const isLowDevice = islp || icfo || queryIslp;
