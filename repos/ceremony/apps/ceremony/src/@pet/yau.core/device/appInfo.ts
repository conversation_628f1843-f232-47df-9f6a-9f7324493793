import Cookie from 'js-cookie';
import { isBrowser } from '../consts';
import { isInIOS, compareVersion } from './env';
/**
 * @description 描述浏览器用户代理头信息的TypeScript定义
 */
export interface UAInfoType {
    applewebkit?: string;
    chrome?: string;
    mozilla?: string;
    safari?: string;
    kwai?: string;
    yoda?: string;
    build?: string;
    nettype?: string;
    version?: string;
    mobile?: string;
    ksnebula?: string;
    tbht?: string;
    /** 4tab下顶导的值 */
    ftsfht?: string;
    /** 4tab下底导的值 */
    fbsfht?: string;
    statusht?: string;
    [key: string]: string | undefined;
}
/**
 * @description iphone版本信息TypeScript定义
 */
export interface IphoneEdition {
    /** 主要版本 */
    main: number;
    /** 次要版本 */
    sub: number;
}
/**
 * @description 获取浏览器用户代理头的信息函数，以key和value形式
 * @returns 返回用户代理头的信息
 */
export function uaGetInfo(): UAInfoType {
    // 兼容SSR
    if (!isBrowser) {
        return {};
    }
    const matchList =
        window.navigator.userAgent.match(/([a-zA-Z]+)\/(\S+)(:\s)?/g) ?? [];
    const info: UAInfoType = {};
    for (const str of matchList) {
        const res = str.split('/');
        const key = res[0].toLowerCase();
        info[key] = res[1].split(',')[0];
    }
    return info;
}
/**
 * @description 手机各种高度的TypeScript定义
 */
export type BarInfo = {
    /**
     * 状态栏高度
     */
    statusBarHeight: number;
    /**
     * 操作栏高度
     */
    toolBarHeight: number;
    /**
     * 底部安全区高度
     */
    bottomSafeHeight: number;
    /**
     * 操作栏高度 + 设备安全区高度
     */
    bottomHeight: number;
};
/** IOS安全区域的最大高度 */
const iosSafeMaxHeight = 38;

/**
 * @description 判断是否在4tab下函数
 * 是返回true，否则返回false
 */
export function isAtFourTab() {
    const { tbht, ftsfht } = uaGetInfo();
    // tbht 非4tab下注入版本不知道是不是还要兼容
    // 暂时先去掉 window.location.search.includes('inActivityWeb=1')
    return Boolean(ftsfht ?? tbht);
}
/**
 * @description 判断layoutType是否为4函数
 * @returns 若layoutType为4返回true，不为返回false
 */
export function isAtLayoutType4() {
    // 兼容SSR
    if (!isBrowser) {
        return false;
    }
    return window.location.search.includes('layoutType=4');
}
/**
 * @description 判断layoutType是否为3函数
 * @returns 若layoutType为3返回true，不为返回false
 */
export function isAtLiveWebView() {
    // 兼容SSR
    if (!isBrowser) {
        return false;
    }
    return window.location.search.includes('layoutType=3');
}
/**
 * @description 获取浏览器类型函数，从cookie中获取
 * @returns 返回浏览器类型，cookie中无则返回0
 */
function getBrowserType() {
    return Cookie.get('browseType') ?? '0';
}
/**
 * @description 判断是否有底导，从cookie中获取
 * @returns 若有返回true，无则返回false
 */
function hasBottomNav() {
    return ['true', '1'].includes(Cookie.get('bottom_navigation') ?? '');
}
/**
 * @description 获取底部导航栏高度
 * @returns 返回底部导航栏高度，否则返回-1
 */
function getNewBottomAreaHeight() {
    // 兼容SSR
    if (!isBrowser) {
        return -1;
    }
    const { fbsfht } = uaGetInfo();
    return fbsfht !== undefined ? Number(fbsfht) / window.devicePixelRatio : -1;
}

/**
 * @description 判断当前是否处于沉浸式标签页中函数
 * @returns 是则返回true，否则返回false
 */
export function isInImmersiveTab() {
    if (isAtFourTab() && getNewBottomAreaHeight() > -1) {
        const bth = getNewBottomAreaHeight();
        const browserType = getBrowserType();
        if (!isInIOS()) {
            return bth === 0;
        }
        if (browserType === '3') {
            // 极速版底导实现不占位
            return bth < iosSafeMaxHeight;
        }
        if (browserType === '4') {
            // 主App通过安全区来判断是不是沉浸式，没有安全区的机器就不得不放弃
            return bth > 0;
        }
    }
    return false;
}
/**
 * @description 判断当前是否需要底部导航栏的高度
 * @returns 需要则返回true，不需要返回false
 */
function mayNeedBarHeight() {
    // getNewBottomAreaHeight在新版安卓9.10.10之后存在，iOS返回安全区的高度，安卓返回为0的时候说明不存在透明底导的情况
    // 但是9.11.40后底导才开始占位
    // iOS安全区应该是34，这里写大了一些，防止可能有的iOS大于这个值
    const newBottomAreaHeight = getNewBottomAreaHeight();
    const nebulaVersion = uaGetInfo().ksnebula ?? '';
    const poorAndroid = compareVersion(nebulaVersion, '9.11.40') === 'lt';
    return Boolean(
        newBottomAreaHeight < 0 ||
            (isInIOS() && newBottomAreaHeight > iosSafeMaxHeight) ||
            (!isInIOS() &&
                newBottomAreaHeight > 0 &&
                poorAndroid &&
                nebulaVersion),
    );
}
/**
 * @description 判断是否有透明工具栏函数
 * @returns 若有透明工具栏返回true，否则返回false
 */
function hasTransparentToolbar() {
    return (
        hasBottomNav() &&
        getBrowserType() === '3' &&
        isAtFourTab() &&
        mayNeedBarHeight()
    );
}
/**
 * @description 从获取手机型号函数
 * @returns 返回手机型号
 */
export function getPhoneModelFromCookie() {
    return Cookie.get('mod');
}
/**
 * @description 获取iphone型号函数(若传入信息则利用传入的信息，若没传入则从cookie中获取)
 * @param model 可选字符串，表示手机型号
 * @returns 返回手机型号，其中包括主要版本号和次要版本号
 */
export function getPhoneModel(model?: string): IphoneEdition {
    const phoneModel = model ?? getPhoneModelFromCookie();
    if (phoneModel) {
        const infos = phoneModel.split(',');
        if (infos[0] !== undefined) {
            return {
                main: +infos[0].replace(/\D/g, ''),
                sub: infos[1] ? +infos[1] : 0,
            };
        }
    }
    return {
        main: 0,
        sub: 0,
    };
}
/**
 * @description 判断iphone是否没有底部安全区域函数
 * @returns 若有底部安全区域则返回false，否则返回true
 */
function noSafeAreaInsetBottom() {
    if (!isInIOS()) {
        return false;
    }
    const { main, sub } = getPhoneModel();
    if (main > 0) {
        return (
            main < 10 ||
            (main === 10 && sub !== 3 && sub !== 6) ||
            (main === 12 && sub === 8)
        );
    }
    return false;
}

function getToolbarHeight() {
    return hasTransparentToolbar() ? 49 : 0;
}
/**
 * @description 获取iphone底部安全区域高度函数
 * @returns 若没有底部安全区域则返回0，否则返回34
 */
function getBottomSafeAreaHeight() {
    return noSafeAreaInsetBottom() ? 0 : 34;
}
/**
 * @description 定义各种型号iphone状态栏高度
 */
const IOSStatusBarDefaultHeightsMap = {
    '48': ['iPhone11,8', 'iPhone12,1'],
    '47': [
        'iPhone13,2',
        'iPhone13,3',
        'iPhone13,4',
        'iPhone14,2',
        'iPhone14,3',
        'iPhone14,4',
        'iPhone14,5',
    ],
    '44': [
        'iPhone10,3',
        'iPhone10,6',
        'iPhone11,2',
        'iPhone11,6',
        'iPhone11,4',
        'iPhone12,3',
        'iPhone12,5',
        'iPhone13,1',
    ],
    '30': [
        'iPhone7,1',
        'iPhone8,2',
        'iPhone9,2',
        'iPhone9,4',
        'iPhone10,2',
        'iPhone10,5',
    ],
};
/**
 * @description 通过value获取对象的key值函数
 * @param obj 传入需要获取key值的对象
 * @param value 传入value值
 * @returns 返回对应的key值
 */
function findKeyByValue(obj: Record<string, string[]>, value: string) {
    return Object.keys(obj).find((key) => obj[key].includes(value));
}
/**
 * @description 获取iphone状态栏高度
 * @returns 若在能获取到则返回高度值，获取不到返回20
 */
function getIOSDefaultStatusBarHeight() {
    return (
        findKeyByValue(
            IOSStatusBarDefaultHeightsMap,
            getPhoneModelFromCookie() ?? '',
        ) ?? '20'
    );
}
/**
 * @description 获取顶部导航栏高度函数
 * @returns 返回顶部导航栏高度
 */
function getTopAreaHeight() {
    // 兼容SSR
    if (!isBrowser) {
        return 0;
    }
    const { tbht, ftsfht, statusht } = uaGetInfo();
    let topGap = 0;
    if (tbht !== undefined || ftsfht !== undefined) {
        topGap = Number(ftsfht ?? tbht) / window.devicePixelRatio;
    }
    // 在直播间场景不需要statusht值
    else if (Number(statusht) !== 0 && !isAtLiveWebView()) {
        topGap = Number(statusht);
    }
    // iOS App老版本高度取不到的话走兜底写死逻辑
    else if (isAtLayoutType4()) {
        topGap = +getIOSDefaultStatusBarHeight();
    }
    return topGap;
}

let statusBarHeight = -1;
let bottomSafeHeight = -1;
const toolBarHeight = 0;
const bottomHeight = bottomSafeHeight;
/**
 * @description 获取顶部高度和底部安全区域高度
 */
function getAllBarValues() {
    statusBarHeight = getTopAreaHeight();
    // 没有透明底导，这里写死值0
    bottomSafeHeight = getBottomSafeAreaHeight();
}
/**
 * @description 获取高度信息函数，包括状态栏高度，操作栏高度，底部安全区域高度，操作栏高度加设备安全区高度
 * @returns 返回高度信息
 */
export function useBarInfo(): BarInfo {
    if (statusBarHeight < 0 || bottomSafeHeight < 0) {
        getAllBarValues();
    }
    return {
        statusBarHeight,
        toolBarHeight,
        bottomSafeHeight,
        bottomHeight,
    };
}
