import { defaultNavigator, isBrowser } from '../consts';

const KWAI_APP_TOKENS = ['kwai', 'ksthanos', 'ksnebula', 'ksgzone', 'livemate'];
const YODA_APP_TOKENS = ['yoda'];
export function useYodaBridge(userAgent: string) {
    return [...YODA_APP_TOKENS, ...KWAI_APP_TOKENS].some((token) =>
        userAgent.toLowerCase().includes(token),
    );
}

const KS_APP_LIST = [
    'Yoda',
    'Kwai',
    'Kwai_Lite',
    'Kwai_Pro',
    'ksthanos',
    'ksNebula',
    'ksnebula',
];
/**
 * @description 是否在webview环境
 * @param userAgent
 * @returns 在webview环境返回true，否则返回false
 */
export function isInWebview(userAgent = isBrowser ? navigator.userAgent : '') {
    return KS_APP_LIST.some((k) =>
        userAgent.toLowerCase().includes(k.toLowerCase()),
    );
}
/**
 * @description 是否在ios环境
 * @param userAgent
 * @returns 在ios环境返回true，否则返回false
 */
export function isInIOS(userAgent = isBrowser ? navigator.userAgent : '') {
    return /iPhone|iPad|iPod/i.test(userAgent);
}
/**
 * @description 是否在快手极速版环境
 * @param userAgent
 * @returns 在快手极速版环境返回true，否则返回false
 */
export function isInNebula(userAgent = isBrowser ? navigator.userAgent : '') {
    return /ksNebula/i.test(userAgent);
}
// const ua = navigator.userAgent;
const ua = defaultNavigator?.userAgent;

// TODO这货后面尽可能使用uaParser的信息使用
/**
 * @description 是否在ios环境
 * @returns 在ios环境返回true，否则返回false
 */
export function isIOS() {
    if (!ua) return false;
    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
    return !!/\(i[^;]+;( U;)? CPU.+Mac OS X/.exec(ua);
}
/**
 * @description 是否在android环境
 * @returns 在android环境返回true，否则返回false
 */
// TODO这货后面尽可能使用uaParser的信息使用
export function isAndroid() {
    if (!ua) return false;
    return ua.includes('Android') || ua?.includes('Adr');
}
/**
 * @description 是否在快手客户端环境
 * @returns 在快手环境返回true，否则返回false
 */
// 用cookie来判断isInKwai会包含isInNebula
export function isInKwai() {
    if (!ua) return false;
    return /(Kwai(_\w+)?|ksthanos)\//i.test(ua);
}
/**
 * @description 是否在企业微信环境
 * @returns 在企业微信环境返回true，否则返回false
 */
export function isInEnterpriseWeChat() {
    if (!ua) return false;
    // 企业微信
    return / wxwork\//i.test(ua);
}
/**
 * @description 是否在微信环境
 * @returns 在微信环境返回true，否则返回false
 */
export function isInWeChat() {
    if (!ua) return false;
    return /MicroMessenger/i.test(ua) && !isInEnterpriseWeChat();
}
/**
 * @description 是否在qq环境
 * @returns 在qq环境返回true，否则返回false
 */
export function isInQQ() {
    if (!ua) return false;
    return / QQ\//i.test(ua);
}
/**
 * @description 是否在qq空间环境
 * @returns 在qq空间环境返回true，否则返回false
 */
export function isInQzone() {
    if (!ua) return false;
    return /Qzone\//i.test(ua);
}
/**
 * @description 是否在安卓的微信环境
 * @returns 在安卓环境返回true，否则返回false
 */
export function isInAndroidWeChat() {
    return isAndroid() && isInWeChat();
}
/**
 * @description 是否在ios的微信环境
 * @returns 在ios的微信环境返回true，否则返回false
 */
export function isInIOSWeChat() {
    return isIOS() && isInWeChat();
}
/**
 * @description 是否在ios的qq环境
 * @returns 在ios的qq环境返回true，否则返回false
 */
export function isInIOSQQ() {
    return isIOS() && isInQQ();
}
/**
 * @description 是否在支付宝环境
 * @returns 在支付宝环境返回true，否则返回false
 */
export function isInAlipay() {
    if (!ua) return false;
    return /alipay/i.test(ua);
}
/**
 * @description 是否在安卓的支付宝环境
 * @returns 在安卓的支付宝环境返回true，否则返回false
 */
export function isInAndroidAlipay() {
    return isAndroid() && isInAlipay();
}

/**
 * @description 是否在微博环境
 * @returns 在微博环境返回true，否则返回false
 */
export function isInWeibo() {
    if (!ua) return false;
    return /Weibo/i.test(ua);
}
/**
 * @description 是否在百度客户端环境
 * @returns 在百度客户端环境返回true，否则返回false
 */
export function isInBaidu() {
    if (!ua) return false;
    // 百度手机客户端
    return / baiduboxapp\//i.test(ua);
}
/**
 * @description 是否在uc浏览器环境
 * @returns 在uc浏览器环境返回true，否则返回false
 */
export function isInUC() {
    if (!ua) return false;
    // UC浏览器
    return / UCBrowser\//i.test(ua);
}
/**
 * @description 获取所在浏览器环境
 * @returns 返回所在浏览器环境
 */
export function getBrowserDesc() {
    if (isInQQ()) {
        return 'qq';
    }
    if (isInWeChat()) {
        return 'wechat';
    }
    if (isInQzone()) {
        return 'qzone';
    }
    if (isInWeibo()) {
        return 'weibo';
    }
    if (isInBaidu()) {
        return 'baidu';
    }
    if (isInUC()) {
        return 'uc';
    }
    if (isIOS()) {
        return 'ios';
    }
    if (isAndroid()) {
        return 'android';
    }
    return '';
}
/**
 * @description 获取h5环境在端内还是端外环境函数
 * @returns 返回端内或端外或不知名平台
 */
export function getKpf() {
    if (isAndroid()) {
        return isInNebula() || isInKwai()
            ? 'ANDROID_PHONE_H5'
            : 'OUTSIDE_ANDROID_H5';
    }
    if (isIOS()) {
        return isInNebula() || isInKwai() ? 'IPHONE_H5' : 'OUTSIDE_IOS_H5';
    }
    return 'UNKNOWN_PLATFORM';
}

/**
 * @description 获取 ios 版本
 * @returns ios 版本
 */
export function getIOSVersion() {
    if (!ua) return false;
    if (!isIOS()) {
        return false;
    }
    const match = /OS (\d+)_(\d+)_?(\d+)?/.exec(ua);
    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
    if (!match || match.length < 3) {
        return false;
    }
    const version = parseFloat(
        // eslint-disable-next-line radix
        String(parseInt(match[1], 10) + 0.1 * +match[2]),
    );
    if (version > 0) {
        return version;
    }
    return false;
}
/**
 * @description 获取所在快手还是快手极速版
 * @returns 返回快手或快手极速版
 */
export function getKpn() {
    return isInNebula() ? 'NEBULA' : 'KUAISHOU';
}
/**
 * @description 比较版本大小
 * @param a 字符串表示版本信息
 * @param b 字符串表示版本信息
 * @returns a比b大则返回lt，小则返回gt，一样返回eq
 */
export function compareVersion(a: string, b: string) {
    let v1 = a.split('.').map((i) => +i);
    let v2 = b.split('.').map((i) => +i);
    const maxLen = Math.max(v1.length, v2.length);
    // 补零
    v1 = v1
        .concat(new Array(maxLen - v1.length).fill(0))
        .map((item) => Number(item));
    v2 = v2
        .concat(new Array(maxLen - v2.length).fill(0))
        .map((item) => Number(item));

    for (let i = 0; i < maxLen; i++) {
        if (v1[i] < v2[i]) {
            return 'lt';
        }
        if (v1[i] > v2[i]) {
            return 'gt';
        }
    }
    return 'eq';
}
