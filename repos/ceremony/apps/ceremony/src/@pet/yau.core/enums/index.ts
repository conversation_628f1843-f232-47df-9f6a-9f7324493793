import { assertNotNil } from '../asserts';
/**
 * @description 枚举对象的TypeScript定义
 */
export type EnumEntryOf<E, key extends keyof E> = key extends any
    ? [key: key, value: E[key]]
    : never;
/**
 * @description 获取过滤掉key为数字枚举对象的键值对数组
 * @param e 传入的枚举对象
 * @returns 返回过滤key为数字的键值对数组
 */
export function enumEntries<E extends object>(
    e: E,
): Array<EnumEntryOf<E, keyof E>> {
    const entries = Object.entries(e);
    const numericEnum = entries.some(([key, value]) =>
        Number.isInteger(Number(key)),
    );
    return (
        numericEnum
            ? entries.filter(([key, v]) => !Number.isInteger(Number(key)))
            : entries
    ) as any;
}
/**
 * @description 获取枚举对象的键函数
 * @param e 传入的枚举对象
 * @returns 返回枚举对象键组成的数组
 */
export function enumKeys<E extends object>(e: E): Array<keyof E> {
    return enumEntries(e).map(([key]) => key as any);
}
/**
 * @description 获取枚举对象的值函数
 * @param e 传入的枚举对象
 * @returns 返回枚举对象值组成的数组
 */
export function enumValues<E extends object>(e: E): Array<E[keyof E]> {
    return enumEntries(e).map(([key, value]) => value as any);
}
/**
 * @description 将给定的值解析为枚举对象的成员值函数
 * @param e 传入枚举对象
 * @param value 传入的给定值
 * @returns 若枚举对象中存在该值，返回对应的成员值，否则返回undefined
 */
export function tryParseEnum<E extends object>(
    e: E,
    value: number | string,
): E[keyof E] | undefined {
    const values = enumValues(e);
    if (values.includes(value as any)) {
        return value as any;
    }
    return undefined;
}
/**
 * @description 将给定的值解析为枚举对象成员值，为null则报错
 * @param e 传入枚举对象
 * @param value 传入的给定值
 * @returns 若枚举对象中存在该值，返回对应的成员值，若为null或者undefined抛出Error
 */
export function parseEnum<E extends object>(
    e: E,
    value: number | string,
): E[keyof E] {
    const result = tryParseEnum(e, value);
    assertNotNil(result, `Unknown enum type ${value}`);
    return result;
}
