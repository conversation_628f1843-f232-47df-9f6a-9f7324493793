/**
 * @description 判断传入的值是否为非null或非undefined
 * @param x 待判断的值
 * @typeParam T 待判断的值的TS类型
 * @returns 如果传入的值为null或undefined，返回true，否则为false
 */
export function isNotNil<T>(x: T): x is NonNullable<T> {
    // eslint-disable-next-line no-eq-null, eqeqeq
    return x != null;
}

/**
 * @description 断言-判断传入的值是否为非null或非undefined
 * @param v 待判断的值
 * @typeParam T 待判断的值的TS类型
 * @param message 断言失败提醒
 * @throws {@link isNotNil}为false时抛出Error
 */
export function assertNotNil<T>(
    v: T,
    message?: string,
): asserts v is NonNullable<T> {
    if (!isNotNil(v)) {
        throw new Error(message ?? 'Must not be null or undefined');
    }
}

/**
 * @description 判断传如的值是否为非null
 * @param x 待判断的值
 * @typeParam T 待判断的值的TS类型
 * @returns 如果传入的值为null，返回true，否则为false
 */
export function isNotNull<T>(x: T): x is Exclude<T, null> {
    return x !== null;
}

/**
 * @description 断言-判断传入的值是否为非null
 * @param v 待判断的值
 * @typeParam T 待判断的值的TS类型
 * @param message 断言失败提醒
 * @throws {@link isNotNull}为false时抛出Error
 */
export function assertNotNull<T>(
    v: T,
    message?: string,
): asserts v is Exclude<T, null> {
    if (!isNotNil(v)) {
        throw new Error(message ?? 'Must not be null');
    }
}

/**
 * @description 判断传如的值是否为非undefined
 * @param x 待判断的值
 * @typeParam T 待判断的值的TS类型
 * @returns 如果传入的值为undefined，返回true，否则为false
 */
export function isNotUndefined<T>(x: T): x is Exclude<T, undefined> {
    return x !== undefined;
}

/**
 * @description 断言-判断传入的值是否为非undefined
 * @param v 待判断的值
 * @typeParam T 待判断的值的TS类型
 * @param message 断言失败提醒
 * @throws {@link isNotUndefined}为false时抛出Error
 */
export function assertNotUndefined<T>(
    v: T,
    message?: string,
): asserts v is Exclude<T, null> {
    if (!isNotUndefined(v)) {
        throw new Error(message ?? 'Must not be undefined');
    }
}

/**
 * @description 断言函数
 * @param v 待判断的值
 * @param message 断言失败提醒
 * @throws 断言失败时抛出Error
 */
export function assert(v: unknown, message?: string): asserts v {
    if (!v) {
        throw new Error(message ?? 'Assertion failed');
    }
}

/**
 * @description 断言-判断一个值是否为never类型
 * @param _v 待判断的值
 * @param message 断言失败提醒
 * @throws 断言失败时抛出Error
 */
export function assertNever(_v: never, message?: string): never {
    throw new Error(message ?? 'Should not be reach');
}

/**
 * @description 断言失败函数
 * @param message 断言失败提醒
 * @throws 直接抛出Error
 */
export function assertFailed(message?: string): never {
    throw new Error(message ?? 'Should not be reach');
}
