import { onBeforeUnmount } from 'vue-demi';
import { isBrowser } from '../consts';

/**
 * @description 锁定页面hook，不可监听触摸事件及滚动
 * @returns 返回锁定页面和取消锁定页面函数组成的对象
 */
export function useLockPage() {
    let $lockDiv: HTMLDivElement | null = null;
    const lock = (preventStatusBar = true) => {
        if (!isBrowser || $lockDiv) {
            return;
        }

        $lockDiv = document.createElement('div');
        // 先不处理并发 lock page 且参数不一致了
        $lockDiv.style.cssText = `position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: ${
            preventStatusBar ? 999999 : 997
        }; touch-action: none;`;
        document.body.appendChild($lockDiv);
    };

    const unlock = () => {
        if (!isBrowser || !$lockDiv) {
            return;
        }

        document.body.removeChild($lockDiv);
        $lockDiv = null;
    };

    onBeforeUnmount(unlock);

    return {
        lock,
        unlock,
    };
}
