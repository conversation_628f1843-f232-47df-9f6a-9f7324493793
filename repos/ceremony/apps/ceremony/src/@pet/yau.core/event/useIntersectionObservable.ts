import { type Ref, onMounted, onBeforeUnmount } from 'vue-demi';
import { bindObserver, unbindObserver } from '../helper';

/**
 * @description 监听元素进入视口及离开视口hook
 * @param element 监听的元素
 * @param inCallback 元素进入视口执行的回调函数
 * @param outCallback 元素离开视口执行的回调函数
 * @param options 可选，IntersectionObserver的配置选项
 */
export function useIntersectionObservable(
    element: Ref<Element | null>,
    inCallback: () => void,
    outCallback?: () => void,
    options?: IntersectionObserverInit,
) {
    onMounted(() => {
        if (element.value) {
            bindObserver(element.value, inCallback, outCallback, options);
        }
    });

    onBeforeUnmount(() => {
        if (element.value) {
            unbindObserver(element.value);
        }
    });
}
