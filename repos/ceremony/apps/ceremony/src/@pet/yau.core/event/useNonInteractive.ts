import { onBeforeMount, onBeforeUnmount } from 'vue-demi';
import { useDynamicInterval } from '../interval/useDynamicInterval';
import { isBrowser } from '../consts';
import type { Ref } from 'vue-demi';
/**
 * @description 触摸事件触发后，开始定时执行回调函数
 * @param callback 需要执行的回调函数
 * @param options 定期执行的配置
 * @returns 重新开始定期执行函数，暂停定期执行，恢复调度器执行
 */
export function useNonInteractive(
    callback: () => void,
    options: { interval: Ref<number> },
) {
    const { pause, resume, restart } = useDynamicInterval(callback, {
        interval: options.interval,
    });

    onBeforeMount(() => {
        // 兼容SSR
        if (!isBrowser) {
            return;
        }
        window.addEventListener('touchstart', restart, { capture: true });
    });

    onBeforeUnmount(() => {
        pause();
        // 兼容SSR
        if (!isBrowser) {
            return;
        }
        window.removeEventListener('touchstart', restart, { capture: true });
    });

    return {
        restart,
        pause,
        resume,
    };
}
