/* eslint-disable @typescript-eslint/ban-types */
/**
 * @description 雷达自定义事件参数TypeScript定义
 */
export interface EventDimension {
    name: string;
    category?: string | string[];
    event_type?: string | string[];
    src?: string;
    result_type?: string;
    message?: string;
    extra_info?: object | string;
    yoda_version?: string;
    webview_type?: string;
}
/**
 * @description 雷达自定义事件TypeScript定义
 */
export interface sendCustomCallback {
    (dimension: EventDimension, options?: {}): void;
}
/**
 * @description 探测是否支持vue3
 * @returns 如果支持vue3，返回true，否则为false
 */
export function checkSupportEcmaScriptProxy() {
    let supportProxy;
    try {
        // eslint-disable-next-line no-new
        new Proxy({}, {});
        supportProxy = true;
    } catch (e) {
        supportProxy = false;
    }
    return supportProxy;
}

/**
 * @description 业务自定义上报vue3支持情况
 * @param radarCustomEvent 雷达自定义上报事件
 * @typeParam sendCustomCallback 雷达上报事件的TS类型
 */
export function sendSupportProxy(radarCustomEvent: sendCustomCallback) {
    radarCustomEvent({
        name: 'CHECK_SUPPORT_ECMASCRIPT_PROXY',
        result_type: checkSupportEcmaScriptProxy().toString(),
    });
}

/**
 * @description 检测浏览器是否支持ESM
 * @returns 若支持ESM，返回true，否则返回false
 */
export const isESModuleSupported = () => {
    let support;
    try {
        const script = document.createElement('script');
        support = 'noModule' in script && typeof script.type === 'string';
    } catch (e) {
        support = false;
    }
    return support;
};

/**
 * @description 业务自定义上报ESM的支持情况
 * @param radarCustomEvent 雷达自定义上报事件
 * @typeParam sendCustomCallback 雷达上报事件的TS类型
 */
export const sendSupportESM = (radarCustomEvent: sendCustomCallback) => {
    radarCustomEvent({
        name: 'CHECK_BROWSER_SUPPORT_ESMODULE',
        result_type: isESModuleSupported().toString(),
    });
};
