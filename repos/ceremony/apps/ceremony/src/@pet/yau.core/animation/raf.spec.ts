import { expect, test, describe } from 'vitest';
import { raf, cancelRaf } from './raf';

describe('test raf and cancelRaf', () => {
    test('raf', async () => {
        const getTimes = () => {
            return new Promise((resolve, reject) => {
                let n = 5;
                let times = 0;
                const countFn = () => {
                    // console.log('n=', n);
                    if (n === 0) {
                        resolve(times);
                        return;
                    }
                    times++;
                    n--;
                    raf(countFn);
                };
                countFn();
            });
        };
        const result = await getTimes();
        expect(result).toBe(5);
    });
    test('cancelRaf', async () => {
        let times = 0;
        const getTimes = () => {
            return new Promise<void>((resolve, reject) => {
                let id = 0;
                const countFn = () => {
                    // console.log('times=', times);
                    times++;
                    id = raf(countFn);
                };
                countFn();
                const timerId = setInterval(() => {
                    if (times > 5) {
                        cancelRaf(id);
                        resolve();
                        clearInterval(timerId);
                    }
                }, 100);
            });
        };
        await getTimes();
        expect(times).toBeGreaterThan(5);
    });
});
