import { isBrowser } from '../consts';

let prev = Date.now();
function rafPolyfill(fn: FrameRequestCallback): number {
    const curr = Date.now();
    const ms = Math.max(0, 16 - (curr - prev));
    const id = setTimeout(fn, ms);
    prev = curr + ms;
    return id;
}

/**
 * @description requestAnimationFrame的兼容性函数（对于不支持requestAnimationFrame的浏览器，polyfill使用setTimeout）
 * @param fn 回调函数（下一次重绘之前更新动画帧所调用的函数）
 * @returns requestAnimationFrame调用后返回值，一个long整数，请求ID，是回调列表中唯一的标识
 */
export function raf(fn: FrameRequestCallback) {
    // 兼容SSR
    if (!isBrowser) {
        return rafPolyfill(fn);
    }
    const requestAnimationFrame = window.requestAnimationFrame ?? rafPolyfill;
    return requestAnimationFrame.call(window, fn);
}

/**
 * @description cancelAnimationFrame的兼容性函数（对于不支持cancelAnimationFrame的浏览器，polyfill使用clearTimeout）
 * @param id requestAnimationFrame返回值ID
 */
export function cancelRaf(id: number) {
    // 兼容SSR
    if (!isBrowser) {
        clearTimeout?.(id);
    }
    const cancelAnimationFrame =
        window.cancelAnimationFrame ?? window.clearTimeout;
    cancelAnimationFrame.call(window, id);
}
