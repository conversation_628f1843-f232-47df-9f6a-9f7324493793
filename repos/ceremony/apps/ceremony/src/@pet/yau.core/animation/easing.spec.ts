import { expect, test, describe } from 'vitest';
import { easing } from './easing';

describe('test easing', () => {
    test('linear', () => {
        expect(easing.linear(10)).toBe(10);
    });
    test('ease', () => {
        const t = 0.1;
        const n = 0.5 * (1 - Math.cos(Math.PI * t));
        expect(easing.ease(t)).toBe(n);
    });
    test('easeInQuad', () => {
        const t = 0.1;
        const n = t * t;
        expect(easing.easeInQuad(t)).toBe(n);
    });
    test('easeOutQuad', () => {
        const t = 0.1;
        const n = t * (2 - t);
        expect(easing.easeOutQuad(t)).toBe(n);
    });
    test('easeInOutQuad', () => {
        const t = 0.1;
        const n = t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        expect(easing.easeInOutQuad(t)).toBe(n);
    });
    test('easeInCubic', () => {
        const t = 0.1;
        const n = t * t * t;
        expect(easing.easeInCubic(t)).toBe(n);
    });
    test('easeOutCubic', () => {
        let t = 0.1;
        const n = --t * t * t + 1;
        expect(easing.easeOutCubic(0.1)).toBe(n);
    });
    test('easeInOutCubic', () => {
        const t = 0.1;
        const n =
            t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
        expect(easing.easeInOutCubic(t)).toBe(n);
    });
    test('easeInQuart', () => {
        const t = 0.1;
        const n = t * t * t * t;
        expect(easing.easeInQuart(t)).toBe(n);
    });
    test('easeOutQuart', () => {
        let t = 0.1;
        const n = 1 - --t * t * t * t;
        expect(easing.easeOutQuart(0.1)).toBe(n);
    });
    test('easeInOutQuart', () => {
        let t = 0.1;
        const n = t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;
        expect(easing.easeInOutQuart(0.1)).toBe(n);
    });
    test('easeInQuint', () => {
        const t = 0.1;
        const n = t * t * t * t * t;
        expect(easing.easeInQuint(t)).toBe(n);
    });
    test('easeOutQuint', () => {
        let t = 0.1;
        const n = 1 + --t * t * t * t * t;
        expect(easing.easeOutQuint(0.1)).toBe(n);
    });
    test('easeInOutQuint', () => {
        let t = 0.1;
        const n =
            t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;
        expect(easing.easeInOutQuint(0.1)).toBe(n);
    });
});
