import 'url-polyfill';

/**
 * 给指定的图片链接添加格式化后缀 x-oss-process=image/format,webp；x-oss-process=image/format,avif
 * @param {string} imageUrl - 图片链接
 * @param {string} format - 图片格式
 * @returns {string} 新的图片链接
 */
function addFormatSuffix(imageUrl: string, format: 'webp' | 'avif') {
    try {
        const url = new URL(imageUrl);
        url.searchParams.set('x-oss-process', `image/format,${format}`);
        return url.toString();
    } catch (error) {
        return imageUrl;
    }
}

/**
 * 给指定的图片链接添加格式化后缀 x-oss-process=image/format,webp；x-oss-process=image/format,avif
 * @param {string} imageUrl - 图片链接
 * @returns {string} 新的图片链接
 */
export function convertImageUrlToWebpSuffix(imageUrl: string) {
    // 定义支持的图片格式
    const supportedFormats = ['jpg', 'jpeg', 'png'];

    // 获取图片链接的扩展名
    const fileExtension = imageUrl?.split('.')?.pop()?.toLowerCase();

    // 检查扩展名是否在支持的格式中
    if (fileExtension && supportedFormats.includes(fileExtension)) {
        if (document.cookie.includes('support_webp=true')) {
            // 如果浏览器支持webp格式，则将图片链接转换为webp格式
            return addFormatSuffix(imageUrl, 'webp');
        }
    }

    // 如果不是支持的格式,直接返回原始链接
    return imageUrl;
}
