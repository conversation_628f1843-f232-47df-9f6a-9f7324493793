# `useTab` Hook 文档

## 功能概述
`useTab` 是一个 Vue Composition API Hook，用于管理 Tab 组件的状态。它提供了设置默认选中的 Tab 以及切换 Tab 的功能。

## 属性和方法

### 属性
- **activeTabIdx**: 
  - 类型: `number`
  - 描述: 当前选中的 Tab 索引。
  - 默认值: `0`

### 方法
- **changeTab(idx: number)**:
  - 参数: 
    - `idx`: 要切换到的 Tab 索引。
  - 描述: 切换当前选中的 Tab 到指定索引。

## 使用示例

```vue
<template>
  <div>
    <button v-for=\"(tab, index) in tabs\" :key=\"index\" @click=\"changeTab(index)\">
      {{ tab }}
    </button>
    <div v-if=\"activeTabIdx === 0\">
      内容1
    </div>
    <div v-if=\"activeTabIdx === 1\">
      内容2
    </div>
    <div v-if=\"activeTabIdx === 2\">
      内容3
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import useTab from '@pet/ones-use.use-tab';

const tabs = ['Tab 1', 'Tab 2', 'Tab 3'];
const { activeTabIdx, changeTab } = useTab(0);
</script>
```

## 注意事项
- `activeTabIdx` 的初始值可以通过 `defaultVal` 参数进行设置，默认为 `0`。
- `changeTab` 方法会直接修改 `activeTabIdx` 的值，因此在使用时需要注意避免不必要的重新渲染。

## 依赖项
- `vue` (具体版本请参考项目依赖)