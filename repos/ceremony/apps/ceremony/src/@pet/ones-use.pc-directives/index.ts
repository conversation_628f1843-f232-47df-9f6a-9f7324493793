import { isYodaPCContainer } from './utils/index';
import { scrollMounted, scrollUpdated, scrollUnmounted } from './scroll/index';
import { interceptMounted } from './intercept';
import { hideMounted } from './hide';
import type { DirectiveBinding } from 'vue';

enum DirectivesType {
    scroll = 'scroll', // 滚动模式主要是横向滚动
    intercept = 'intercept', // 拦截模式
    hide = 'hide', // 隐藏模式
}

const pcDirectives = {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
        const { arg } = binding;
        if (!isYodaPCContainer) {
            return;
        }
        if (arg === DirectivesType.scroll) {
            scrollMounted(el, binding);
        } else if (arg === DirectivesType.intercept) {
            hideMounted(el, binding);
        } else if (arg === DirectivesType.hide) {
            interceptMounted(el, binding);
        }
    },
    updated(el: any, binding: DirectiveBinding) {
        const { arg } = binding;
        if (arg === DirectivesType.scroll) {
            scrollUpdated(el);
        }
    },
    unmounted(el: any, binding: DirectiveBinding) {
        const { arg } = binding;
        if (arg === DirectivesType.scroll) {
            scrollUnmounted(el);
        }
    },
};

export default pcDirectives;
