import { isYodaPCContainer as isYodaPC } from '@alive-ui/system';
import { Report, query } from '@alive-ui/actions';

const isENV = process.env.NODE_ENV === 'development';
const urlIsYodaPC = query.isYodaPC;
const layoutProperties = [
    'offsetTop',
    'offsetLeft',
    'offsetParent',
    'scrollTop',
    'scrollLeft',
    'scrollWidth',
    'scrollHeight',
    'clientWidth',
    'clientHeight',
    'offsetWidth',
    'offsetHeight',
];
export const getScrollElementProperties = (el: HTMLElement) => {
    const rect = el.getBoundingClientRect();
    const properties: { [key: string]: any } = {};
    layoutProperties.forEach((property) => {
        try {
            properties[property] = el[property as keyof HTMLElement];
        } catch (error) {
            Report.biz.error('pcScroll更换属性失败', { error });
        }
    });

    return {
        ...properties,
        getBoundingClientRect: () => rect,
    };
};

export const isYodaPCContainer = isENV ? urlIsYodaPC : isYodaPC;
