// directives/scroll.ts
import BScroll from '@better-scroll/core';
import { getScrollElementProperties, isYodaPCContainer } from '../utils/index';
import { applyContainerStyles, applyContentStyles } from './style';
import type { DirectiveBinding } from 'vue';

const initializeBScroll = (el: HTMLElement, binding: DirectiveBinding) => {
    // 保存原有scrollLeft
    const tempLeft = el.scrollLeft;
    el.scrollLeft = 0;
    const bs = new BScroll(el, {
        probeType: 3,
        scrollX: true,
        freeScroll: true,
        startX: -tempLeft,
    });

    // 监听滚动事件，将滚动位置信息传递给绑定的回调
    bs.on('scroll', (pos: { x: number; y: number }) => {
        binding.value?.({
            target: {
                ...getScrollElementProperties(el),
                scrollLeft: -pos.x,
                scrollTop: -pos.y,
            },
        });
    });

    return bs;
};
const setupScrollContent = (el: HTMLElement) => {
    // 设置el的样式
    applyContainerStyles(el);
    // 创建scrollContent内容
    const scrollContent = document.createElement('div');
    scrollContent.classList.add('scroll-content');
    applyContentStyles(scrollContent);

    // 将原有的子节点转移到 scrollContent 容器中
    Array.from(el.childNodes).forEach((node) =>
        scrollContent.appendChild(node),
    );
    el.appendChild(scrollContent);

    return scrollContent;
};

export const scrollMounted = (el: HTMLElement, binding: DirectiveBinding) => {
    // 设置el的dom内容
    setupScrollContent(el);

    // 延迟初始化
    setTimeout(() => {
        const bs = initializeBScroll(el, binding);
        (el as any).__bs = bs; // 保存 BScroll 实例，供解绑时销毁
    }, 800);
};

export const scrollUpdated = (el: any) => {
    const bs = el.__bs;
    if (bs) {
        bs.refresh();
    }
};
export const scrollUnmounted = (el: any) => {
    const bs = el.__bs;
    if (bs) {
        bs.destroy();
    }
};
const pcScrollDirective = {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
        if (!isYodaPCContainer) {
            return;
        }
        scrollMounted(el, binding);
    },
    updated(el: any, binding: DirectiveBinding) {
        scrollUpdated(el);
    },
    unmounted(el: any) {
        scrollUnmounted(el);
    },
};

export default pcScrollDirective;
