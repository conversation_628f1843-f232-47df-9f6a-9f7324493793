import { Toast } from '@lux/sharp-ui-next';
import { isYodaPCContainer } from '../utils/index';
import type { DirectiveBinding } from 'vue';
export const interceptMounted = (
    el: HTMLElement,
    binding: DirectiveBinding,
) => {
    const cloneEl: any = el.cloneNode(true);
    const { value } = binding;

    el.parentNode?.insertBefore(cloneEl, el);
    el.remove();
    cloneEl.onclick = (e: {
        preventDefault: () => void;
        stopPropagation: () => void;
    }) => {
        Toast.info(value || 'PC端不支持该功能');
        e.preventDefault();
        e.stopPropagation();
        return;
    };
};

const pcIntercept = {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
        if (!isYodaPCContainer) {
            return;
        }
        interceptMounted(el, binding);
    },
};
export default pcIntercept;
