import { isYodaPCContainer } from '../utils/index';
import type { DirectiveBinding } from 'vue';
export const hideMounted = (el: HTMLElement, binding: DirectiveBinding) => {
    el.style.visibility = 'hidden';
    if (binding.value?.destroy) {
        el.parentNode?.removeChild(el);
    }
};
const pcHide = {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
        if (!isYodaPCContainer) {
            return;
        }
        hideMounted(el, binding);
    },
};

export default pcHide;
