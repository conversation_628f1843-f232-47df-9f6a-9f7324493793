/* eslint-disable max-lines-per-function */
import { getCurrentInstance, ref, computed, watch } from 'vue';
import { defineStore, storeToRefs } from 'pinia';
import useKConf from '@pet/ones-use.useKconfBatch';
import { getQuery, isOutLiveRoom } from '@alive-ui/actions';
import { getStageStatus } from '../services/stage-status';
import { stageTypeKey2NavTab } from '@/utils/stageType';
enum SourceEntrance {
    bChampion = 'champion-b',
    aChampion = 'champion-a',
}
const getStageType = () => getQuery('stageType') || 0;
export default defineStore('fixed-bottom-nav-store', () => {
    // 相关变量初始值
    const stageType = getStageType();
    const { kconfData } = storeToRefs(useKConf());
    const { proxy } = getCurrentInstance() as any;
    const currentUrlStageType = ref(stageType);
    const currentActive = ref(0);
    const path = ref<string>(proxy?.$route.path);
    const pathShowNav = ref<boolean>(!!proxy.$route?.meta?.showNav);
    // 计算值
    const toTabsConfig = (params: any = {}) => {
        const { arr = [], config = {}, stageMap } = params;
        const result: any = {};
        arr.forEach((item: any) => {
            const { label, ...rest } = config[item] || {};
            result[item] = {
                ...rest,
                label:
                    (label ? label : stageMap[currentUrlStageType.value]) || '',
            };
        });
        return result;
    };
    const config = computed(() => {
        let result: any;
        const tabs = kconfData.value.common?.fixedBottomConfig?.tabs || {};
        const __config =
            kconfData.value.common?.fixedBottomConfig?.config || {};
        const stageMap = stageTypeKey2NavTab(
            kconfData.value.common?.stageTypeList,
        );
        const competitionGroup =
            kconfData.value.common?.fixedBottomConfig?.competitionGroup || {};
        const outLiveRoomDel =
            kconfData.value.common?.fixedBottomConfig?.outLiveRoomDel || [];
        const publicData = {
            config: __config,
            stageMap,
        };
        const routerName = proxy.$route.name || '';
        const sourceEntrance = getQuery('sourceEntrance');
        // 挂榜特殊处理，进入挂榜页一定要携带sourceEntrance参数，并指明是到a还是b赛（背景： 通过挂件进入的事1/3tab， 2tab到底应该显示什么？）
        if (
            sourceEntrance === SourceEntrance.aChampion ||
            sourceEntrance === SourceEntrance.bChampion ||
            routerName.includes('champion')
        ) {
            result = toTabsConfig({
                arr:
                    sourceEntrance === SourceEntrance.aChampion
                        ? tabs.aChampion
                        : tabs.bChampion,
                ...publicData,
            });
        } else {
            // 遍历分组并获取tab信息
            for (const key of Object.keys(competitionGroup)) {
                const everyKeys =
                    competitionGroup[key as keyof typeof competitionGroup];
                if (
                    everyKeys.length &&
                    everyKeys.includes(+currentUrlStageType.value)
                ) {
                    result = toTabsConfig({
                        arr: tabs[key] || [],
                        ...publicData,
                    });
                    break;
                }
            }
        }
        // 间外删除内容
        if (isOutLiveRoom) {
            outLiveRoomDel?.forEach((item: string | number) => {
                if (result?.[item]) {
                    delete result[item];
                }
            });
        }
        if (!pathShowNav.value) {
            return;
        }
        return { ...(result || {}) };
    });
    const pathMap = computed(() => {
        if (config.value) {
            return Object.keys(config.value).map((__path) => __path);
        }
        return null;
    });
    const labelMap = computed(() => {
        if (config.value) {
            return Object.keys(config.value).map(
                (__path) => config?.value?.[__path]?.label,
            );
        }
        return null;
    });
    watch(
        () => config.value,
        () => {
            if (config.value && pathMap.value) {
                const index = pathMap.value?.findIndex((item: any) => {
                    return item === path.value.replace('/', '');
                });
                if (index > -1 && index !== currentActive.value) {
                    currentActive.value = index;
                }
            }
        },
        {
            immediate: true,
        },
    );
    // 路径修改需要重新请求下stageType
    watch(
        () => proxy.$route.path,
        (val, old) => {
            if (old && val && val !== old) {
                currentUrlStageType.value = getStageType();
                getStageTypeInfo();
            }
        },
    );
    watch(
        () => [proxy.$route.path, pathMap.value],
        ([val, __pathMap]) => {
            path.value = val;
            pathShowNav.value = proxy.$route?.meta?.showNav;
            const __stageType = getStageType();
            if (__stageType) {
                currentUrlStageType.value = __stageType;
            }
            if (__pathMap) {
                const index = __pathMap.findIndex((item: any) => {
                    return item === val.replace('/', '');
                });
                if (index > -1 && currentActive.value !== index) {
                    currentActive.value = index;
                }
            }
        },
        {
            immediate: true,
        },
    );
    function changeCurrent(index: number) {
        currentActive.value = index;
        proxy?.$router?.replace({
            name: pathMap.value?.[currentActive.value],
        });
    }
    // 如果url里没有携带当前的stageType值，则要走接口拉取当前的进行中的stageType，补充到链接里
    async function getStageTypeInfo() {
        const showNav = proxy.$route?.meta?.showNav;
        if (!currentUrlStageType.value && showNav) {
            try {
                const res = await getStageStatus();
                currentUrlStageType.value = +res?.stageType;
                proxy.$router.replace({
                    query: {
                        stageType: currentUrlStageType.value,
                    },
                });
            } catch (error) {
                console.log('error', error);
            }
        }
    }
    return {
        config,
        labelMap,
        currentActive,
        currentUrlStageType,
        changeCurrent,
        getStageTypeInfo,
    };
});
