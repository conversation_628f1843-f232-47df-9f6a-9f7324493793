# 固定底部导航组件文档

## 功能概述

固定底部导航组件 (`@pet/ones-ui.fixed-bottom`) 是一个用于在页面底部显示导航栏的 Vue 组件。该组件根据不同的赛程（stage）、赛段（schedule）和赛道（lane）动态显示不同的导航项，并支持响应式布局和动画效果。

## 属性和方法

### 属性

**无**

## 使用示例

```vue
<template>
  <div>
    <FixedBottom />
  </div>
</template>

<script lang=\"ts\" setup>
import FixedBottom from '@pet/ones-ui.fixed-bottom';
</script>
```

## 注意事项

- 组件依赖于 `pinia` 等第三方包，确保这些模块已正确安装和配置。
- 组件会根据当前赛程、赛段和赛道的信息动态显示导航项，如果这些信息为空或不符合条件，导航栏将不会显示。
- 组件支持响应式布局和动画效果，确保在不同设备上都能正常显示。

## 依赖项

- `pinia`
- `@alive-ui/base`
- `@alive-ui/actions`