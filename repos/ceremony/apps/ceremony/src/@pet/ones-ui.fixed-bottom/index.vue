<template>
    <Nav
        v-if="showNav"
        :class="classes['fixed-bottom']"
        :active-index="navStore.currentActive"
        @change="modCtx.changeByIdxFn"
    >
        <NavItem
            v-for="(label, index) in labelMap"
            :key="index"
            :class="{
                'nav-item-class': true,
                [classes['nav-item-' + labelMap?.length]]:
                    labelMap && labelMap?.length > 1,
                [classes['padding-left-20']]:
                    labelMap?.length === 2 && currentActive === 0,
                [classes['padding-right-20']]:
                    labelMap?.length === 2 && currentActive === 1,
                [classes['nav-item-active']]: currentActive === index,
                [classes['nav-item-confirm']]: currentActive === index,
                [classes['nav-item-active-' + labelMap?.length]]:
                    currentActive === index,
                [classes['gis-nav-active-animate-' + labelMap?.length]]:
                    currentActive === index && !isLowDevice(),
            }"
        >
            <span :class="{ [classes['font-scale']]: currentActive === index }">
                {{ label }}
            </span>
        </NavItem>
    </Nav>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { ModTabService, type ModTabContext } from '@pet/ones-use.onekittmp/mod';
import { Nav as ANav } from '@alive-ui/base';
import { isLowDevice } from '@alive-ui/actions';
import usNavStore from './models/index';
import useHomeStore from '@/modules/main/models/index-home';

// const BREAK_STAGE_TYPE = 120;
const { indexInfoData } = storeToRefs(useHomeStore());

const { Nav, NavItem } = ANav;
const navStore = usNavStore();
navStore.getStageTypeInfo();
const { currentActive, labelMap } = storeToRefs(navStore);

const modCtx: ModTabContext = {
    changeFn: (idx: number) => navStore.changeCurrent(idx),
};
ModTabService(modCtx);

const showNav = computed(() => {
    // const { config, labelMap, currentUrlStageType } = navStore;
    // 休赛不展示底部导航，通过接口返回字段判断即可，没有独立的stageType
    if (indexInfoData?.value?.offseason) {
        return false;
    }
    return (
        navStore.config &&
        navStore.labelMap &&
        navStore.labelMap.length > 1 &&
        !!navStore.currentUrlStageType
    );
});
</script>

<style lang="less" module="classes">
.fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
    width: 100%;
    will-change: transform;
    left: 50%;
    transform: translateX(-50%);
    animation: navFadeIn 0.2s ease-in-out forwards;
    .nav-item-2 {
        width: 207px;
        height: 40px;
    }

    .padding-right-20 {
        &:nth-of-type(1) {
            padding-right: 28px;
        }
    }

    .padding-left-20 {
        &:nth-of-type(2) {
            padding-left: 28px;
        }
    }

    .nav-item-3 {
        width: 127px;
        height: 40px;
    }

    .font-scale {
        font-size: 22px;
    }

    .nav-item-active-3 {
        width: 160px;
    }

    .gis-nav-active-animate-3 {
        width: 160px;
        background: url('./assets/nav-active-animate-2_2x.min.png') center /
            100% no-repeat;
    }

    .gis-nav-active-animate-2 {
        width: 160px;
        padding-bottom: 17px;
        background: url('./assets/nav-active-animate-2_2x.min.png') center /
            100% no-repeat;
    }

    // nav-item-confirm 为了提高选择器优先级
    .nav-item-confirm.nav-item-active {
        height: 90px;
        padding-bottom: 24px;
    }
}
</style>
<style lang="less" scoped>
// .nav-item-class {
//     :deep(.card-font) {
//         font-family: zaozi-zhehei;
//     }
// }

@keyframes navFadeIn {
    0% {
        opacity: 0;
        /* 初始不透明度 */
    }

    100% {
        opacity: 1;
        /* 完全透明 */
    }
}
</style>
