import { request, liveStreamId, activityBiz } from '@alive-ui/actions';

export interface StageStatusPostResponse {
    stageType: number;
    navigatorName: string;
    realStageType?: number;
}

// 首页赛程数据：https://kdev.corp.kuaishou.com/web/api-mock/repo/mgr?activeKey=group&apiId=41020&version=-1
export const getStageStatus = async (subBiz?: string) => {
    const PATH = '/webapi/live/revenue/operation/activity/complex/stageStatus';
    const res = await request.post<StageStatusPostResponse>(PATH, {
        subBiz,
        activityBiz,
        liveStreamId,
    });

    return res?.data;
};
