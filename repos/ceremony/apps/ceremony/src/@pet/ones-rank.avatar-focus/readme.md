### 用户头像关注组件

-   头像
-   排名
-   +号关注

## 功能描述

该组件用于展示用户的头像，支持显示排名和直播状态。可以根据不同的需求配置头像的大小、是否显示排名和直播图标等。

## Props

| 属性名          | 类型                     | 默认值 | 描述                                     |
|-----------------|--------------------------|--------|------------------------------------------|
| `item`          | `ItemSchema`            | -      | 用户信息，包括头像、ID、直播状态等     |
| `avatarSize`    | `'lg' | '2xs' | 'xs' | 'sm'` | `'sm'` | 头像尺寸，支持大、中、小等不同尺寸     |
| `needTopIndex`  | `boolean`                | `true` | 是否在头像顶部展示排名                  |
| `needTopLive`   | `boolean`                | `false`| 是否在头像顶部展示直播图标              |
| `logParams`     | `{ schedule_id?: number; rank_id?: number; }` | - | 埋点              |

## 使用方式

```vue
<template>
  <Avatar
    :item="userItem"
    :avatarSize="'lg'"
    :needTopIndex="true"
    :needTopLive="true"
    :logParams="{ schedule_id: 123, rank_id: 456 }"
  >
    <template #bottom>
      <div>自定义底部内容</div>
    </template>
    <template #bottomInner>
      <div>自定义底部内部内容</div>
    </template>
  </Avatar>
</template>

<script setup>
import Avatar from './path/to/Avatar.vue';

const userItem = {
  itemId: 'user123',
  headUrl: 'https://example.com/avatar.jpg',
  liveStreamIdList: ['stream1', 'stream2'],
  liveStreamId: 'stream1',
  mysteryMan: false,
  h5RankShowIndex: 1,
  followStatus: false,
  activityId: 'activity123'
};
</script>
```
### Mock 数据

```javascript
const userItem = {
  itemId: 'user123',
  headUrl: 'https://example.com/avatar.jpg',
  liveStreamIdList: ['stream1', 'stream2'],
  liveStreamId: 'stream1',
  mysteryMan: false,
  h5RankShowIndex: 1, // 显示的排名
  followStatus: false, // 关注状态
  activityId: 'activity123' // 活动ID
};

```
