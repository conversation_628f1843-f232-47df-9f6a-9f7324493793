<script lang="ts" setup>
import FocusIcon from '@pet/ones-rank.add-focus/index.vue';
import { Avatar as APAvatar } from '@alive-ui/pro';
import { activityBiz } from '@alive-ui/actions';
import LiveIcon from './assets/top-live-icon_2x.png';
import type { ItemSchema } from '@pet/ones-rank.base-item/schema';
interface AvatarSchema {
    item: ItemSchema;
    avatarSize?: 'lg' | '2xs' | 'xs' | 'sm' | 'xl';
    needTopIndex?: boolean; // 头像顶部是否需要展示排名
    needTopLive?: boolean; // 头像顶部是否需要展示直播icon
    needFrame?: boolean; // 是否需要自定义头像框
    index?: number;
    logParams?: {
        schedule_id?: number | string;
        rank_id?: number | string;
    };
}
const props = withDefaults(defineProps<AvatarSchema>(), {
    avatarSize: 'sm',
    needTopIndex: true,
    needTopLive: false,
    needFrame: false,
    index: 0,
});

const emit = defineEmits(['success', 'fail']);

const onSuccess = () => {
    emit('success', props.index);
};
const onFail = () => {
    emit('fail', props.index);
};
</script>

<template>
    <APAvatar
        class="margin-auto"
        :user-id="item.itemId"
        :head-url="item.headUrl"
        :live-stream-id-list="item.liveStreamIdList"
        :live-stream-id="item.liveStreamId"
        :is-mystery-man="item.mysteryMan"
        :size="avatarSize"
        :extra-info="{
            activity_name: activityBiz,
        }"
    >
        <template v-if="props.needFrame" #frame>
            <slot name="frame"></slot>
        </template>
        <template #topInner>
            <div
                v-if="item.h5RankShowIndex && needTopIndex"
                class="top-live-icon-num text-din text-0 a-text-highlight text-9px flex-center-center"
            >
                {{ item.h5RankShowIndex.padStart(2, '0') }}
            </div>
            <img
                v-if="needTopLive && item?.liveStreamId"
                :src="LiveIcon"
                style="width: 100%; height: 100%"
            />
        </template>
        <template #bottom>
            <slot name="bottom">
                <FocusIcon
                    v-pcDirectives:hide
                    v-click-log="{
                        action: 'OP_ACTIVITY_RANK_LIST_ITEM',
                        params: {
                            is_follow: item.followStatus,
                            rank_show_index: item.h5RankShowIndex,
                            target_user_id: item.itemId,
                            target_live_stream_id: item.liveStreamId,
                            click_obj: 'FOLLOW_BTN',
                            ...logParams,
                        },
                    }"
                    :focus-params="{
                        userId: item.itemId as unknown as string,
                        activityId: item?.activityId,
                    }"
                    :follow-status="item.followStatus"
                    @success="onSuccess"
                    @fail="onFail"
                />
            </slot>
        </template>
        <template #bottomInner>
            <slot name="bottomInner" />
        </template>
        <template #bottomRight>
            <span></span>
        </template>
    </APAvatar>
</template>

<style lang="less" scoped>
.top-live-icon-num {
    width: 100%;
    height: 100%;
    background: url('./assets/top-live-icon-num_2x.png') center / 100% no-repeat;
}
</style>
