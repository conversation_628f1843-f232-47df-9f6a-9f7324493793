<template>
    <BackStatic
        v-if="showBackButton"
        class="back-btn"
        @click="handleBackClick"
    />
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router';
import { watch, ref } from 'vue';
import { BackStatic } from '@alive-ui/icon';
import { exitWebView, getQuery } from '@alive-ui/actions';

// 返回
const router = useRouter();
const route = useRoute();
// 处理返回逻辑
// 有showFirstBackBtn参数表示为二级页
// layoutType=4认为是间外
/**
 * 一级页返回按钮展示逻辑
 * 间内不展示
 * 间外展示，点击exit
 *
 * 二级页返回按钮展示逻辑
 * 固定展示，点击back
 */
const useBackButtonLogic = () => {
    const showFirstBackBtn = getQuery('showFirstBackBtn');
    const layoutType = getQuery('layoutType');

    let shouldShowBackButton = false;
    let handleBack = () => {
        router.back();
    };
    if (showFirstBackBtn === 'true') {
        shouldShowBackButton = true;
    } else if (layoutType === '4') {
        shouldShowBackButton = true;

        handleBack = () => {
            exitWebView();
        };
    }

    return { shouldShowBackButton, handleBack };
};
const showBackButton = ref(false);
let handleBackClick = () => {};

const init = () => {
    // 使用返回按钮逻辑
    const { shouldShowBackButton, handleBack } = useBackButtonLogic();
    showBackButton.value = shouldShowBackButton;
    handleBackClick = handleBack;
};

// 监听路由变化，同路由跳转场景
watch(
    route,
    () => {
        init();
    },
    {
        deep: true,
        immediate: true,
    },
);
</script>
<style lang="less" scoped>
.back-btn {
    z-index: 5;
}
</style>
