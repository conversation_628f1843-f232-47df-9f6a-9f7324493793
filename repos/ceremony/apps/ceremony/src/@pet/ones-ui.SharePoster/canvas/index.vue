<template>
    <div id="ksCanvas" class="ks-canvas">
        <canvas
            ref="canvasRef"
            class="canvas-img"
            :width="width"
            :height="height"
            :style="{
                width: `${Number(width) / ratio}px`,
                height: `${Number(height) / ratio}px`,
            }"
        />
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
import { canvasProps } from './props';

export default defineComponent({
    name: 'KsCanvas',
    props: canvasProps,
    setup(props) {
        const canvasRef = ref<HTMLCanvasElement>();

        onMounted(() => {
            // eslint-disable-next-line @typescript-eslint/prefer-optional-chain
            props.useCanvas && props.useCanvas(canvasRef);
            // if (props.callback) {
            //     props.callback(canvasRef);
            // }
        });

        onBeforeUnmount(() => {
            if (canvasRef.value) {
                canvasRef.value.width = 0;
                canvasRef.value.height = 0;
                canvasRef.value.remove();
            }
        });

        return {
            canvasRef,
        };
    },
});
</script>

<style lang="less" scoped>
.ks-canvas {
    position: fixed;
    top: 0;
    left: 0;
    // z-index: -1;
    // display: none;
    z-index: 999;
    display: none;
    // width: 414px;
    // height: 736px;
    margin: 0 auto;
}
</style>
