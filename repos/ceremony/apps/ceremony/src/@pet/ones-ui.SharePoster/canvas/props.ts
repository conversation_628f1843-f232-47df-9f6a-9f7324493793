import type { ExtractPropTypes, PropType, Ref } from 'vue';

export const canvasProps = {
    width: {
        type: [Number, String],
        default: '414',
    },
    height: {
        type: [Number, String],
        default: '896',
    },
    ratio: {
        type: Number,
        default: 1,
    },
    // callback: {
    //     type: Function as PropType<(canvas: Ref<HTMLCanvasElement | undefined>) => void>,
    //     default: () => () => {},
    // },
    useCanvas: {
        type: Function as PropType<
            (canvas: Ref<HTMLCanvasElement | undefined>) => any
        >,
        default: () => {
            return (canvas: Ref<HTMLCanvasElement | undefined>) => {};
        },
    },
};

export type TCanvasProps = ExtractPropTypes<typeof canvasProps>;
