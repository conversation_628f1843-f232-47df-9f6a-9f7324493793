import { computed, createApp, nextTick, ref } from 'vue';
import { sleep } from '@alive-ui/actions';
import Canvas from './index.vue';
import type Vue from 'vue';
import type { TCanvasProps } from './props';

// const CanvasConstructor = Vue.extend(Canvas);
const DEFAULT_PROPS: TCanvasProps = {
    width: 828,
    height: 1472,
    ratio: 2,
    // callback: () => {},
    useCanvas: () => {},
};

// 创建一个ref来存储加载组件的实例
const $canvas = ref<Vue.App<Element>>();
const canvasInstance = ref<any>(null);

// 创建应用实例并挂载到一个临时元素
// const $canvas = createApp(Canvas, { visible: computed(() => visible.value) });
// canvasInstance.value = $canvas.mount(document.createElement('div'));

const initInstance = (propsData: TCanvasProps) => {
    if ($canvas.value) {
        $canvas.value.unmount();
    }

    $canvas.value = createApp(Canvas, propsData);
    canvasInstance.value = $canvas.value.mount(document.createElement('div'));
    // instance = new CanvasConstructor({
    //     el: document.createElement('div'),
    //     propsData, // 触发setup    // });

    // canvasInstance.value('close', () => {
    //     Object.assign(instance, { ...DEFAULT_PROPS, visible: false }); // reset props
    // });
};

// const close = async ({stay: true, callback: any, item: any) => {
// const close = async (options: { stay?: boolean; callback?: any; item?: any } = {}) => {
//     const { stay, callback, item } = options;
//     let stopClose = true;

//     if (typeof callback === 'function') {
//         stopClose = item ? callback(item) : callback();

//         if (stopClose === undefined) {
//             stopClose = true;
//         }
//     }

//     if (!stopClose || stay) {
//         return;
//     }

//     Object.assign(instance, { ...DEFAULT_PROPS }, { visible: false }); // this.visible = !this.visible;
//     const el = instance.$el;
//     await sleep(500);
//     instance.$destroy();
//     el.parentNode && el.parentNode.removeChild(el);
// };

// const show = (options: any = DEFAULT_PROPS) => {
//     if (!instance || !document.body.contains(instance.$el)) {
//         initInstance(options);
//         document.body.appendChild(instance.$el);
//     }

//     // Object.assign(instance, { ...DEFAULT_PROPS }, options, { visible: true }); 这种写法无法触发setup
//     return instance;
// };

const KsCanvas = {
    show(options: any = DEFAULT_PROPS) {
        if (
            !canvasInstance.value ||
            !document.body.contains(canvasInstance.value.$el)
        ) {
            initInstance(options);
            document.body.appendChild(canvasInstance.value.$el);
        }
    },

    async hide(options: { stay?: boolean; callback?: any; item?: any } = {}) {
        const { stay, callback, item } = options;
        let stopClose = true;

        if (typeof callback === 'function') {
            stopClose = item ? callback(item) : callback();

            if (stopClose === undefined) {
                stopClose = true;
            }
        }

        if (!stopClose || stay) {
            return;
        }

        await sleep(500);
        nextTick(() => {
            $canvas.value?.unmount();
            if (document.body.contains(canvasInstance.value.$el)) {
                document.body.removeChild(canvasInstance.value.$el);
            }
        });
    },
};

export default KsCanvas; // TODO: readme
