<template>
    <div v-if="visible" class="ks-loading">
        <sp-loading />
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { Loading } from '@lux/sharp-ui-next';

export default defineComponent({
    name: 'KsLoading',
    components: {
        'sp-loading': Loading,
    },
    props: {
        visible: {
            type: Boolean,
            required: false,
            default: false,
        },
    },
    // TODO: 可以全量扩展一下loading的props，后续需要时再加
    setup() {},
});
</script>

<style lang="less" scoped>
.ks-loading {
    position: fixed;
    top: 50%;
    left: 50%;
    // 层级问题导致loading无法正常展示, 这里参考refresh-icon层级
    z-index: 10;
    width: 40px;
    height: 40px;
    margin-top: -20px;
    margin-left: -20px;
}
</style>
