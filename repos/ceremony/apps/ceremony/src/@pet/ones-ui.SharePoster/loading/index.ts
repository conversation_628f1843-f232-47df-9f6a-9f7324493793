import { computed, createApp, nextTick, ref } from 'vue';
import MyLoading from './index.vue'; // 更改组件名称为MyLoading，避免与$loading混淆

const visible = ref(false);

// 创建一个ref来存储加载组件的实例
const loadingInstance = ref<any>(null);

// 创建应用实例并挂载到一个临时元素
const $loading = createApp(MyLoading, {
    visible,
});
loadingInstance.value = $loading.mount(document.createElement('div'));

const KsLoading = {
    visible,
    show() {
        if ($loading) {
            $loading.unmount();
        }
        document.body.appendChild(loadingInstance.value.$el);
        nextTick(() => {
            visible.value = true;
        });
    },

    hide() {
        visible.value = false;
        nextTick(() => {
            $loading.unmount();
            if (document.body.contains(loadingInstance.value.$el)) {
                document.body.removeChild(loadingInstance.value.$el);
            }
        });
    },
};

export default KsLoading;
