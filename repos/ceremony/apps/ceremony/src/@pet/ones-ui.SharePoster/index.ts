import { Toast } from '@lux/sharp-ui-next';
import { Report } from '@alive-ui/actions';
import KsCanvas from './canvas';
import type { Ref /* ,nextTick, ref */ } from 'vue';
import type { TCanvasProps } from './canvas/props';

export interface ShareProps {
    canvasProps: TCanvasProps;
    // shareParams: { // share桥参数 // TODO: ts 跟分享中台确认
    //     param: {
    //         subBiz: string; // pm申请  @pm
    //         shareObjectId: string; //  唯一标识一个活动
    //         showSharePanel: boolean; //   不拉起面板的时候，也就是 showSharePanel 传false 的时候，会走到新海报协议里，这时候支持不加二维码
    //         // shareDirectActionUrl: "kwaishare://shareAny/wechat",
    //         posterConfigs: {
    //             posterImageBytes: string; // 或者通过posterImageBytes 传入base64
    //             posterImageAspectRatio: number;
    //             qrImageAspectRatio: number;
    //             qrImageRelativeY: number;
    //             qrImageRelativeX: number;
    //             qrImageRelativeWidth: number;
    //         };
    //         shareInitConfigs?: {
    //             extInitPosterParams: { // 海报的布局
    //                 absoluteTopMargin: number;
    //                 absoluteBottomMargin: number;
    //                 relativeWidth: number;
    //             };
    //             // extTokenStoreParams: {
    //             //     wishId,
    //             //     userId,
    //             //     liveStreamId,
    //             // },
    //             // 站内地址： 分享到私信，点击的跳转地址     站外地址:只写path， 中台补充域名. 和（落地页地址）差不多，或者提供一个curl给中台，帮助配置
    //             // 测试域名: 使用中台落脚页，由中台提供
    //             extInitPainterParams: {
    //                 type: string; // 不能滚 固定传这个值
    //                 imageBytes: string; // social-share-bridge   base64图片
    //                 qrImageRelativeY: number;
    //                 qrImageRelativeX: number;
    //                 qrImageRelativeWidth: number;
    //             };
    //         };
    //         extAnyPainterParams?: {
    //             type: string;
    //             imageBytes: string;
    //             qrImageRelativeY: number;
    //             qrImageRelativeX: number;
    //             qrImageRelativeWidth: number;
    //         };
    //     };

    // };
    shareHandler: (imageBytes: string) => Promise<void>;
    onShareCompleted?: VoidFunction;
    immediate?: boolean;
}

let shareOptions: ShareProps | null = null;

// 1. emitShare调用 2.用户自己调用
// eslint-disable-next-line @typescript-eslint/require-await
export const emitShare = async (options?: ShareProps) => {
    if (options) {
        // 用户重新定义或者覆盖
        shareOptions = options;
    }
    const { canvasProps, onShareCompleted, shareHandler } = shareOptions ?? {}; // 1. emitShare调用 2.用户自己调用
    const { width, height, ratio, useCanvas } = canvasProps ?? {};
    KsCanvas.show({
        width,
        height,
        ratio,
        useCanvas: async (canvasRef: Ref<HTMLCanvasElement | undefined>) => {
            // await nextTick();
            const _canvas = canvasRef.value;
            // 预览canvas, 便于调试
            const isDev = import.meta.env.DEV;

            if (useCanvas && _canvas) {
                try {
                    await useCanvas(canvasRef);
                    console.log('_canvas?.toDataURL()');
                    // console.log(_canvas?.toDataURL());
                    // await nextTick(); // canvas 渲染完毕
                    const imageBytes = _canvas
                        .toDataURL()
                        .replace(/data:image\/\w+;base64,/, '');
                    !isDev && KsCanvas.hide();
                    // shareParams.param.posterConfigs.posterImageBytes = imageBytes;
                    // promisifyBridge('share', {
                    //     ...{
                    //         param: {
                    //             posterConfigs: {
                    //                 posterImageBytes: imageBytes,
                    //             },
                    //         },
                    //     },
                    //     ...shareParams,
                    // });
                    // @typescript-eslint/await-thenable
                    shareHandler && (await shareHandler(imageBytes));
                    onShareCompleted?.();
                } catch (error) {
                    if (error) {
                        Report.biz.error('海报分享失败', {
                            error,
                        });
                        Toast.info(error as string);
                    }
                } finally {
                    const canvasDom = document.getElementById('ksCanvas');
                    // 海报canvas默认不可见
                    if (isDev && canvasDom) {
                        canvasDom.style.display = 'unset';

                        return;
                    }
                    KsCanvas.hide();
                }
            }
        },
    });
};

export const createShareFactory = async (options: ShareProps) => {
    // 1. 初始化分享参数
    shareOptions = options;
    // 2. 是否立即触发分享
    if (options.immediate) {
        await emitShare(options);
    }

    // 3. 返回分享函数
    return emitShare;
};

const KsShare = {
    createShareFactory,
    emitShare,
};

export default KsShare; // TODO: readme
