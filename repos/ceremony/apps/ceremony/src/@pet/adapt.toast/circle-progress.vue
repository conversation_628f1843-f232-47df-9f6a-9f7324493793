<script lang="ts" setup>
/**
 * 23CNY LoadingDialogSectorProgress.vue
 */
import { computed, ref, watch } from 'vue-demi';

const props = defineProps<{
    value: number;
}>();

const emit = defineEmits<{
    (event: 'transitionend'): void;
}>();

const VIEW_BOX_SIZE = 38;
const CIRCLE_STOKE = 2;

const OUTER_RADIUS = (VIEW_BOX_SIZE - CIRCLE_STOKE) / 2;
const INNER_STROKE = OUTER_RADIUS - CIRCLE_STOKE / 2 - CIRCLE_STOKE;
const INNER_RADIUS = INNER_STROKE / 2;
const CIRCUMFERENCE = 2 * Math.PI * INNER_RADIUS;

const validValue = computed(() => {
    return Math.max(0, props.value) / 100;
});

const stroke = computed(() => {
    return CIRCUMFERENCE * validValue.value;
});

const dash = computed(() => {
    return CIRCUMFERENCE - stroke.value;
});

const dashGapDuration = ref(0);

watch(
    () => props.value,
    (val, oldVal) => {
        dashGapDuration.value = Math.abs(val - oldVal);
    },
);

const dashStyle = computed(() => {
    return {
        transition: `${dashGapDuration.value * 10}ms linear`,
    };
});

function handleTransitionend() {
    emit('transitionend');
}
</script>
<template>
    <div class="sector-progress">
        <svg :viewBox="`0 0 ${VIEW_BOX_SIZE} ${VIEW_BOX_SIZE}`" fill="none">
            <circle
                cx="50%"
                cy="50%"
                :r="OUTER_RADIUS"
                stroke="white"
                :stroke-width="CIRCLE_STOKE"
                fill="none"
            />
            <circle
                class="sector-progress_main"
                cx="-50%"
                cy="50%"
                :r="INNER_RADIUS"
                transform="rotate(-90)"
                :stroke-width="INNER_STROKE"
                :stroke-dasharray="`${stroke} ${dash}`"
                stroke="white"
                :style="dashStyle"
                @transitionend="handleTransitionend"
            />
        </svg>
    </div>
</template>
<style lang="scss" scoped>
.sector-progress {
    width: 38px;
    height: 38px;
}
</style>
