<pet-info lang="json">
{ "title": "纯文案提示", "description": "最常见的提示" }
</pet-info>
<script lang="ts" setup>
import { ref } from 'vue-demi';
import Button from '@pet/adapt.button/index.vue';
import { toast } from '../index';

const isLoading = ref(false);
async function showToast() {
    isLoading.value = true;
    await toast('最长文案不超过十五个字十五个字十五个字十五个字十五个字');
    await toast('2');
    await toast('3');
    isLoading.value = false;
}

function showToast2() {
    setTimeout(async () => {
        await toast('1');
        console.log('first toast done');
    }, 1000);
    setTimeout(async () => {
        await toast('2');
        console.log('second toast done');
    }, 1700);
}
</script>

<template>
    <div style="display: flex">
        <Button
            :height="46"
            :type="'primary'"
            :loading="isLoading"
            @click="showToast"
            >显示Toast</Button
        >
        <Button :height="46" :type="'primary'" @click="showToast2"
            >触发延迟Toast</Button
        >
    </div>
</template>
