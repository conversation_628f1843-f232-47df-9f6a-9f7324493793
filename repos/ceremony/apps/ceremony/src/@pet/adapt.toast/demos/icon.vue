<pet-info lang="json">
{ "title": "展示图标", "description": "最多展示7个字" }
</pet-info>
<script lang="ts" setup>
import { ref, watch } from 'vue-demi';
import Button from '@pet/adapt.button/index.vue';
import { toast, toastDestroy } from '../index';

// import { toast } from '../index';

function showToast(type: 'success' | 'error') {
    toast('最多显示七个字', {
        type,
    });
}

const show = ref(false);
function showLoadingToast() {
    show.value = !show.value;
}

watch(show, (val) => {
    if (val) {
        toast('加载中', {
            type: 'loading',
        });
    } else {
        toastDestroy();
    }
});
</script>

<template>
    <section class="box">
        <div>
            <h3>显示成功</h3>
            <Button
                class="button"
                :height="46"
                :type="'primary'"
                @click="showToast('success')"
                >显示成功</Button
            >
        </div>
        <div>
            <h3>显示失败</h3>
            <Button
                class="button"
                :height="46"
                :type="'primary'"
                @click="showToast('error')"
                >显示失败</Button
            >
        </div>
        <div>
            <h3>显示loading</h3>
            <Button @click="showLoadingToast">{{
                show ? '隐藏Loading' : '显示Loading'
            }}</Button>
        </div>
    </section>
</template>

<style lang="scss" scoped>
.box {
    font-size: 16px;
}
.button {
    margin: 0 5px;
}
</style>
