<pet-info lang="json">
{ "title": "自定义Toast", "description": "展示progress" }
</pet-info>
<script lang="ts" setup>
import But<PERSON> from '@pet/adapt.button/index.vue';
import { toast } from '../index';

function showToast() {
    const el = document.getElementById('toast-box')!;
    toast('自定义', {
        type: 'success',
        parentNode: el,
        customClass: 'id-toast-box-toast',
        duration: 2000,
        onHide: () => {
            console.log('toast is hide');
        },
    });
}

function showCustomeToast() {
    const el = document.getElementById('toast-box')!;
    toast('自定义圆角32', {
        type: 'success',
        parentNode: el,
        customClass: 'id-toast-custome-box-toast',
        duration: 2000,
        onHide: () => {
            console.log('toast is hide');
        },
    });
}
</script>

<template>
    <section>
        <div>
            <div id="toast-box" class="toast-box">toast将会在这里展示</div>
            <Button :height="46" :type="'primary'" @click="showToast"
                >显示Toast</Button
            >
            <Button :height="46" :type="'primary'" @click="showCustomeToast"
                >自定义圆角32</Button
            >
        </div>
    </section>
</template>

<style lang="scss" scoped>
.toast-box {
    position: relative;
    height: 100px;
    line-height: 100px;
    background: #f2f2f2;
    margin-bottom: 10px;
    text-align: center;
    font-size: 16px;
}
</style>

<style lang="scss">
.id-toast-box-toast.toast {
    --adapt-toast-position-attribute: absolute;
}

.id-toast-custome-box-toast.toast {
    --adapt-toast-position-attribute: absolute;
    --adapt-toast-content-border-radius: 32px;
    --adapt-toast-content-font-color: #abc9ff;
    --adapt-toast-content-icon-background-width: 140px;
    --adapt-toast-content-icon-padding: 14px 10px;
    --adapt-toast-content-with-icon-font-size: 16px;
    --adapt-toast-content-icon-width: 60px;
    --adapt-toast-content-icon-color: #c4f755;
}
</style>
