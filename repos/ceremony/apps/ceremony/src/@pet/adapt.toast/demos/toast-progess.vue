<pet-info lang="json">
{ "title": "进度展示", "description": "可以定义Toast" }
</pet-info>
<script lang="ts" setup>
import { ref } from 'vue-demi';
import Button from '@pet/adapt.button/index.vue';
import { useProgressToast } from '../useprogress-toast';
import ProgressToast from '../progress-toast.vue';
import { toast } from '../index';

const show = ref(false);
const show2 = ref(false);

const { createToast } = useProgressToast();

const content = ref('start');
const progressValue = ref(0);
const showBtn = ref(false);
const toastText = ref('');

function init() {
    show.value = false;
    content.value = 'start';
    progressValue.value = 0;
    showBtn.value = false;
}

async function showToast() {
    init();

    show.value = true;

    setTimeout(() => {
        content.value = '当前进度50%';
        progressValue.value = 50;
    }, 1000);

    setTimeout(() => {
        showBtn.value = true;
    }, 2000);

    setTimeout(() => {
        content.value = '当前进度100%';
        progressValue.value = 100;
        showBtn.value = false;
    }, 4000);

    await createToast({
        content,
        progressValue,
        showBtn,
        onProgressCancel: () => {
            init();
            toastText.value = '上传取消';
        },
        onProgressEnd: () => {
            init();
            toastText.value = '上传完成';
        },
    });

    await toast(toastText.value);

    await toast('感谢体验');
}

function showToast2() {
    content.value = 'start';
    progressValue.value = 0;
    show2.value = true;
    setTimeout(() => {
        content.value = '3000 ago';
        progressValue.value = 50;
    }, 1000);

    setTimeout(() => {
        content.value = '6000 ago';
        progressValue.value = 100;
    }, 4000);
}

function initValues() {
    content.value = 'start';
    progressValue.value = 0;
    show2.value = false;
}
</script>

<template>
    <div>
        <Button
            :height="46"
            :type="'primary'"
            style="margin-right: 10px"
            @click="showToast"
            >{{ show ? '隐藏' : '显示' }}Demo1</Button
        >
        <Button :height="46" :type="'primary'" @click="showToast2"
            >{{ show2 ? '隐藏' : '显示' }}Demo2</Button
        >
        <ProgressToast
            v-if="show2"
            :content="content"
            :progress-value="progressValue"
            @hide="initValues"
        />
    </div>
</template>
