# @pet/adapt.toast

通用 toast 组件：

命令式调用一个 toast，格式为:

```js
import { toast } from "@pet/base.toast";

async function showToast() {
    await toast("显示Toast", {
        duration: 2000, // 停留时间
        parentNode: document.body, // 绑定的父级容器
        animation: true, // 是否带动效弹出
        type: "success" | "error" | "loading" | "normal" | "progress",
        customClass: "", // 自定义样式
        mask: false, // 是否为遮罩展示
        onBeforeUnmount: () => {
            console.log("toast is being destroy");
        },
        onHide: () => {
            console.log("toast is out");
        }, // 隐藏时的回调
    });
}
```
