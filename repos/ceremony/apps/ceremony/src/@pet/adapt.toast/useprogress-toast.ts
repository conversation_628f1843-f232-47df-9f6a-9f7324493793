import { createApp, h, type Ref } from 'vue-demi';
import ProgressToast from './progress-toast.vue';
import type { App } from 'vue';

interface ProcessToastOptions {
    content?: Ref<string>;
    progressValue?: Ref<number>;
    showBtn?: Ref<boolean>;
    onProgressEnd?: () => void;
    onProgressCancel?: () => void;
    onHide?: () => void;
    onBeforeUnmount?: () => void;
}

export function useProgressToast() {
    let app: App | null;
    let toastContainer: HTMLElement | null;

    function createToastContainer() {
        const toastContainerEl = document.createElement('div');
        toastContainerEl.setAttribute('id', '__toast_progress');
        const rootContainer = document.querySelector('body');
        rootContainer?.appendChild(toastContainerEl);
        return toastContainerEl;
    }

    function destroyCurrentInstance() {
        if (app) {
            app.unmount();
            app = null;
            toastContainer?.remove();
            toastContainer = null;
        }
    }

    function createToast(options?: ProcessToastOptions) {
        return new Promise<void>((resolve) => {
            function onProgressEnd() {
                if (options?.onProgressEnd) {
                    options.onProgressEnd();
                }
            }

            function onProgressCancel() {
                if (options?.onProgressCancel) {
                    options.onProgressCancel();
                }
            }

            function onHide() {
                if (options?.onHide) {
                    options.onHide();
                }
                destroyCurrentInstance();
                resolve();
            }

            function onBeforeUnmount() {
                if (options?.onBeforeUnmount) {
                    options.onBeforeUnmount();
                }
            }

            const ProgressToastComponent = {
                setup() {
                    return () =>
                        h(ProgressToast, {
                            content: options?.content?.value,
                            progressValue: options?.progressValue?.value,
                            showBtn: options?.showBtn?.value,
                            onProgressEnd,
                            onProgressCancel,
                            onHide,
                            onBeforeUnmount,
                        });
                },
            };

            if (!app) {
                toastContainer = createToastContainer();
                app = createApp(ProgressToastComponent);
                app.mount(toastContainer);
            }
        });
    }

    return {
        createToast,
        destroyCurrentInstance,
    };
}
