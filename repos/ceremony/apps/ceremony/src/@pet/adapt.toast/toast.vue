<script lang="ts" setup>
import {
    computed,
    onBeforeUnmount,
    onMounted,
    onUpdated,
    ref,
    watch,
} from 'vue-demi';
import SFTransition from '@pet/adapt.transition/index.vue';
import Loading from '@pet/adapt.loading/infinity-loading.vue';
import IconSuccess from './assets/success.vue';
import IconError from './assets/error.vue';

export type ToastType = 'success' | 'error' | 'loading' | 'progress' | 'normal';

export interface ToastProps {
    type?: ToastType;
    content?: string;
    animation?: boolean;
    duration?: number;
    customClass?: string;
    mask?: boolean;
    showBtn?: boolean;
}

export interface ToastExpose {
    showToast: () => void;
    destroyToast: () => void;
}

export interface ToastEmits {
    (event: 'hide'): void;
    (event: 'beforeUnmount'): void;
    (event: 'btn-click'): void;
}

const props = withDefaults(defineProps<ToastProps>(), {
    type: 'normal',
    animation: true,
    content: '',
    duration: 2000,
});

const emit = defineEmits<ToastEmits>();

const DEFAULT_DESTROY_DURATION = 10000;

const visible = ref(true);
const realVisible = ref(true);

const unWatchShow = watch(visible, (val) => {
    if (val) {
        realVisible.value = val;
    }
});

let timer: number | null = null;
function hide() {
    visible.value = false;
}

const computedContent = computed(() => {
    return props.content.split('\n');
});

const animationType = computed(() =>
    props.animation ? 'scale-in-out' : 'no-effect',
);
const hasIcon = computed(
    () =>
        typeof props.type === 'string' &&
        ['success', 'error', 'loading', 'progress'].includes(props.type),
);
const hasIconWrapper = computed(() => hasIcon.value && 'has-icon');
const iconType = computed(() => {
    switch (props.type) {
        case 'success':
            return IconSuccess;
        case 'error':
            return IconError;
        case 'loading':
            return Loading;
        default:
            return null;
    }
});
const hasMask = computed(() => props.mask && 'has-mask');

function update() {
    // eslint-disable-next-line no-eq-null, eqeqeq
    if (timer != null) {
        clearTimeout(timer);
    }
    if (visible.value && Boolean(props.duration) && props.type !== 'progress') {
        const duration =
            props.type !== 'loading'
                ? props.duration
                : DEFAULT_DESTROY_DURATION;
        timer = window.setTimeout(() => {
            hide();
        }, duration);
    }
}

function destroyToast() {
    hide();
}

onMounted(() => {
    update();
});
onUpdated(() => {
    update();
});
onBeforeUnmount(() => {
    // eslint-disable-next-line no-eq-null, eqeqeq
    if (timer != null) {
        window.clearTimeout(timer);
    }
    unWatchShow();
    destroyToast();
    emit('beforeUnmount');
});

function showToast() {
    visible.value = true;
}

function toastLeave() {
    emit('hide');
    realVisible.value = false;
}

function ctrlBtnClick() {
    emit('btn-click');
}

defineExpose<ToastExpose>({
    showToast,
    destroyToast,
});
</script>
<script lang="ts">
export default {
    name: 'AdaptToast',
};
</script>

<template>
    <div v-if="realVisible" class="toast" :class="[customClass, hasMask]">
        <SFTransition :name="animationType" appear @after-leave="toastLeave">
            <div v-if="visible" class="toast-content" :class="hasIconWrapper">
                <div v-if="hasIcon" class="toast-icon-wrapper">
                    <component :is="iconType" class="toast-icon" />
                    <slot />
                </div>
                <template v-for="(info, index) in computedContent">
                    {{ info }}
                    <template v-if="index !== computedContent.length - 1">
                        <br :key="index" />
                    </template>
                </template>
                <span
                    v-if="showBtn"
                    class="ctrl-btn"
                    role="button"
                    @click="ctrlBtnClick"
                    >取消</span
                >
            </div>
        </SFTransition>
    </div>
</template>
<style>
:root {
    /* toast层级 */
    --adapt-toast-position-z-index: 9999;
    /* 自定义position */
    --adapt-toast-position-attribute: fixed;
    /* toast背景色 */
    --adapt-toast-content-background: rgba(0, 0, 0, 0.7);
    /* toast 纯文字 字号 */
    --adapt-toast-content-font-size: 16px;
    /* toast字号带有icon */
    --adapt-toast-content-with-icon-font-size: 14px;
    /* toast圆角 */
    --adapt-toast-content-border-radius: 8px;
    /* toast字体颜色 */
    --adapt-toast-content-font-color: #fff;
    /* toast icon背景填充padding */
    --adapt-toast-content-icon-padding: 12px 0;
    /* toast icon背景宽度 */
    --adapt-toast-content-icon-background-width: 120px;
    /* toast icon大小 */
    --adapt-toast-content-icon-width: 48px;
    /* toast icon颜色 */
    --adapt-toast-content-icon-color: #fff;
}
</style>
<style lang="scss" scoped>
.toast {
    position: var(--adapt-toast-position-attribute);
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: none;
    z-index: var(--adapt-toast-position-z-index);
    &.has-mask {
        pointer-events: auto;
    }
}
.toast-content {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 12px 20px;
    margin: 0 50px;
    background: var(--adapt-toast-content-background);
    border-radius: var(--adapt-toast-content-border-radius);
    font-size: var(--adapt-toast-content-font-size);
    font-weight: 400;
    color: var(--adapt-toast-content-font-color);
    line-height: 24px;
    max-width: 16em;
    pointer-events: auto;
    &.has-icon {
        width: var(--adapt-toast-content-icon-background-width);
        padding: var(--adapt-toast-content-icon-padding);
        font-size: var(--adapt-toast-content-with-icon-font-size);
        line-height: 21px;
    }

    .ctrl-btn {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 88px;
        height: 26px;
        border-radius: 27px;
        background: rgba(255, 255, 255, 0.2);
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        margin-top: 12px;
    }
}
.toast-icon-wrapper {
    width: var(--adapt-toast-content-icon-width);
    height: var(--adapt-toast-content-icon-width);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    .toast-icon {
        width: 100%;
        height: 100%;
        color: var(--adapt-toast-content-icon-color);
    }
}
</style>
