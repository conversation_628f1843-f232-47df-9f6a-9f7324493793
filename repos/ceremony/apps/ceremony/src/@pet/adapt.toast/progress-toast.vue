<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue-demi';
import Toast from './toast.vue';
import CircleProgress from './circle-progress.vue';

interface Props {
    progressValue?: number;
    content?: string;
    showBtn?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
    progressValue: 0,
    showBtn: true,
});

const emit = defineEmits<{
    (event: 'hide'): void;
    (event: 'beforeUnmount'): void;
    (event: 'progressEnd'): void;
    (event: 'progressCancel'): void;
}>();

// eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
const toastRef = ref<InstanceType<typeof Toast> | null>(null);

function onTransitionEnd() {
    if (props.progressValue >= 100) {
        emit('progressEnd');
        toastRef.value?.destroyToast();
    }
}

function onBtnClick() {
    toastRef.value?.destroyToast();
    emit('progressCancel');
}

function onHide() {
    emit('hide');
}

onBeforeUnmount(() => {
    emit('beforeUnmount');
});
</script>

<template>
    <Toast
        ref="toastRef"
        class="progress-toast"
        :show-btn="showBtn"
        type="progress"
        mask
        :content="content"
        @btn-click="onBtnClick"
        @hide="onHide"
    >
        <CircleProgress
            :value="progressValue ?? 0"
            @transitionend="onTransitionEnd"
        />
    </Toast>
</template>
