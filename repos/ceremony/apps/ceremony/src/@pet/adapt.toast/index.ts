import { createApp, h, ref, type Ref } from 'vue-demi';
import Toast, { type ToastProps } from './toast.vue';
import type { App } from 'vue';

interface IToastOptions extends ToastProps {
    parentNode?: HTMLElement | null;
    onHide?: () => void;
    onBeforeUnmount?: () => void;
}

interface IToast extends IToastOptions {
    content?: string;
}

let app: App | null;
let toastContainer: HTMLElement | null;

const destroyCurrentInstance = () => {
    if (app) {
        app.unmount();
        app = null;
        toastContainer?.remove();
        toastContainer = null;
    }
};

const createToast = (props: IToast = {}) => {
    return new Promise<void>((resolve) => {
        const propsData: Ref<IToast> = ref(props);

        function onHide() {
            if (propsData.value.onHide) {
                propsData.value.onHide();
            }
            destroyCurrentInstance();
            resolve();
        }

        function onBeforeUnmount() {
            if (propsData.value.onBeforeUnmount) {
                propsData.value.onBeforeUnmount();
            }
        }

        if (!app) {
            toastContainer = document.createElement('div');
            toastContainer.setAttribute('id', '__toast');
            const rootContainer =
                propsData.value.parentNode ?? document.querySelector('body');
            rootContainer?.appendChild(toastContainer);

            const ToastInstance = h(Toast, {
                ...propsData.value,
                onHide,
                onBeforeUnmount,
            });

            app = createApp(ToastInstance);
            app.mount(toastContainer);
        }
    });
};

/**
 * toast 提示
 * @param content 文本内容
 * @param opt toast参数
 */

export const toast = (content = '', opt: IToastOptions = {}) => {
    const { type = 'normal' } = opt;

    const options = { content, ...opt };

    if (content.length === 0 && type !== 'loading') {
        console.error(`-|--- toast: 无内容 ---|-`);
    }

    if (type !== 'normal' && content.length > 7) {
        console.error(`-|--- toast: 最多显示七个字 ---|-`);
    }

    destroyCurrentInstance();

    return createToast(options);
};

export const toastDestroy = () => {
    destroyCurrentInstance();
};
