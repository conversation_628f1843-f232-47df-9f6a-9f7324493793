<script lang="ts" setup>
import { throttle } from 'lodash-es';
import { Toast } from '@lux/sharp-ui-next';
import {
    dispatchLiveRouter,
    isYodaPCContainer,
    showToast,
} from '@alive-ui/system';
import { useBaseRankItemContext } from '@alive-ui/pro';
import { Button as AButton } from '@alive-ui/base';
import { bolIsAuthor } from '@alive-ui/actions';

const { logRankItemClick } = useBaseRankItemContext() || {};

const { giftLink, useLink, giftId } = withDefaults(
    defineProps<{
        giftLink?: string;
        giftId?: string;
        useLink?: boolean;
    }>(),
    {
        giftLink: '',
        giftId: '',
        useLink: false,
    },
);

const openGiftPanel = () => {
    if (isYodaPCContainer) {
        showToast('请到手机端进行查看');
        return;
    }

    if (bolIsAuthor) {
        Toast.info('开播中，不可参与哦～');
        return;
    }
    logRankItemClick?.('HELP');
    dispatchLiveRouter({
        path: useLink
            ? giftLink
            : `kwailive://giftpanel?tab=NormalGift&selectedGiftId=${giftId}`,
        keepDisplayWebView: false,
    }).catch(() => {});
};

const onClick = throttle(openGiftPanel, 800, {
    leading: true,
    trailing: false,
});
</script>

<template>
    <AButton type="primary" size="sm" @click="onClick">
        <slot> 去送礼 </slot>
    </AButton>
</template>
