import { ref } from 'vue';
import { Toast } from '@lux/sharp-ui-next';

//  TODO 任务队列 cancel 上一个请求
export function useRefreshManager() {
    const isLoading = ref(false);
    const refreshTasks = [] as {
        showLoading: boolean;
        refreshFn: (...args: any[]) => Promise<any>;
    }[];

    //	刷新任务
    const addRefreshTask = (
        showLoading: boolean,
        refreshFn: (...args: any[]) => Promise<any>,
    ) => {
        refreshTasks.push({
            showLoading,
            refreshFn,
        });
        if (!isLoading.value) run();
    };
    //	刷新任务
    const execRefreshTask = async () => {
        const { showLoading, refreshFn } = refreshTasks.shift()!;
        //	loading
        if (!isLoading.value && showLoading) {
            isLoading.value = true;
            Toast.loading('正在加载', 0, true);
        }
        await refreshFn();
    };
    //	执行任务
    const run = async () => {
        while (refreshTasks.length) {
            await execRefreshTask();
        }
        Toast.hide();
        isLoading.value = false;
    };

    return {
        addRefreshTask,
        run,
    };
}
