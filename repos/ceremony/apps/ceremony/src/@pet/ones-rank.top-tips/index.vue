<template>
    <div
        :class="{
            'order-5 tip-wrapper tip-width': !!topTipsText && needTop,
            'tip-width': needTop,
        }"
    >
        <div
            v-if="topTipsText && needTop"
            v-click-log="logParams"
            v-show-log="logParams"
            class="trapezoid flex-center-center"
            @click="goPage"
        >
            {{ topTipsText }}
            <div class="arrow-right-icon" />
        </div>
        <slot />
    </div>
</template>
<script setup lang="ts">
const props = defineProps({
    needTop: {
        type: Boolean,
        default: false,
    },
    logType: {
        type: String,
        default: '',
    },
    topTipsText: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['goPage']);
const goPage = () => {
    emit('goPage');
};
const logParams = {
    action: 'OP_ACTIVITY_FUNCTION_BUTTON',
    params: {
        btn_type: props.logType,
    },
};
</script>

<style lang="less" scoped>
.tip-wrapper {
    background: rgba(242, 242, 255, 0.06);
    border-radius: 8px;
    padding-bottom: 8px;
}
.tip-width {
    width: 358px;
}
.trapezoid {
    margin: 0 auto;
    font-size: 12px;
    font-weight: 500;
    color: #ffd400;
    width: fit-content;
    height: 20px;
    padding: 0 9px 0 12px;
    position: relative;
    background-color: #393531; /* 梯形背景色 */
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    clip-path: polygon(98% 100%, 2% 100%, 0% 0%, 100% 0%);
}
.arrow-right-icon {
    width: 10px;
    height: 10px;
    background: url('./arrow-right-icon_2x.png') center / 100% no-repeat;
    margin-left: 1px;
}
</style>
