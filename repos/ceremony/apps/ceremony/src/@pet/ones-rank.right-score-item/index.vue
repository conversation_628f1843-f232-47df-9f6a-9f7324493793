<template>
    <RankItemBase :item="item">
        <template #extra><slot name="extra" /></template>
        <template #info>
            <span></span>
        </template>
        <template #right>
            <!-- 定义右侧内容 -->
            <div class="right-score text-right">
                <div class="source-unit-desc a-text-main text-12 opacity-60">
                    {{ scoreLabel }}
                </div>
                <div class="count a-text-highlight text-din text-12">
                    <slot name="score">
                        {{ item.h5ShowScore }}
                    </slot>
                </div>
            </div>
        </template>
    </RankItemBase>
</template>

<script lang="ts" setup>
// import LiveIcon from '@pet/ones-ui.dynamic-living-icon/index.vue';
import RankItemBase from '@pet/ones-rank.base-item/index.vue';
import { Avatar as APAvatar } from '@alive-ui/pro';
import type { PropType } from 'vue';
import type { ItemRankInfo } from '@pet/ones-rank.base-item/schema';
defineProps({
    item: {
        type: Object as PropType<ItemRankInfo>,
        default: () => {
            return {};
        },
    },
    scoreLabel: {
        type: String,
        default: '热度值',
    },
});
</script>

<style lang="less" scoped></style>
