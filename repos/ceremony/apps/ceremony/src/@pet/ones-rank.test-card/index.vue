<script lang="ts" setup>
import { computed } from 'vue';

const props = defineProps<{
    serviceData: string;
    aw: any;
    pd: any;
    kconfData: any;
    hotList: any;
}>();

//  异常测试
const a = computed(() => {
    return props.hotList.a.v;
});
</script>

<template>
    <div class="test-ones-card">
        <!-- <h1>{{ serviceData }}</h1> -->
        <h1>{{ hotList }}</h1>
        <h1>{{ a }}</h1>
    </div>
</template>

<style lang="less" scoped></style>
