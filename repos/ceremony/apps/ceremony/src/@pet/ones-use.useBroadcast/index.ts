import { onUnmounted } from 'vue';
import mitt from 'mitt';
import { getCurrentTime, setServerTimeOffset } from '@alive-ui/system';

export enum EventsEnum {
    countDownBroadcast = 'count-down-broadcast',
}
type Events = {
    [EventsEnum.countDownBroadcast]: number;
};

function useBroadcast(interval: number) {
    const busEvent = mitt<Events>();
    let timer: ReturnType<typeof setInterval>;

    const startCountDownBroadcast = async () => {
        await setServerTimeOffset();
        timer = setInterval(() => {
            const curTime = getCurrentTime();
            busEvent.emit(EventsEnum.countDownBroadcast, curTime);
        }, interval);
    };

    //  后续更多的广播事件可以追加

    onUnmounted(() => {
        timer && clearInterval(timer);
        busEvent.all.clear();
    });

    return {
        busEvent,
        startCountDownBroadcast,
    };
}

export const countDownBroadcast = useBroadcast(1000);
