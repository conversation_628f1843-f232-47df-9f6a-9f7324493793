<template>
    <Nav
        v-if="currentNavMap.length"
        class="daily-fixed-bottom"
        :active-index="currentActive"
        @change="tabChange"
    >
        <NavItem
            v-for="(nav, index) in currentNavMap"
            :key="nav.route"
            class="nav-item-class"
            :class="{
                'nav-item-active nav-item-confirm': currentActive === index,
                [`nav-item-${currentNavMap.length}`]: true,
                [`nav-item-active-${currentNavMap.length}`]:
                    currentActive === index,
                [`gis-nav-active-animate-${currentNavMap.length}`]:
                    currentActive === index && !isLowDevice(),
            }"
        >
            <span
                v-if="currentNavMap.length < 4"
                :class="{ 'font-scale': currentActive === index }"
            >
                {{ nav.label }}
            </span>
        </NavItem>
    </Nav>
</template>

<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router';
import { computed } from 'vue';
import useKconf from '@pet/ones-use.useKconf';
import { getHost } from '@pet/ones-use.env/index';
import { Toast } from '@lux/sharp-ui-next';
import { Nav as ANav } from '@alive-ui/base';
import {
    appendParam,
    isLowDevice,
    isOutLiveRoom,
    isYodaPCContainer,
} from '@alive-ui/actions';
import { BottomEnum } from './types';
import useStore from './store';

const props = defineProps<{
    bottomType: BottomEnum;
}>();

const { Nav, NavItem } = ANav;
const route = useRoute();
const router = useRouter();
const kconf = useKconf();
const store = useStore();

function handleNavMap(navMap: any[]) {
    if (isOutLiveRoom) {
        return navMap.filter((nav) => {
            return nav.route !== '/star';
        });
    }

    if (isYodaPCContainer) {
        return navMap.filter((nav) => {
            return nav.route !== '/party';
        });
    }

    return navMap;
}

const currentNavMap = computed(() => {
    switch (props.bottomType) {
        case BottomEnum.FINAL:
            const finalNavMap =
                kconf.kconfData?.inviteConfigs?.fnalNavMap ?? [];
            return handleNavMap(finalNavMap);
        case BottomEnum.DAILY:
            const dailyNavMap =
                kconf.kconfData?.inviteConfigs?.dailyNavMap ?? [];
            return handleNavMap(dailyNavMap);
        case BottomEnum.OtherDay:
            const giftNavMap = kconf.kconfData?.inviteConfigs?.giftNavMap ?? [];
            return handleNavMap(giftNavMap);
        case BottomEnum.Ended:
            const giftFinalNavMap =
                kconf.kconfData?.inviteConfigs?.giftFinalNavMap ?? [];
            return handleNavMap(giftFinalNavMap);
        default:
            return [];
    }
});

const currentActive = computed(() => {
    const path = route.path ?? '';
    return currentNavMap.value.findIndex((item) => item.route === path);
});

const currentNav = computed(() => {
    const curNav = currentNavMap.value[currentActive.value] ?? {};
    return curNav;
});

const tabChange = (index: number) => {
    //  这里和后端约定了，4tab 的 activityBiz 硬编码，其他阶段的 biz 都从 query 上拿
    //  在跳转时也不用改 biz 值了，4tab 的 activityBiz 硬编码
    const originQuery = route.query ?? {};
    const targetNav = currentNavMap.value[index];
    store.referrer = targetNav.route;
    const extraQuery = targetNav.extraQuery ?? {};
    const query = {
        ...originQuery,
        ...extraQuery,
    };
    // const query = {
    //     ...originQuery,
    //     //  如果底 tab 不同，请在 inviteConfigs 中配置对应的 biz
    //     activityBiz,
    // };
    if (isYodaPCContainer && targetNav.pcToast) {
        Toast.info(targetNav.pcToast);
        return;
    }

    if (targetNav.routeType.main === 'spaReplace') {
        router.replace({
            path: targetNav.route,
            query,
        });
    } else {
        const host = getHost();
        const path =
            targetNav.route === '/magic-marbles'
                ? targetNav.path.main
                : targetNav.path.liveA;

        const completePath = `${path}${targetNav.route}`;
        location.replace(appendParam(`${host}${completePath}`, query));
    }
};
</script>

<style lang="less" scoped>
.daily-fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
    width: 100%;
    will-change: transform;
    left: 50%;
    transform: translateX(-50%);
    animation: navFadeIn 0.2s ease-in-out forwards;
    &.nav-4-bg-0 {
        width: 414px;
        height: 88px;
        background: url('./assets/final/nav-0.png') center / 100% no-repeat;
    }
    &.nav-4-bg-1 {
        width: 414px;
        height: 88px;
        background: url('./assets/final/nav-1.png') center / 100% no-repeat;
    }
    &.nav-4-bg-2 {
        width: 414px;
        height: 88px;
        background: url('./assets/final/nav-2.png') center / 100% no-repeat;
    }
    &.nav-4-bg-3 {
        width: 414px;
        height: 88px;
        background: url('./assets/final/nav-3.png') center / 100% no-repeat;
    }
    .nav-item-4 {
        width: 105px;
        height: 40px;
    }

    // .padding-right-20 {
    //     &:nth-of-type(1) {
    //         padding-right: 28px;
    //     }
    // }

    // .padding-left-20 {
    //     &:nth-of-type(2) {
    //         padding-left: 28px;
    //     }
    // }

    .nav-item-3 {
        width: 127px;
        height: 40px;
    }

    .font-scale {
        font-size: 22px;
    }

    .nav-item-active-3 {
        width: 160px;
    }

    .gis-nav-active-animate-3 {
        width: 160px;
        background: url('./assets/nav-active-animate-2_2x.min.png') center /
            100% no-repeat;
    }

    .gis-nav-active-animate-4 {
        width: 105px;
        height: 59px !important;
        background: url('./assets/nav-active-animate-2_2x.min.png') center /
            100% no-repeat;
    }
}
</style>
<style lang="less" scoped>
.nav-item-class {
    :deep(.card-font) {
        font-family: zaozi-zhehei;
    }
}

@keyframes navFadeIn {
    0% {
        opacity: 0;
        /* 初始不透明度 */
    }

    100% {
        opacity: 1;
        /* 完全透明 */
    }
}
</style>
