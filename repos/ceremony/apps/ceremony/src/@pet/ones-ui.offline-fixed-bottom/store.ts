import { ref } from 'vue';
import { defineStore } from 'pinia';
import { is4Tab } from '@pet/ones-use.is4Tab';
import { BottomEnum } from './types';
import { getBottomStage } from './services';

export default defineStore('offline-fixed-bottom', () => {
    const bottomStage = ref<BottomEnum>(BottomEnum.NONE);
    //  扩展来源字段，以后可能用的到
    const referrer = ref('');

    async function init() {
        if (!is4Tab) {
            try {
                const data = await getBottomStage();
                bottomStage.value = data.data ?? BottomEnum.NONE;
            } catch (error) {}
        }
    }

    return {
        init,
        bottomStage,
        referrer,
    };
});
