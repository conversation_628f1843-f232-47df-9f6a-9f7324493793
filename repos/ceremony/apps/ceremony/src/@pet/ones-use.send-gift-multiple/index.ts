import { storeToRefs } from 'pinia';
import useAuthorMatch from '@pet/ones-use.bolIsAuthor';
import { Dialog, Toast } from '@lux/sharp-ui-next';
import {
    sendGiftToAnchorV2,
    activityBiz,
    exitWebView,
    isYodaPCContainer,
    queryWalletBalance,
    invoke,
    Report,
    getQuery,
    openRechargeCard,
} from '@alive-ui/actions';
export type ToUserInfo = {
    userId: number | string;
    userName?: string;
    subRecipientScene?: string;
    subRecipientId?: number | string;
    subRecipientName?: string;
};
export interface giftItem {
    giftId: string; // 礼物id
    giftName: string; // 礼物名字
    giftToken: string; // 礼物token
    unitPrice: number; // 礼物价格
}

const getSendGiftToAnchorParams = (userInfo: ToUserInfo, curGift: giftItem) => {
    const {
        userId,
        userName,
        subRecipientScene,
        subRecipientName,
        subRecipientId,
    } = userInfo;

    const extraInfo = subRecipientScene
        ? {
              // 多人直播间
              sendExtraInfo: JSON.stringify({
                  subRecipientInfo: {
                      subRecipientScene,
                      subRecipientId,
                      subRecipientName,
                  },
              }),
              logExtraInfo: {
                  sub_recipient_scene: subRecipientScene,
                  sub_recipient_id: subRecipientId, // 非枚举文本型,子收礼人id
                  pageSource: 1,
                  globalActivityName: activityBiz,
              },
          }
        : {
              // 送给当前直播间
              logExtraInfo: {
                  pageSource: 1,
                  globalActivityName: activityBiz,
              },
          };

    return {
        giftId: curGift?.giftId,
        giftToken: curGift?.giftToken,
        batchCount: 1,
        isCombo: false,
        comboScene: '',
        ...extraInfo,
    };
};

const getSendGiftToGuestParams = (userInfo: ToUserInfo, curGift: giftItem) => {
    const { userId, userName } = userInfo;

    return {
        giftId: curGift.giftId,
        batchCount: 1,
        giftToken: curGift.giftToken,
        isCombo: false,
        receiverUserId: userId,
        receiverUserInfo: {
            user_id: userId,
            user_name: userName,
        },
        logExtraInfo: {
            pageSource: 1,
            globalActivityName: activityBiz,
        },
    };
};
/**
 * 异步函数，用于处理礼物发送逻辑。
 *
 * @param curGift - 当前选择的礼物信息。
 * @param sendSuccessLog - 成功发送礼物后的日志记录函数。
 * @param successCb - 成功发送礼物后的回调函数。
 */
export const sendGiftMultiple = async (
    curGift: giftItem,
    sendSuccessLog: () => void,
    successCb: () => void,
) => {
    const { isAuthorMatch } = storeToRefs(useAuthorMatch());

    if (isAuthorMatch.value || isYodaPCContainer) {
        Toast.info('直播中，无法送礼哦～');
        return;
    }
    console.log('curGift==', curGift);
    const defaultInfos: ToUserInfo = { userId: String(getQuery('authorId')) };
    let toUserInfosList = [];
    try {
        const toUserInfos = getQuery('toUserInfos');
        console.log(
            'decodeURIComponent(toUserInfos as string)',
            decodeURIComponent(String(toUserInfos)),
        );
        toUserInfosList = toUserInfos
            ? (JSON.parse(decodeURIComponent(toUserInfos)) as ToUserInfo[])
            : [defaultInfos];
    } catch (e) {
        const error = e && (e as Error);
        toUserInfosList = [defaultInfos];
    }
    try {
        const res: any = await queryWalletBalance();
        console.log('钱包res', res);
        if (res) {
            // 得满足送当前选中所有主播
            if (
                res.data?.ksCoin <
                curGift?.unitPrice * toUserInfosList.length
            ) {
                Toast.info('余额不足');
                openRechargeCard('H5', true, () => {
                    Toast.info('当前版本过低，请更新新版本。');
                });
                return;
            }
            console.log('toUserInfosList.value', toUserInfosList);
            const PromiseList = toUserInfosList.map((info: ToUserInfo) => {
                console.log('info', info);
                const isAnchor =
                    String(info.userId) === String(getQuery('authorId')) ||
                    info.subRecipientScene;
                if (isAnchor) {
                    // 送给主播
                    const param = getSendGiftToAnchorParams(info, curGift);
                    console.log('主播param', param);
                    return sendGiftToAnchorV2(param).then((subres: any) => {
                        console.log('res', res);
                        if (subres?.data?.response) {
                            sendSuccessLog();
                        } else {
                            Toast.info(`给${info.userName}送礼失败`);
                        }
                    });
                }
                // 送给嘉宾
                const param = getSendGiftToGuestParams(info, curGift);
                console.log('嘉宾param', param);
                return invoke('KwaiLive.sendGiftToGuestV2', param).then(
                    (subres: any) => {
                        console.log('res', res);
                        if (subres?.data?.response) {
                            sendSuccessLog();
                        } else {
                            Toast.info(`给${info.userName}送礼失败`);
                        }
                    },
                );
            });
            Promise.all(PromiseList).then(() => {
                Toast.info('赠送成功');

                successCb();
                exitWebView();
            });
        }
    } catch (error) {
        Report.biz.error('常规礼物-送礼错误', {
            error,
        });
    }
};
