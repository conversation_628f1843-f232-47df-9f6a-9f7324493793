<template>
    <div class="card-wrap a-text-main" @click="onClick">
        <div class="card-top a-text-main">
            <div class="card-top-left">
                <div class="card-top-left-title">
                    <slot name="topLeftTtle" />
                </div>
            </div>
            <div
                v-if="!hiddenRightIcon"
                class="card-top-right"
                :class="{ 'card-top-right-noarrow': hiddenRightIconArrow }"
            >
                <slot name="topRightText" />
            </div>
        </div>
        <div
            v-if="!hideContent"
            class="card-container"
            :class="{ 'card-container-bg': $slots.cardContainerBg }"
        >
            <slot name="cardContainerBg" />
            <slot name="cardContainerUnBg" />
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps({
    hiddenRightIcon: {
        type: Boolean,
        default: () => false,
    },
    hiddenRightIconArrow: {
        type: Boolean,
        default: () => false,
    },
    hideContent: {
        type: Boolean,
        default: () => false,
    },
});
const emit = defineEmits(['click']);
const onClick = () => {
    emit('click');
};
</script>

<style lang="less" scoped>
.card-wrap {
}
.card-top {
    --titleLinearGradient: linear-gradient(
        141.56deg,
        #fff 27.32%,
        #ffc4a3 82.93%
    );

    display: flex;
    height: 18px;

    justify-content: space-between;
    .card-top-left-title {
        width: max-content;
        font-family: HYYaKuHei;
        font-size: 14px;
        font-weight: 400;
        line-height: 0.36rem;
        color: transparent;
        text-align: left;
        vertical-align: middle;
        background: linear-gradient(89.98deg, #fff 0.03%, #ffc4a3 95.69%);
        background-clip: text;
    }
    .card-top-left-title-simple {
        display: inline-block;
        font-family: HYYaKuHei;
        font-size: 14px;
        line-height: 18px;
        color: transparent;
        text-align: left;
        vertical-align: middle;
        background: var(--title-text);
        background-clip: text;
    }

    .card-top-right {
        display: flex;
        padding-right: 12px;
        font-size: 12px;
        background: url('../assets/arrow-right-icon_2x.png') right / 100%
            no-repeat;
        opacity: 0.6;
        background-size: 10px;
        align-items: center;
    }

    .card-top-right-noarrow {
        background: none;
        padding-right: 0;
    }
}
.card-container {
    display: flex;
    height: 48px;
    margin-top: 8px;
    align-items: center;
    justify-content: space-between;
}
.card-container-bg {
    display: flex;
    padding: 0 10px;
    font-family: 'PingFang SC';
    font-size: 12px;
    background-color: rgba(242, 242, 255, 6%);
    border-radius: 8px;
    align-items: center;
    justify-content: center;
}
.content-center {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
    font-size: 12px;
}
</style>
