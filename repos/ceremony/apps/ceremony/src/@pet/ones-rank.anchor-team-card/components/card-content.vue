<template>
    <cardTemplate @click="goToPage">
        <!-- 主title活动期 非活动期不同 -->
        <template v-if="teamStatus === TEAMSTATUS.ACTIVITY" #topLeftTtle>
            主播组队共夺荣耀
            <div class="card-top-left-desc" @click.stop="changeIsNickName">
                {{ isNickName ? '贡献值' : '昵称' }}
            </div>
        </template>
        <template v-else-if="teamPlay?.extInfo?.isPublicityPeriod" #topLeftTtle>
            战队赛公示
        </template>
        <template v-else #topLeftTtle> 战队赛报名 </template>

        <template #topRightText>
            <div>
                {{ rightText }}
            </div>
        </template>
        <!-- 活动中 -->
        <template v-if="teamStatus === TEAMSTATUS.ACTIVITY" #cardContainerUnBg>
            <!-- 活动期，队长在第一个 -->
            <div
                class="activity-avatar-list flex-center-center"
                :class="{ 'avatar-list-box-many': avatarList?.length > 3 }"
            >
                <div
                    v-for="(item, index) in avatarList.slice(0, 6)"
                    :key="index"
                    class="user-item flex-center-center"
                >
                    <div class="avatar-item">
                        <img
                            :src="avatarUrl(item)"
                            class="avatar-item-img a-stroke-main"
                        />
                        <span v-if="item.teamLeader" class="team-leader" />
                    </div>

                    <span class="avatar-name avatar-score">
                        {{
                            isNickName
                                ? nameSlice(item.itemName, 5)
                                : item.h5ShowScore
                        }}
                    </span>
                </div>
            </div>
        </template>
        <!-- 公示期 -->
        <template
            v-else-if="teamStatus === TEAMSTATUS.ISPUBLICITYPERIOD"
            #cardContainerBg
        >
            <div class="flex-center-center card-unactive-box-center">
                主播未寻找到好友组成战队
            </div>
        </template>
        <!-- 未组队主播侧 -->
        <template
            v-else-if="teamStatus === TEAMSTATUS.UNTEAM && bolIsAuthor"
            #cardContainerUnBg
        >
            <div class="card-unteam-btn" />
        </template>
        <!--未组队 观众端 -->
        <template
            v-else-if="teamStatus === TEAMSTATUS.UNTEAM && !bolIsAuthor"
            #cardContainerBg
        >
            <div class="card-unactive-box-center flex-center-center">
                主播正在组队中，点击查看
            </div>
        </template>
        <!-- 已组队 -->
        <template v-else-if="teamStatus === TEAMSTATUS.TEAMED" #cardContainerBg>
            <div class="card-unactive-box">
                <span>主播已组队</span>
                <div class="unactive-avatar-list">
                    <div
                        v-for="(item, index) in avatarList"
                        :key="index"
                        class="avatar-item"
                    >
                        <img
                            class="avatar-item-img a-stroke-main"
                            :src="avatarUrl(item)"
                        />
                    </div>
                </div>
            </div>
        </template>
    </cardTemplate>
</template>

<script setup lang="ts">
import { computed, ref, getCurrentInstance } from 'vue';
import { storeToRefs } from 'pinia';
import {
    activityBiz,
    appendParam,
    authorId,
    bolIsAuthor,
    entry_src,
    layoutType,
    liveStreamId,
    nameSlice,
} from '@alive-ui/actions';
import cardTemplate from './card-template.vue';
import useKConfStore from '@/@pet/ones-use.useKconfBatch/index';
const { proxy } = getCurrentInstance() as any;

const { kconfData } = storeToRefs(useKConfStore());

const isNickName = ref(false);

const TEAMSTATUS = {
    UNTEAM: 1,
    TEAMED: 2,
    ACTIVITY: 3,
    ISPUBLICITYPERIOD: 4,
};
const props = defineProps({
    teamPlay: {
        type: Object,
        required: true,
    },
    bottomInfo: {
        type: Object,
        required: true,
    },
});

function avatarUrl(item: { headUrl: string }) {
    return (
        item?.headUrl ||
        kconfData.value.mainPage?.newAnchorCard?.teamInfo?.defaultAvatar
    );
}

const teamStatus = computed(() => {
    // 活动期
    if (props.teamPlay?.extInfo?.competing) {
        return TEAMSTATUS.ACTIVITY;
    }

    // 已组队
    if (props.teamPlay?.extInfo?.authorTeamInfo) {
        return TEAMSTATUS.TEAMED;
    }

    // 公示期
    if (props.teamPlay?.extInfo?.isPublicityPeriod) {
        return TEAMSTATUS.ISPUBLICITYPERIOD;
    }
    // 未组队
    return TEAMSTATUS.UNTEAM;
});

// 头像展示列表
const avatarList = computed(() => {
    // 活动期（已开启
    if (teamStatus.value === TEAMSTATUS.ACTIVITY) {
        if (props.bottomInfo?.itemRankInfo) {
            return props.bottomInfo?.itemRankInfo.sponsors || [];
        }

        return [];
    }

    // 非活动期（未开启） 返回组员信息
    return (
        [props.teamPlay?.extInfo?.authorTeamInfo?.leaderInfo].concat(
            props.teamPlay?.extInfo?.authorTeamInfo?.memberList,
        ) || []
    );
});
// 右上角文字
const rightText = computed(() => {
    const rightTextIng =
        kconfData.value.mainPage?.newAnchorCard?.teamInfo?.rightTextIng;
    const rightTextUnIng =
        kconfData.value.mainPage?.newAnchorCard?.teamInfo?.rightTextUnIng;
    return teamStatus.value === TEAMSTATUS.ACTIVITY
        ? rightTextIng
        : rightTextUnIng;
});

// const joinTeam = () => {
//     teamModify({
//         popupType: PopupTypeEnum.JOIN,
//         avatarUrl: '',
//         liveStreamId: '',
//         inputValue: '',
//     });
// };

const changeIsNickName = () => {
    isNickName.value = !isNickName.value;
};
// 卡片跳转
const goToPage = () => {
    proxy.$router.push({
        name: 'team',
        query: {
            showFirstBackBtn: true,
        },
    });
};
</script>

<style lang="less" scoped>
.user-item {
    flex-flow: column;
    width: 66px;
}
.avatar-item {
    position: relative;
    &-img {
        display: block;
        width: 26px;
        height: 26px;
        border: 1px solid;
        border-radius: 50%;
    }
}

.avatar-name,
.avatar-score {
    margin-top: 6px;
    font-size: 10px;
    line-height: 10px;
}
.team-leader {
    position: absolute;
    top: -8px;
    right: -13px;
    width: 24px;
    height: 12px;
    background: url('./../assets/zhu-bo-biao-qian_2x.png') center / 100%
        no-repeat;
    content: '';
}

.unactive-avatar-list {
    display: flex;
    padding-right: 5px;
    .avatar-item {
        margin-right: -5px;
    }
}
.card-unteam-btn {
    width: 296px;
    height: 48px;
    margin: 0 auto;
    margin-top: 8px;
    background: url('../assets/nei-rong_2x.png') center / 100% no-repeat;
}
.card-unactive-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
}
.card-unactive-box-center {
    width: 100%;
    height: 100%;
}
.activity-avatar-list {
    width: 100%;
    height: 100%;
    margin-top: 8px;
}
.card-top-left-desc {
    position: relative;
    display: inline-flex;
    margin-left: 4px;
    font-family: 'PingFang SC';
    font-size: 12px;
    color: rgba(#ffdfbf, 0.6);
    align-items: center;
    justify-content: center;

    &::after {
        display: block;
        width: 10px;
        height: 10px;
        margin-left: 2px;
        background: url('../assets/switch-icon_2x.png') center / 100% no-repeat;
        content: '';
    }
}
</style>
