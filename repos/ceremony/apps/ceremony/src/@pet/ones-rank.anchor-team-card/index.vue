<template>
    <div v-if="bottomInfo && teamPlay" class="team-card">
        <div v-if="pageType === 'team'">
            <CardContent :team-play="teamPlay" :bottom-info="bottomInfo" />
        </div>
        <div v-if="pageType === 'home'">
            <cardContentHome
                :team-play="teamPlay"
                :bottom-info="bottomInfo"
            ></cardContentHome>
        </div>
    </div>
</template>
<script setup lang="ts">
import CardContent from './components/card-content.vue';
import cardContentHome from './components/card-content-home.vue';

defineProps({
    teamPlay: {
        type: Object,
        default: () => {},
    },
    bottomInfo: {
        type: Object,
        default: () => {},
    },
    pageType: {
        type: String as () => 'home' | 'team',
        default: 'team',
    },
});
</script>

<style lang="less">
.team-card {
    width: 100%;
    margin-bottom: 12px;
    background: rgba(154, 189, 255, 0.06);
    border-radius: 8px;
    padding: 12px;
}
.swiper-card {
    &-inner {
        height: 98px;
        margin-left: 0;
    }
    &-item {
        height: 98px;
        padding: 12px;
        border-radius: 8px;
        flex-shrink: 0;
        background: rgba(242, 242, 255, 6%);
    }
}
</style>
