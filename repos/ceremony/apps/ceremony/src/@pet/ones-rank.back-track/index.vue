<style lang="less" scoped>
.back-track {
    box-sizing: border-box;
    width: 382px;
    height: 34px;
    padding: 8px 12px;
    display: flex;
    margin: auto;
    justify-content: center;
    align-items: center;
    background: #f1f1ff0f;
    border-radius: 8px;
    .back-button {
        box-sizing: border-box;
        width: 16px;
        height: 16px;
        overflow: hidden;
    }
    .back-button-secondary {
        box-sizing: border-box;
        // width: 120px;
        height: 18px;
        color: #ffdfbf;
        font-size: 12px;
        z-index: 2;
        text-align: center;
        vertical-align: top;
        line-height: 18px;
        font-family: 'PingFang SC';
    }
}
</style>

<template>
    <div v-if="showBack" class="back-track" @click="backTo">
        <img class="back-button" src="./back_2x.png" />
        <span class="back-button-secondary">返回当前主播所在赛道</span>
    </div>
</template>

<script lang="ts" setup>
import { defineEmits, computed } from 'vue';
interface Data {
    isReplay?: boolean;
    isJoinActivity?: boolean;
    isJoinCurrRank?: boolean;
    isDirectPromotionFinal?: boolean;
    changeLane?: boolean;
    anchorStageType?: number;
    curStageType?: number;
    clearingEndTime?: boolean;
    inGame?: boolean;
}
const props = withDefaults(
    defineProps<{
        data: any;
        type: 'main' | 'sub';
    }>(),
    {
        data: () => ({}),
        type: 'main',
    },
);
const emits = defineEmits(['back']);
const showBack = computed(() => {
    const {
        isReplay,
        isJoinActivity,
        isJoinCurrRank,
        isDirectPromotionFinal,
        changeLane,
        anchorStageType,
        curStageType,
        clearingEndTime,
        inGame,
    } = props.data || {};
    // 定榜不显示返回
    if (clearingEndTime) {
        return false;
    }
    // if (
    //     props.type === 'main' &&
    //     curStageType &&
    //     anchorStageType &&
    //     curStageType !== anchorStageType
    // ) {
    //     return true;
    // }
    return (
        !isReplay &&
        isJoinActivity &&
        !isJoinCurrRank &&
        !isDirectPromotionFinal &&
        changeLane &&
        inGame
    );
});
const backTo = () => {
    emits('back');
};
</script>
