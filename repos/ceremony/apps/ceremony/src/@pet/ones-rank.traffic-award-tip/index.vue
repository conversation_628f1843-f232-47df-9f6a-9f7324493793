<!-- top流量条提示 -->
<template>
    <div v-if="trafficAward" class="rank-traffic-tip text-12 a-text-main">
        <span
            v-for="(textItem, textIndex) in trafficAward.strArray"
            :key="textIndex + '' + textItem"
            :class="{ 'highlight-award': textItem.highlight }"
        >
            &nbsp;{{ textItem.text }}
        </span>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { dataFun } from './data';
import type { TopNTrafficAward } from '@pet/ones-rank.schema/query-rank';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
interface trafficData extends PropsCommonParams {
    data?: {
        topNTrafficAward?: TopNTrafficAward;
    };
}
const props = withDefaults(defineProps<trafficData>(), {
    data: () => {
        return {};
    },
    useProps: false,
    contextName: Symbol.for('ctx'),
});
const data = props.useProps ? ref(props.data) : dataFun(props.contextName);

const formatTrafficAwardTpl = (
    timeStr: string,
    count: string,
    textTemplate: string,
) => {
    const awardStr = textTemplate
        .replace('%s', timeStr ? `%s${timeStr}%s` : '')
        .replace('%d', count ? `%s${count || ''}%s` : '');

    return awardStr
        .split('%s')
        .filter((text) => text)
        .map((text) => ({
            text,
            highlight: text === count || text === timeStr,
        }));
};

const formatNumber = (v: number) => {
    if (Number.isNaN(v)) {
        return '';
    }

    if (v < 10000) {
        return v;
    }

    return `${Math.floor(v / 10000)}w`;
};
const trafficAward = computed(() => {
    const topNTrafficAward = data.value?.topNTrafficAward;

    if (
        !topNTrafficAward ||
        Object.keys(topNTrafficAward).length === 0 ||
        !topNTrafficAward?.textTemplate
    ) {
        return null;
    }
    const { rankPointEnd, timeStr, count, textTemplate } = topNTrafficAward;
    const strArray = formatTrafficAwardTpl(
        timeStr,
        formatNumber(+count).toString(),
        textTemplate,
    );

    if (!strArray.length) {
        return null;
    }

    return {
        position: +rankPointEnd,
        strArray,
    };
});
</script>
<style lang="less" scoped>
.rank-traffic-tip {
    width: 358px;
    height: 32px;
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 16px;
    background: rgba(217, 221, 255, 0.06);
    border-radius: 8px;
    justify-content: center;
    margin: 14px auto;
}
</style>
