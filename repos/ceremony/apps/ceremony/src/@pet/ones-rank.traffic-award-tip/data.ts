import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';
export const dataFun = (contextName: symbol) => {
    const rankInfo = contextName
        ? inject<Ref<QueryRankPostResponse>>(contextName)
        : null;
    const data = computed(() => {
        return {
            topNTrafficAward: rankInfo?.value?.extraData?.topNTrafficAward,
        };
    });
    return data;
};
