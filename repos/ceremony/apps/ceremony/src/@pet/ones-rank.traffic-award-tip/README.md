# Top 流量条提示组件

## 功能概述

该组件用于显示排行榜中的流量奖励提示信息。根据提供的 `topNTrafficAward` 数据，格式化并高亮显示特定的时间和数量信息。

## 属性

| 属性名 | 类型 | 默认值 | 描述 |
| --- | --- | --- | --- |
| data | object | `{}` | 包含 `topNTrafficAward` 的数据对象，用于展示流量奖励信息。 |
| useProps | boolean | `false` | 是否直接使用传递的 `props` 中的数据。 |
| contextName | string | `''` | 如果 `useProps` 为 `false`，则使用此参数从外部数据源获取数据。 |

## 使用示例

```vue
<template>
  <RankTrafficTip :data="rankData" />
</template>

<script setup>
import RankTrafficTip from './RankTrafficTip.vue';

const rankData = {
  topNTrafficAward: {
    rankPointEnd: 100,
    timeStr: '2小时',
    count: 10000,
    textTemplate: '比赛结束前%s内，排名前%d的用户将获得额外流量奖励'
  }
};
</script>
```

## 注意事项

- 确保 `topNTrafficAward` 对象中的 `textTemplate` 字段包含 `%s` 和 `%d` 占位符，以正确格式化时间和数量。
- 如果 `useProps` 为 `true`，则直接使用传递的 `props` 中的数据；否则，从 `contextName` 指定的外部数据源获取数据。

## 依赖项

- `@pet/ones-rank.schema/query-rank`
- `@pet/ones-rank.schema/global`