<template>
    <div
        v-if="swiperAwardData.length"
        ref="awardAreaRef"
        class="award-area mt-16px pos-relative"
    >
        <div
            ref="topTipRef"
            class="top-tip w-16px h-8px pos-absolute top-[24.5px]"
            :class="`top-tip-${awardSwiperIndex}`"
            :style="{
                left: `${topTipArrowLeft}px`,
            }"
        ></div>
        <!-- 奖励tab -->
        <div
            v-if="tabs?.length"
            class="award-tabs mb-12px leading-20px flex-start-center"
        >
            <div
                v-for="(tab, idx) in tabs"
                :key="tab"
                class="award-tab text-14px mr-12px"
                :class="{ active: idx === awardSwiperIndex }"
                @click="onTabChange(idx)"
            >
                <span class="tab-name">{{ tab }}</span>
            </div>
        </div>
        <div ref="awardSwiperRef" class="award-swiper-container rounded-8px">
            <div class="swiper-wrapper">
                <div
                    v-for="(awardData, index) in swiperAwardData"
                    :key="index"
                    :class="[
                        'swiper-slide',
                        'rank-award-content',
                        'flex',
                        'items-center',
                        'mx-auto',
                        'pb-16px',
                        'a-bg-substrate',
                        'rounded-8px',
                        getLengthClass(awardData),
                        {
                            'single-area-wrap':
                                awardData.type === AwardTypeEnum.Single,
                        },
                    ]"
                >
                    <!-- 多个奖励排列 -->
                    <template v-if="awardData.type === AwardTypeEnum.Muti">
                        <div
                            v-pcDirectives:scroll="handleScroll"
                            class="rank-award-scfollList rounded-8px"
                            @scroll="handleScroll"
                        >
                            <div
                                v-for="(award, index) in awardData.awardList"
                                :key="award.awardId"
                                class="rank-award-item"
                                @click="
                                    () => {
                                        handleAwardClick(award, index);
                                    }
                                "
                            >
                                <div class="pos-relative">
                                    <img
                                        :src="award.awardIcon"
                                        alt="活动奖励说明"
                                    />
                                    <div
                                        v-if="
                                            ![
                                                AwardStatusEnum.NO_NEED_UNLOCK_ONE,
                                                AwardStatusEnum.NO_NEED_UNLOCK_TWO,
                                            ].includes(award.status)
                                        "
                                        class="flex-center-center w-30px pl-2px pr-2px award-tip pos-absolute h-12px rounded-9px top-11px right-[-8px]"
                                        :class="{
                                            unlocking:
                                                award.status ==
                                                AwardStatusEnum.UNLOCKING,
                                        }"
                                    >
                                        {{
                                            ![
                                                AwardStatusEnum.NO_NEED_UNLOCK_ONE,
                                                AwardStatusEnum.NO_NEED_UNLOCK_TWO,
                                            ].includes(award.status)
                                                ? AwardStatusMap[award.status]
                                                : award.awardTip
                                        }}
                                    </div>
                                </div>
                                <div class="rank-award-text">
                                    {{ award.awardName }}
                                </div>
                                <AwardProgress
                                    v-if="
                                        ![
                                            AwardStatusEnum.NO_NEED_UNLOCK_ONE,
                                            AwardStatusEnum.NO_NEED_UNLOCK_TWO,
                                        ].includes(award.status)
                                    "
                                    :max="
                                        getMax(
                                            award.score,
                                            award.targetScore,
                                            award.status,
                                        )
                                    "
                                    :value="
                                        getValue(
                                            award.score,
                                            award.targetScore,
                                            award.status,
                                        )
                                    "
                                    class="mt-4px"
                                >
                                    <template #text>
                                        <div
                                            class="text-din a-text-main text-9px"
                                        >
                                            {{
                                                award.status ===
                                                AwardStatusEnum.UNLOCKED
                                                    ? '已完成'
                                                    : `${award.h5ShowScore}/${award.targetH5ShowScore}`
                                            }}
                                        </div>
                                    </template>
                                </AwardProgress>
                            </div>
                        </div>
                        <div
                            v-show="leftMaskShow && awardSwiperLength > 4"
                            class="rank-award-scfollList-left-mask award-mask"
                        ></div>
                        <div
                            v-show="rightMaskShow && awardSwiperLength > 4"
                            class="rank-award-scfollList-right-mask award-mask"
                        ></div>
                    </template>
                    <!-- 单个奖励 -->
                    <div
                        v-else-if="awardData.type === AwardTypeEnum.Single"
                        class="single-area flex-start-center w-[100%] h-114px"
                    >
                        <img :src="awardData.previewPrivilegeIcon" />
                        <div class="single-desc ml-10px">
                            <div class="text-bold text-14px a-text-main">
                                {{ awardData.previewTitle }}
                            </div>
                            <div class="mt-4px text-12px a-text-main-o2">
                                {{ awardData.previewSubTitle }}
                            </div>
                        </div>
                    </div>
                    <div class="hack-cover"></div>
                </div>
            </div>
        </div>
        <!-- 弹窗组件，多个奖励排布有半屏弹窗 -->
        <Popup
            v-if="showPrizeDetail && curAward.type === AwardTypeEnum.Muti"
            v-model="showPrizeDetail"
            :show-mask="true"
            position="bottom"
            :mask-closeable="true"
            popup-class="prize-popup-wrap"
            @hide="showPrizeDetail = false"
        >
            <slot
                name="award-scroll-popup-content"
                :top-awards="curAward.topAwards"
                :popup-title="popupTitle"
                :show-prize-detail="showPrizeDetail"
            >
                <PopupContent
                    v-model:swiper-index="swiperIndex"
                    :top-awards="curAward.topAwards"
                    :popup-title="popupTitle"
                    :show-prize-detail="showPrizeDetail"
                />
            </slot>
        </Popup>
    </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, nextTick, onMounted, onUnmounted } from 'vue';
import { Autoplay, FreeMode } from 'swiper/modules';
import Swiper from 'swiper';
import { stickLockScroll } from '@pet/ones-use.useStickyElement';
import useKConfBatch from '@pet/ones-use.useKconfBatch';
import {
    PrivielgeShowEnum,
    AwardStatusMap,
    AwardStatusEnum,
    type PeakPrivilege,
    type TrafficPrivilegeUnlockPreviewInfo,
} from '@pet/ones-rank.schema/query-rank';
import PopupContent from '@pet/ones-rank.award-collapse/components/award-popup-content.vue';
import AwardProgress from '@pet/ones-rank.addition-tips/index.vue';
// import { Popup, Swiper } from '@lux/sharp-ui-next';
import { Popup } from '@lux/sharp-ui-next';
import { stopMove, type StopFunItem } from '@alive-ui/actions';
import type { ChampionAward, TopAward } from './schema';
import 'swiper/css';
import 'swiper/css/autoplay';
import 'swiper/css/free-mode';

enum AwardTypeEnum {
    Single,
    Muti,
}
type SwiperAwardSingle = {
    previewTitle: string;
    previewSubTitle: string;
    previewPrivilegeIcon: string;
    tabName?: string;
    type: AwardTypeEnum;
};
type SwiperAwardMuti = {
    awardList: PeakPrivilege['privilegeDetails'] | ChampionAward[];
    topAwards: TopAward[];
    type: AwardTypeEnum;
};

type SwiperAward =
    | (SwiperAwardSingle & { type: AwardTypeEnum.Single })
    | (SwiperAwardMuti & { type: AwardTypeEnum.Muti });

const props = withDefaults(
    defineProps<{
        top3Award: {
            awardList: ChampionAward[];
            topAwards: TopAward[];
            isFinals: boolean;
            todayPrivilegeTitle?: string;
        };
        peakPrivilege: PeakPrivilege;
        unlockInfo: TrafficPrivilegeUnlockPreviewInfo;
        showScene: number;
    }>(),
    {
        top3Award: () => ({}) as any,
        peakPrivilege: () => ({}) as PeakPrivilege,
        unlockInfo: () => ({}) as TrafficPrivilegeUnlockPreviewInfo,
    },
);

const kconf = useKConfBatch();
const animationConfig = computed(() => {
    return (
        kconf.kconfData?.mainPage?.awardSwiperAnimation ?? {
            during: 3000,
            transitionTime: 500,
        }
    );
});

const leftMaskShow = ref(false);
const rightMaskShow = ref(true);
// const awardLength = computed(() => {
//     return awardData.value?.awardList?.length || 0;
// });

const popupTitle = computed(() => {
    //  2025夏季盛典不区分决赛和非决赛阶段，但是保留这个判断逻辑吧，以免后续又要区分
    return props.top3Award?.isFinals ? '比赛奖励' : '比赛奖励';
});

const showFansAwards = computed(() => {
    return props.showScene === 1 && props.unlockInfo.tabName;
});

const tabs = computed(() => {
    const { awardList = [] } = props.top3Award;
    const { privilegeDetails = [] } = props.peakPrivilege;
    const res: string[] = [];
    if (showFansAwards.value) {
        res.push(props.unlockInfo.tabName);
    }

    switch (props.peakPrivilege.privilegeShowStatus) {
        case PrivielgeShowEnum.TODAY:
            res.push(props.peakPrivilege.privilegeTitle);
            break;
        case PrivielgeShowEnum.FINAL:
            res.push(props.top3Award.todayPrivilegeTitle!);
            break;
        case PrivielgeShowEnum.TODAY_FINAL:
            //  这里是兼容奖励数组为空的逻辑
            if (privilegeDetails.length) {
                res.push(props.peakPrivilege.privilegeTitle);
            }
            if (awardList.length) {
                res.push(props.top3Award.todayPrivilegeTitle!);
            }
            break;
        default:
            break;
    }
    return res;
});

const awardSwiperRef = ref();
const awardSwiperIndex = ref(0);
const swiperAwardData = computed<SwiperAward[]>(() => {
    const {
        privilegeShowStatus,
        privilegeDetails = [],
        topAwards = [],
    } = props.peakPrivilege;
    const { awardList = [], topAwards: top3AwardList = [] } = props.top3Award;

    const showAwardList = awardList.map((item) => {
        return {
            ...item,
            status: AwardStatusEnum.NO_NEED_UNLOCK_ONE,
        };
    });
    const allAwards: SwiperAward[] = [];
    if (showFansAwards.value && props.unlockInfo.previewPrivilegeIcon) {
        allAwards.push({
            ...props.unlockInfo,
            type: AwardTypeEnum.Single,
        });
    }
    if (privilegeShowStatus === PrivielgeShowEnum.TODAY) {
        privilegeDetails.length &&
            allAwards.push({
                awardList: privilegeDetails,
                topAwards,
                type: AwardTypeEnum.Muti,
            });
    } else if (privilegeShowStatus === PrivielgeShowEnum.FINAL) {
        showAwardList.length &&
            allAwards.push({
                awardList: showAwardList as PeakPrivilege['privilegeDetails'],
                topAwards: top3AwardList,
                type: AwardTypeEnum.Muti,
            });
    } else {
        if (privilegeDetails.length) {
            allAwards.push({
                awardList: privilegeDetails,
                topAwards,
                type: AwardTypeEnum.Muti,
            });
        }
        if (showAwardList.length) {
            allAwards.push({
                awardList: showAwardList as PeakPrivilege['privilegeDetails'],
                topAwards: top3AwardList,
                type: AwardTypeEnum.Muti,
            });
        }
    }
    return allAwards;
});
const curAward = computed(() => {
    return swiperAwardData.value[awardSwiperIndex.value] ?? ({} as any);
});
const awardSwiperLength = computed(() => {
    if (curAward.value.type === AwardTypeEnum.Muti) {
        return curAward.value.awardList?.length ?? 0;
    }
    if (curAward.value.type === AwardTypeEnum.Single) {
        return 4;
    }
    return 0;
});
const topTipArrowLeft = ref(0);
const onTabChange = (index: number) => {
    if (!transitionEnd.value) return;

    awardSwiperIndex.value = index;
    theSwiper.value?.slideToLoop(
        index,
        animationConfig.value.transitionTime,
        false,
    );
    updateTopTip();
};
const awardAreaRef = ref();
const topTipRef = ref();
let awardAreaBoundings: DOMRect | undefined;
let topTipArrowBoundings: DOMRect | undefined;

//  swiper
const theSwiper = ref<any>(null);
const transitionEnd = ref(true);
onMounted(() => {
    nextTick(() => {
        initSwiper();
        updateTopTip();
    });
});
const initSwiper = () => {
    try {
        if (!awardSwiperRef.value) return;

        theSwiper.value = new Swiper('.award-swiper-container', {
            modules: [Autoplay, FreeMode],
            slidesPerView: 1,
            loop: true,
            spaceBetween: 0,
            observer: true,
            autoplay: {
                delay: animationConfig.value.during,
                disableOnInteraction: false,
            },
            freeMode: {
                enabled: true,
                momentum: false,
            },
            speed: animationConfig.value.transitionTime,
            allowTouchMove: false,
            preventInteractionOnTransition: true,
            on: {
                touchStart: () => {
                    if (theSwiper.value) {
                        theSwiper.value.autoplay?.stop(); // 用户滑动后停止自动播放
                    }
                },
                touchEnd: () => {
                    if (theSwiper.value) {
                        theSwiper.value.autoplay?.start(); // 用户松开后恢复自动播放
                    }
                },
                slideChange: (swiper) => {
                    if (swiper.realIndex !== awardSwiperIndex.value) {
                        awardSwiperIndex.value = swiper.realIndex;
                        updateTopTip();
                    }
                },
                transitionStart: () => {
                    transitionEnd.value = false;
                },
                transitionEnd: () => {
                    transitionEnd.value = true;
                },
            },
        });
    } catch (error) {
        console.log(error, 'errorerrorerror');
    }
};
const updateTopTip = () => {
    nextTick(() => {
        if (!awardAreaBoundings) {
            awardAreaBoundings = awardAreaRef.value?.getBoundingClientRect();
        }
        if (!topTipArrowBoundings) {
            topTipArrowBoundings =
                topTipRef.value?.getBoundingClientRect() ?? {};
        }
        const tabEl = document.querySelector('.award-tab.active');
        const tabElBoundings =
            tabEl?.getBoundingClientRect() ?? ({} as DOMRect);
        if (
            awardAreaBoundings &&
            tabElBoundings.left &&
            topTipArrowBoundings?.width
        ) {
            topTipArrowLeft.value =
                tabElBoundings.left -
                awardAreaBoundings.left +
                (tabElBoundings.width - topTipArrowBoundings.width) / 2;
        }
    });
};

const swiperIndex = ref(0);

const getLengthClass = (awardData: SwiperAward) => {
    const length =
        awardData.type === AwardTypeEnum.Muti
            ? awardData.awardList?.length ?? 0
            : 4;
    if (!length) return '';
    return length > 4 ? 'length-more-than-4' : `length-${length}`;
};

const handleScroll = (e: any) => {
    const scrollRef = e.target;
    if (!scrollRef) return;
    if (scrollRef.scrollLeft === 0) {
        leftMaskShow.value = false;
    } else if (
        scrollRef.scrollLeft + scrollRef.offsetWidth + 0.5 >=
        scrollRef.scrollWidth
    ) {
        rightMaskShow.value = false;
    } else {
        leftMaskShow.value = true;
        rightMaskShow.value = true;
    }
};
const handleAwardClick = (award: any, index: number) => {
    showPrizeDetail.value = true;
    swiperIndex.value = index;
};

const getMax = (
    score: number,
    targetScore: number,
    status: AwardStatusEnum,
) => {
    if (!targetScore) return 100;
    return targetScore;
};

const getValue = (
    score: number,
    targetScore: number,
    status: AwardStatusEnum,
) => {
    if (status === AwardStatusEnum.UNLOCKED) {
        if (!targetScore) return 100;
        return targetScore;
    }
    if (!score || score < 0.1 * targetScore) return 0.1 * targetScore;

    return score;
};

const showPrizeDetail = ref(false);

// 处理ios弹窗溢出滚动
let stopMoveObj: StopFunItem;

watch(
    () => showPrizeDetail.value,
    (o) => {
        if (o) {
            stickLockScroll.value = true;
            stopMoveObj = stopMove();
            theSwiper.value?.autoplay?.stop();
        } else {
            stopMoveObj?.cancelMove?.();
            stickLockScroll.value = false;
            theSwiper.value?.autoplay?.start();
        }
    },
    { immediate: true },
);

onUnmounted(() => {
    theSwiper.value?.destroy(true, true);
    theSwiper.value = null;
    stopMoveObj?.cancelMove?.();
    stopMoveObj?.destroy?.();
});
</script>

<style lang="less" scoped>
.award-swiper-container {
    overflow: hidden;
}
.single-area-wrap {
    padding: 0 0 !important;
}
.single-area {
    margin-left: 28px;
    img {
        height: 72px;
        width: 72px !important;
        object-fit: contain;
    }
}
.hack-cover {
    background-color: #0c144f;
    position: absolute;
    height: 100%;
    top: 0;
    left: 0;
    width: 1px;
}
.top-tip {
    background: url('./top-arraw.png') no-repeat center/100%;
}
// .top-tip-0 {
//     left: 21px;
// }
// .top-tip-1 {
//     left: 105px;
// }
// .top-tip-2 {
//     left: 105px;
// }
.award-mask {
    height: 100% !important;
    border-radius: 0 8px 8px 0;
}
.prize-popup-wrap {
    :deep(.prize-popup) {
        @apply a-bg-page;
    }
}
.rank-award-content {
    width: 358px;
    min-height: 114px;
    font-size: 0;
    padding: 19px 0 16px;
    position: relative;
    box-sizing: border-box;
    img {
        width: 100%;
    }
}
.award-tabs {
    .award-tab {
        @apply a-text-title;
        font-family: HYYakuHei;
        opacity: 0.5;
    }
    .active {
        opacity: 1;
    }
}
.award-tip {
    background: linear-gradient(264.25deg, #ffe4cb 0%, #fee1ba 93.85%);
    color: #310b0f;
    font-size: 8px;
    padding: 1px 3px;
    white-space: nowrap;
}
.unlocking {
    background: #ff5477;
    color: #fff;
}
.lock-process {
    background-color: rgba(145, 57, 83, 0.2);
}
.rank-award-scfollList {
    width: 100%;
    height: 100%;
    overflow-x: scroll;
    // overflow-y: hidden;
    display: flex;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
        display: none;
        width: 0;
    }
    &-left-mask {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 32px;
        height: 107px;
        background: url(./rank-award-scroll-mask_2x.png) center / 100% no-repeat;
        transform: scaleX(-1);
    }

    &-right-mask {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 32px;
        height: 107px;
        background: url(./rank-award-scroll-mask_2x.png) center / 100% no-repeat;
    }
}
.rank-award-text {
    font-size: 10px;
    line-height: 14px;
    opacity: 0.6;
    text-align: center;
    color: #ffdfbf;
}
.rank-award-item {
    width: 84px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:last-child {
        margin-right: 0 !important;
    }
    img {
        width: 74px;
        height: 74px;
        margin-bottom: 4px;
    }
}
// item长度为1的样式
.length-1 {
    padding: 8px 0 16px;
    .rank-award-scfollList {
        justify-content: center;
    }
}
// item长度为2的样式
.length-2 {
    padding: 8px 0 16px;
    .rank-award-item {
        margin-right: 32px;
    }
    .rank-award-scfollList {
        justify-content: center;
    }
}
// item长度为3的样式
.length-3 {
    padding: 8px 0 16px;
    .rank-award-item {
        margin-right: 20px;
    }
    .rank-award-scfollList {
        justify-content: center;
    }
}
// item长度为4的样式
.length-4 {
    padding: 8px 0 16px;
    .rank-award-scfollList {
        justify-content: center;
    }
}
// item长度大于4
.length-more-than-4 {
    padding: 8px 12px 16px;
    .rank-award-item {
        margin-right: 12px;
    }
    .rank-award-scfollList {
        justify-content: flex-start;
    }
}
.rank-award-item {
    img {
        height: 72px;
        width: 72px;
    }
}
</style>
