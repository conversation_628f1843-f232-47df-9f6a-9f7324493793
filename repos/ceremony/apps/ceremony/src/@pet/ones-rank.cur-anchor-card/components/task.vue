<template>
    <!-- 只要有值，就要展示 -->
    <div
        v-if="
            ((Object.keys(data || {}).length && !isDef(data.showConfig)) ||
                data?.showScene === 0) &&
            !kconfData.mainPage?.task.close
        "
        v-show-log="{
            action: 'OP_ACTIVITY_HIDDEN_TASK_CARD',
            params: {
                // 1：未达门槛也没解锁 2：达门槛未解锁 3：已解锁  4: 已完成
                type:
                    peakRankTask?.finishTask === false ? 1 : +data?.status + 2,
            },
        }"
        class="task"
        :class="{
            'task-hide':
                (peakRankTask?.finishTask === false &&
                    data?.status === TASK_LOCK.NONE) ||
                data?.status === TASK_LOCK.DONE,
        }"
    >
        <!-- 状态1 决赛任务未完成，无法开始做小单任务 -->
        <div
            v-if="
                peakRankTask?.finishTask === false &&
                data?.status === TASK_LOCK.NONE
            "
        >
            <span class="task-title">涨粉挑战</span>
            <span class="task-title-desc">{{
                kconfData.mainPage?.task?.none?.[0]
            }}</span>
            <span class="task-title-count">{{
                data?.taskList?.[+data?.taskList?.length - 1]?.privilegeDesc
            }}</span>
        </div>
        <!-- 状态2/3 -->
        <div
            v-else-if="
                // 决赛任务已经完成，开始做小单任务（服务端状态0/1&决赛任务已完成,  没有决赛任
                (peakRankTask?.finishTask ||
                    !Object.keys(peakRankTask || {}).length) &&
                (data?.status === TASK_LOCK.NONE ||
                    data?.status === TASK_LOCK.DOING)
            "
        >
            <div class="task-title">
                {{ kconfData.mainPage?.task?.doing?.[0] }}
            </div>
            <div class="task-thing">
                <span class="task-thing-cur">{{ data?.showScore }}</span>
                <span class="task-thing-total"
                    >/{{ data?.targetShowScore }}</span
                >
                <span class="task-thing-desc">热度值</span>
                <span class="tanhao" @click="showTips = !showTips">
                    <span v-if="showTips" class="tanhao-tips">
                        {{ kconfData.mainPage?.task?.tip }}
                    </span>
                </span>
            </div>
            <div class="task-bg">
                <ProgressCircle
                    v-if="data?.taskList?.length"
                    class="task-bg-circle"
                    :value="process"
                    :max="100"
                    :height="70"
                    :width="304"
                    :radius="800"
                    :bottom-margin="50"
                    :side-margin="50"
                    :angle-span="22.5"
                    track-gradient-string="linear-gradient(88.09deg, rgba(255, 84, 119, 0) -0.09%, rgba(255, 84, 119, 0.2) 11.23%, rgba(255, 84, 119, 0.2) 86.66%, rgba(255, 84, 119, 0) 98.98%)"
                    progress-gradient-string="linear-gradient(90deg, rgba(255, 71, 146, 0) 7.43%, rgba(255, 83, 126, 0.8) 55.47%, #FF6B57 100.7%)"
                    :nodes="nodes"
                >
                    <template #node="{ isActive, index, node: item }">
                        <!-- SVG -->
                        <circle
                            v-if="isActive"
                            cx="0"
                            cy="0"
                            r="3"
                            :fill="'#fff'"
                        />
                        <!-- lock -->
                        <image
                            v-else-if="index === nextTargetIndex"
                            href="../assets/light.png"
                            x="-8"
                            y="-8"
                            width="16"
                            height="16"
                            transform="rotate(8 0 0)"
                        />
                        <image
                            v-else
                            href="../assets/dark.png"
                            x="-8"
                            y="-8"
                            width="16"
                            transform="rotate(8 0 0)"
                            height="16"
                        />
                        <!-- text -->
                        <text
                            y="24"
                            :style="{
                                opacity: index === nextTargetIndex ? 1 : 0.5,
                            }"
                            text-anchor="middle"
                            fill="rgba(255, 223, 191, 1)"
                            font-size="12"
                            font-family="PingFang SC"
                            transform="rotate(8 0 0)"
                            font-weight="bold"
                        >
                            {{ item?.h5ShowFansCount }}
                        </text>

                        <text
                            y="38"
                            text-anchor="middle"
                            :style="{
                                opacity: index === nextTargetIndex ? 0.6 : 0.3,
                            }"
                            fill="rgba(255, 223, 191, 1)"
                            font-size="10"
                            font-family="PingFang SC"
                            transform="rotate(8 0 0)"
                            font-weight="normal"
                        >
                            {{
                                item?.finish
                                    ? item?.finishPrivilegeDesc
                                    : item?.privilegeDesc
                            }}
                        </text>
                    </template>
                </ProgressCircle>
            </div>
        </div>

        <!-- 状态4：已经完成小单任务。（服务端状态2） -->
        <div v-else-if="data?.status === TASK_LOCK.DONE">
            <span class="task-title">挑战成功</span>
            <span class="task-title-desc">{{
                kconfData.mainPage?.task?.done?.[0]
            }}</span>
            <span class="task-title-count">{{
                data?.taskAllFinishTotalPrivilegeCount
            }}</span>
        </div>
    </div>
</template>

<!-- todo: 状态比较多，希望服务端直接定义清晰的枚举状态，前端减少处理 -->

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { isDef } from '@pet/ones-use.usePageModulsConfig';
import useKConfBatch from '@pet/ones-use.useKconfBatch';
import ProgressCircle from '@pet/ones-ui.progress-circle/index.vue';
import type {
    TrafficPrivilegeUnlock,
    PeakRankTask,
} from '@pet/ones-rank.schema/query-rank';

const { kconfData } = storeToRefs(useKConfBatch());
const showTips = ref(false);

enum TASK_LOCK {
    // 未解锁
    NONE = 0,
    // 已解锁
    DOING = 1,
    // 任务完成
    DONE = 2,
}
const props = withDefaults(
    defineProps<{
        data: TrafficPrivilegeUnlock;
        peakRankTask: PeakRankTask;
    }>(),
    {
        data: () => ({}) as TrafficPrivilegeUnlock,
        peakRankTask: () => ({}) as PeakRankTask,
    },
);

// 这里有几种场景，分别对应3 、 4  、5 组情况
const threeNodes = [
    { value: 25, label: '' },
    { value: 50, label: '' },
    { value: 75, label: '' },
];
const fourNodes = [
    { value: 20, label: '' },
    { value: 40, label: '' },
    { value: 60, label: '' },
    { value: 80, label: '' },
];
const fiveNodes = [
    { value: 16.7, label: '' },
    { value: 33.3, label: '' },
    { value: 50, label: '' },
    { value: 66.7, label: '' },
    { value: 83.3, label: '' },
];
const nodeList = [threeNodes, fourNodes, fiveNodes];
interface INodes {
    value: number;
    label: string;
    [key: string]: string | number;
}
const nodes = ref([] as INodes[]);
const process = ref(0);
const nextTargetIndex = ref(0);

watch(
    () => props.data?.taskList,
    (val) => {
        if (!val?.length || val.length < 3) {
            return;
        }
        // 找到第一个还没完成的索引
        const hasFind = val.some((item: { finish: boolean }, index: number) => {
            if (!item.finish) {
                nextTargetIndex.value = index;
                return true;
            }
            return false;
        });
        // 已经完成了,则收起，不考虑展开的状态了
        if (!hasFind) {
            return;
        }

        // 确定nodes数组
        const temp: INodes[] = [];
        nodeList[val.length - 3].forEach((item, index) => {
            const node = { ...item, ...val[index] } as unknown as INodes;
            temp.push(node);
        });
        nodes.value = temp;

        const positionAtNode = temp.some(
            (item) => +item.targetScore === +props.data?.score,
        );
        // 假进度
        // 还没达到第一个节点，则减去8，让进度在第一个节点左侧
        if (nextTargetIndex.value === 0) {
            process.value =
                (temp[nextTargetIndex.value].value + temp[0].value) / 2 - 8;
            // 刚好在节点上
        } else if (positionAtNode) {
            process.value = temp[nextTargetIndex.value - 1].value;
            // 不在节点上，则显示在前后两个节点中间
        } else {
            process.value =
                (temp[nextTargetIndex.value].value +
                    temp[+nextTargetIndex.value - 1].value) /
                2;
        }
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.task {
    color: var(--main-color, #ffdfbf);
    font-size: 12px;
    padding: 12px;
    background: url(../assets/task.png) no-repeat;
    background-size: cover;
    margin-top: 17px;
    height: 139px;
    border-radius: 8px;

    // 折叠是北京不同
    &.task-hide {
        background: rgba(154, 189, 255, 0.08);
        height: auto;
    }

    &-title {
        background: var(
            --title-text,
            linear-gradient(89.98deg, #fff 0.03%, #ffc4a3 95.69%)
        );
        font-size: 14px;
        line-height: 20px;
        font-family: 'HYYakuHei';
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;

        &-desc {
            opacity: 0.6;
            font-family: PingFang SC;
            line-height: 18px;
            margin-left: 4px;
        }
        &-count {
            color: var(--highlight-color, #ff5477);
            font-weight: 600;
            font-family: PingFang SC;
            line-height: 18px;
        }
    }

    &-thing {
        margin-top: 4px;
        line-height: 18px;

        &-total {
            opacity: 0.6;
        }

        &-desc {
            margin-left: 4px;
            opacity: 0.6;
        }

        .tanhao {
            width: 14px;
            height: 14px;
            margin-left: 2px;
            display: inline-block;
            background: url('../assets/tanhao.png') center / 100% no-repeat;
            vertical-align: sub;
            position: relative;

            &-tips {
                position: absolute;
                top: -8px;
                right: -141px;
                width: 228px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: 500;
                transform: translateY(-100%);
                padding: 8px 16px;
                color: #fff;
                background: rgba(#000, 0.7);

                &::after {
                    content: '';
                    position: absolute;
                    bottom: -5px;
                    right: 143px;
                    width: 10px;
                    height: 5px;
                    background: url('../assets/jiantou.png') center / 100%
                        no-repeat;
                }
            }
        }
    }
    &-bg {
        position: relative;
        height: 52px;
        &-circle {
            position: absolute;
            transform: rotate(-8deg) translateY(10px) translateX(20px);
            transform-origin: center center;
        }
    }
}
</style>
