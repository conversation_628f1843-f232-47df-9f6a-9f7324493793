import type { ChampionAward, TopAward } from '@pet/ones-rank.schema/query-rank';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
interface AwardSchema {
    // 模块key
    randomKey: number;
    // 轮播文案
    marqueeList?: string[];
    // 单个下发奖励图片
    awardURL?: string;
    // 轮播奖励
    awardList?: ChampionAward[];
    // 是否展示奖励弹窗
    showAward?: boolean;
    // 奖励操作文案
    operateText?: string;
    // 顶部展示奖励图
    topAwards?: TopAward[];
    // 是否是决赛
    isFinals?: boolean;
}

export type { ChampionAward, TopAward, AwardSchema, PropsCommonParams };
