<script lang="ts" setup>
import { useRouter, type LocationQueryValueRaw } from 'vue-router';
import CurrentAnchor from '@pet/ones-rank.current-anchor/index.vue';
import TeamCard from '@pet/ones-rank.anchor-team-card/index.vue';
import { isOutLiveRoom, liveStreamId } from '@alive-ui/actions';
import Task from './components/task.vue';
import Award from './components/award/index.vue';
import type { AuthorCardAreaItem } from '@pet/ones-rank.secondary-entry/schemas';
import type {
    PeakPrivilege,
    PeakRankTask,
    TrafficPrivilegeUnlock,
    GrowFansGame,
    CountyChampionAuthor,
    BottomInfo,
} from '@pet/ones-rank.schema/query-rank';
import type { ChampionAward, TopAward, AuthorAdditionInfo } from './schema';

const props = defineProps<{
    data: {
        rankId: LocationQueryValueRaw;
        anchorData: {
            itemId: string;
            headUrl: string;
            liveStreamId: string;
            liveStreamIdList: string[];
            mysteryMan: boolean;
        };
        anchorRank: {
            displayHint: string;
            h5ShowHintScore: string;
            h5RankShowIndex: string;
            authorRankTip: string;
        };
        authorAdditionInfo: AuthorAdditionInfo;
        isReplay: boolean;
        isJoinCurrRank: boolean;
        isDirectPromotionFinal: boolean;
        isRepechageActivity: boolean;
        isJoinActivity: boolean;
        isJoinShadowActivity: boolean;
        changeLane: boolean;
        repechageResult: string;
        clearingEndTime: boolean;
        haveRankData: boolean;
        showWithExtraData: boolean;
        bottomInfo: BottomInfo;
        // isAfter23: boolean;
    };
    stageType: number;
    /**
     * 今日 top3 奖励
     */
    top3Award: {
        awardList: ChampionAward[];
        topAwards: TopAward[];
        isFinals: boolean;
        todayPrivilegeTitle: string;
    };
    /**
     * 是否满足决赛日未达门槛
     */
    isNotReachFinal: boolean;
    /**
     * 决赛奖励
     */
    peakPrivilege: PeakPrivilege;
    /**
     * 决赛任务未达门槛
     */
    peakRankTask: PeakRankTask;
    /**
     * 隐藏任务解锁
     */
    trafficPrivilegeUnlock: TrafficPrivilegeUnlock;
    /**
     * 全国涨粉包
     */
    growFansGame: GrowFansGame;
    /**
     * TODO:战队赛
     */
    teamPlay: AuthorCardAreaItem[];
    /**
     * 家乡荣耀席
     */
    countyChampionAuthor: CountyChampionAuthor;
}>();
</script>

<template>
    <CurrentAnchor
        class="cur-anchor-card"
        :data="data"
        :team-data="{
            bottomInfo: data.bottomInfo,
            teamPlay: teamPlay?.[0],
        }"
        :peak-rank-task="peakRankTask"
        :stage-type="stageType"
        :page-type="'home'"
    >
        <template #forward-anchor-bottom-area>
            <!-- 战队赛活动期 -->
            <TeamCard
                v-if="teamPlay?.[0]?.extInfo?.competing"
                :team-play="teamPlay?.[0]"
                :bottom-info="data.bottomInfo"
                page-type="home"
            />
        </template>
        <template #anchor-bottom-area>
            <!-- 决赛任务 -->
            <Task
                :data="trafficPrivilegeUnlock"
                :peak-rank-task="peakRankTask"
            />
            <!-- 奖励区域 -->
            <Award
                :top3-award="top3Award"
                :peak-privilege="peakPrivilege"
                :unlock-info="
                    trafficPrivilegeUnlock?.trafficPrivilegeUnlockPreviewInfo
                "
                :show-scene="trafficPrivilegeUnlock?.showScene"
            />
        </template>
        <template #behind-anchor-bottom-area>
            <!-- 战队赛预热期 预热：isPublicityPeriod： false -->
            <TeamCard
                v-if="!teamPlay?.[0]?.extInfo?.isPublicityPeriod"
                :team-play="teamPlay?.[0]"
                :bottom-info="data.bottomInfo"
                page-type="home"
            />
        </template>
        <template #other-bottom-area>
            <Award
                :top3-award="top3Award"
                :peak-privilege="peakPrivilege"
                :unlock-info="
                    trafficPrivilegeUnlock?.trafficPrivilegeUnlockPreviewInfo
                "
                :show-scene="trafficPrivilegeUnlock?.showScene"
            />
        </template>
    </CurrentAnchor>
</template>

<style lang="less" scoped>
.cur-anchor-card {
    margin: 14px 0px;
    border-radius: 16px;
}
</style>
