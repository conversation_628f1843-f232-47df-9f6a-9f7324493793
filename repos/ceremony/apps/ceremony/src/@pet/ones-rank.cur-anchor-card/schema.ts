import type { ChampionAward, TopAward } from '@pet/ones-rank.schema/query-rank';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';

export enum BoostCardStatus {
    // 1错失,2获得,3对决中,4未开始,5获得
    notGet = 1,
    obtained = 2,
    underWay = 3,
    notOpen = 4,
    finished = 5,
}

export enum BoostCardType {
    // 1凝聚力,2小时榜,3,车轮战
    aggregation = 1,
    hourRank = 2,
    carousel = 3,
}

export interface AuthorAdditionInfo {
    additionInfo: {
        showAdditionRate: string;
        additionRate: string;
        additionFactor: string;
    };
    state: BoostState;
    tips: string;
    prefixText: string;
}

export enum BoostState {
    // 0 待生效 1 进行中 2加成中
    notEffective = 0,
    underWay = 1,
    boosting = 2,
}
// export enum BoostCardStatusClass {
//     // 1错失,2获得,3对决中,4未开始
//      = 'not-get',
// }

export enum StageTypes {
    // 公会赛
    guild = 130,
    // 公会赛弹幕赛道
    guildBullet = 140,
    // 战队赛
    team = 220,
}
interface AwardSchema {
    // 模块key
    randomKey: number;
    // 轮播文案
    marqueeList?: string[];
    // 单个下发奖励图片
    awardURL?: string;
    // 轮播奖励
    awardList?: ChampionAward[];
    // 是否展示奖励弹窗
    showAward?: boolean;
    // 奖励操作文案
    operateText?: string;
    // 顶部展示奖励图
    topAwards?: TopAward[];
    // 是否是决赛
    isFinals?: boolean;
}

export type { ChampionAward, TopAward, AwardSchema, PropsCommonParams };
