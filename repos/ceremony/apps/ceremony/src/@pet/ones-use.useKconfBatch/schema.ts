import type { StarSchema } from './schema-star';
import type { PropCardSchema } from './schema-prop-card';
import type { PeakBattleSchema } from './schema-peak-battle';
import type { NiudanSchema } from './schema-niudan';
import type { mainPageSchema } from './schema-main-page';
import type { KvSchema } from './schema-kv';
import type { ExtraSchema } from './schema-extra';
import type { ContentPageSchema } from './schema-content-page';
import type { CommonSchema } from './schema-common';
import type { CohesionSchema } from './schema-cohesion';

export interface IKconfData {
    kv: KvSchema;
    common: CommonSchema;
    extra: ExtraSchema;
    mainPage: mainPageSchema;
    star: StarSchema;
    contentPage: ContentPageSchema;
    niudan: NiudanSchema;
    peakBattle: PeakBattleSchema;
    cohesion: CohesionSchema;
    propCard: PropCardSchema;
}
