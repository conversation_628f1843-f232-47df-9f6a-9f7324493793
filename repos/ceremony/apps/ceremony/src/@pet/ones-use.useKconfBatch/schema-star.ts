export interface StarSchema {
    kv: Kv;
    rule: Rule;
    error: Error;
    showMemoirs: boolean;
    award: Award;
    task: Task;
    starEffect: any;
    videoDowngrade: boolean;
    limitedTimeTask: LimitedTimeTask;
    calendar: Calendar;
    taskRepair: TaskRepair;
    card: StarCard;
    cup: { subTitle: string };
    isShowRankIcon: boolean;
}

export interface StarCard {
    [key: string]: {
        title: string;
        desc: string;
        img: string;
    };
}

export interface TaskRepair {
    repair: string;
}

export interface Calendar {
    taskCardTips: string;
    tips: string;
    repair: string;
    rightTxt: string;
}

export interface LimitedTimeTask {
    title: string;
    finishTitle: string;
    notFinishTitle: string;
    notFinishSubTitle: string;
}

export interface Award {
    cupMap: { [key: string]: CupMap };
    section: Section;
    cup: string;
    redeem: Redeem;
    history: History;
}

export interface CupMap {
    img: string;
    video: string;
}

export interface History {
    title: string;
    star: string;
    redeem: string;
    format: string;
}

export interface Redeem {
    submitTitle: string;
    submitContent: string;
    submitAction: string;
    cancelAction: string;
    failTitle: string;
    failContent: string;
    failCancel: string;
    failSubmit: string;
    success: string;
}

export interface Section {
    disAbledExchange: string;
    emitAuthor: string;
    exchange: string;
    unExchange: string;
    exchanged: string;
    cupExchange: string;
    cupExchanged: string;
    cupUnExchangeStart: string;
    cupUnExchangeEnd: string;
    cupUnExchangeEnd2: string;
    cupEmitAuthor: string;
    cupDisabledExchange: string;
    disabledExchangeToast: string;
    userExchangeToast: string;
    userDisabledExchangeToast: string;
}

export interface Error {
    errorText: string;
    forbiddenText: string;
    emptyText: string;
    kv: string;
}

export interface Kv {
    kvImg: string;
    subtitle: string;
    signText: string;
    signUrl: string;
}

export interface Rule {
    link: string;
    icon: string;
}

export interface StarEffect {
    YELLOW: string;
    PURPLE: string;
}

export interface Task {
    totalStarTitle: string;
    curStarTitle: string;
    taskTitle: string;
    taskDesc: string;
    disbandTeamTxt: DisbandTeamTxt;
    addTeamTxt: AddTeamTxt;
    taskOutTeamInfo: TaskOutTeamInfo;
    taskInTeamInfo: TaskInTeamInfo;
    starUnit: string;
    record: string;
    rightTxt: string;
}

export interface AddTeamTxt {
    title: string;
    placeholder: string;
    confirmBtn: string;
}

export interface DisbandTeamTxt {
    title: string;
    content: string;
    cancelBtn: string;
    confirmBtn: string;
}

export interface TaskInTeamInfo {
    remove: string;
}

export interface TaskOutTeamInfo {
    add: string;
    remind: string;
    copy: string;
}
