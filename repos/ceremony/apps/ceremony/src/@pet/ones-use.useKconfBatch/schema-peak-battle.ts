export interface PeakBattleSchema {
    peak: Peak;
}

export interface Peak {
    headKv: string;
    headKvRule: string;
    headKvRuleUrl: string;
    overview: Overview;
    wonderful: Wonderful;
}

export interface Overview {
    tabTitle: string;
    tabIcon: string;
    notStartText4p: string;
    notStartText2p: string;
    inProgressText: string;
    notParticipatedText: string;
}

export interface Wonderful {
    tabTitle: string;
    tabIcon: string;
    tipStart: string;
    tipActive: string;
    tipEnd: string;
    elseStatus: ElseStatus;
}

export interface ElseStatus {
    title: string;
    text: string;
}
