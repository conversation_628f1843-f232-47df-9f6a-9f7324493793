export interface ExtraSchema {
    glory: Glory;
    authorAddition: AuthorAddition;
}

export interface Glory {
    topBg: string;
    ruleBgBottom: string;
    ruleBgTop: string;
    ruleBgMiddle: string;
    giftIcon: string;
    title: string;
    multipleTip: string;
}

//  身份加成
export interface AuthorAddition {
    kv: string;
    ruleIcon: string;
    ruleUrl: string;
    cohesionRuleUrl: string;
}
