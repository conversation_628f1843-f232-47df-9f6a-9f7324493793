# `useKconfBatch` kconf 配置消费 hook

## 功能概述

`useKconfBatch` 批量获取多个kconf配置。

## 属性

-   **pageStatus** (`Ref<PageStatus>`): 表示配置的状态。
-   **kconfData** (`Ref<CONFIG>`): 存储主业务配置的数据。

## 方法

-   **initData** (`(options: batchOptions) => void`): 初始化配置数据。
    -   **参数**:
        -   **keys** (`[key1: string; key2?: string ]`): 所有的配置键。

## 使用示例

### 初始化 Store

```typescript
import useKconfBatch from "@pet/ones-use.useKconfBatch/index";

const { initData } = useKconfBatch();
initData({
    // 必传，申明要用到的模型名称（kconf-key）
    keys: ["frontend.activity.glory", "openApiBass.spring.newStorm"],
    // 别名设置，可选，主要作用： 缩短访问路径，通过别名，可以使得组件复用度更高
    alias: {
        "openApiBass.spring.newStorm": "newStorm",
    },
});
// 如果你的页面请求必须依赖kconf数据返回，你这样这样使用
initData()
    // 成功
    .then(() => {})
    // 失败
    .catch(() => {});
```

### 访问配置数据

```typescript
import useKconfBatch from "@pet/ones-use.useKconfBatch/index";

// 直接访问
const kConfStore = useKconfBatch(); // 直接获取配置
console.log(kConfStore.kconfData['openApiBass.spring.newStorm']);
// 别名访问，
console.log(kConfStore.kconfData.newStorm]);

// 解构方式
const { kconfData } = storeToRefs(kConfStore);
console.log(kconfData.value['openApiBass.spring.newStorm']);
// 别名访问，
console.log(kconfData.value.newStorm);


## 依赖项

-   **Vue**: `ref`
-   **Pinia**: `defineStore`
-   **@alive-ui/actions**: `getPageStatus`, `getKConfBatch`, `Report`
-   **@pet/ones-use.useKconfBatch/schemas**: `CONFIG`
```
