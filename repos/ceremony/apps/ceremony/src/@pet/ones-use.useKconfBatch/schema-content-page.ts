export interface HotListConfig {
    kvImage: string;
}
export interface ContentPageSchema {
    kvImg: string;
    previewPrize: PreviewPrize[];
    scheduleConfig: ScheduleConfig[];
    previewUserImg: string;
    previewUserText: PreviewUserText;
    previewBottom: PreviewBottom;
    titleList: TitleList;
    moduleList: ModuleList;
    answer: Answer;
    answerPop: AnswerPop;
    authorLaneTips: string;
    hotListConfig: HotListConfig;
    anchorGlory: {
        showMoreKwaiUrl: boolean;
    };
}

export interface Answer {
    isShowAnswer: boolean;
    strategyB: string;
    strategyC: string;
    strategyF: string;
    cardInfo: CardInfo;
    authorCardInfo: CardInfo;
    bookToast: BookToast;
    tips: string;
}

export interface CardInfo {
    title: string;
    subTitle: string;
    iconTitle: string;
}

export interface BookToast {
    bookSuccess: string;
    bookFail: string;
    cancelBookSuccess: string;
    cancelBookFail: string;
}

export interface AnswerPop {
    noDataText: string;
    noRewardText: string;
}

export interface ModuleList {
    preheatAnwer: string[];
    preheatUnAnwer: string[];
    preheatAnswer: string[];
    preheatUnAnswer: string[];
    normalAnswer: string[];
    preheatAnswerGuildTop: string[];
    preheatUnAnswerGuildTop: string[];
    normalAnswerGuildTop: string[];
}

export interface PreviewBottom {
    imgUrl: string;
}

export interface PreviewPrize {
    rankId: number;
    classifyId: number;
    displayName: string;
    img: string;
}

export interface PreviewUserText {
    participate1: string;
    participate2: string;
    popupTitle: string;
}

export interface ScheduleConfig {
    name: string;
    value: string;
    url: string;
}

export interface TitleList {
    TaskRedbag: string;
    Preview: string;
    PreviewUser: string;
    PreviewBottom: string;
    Answer: string;
    Reward: string;
    HotList: string;
    HotListSub: string;
    AnchorGlory: string;
    AnchorGlorySub: string;
    LivePhotoList: string;
    VideoGuide: string;
    LivePhotoListSubTab: LivePhotoListSubTab;
}

export interface LivePhotoListSubTab {
    live: string;
    photo: string;
}
