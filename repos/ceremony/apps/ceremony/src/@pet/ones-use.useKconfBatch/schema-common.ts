type EnvRouter = {
    prod: string;
    staging: string;
};

type TabItem = {
    envRouter: EnvRouter;
    label?: string;
};

type FixedBottomConfig = {
    config: Record<string, TabItem>;
    outLiveRoomDel: string[];
    tabs: Record<string, string[]>;
    competitionGroup: {
        competition: number[];
    };
};

type RuleConfig = {
    main: string;
    // main_back 是注释掉的，可以忽略
    rankTip: string;
    answer: string;
    gamePreview: string;
    barrage: string;
    starGroup: string;
    jiugongge: string;
    gamePeak: string;
    teamSecondaryGroupStrategy: string;
    teamSecondaryRule: string;
    pk: string;
    superFansGroup: string;
    gift: string;
    popularity: string;
    guildCash: string;
    guildCashKing: string;
    guildCashStar: string;
    authorCash: string;
    randomGift: string;
    userStanding: string;
    lottery: string;
    star: string;
    pkSecondary: string;
    cohesive: string;
    preview: string;
    newBuff: string;
    newSchedule: string;
    reviveDirectConnection: string;
    guildCashTask: string;
    superFans: string;
};

type AddressKRNConfig = {
    inLiveRoomUrl: string;
    outLiveRoomUrl: string;
};

type AddressConfig = {
    userAddress: string;
};

export type ChampionType = {
    stageType: number;
    needHeadUrl?: boolean;
    top3Type: 'PERSONAL' | 'GROUP';
    switchType: 'normal' | 'city';
    switchText?: string;
    showRestList: boolean;
    title?: string; // 预留的后备标题
    hashName: string; // 前端用于映射stageType，解决可读性问题
    pickerTitle?: string;
};
export type StageTypeItem = {
    stageName: string;
    rankProxyKey: string;
    stageType: number;
    detailType: string;
    shareBg: string;
    theme: string;
    navTab: string;
    champion?: ChampionType;
    rankType: string;
};

export type CommonSchema = {
    fixedBottomConfig: FixedBottomConfig;
    rule: RuleConfig;
    addressKRNConfig: AddressKRNConfig;
    addressConfig: AddressConfig;
    doNotDetect: boolean;
    containers: string[];
    ignoreContainers: string[];
    close123SDetect: boolean;
    close1SDetect: boolean;
    close2SDetect: boolean;
    close3SDetect: boolean;
    stageTypeList: StageTypeItem[];
    ruleMap: RuleMap;
    competitionBgUrlDefault: string;
    shareDefaultBg: string;
    avatarFrame: string;
    stageGroup: {
        cityGroup: number[];
        renqiGroup: number[];
        gonghuiGroup: number[];
        linkGroup: { [key: string | number]: string };
        yureGroup: number[];
        pkGroup: number[];
        shareBgGroup: number[];
    };
    userStandingRule: string;
    reviveDirectConnection: string;
    ruleMain: string;
};

export interface RuleMap {
    iconUrl: string;
    list: List;
}

export interface List {
    magicRule: MagicRule;
}

export interface MagicRule {
    url: string;
    icon: string;
    name: string;
    show: boolean;
}
