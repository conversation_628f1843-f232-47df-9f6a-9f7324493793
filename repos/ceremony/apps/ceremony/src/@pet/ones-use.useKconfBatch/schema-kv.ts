// 小 kv 图
interface SmallKv {
    default: string;
    champion: string;
    secondary: string;
}

// 单个大 kv 配置
interface BigKvItem {
    bigKv: string;
    videoUrl: string;
    final_champion_kv: string;
    final_champion_kv_video: string;
}

// bigKvTheme 里面的 key
type BigKvThemeKey =
    | 'zongJue'
    | 'default'
    | 'lan'
    | 'hong'
    | 'lv'
    | 'zi'
    | 'huang'
    | 'reDuwang'
    | 'kvB'
    | 'danmu'
    | 'cityDanmu'
    | 'diquDanmu'
    | 'tuanbo'
    | 'cityTuanbo'
    | 'diquTuanbo'
    | 'gongge'
    | 'cityGongge'
    | 'diquGongge';

// bigKvTheme 类型
type BigKvTheme = Record<BigKvThemeKey, BigKvItem>;

// 单个地区图库项
interface DistrictItem {
    bigKv: string;
    videoUrl: string;
}

// 地区名称
type DistrictName =
    | '北京'
    | '甘肃'
    | '广东'
    | '河北'
    | '河南'
    | '黑龙江'
    | '湖南'
    | '吉林'
    | '辽宁'
    | '内蒙古'
    | '山东'
    | '陕西'
    | '浙江'
    | '重庆'
    | '南部赛区'
    | '东部赛区'
    | '北部赛区'
    | '西部赛区';

// district 类型
type District = Record<DistrictName, DistrictItem>;

// 单个赛事配置项
interface CompetitionItem {
    theme: BigKvThemeKey;
    desc: string;
}

// 赛事 id 类型（这里只列出了一部分）
type CompetitionId =
    | '10'
    | '20'
    | '21'
    | '22'
    | '30'
    | '31'
    | '40'
    | '41'
    | '42'
    | '50'
    | '51'
    | '52'
    | '60'
    | '70'
    | '71' /* ... 后面还有 */;

// competition 类型
type Competition = Record<CompetitionId, CompetitionItem>;

// 最顶层对象类型
export interface KvSchema {
    smallKv: SmallKv;
    bigKvTheme: BigKvTheme;
    district: District;
    competition: Competition;
}
