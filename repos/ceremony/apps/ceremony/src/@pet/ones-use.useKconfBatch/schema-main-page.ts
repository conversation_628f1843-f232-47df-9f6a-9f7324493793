export interface mainPageSchema {
    showTopLeftIcon: boolean;
    honorStartTime: string;
    newAnchorCard: NewAnchorCard;
    bufferModal: BufferModal;
    restDay: RESTDay;
    kingKv: string;
    kingNormalKv: string;
    noblePage: string;
    gameRewardsText: string;
    throughFinals: ThroughFinals;
    cBuffCardRules: CBuffCardRules;
    teamKV: string;
    teamRuleUrl: string;
    curAnchorCard: {
        giftId: number;
        assistBtn: string;
    };
    task: {
        none: string[];
        doing: string[];
        done: string[];
        close: boolean;
        tip: string;
    };
    nationalCompetition: {
        kvImg: string;
    };
    countyChampion: {
        title: string;
        desc: string;
    };
    guildRank: {
        guildRankKv: string;
        cardTitle: string;
    };
    awardSwiperAnimation: {
        during: number;
        transitionTime: number;
    };
    gonghuiHonor: {
        kv: string;
        ruleImg: string;
    };
}

export interface RESTDay {
    honorJpeg: string;
    honorWebp: string;
    futureWebp: string;
    futureJpeg: string;
    titie: string;
    restText: string;
    honorImg: string;
    futureImg: string;
}

export interface ResourceMap {
    star: Fans;
    pk: Fans;
    fans: Fans;
    guild: Fans;
}

export interface Fans {
    ruleKey: string;
}

export interface NewAnchorCard {
    teamInfo: TeamInfo;
}

export interface TeamInfo {
    rightTextIng: string;
    rightTextUnIng: string;
    defaultAvatar: string;
    teamUrl: TeamURL;
}

export interface TeamURL {
    staging: string;
    prod: string;
}
export interface BufferModal {
    title: string;
    desc: string;
}

export interface ThroughFinals {
    title: string;
    subTitle: string;
    Kv: string;
    defaultAvatar: string;
    rewardImg: string;
}

export interface CBuffCardRules {
    additionRule: string;
    wayTipRule: string;
    lookRule: string;
    avatarTitle: string;
}
