export interface NiudanSchema {
    lotteryConfigs: LotteryConfigs;
}

export interface LotteryConfigs {
    cardTitle: string;
    cardSubTitle: string;
    coinName: string;
    getCionLabel: string;
    isDoublePanel: boolean;
    notParallelTab: boolean;
    lotteryOnceImg: string;
    lotteryOnceModal: boolean;
    smallModalBg?: string;
    bigModalBg?: string;
}

export interface BlindBox {
    poster: string;
    oneLottery: Lottery;
    tenLottery: Lottery;
    audio?: { [key: string]: string };
}

export interface Lottery {
    audio: string[];
    general: string[];
    transparent: string[];
}
