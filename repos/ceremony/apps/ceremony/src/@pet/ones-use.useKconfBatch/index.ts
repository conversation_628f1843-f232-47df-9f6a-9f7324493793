import { ref } from 'vue';
import { defineStore } from 'pinia';
import { getPageStatus, getKConfBatch } from '@alive-ui/actions';
import type { IKconfData } from './schema';
interface batchOptions {
    keys: ReadonlyArray<string>;
    alias?: Record<string, string>;
}

/**
 *
 * @param data 数据对象
 * @param keys  keys
 * @param alias?  自定义别名
 * @returns void
 */
const addAlias = (
    data: Record<string, any>,
    keys: ReadonlyArray<string>,
    alias?: Record<string, string>,
) => {
    // 默认第三级的key就是别名
    keys.forEach((item: string) => {
        // 自定义高优
        const name = alias?.[item] || item.split('.')[2];
        data[name] = data[item];
    });
};
// 用于确保每个活动的唯一性
const kconfDataKey = location.pathname;
export default defineStore('stores_kconf_batch', () => {
    const pageStatus = ref(getPageStatus('init'));
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
    const kconfData = ref({} as IKconfData);

    async function initData(options: batchOptions) {
        return new Promise(async (res, rej) => {
            pageStatus.value = getPageStatus('loading');
            const injectData = window.__GUNDAM_KCONF_INJECT_DATA__;
            const isAllInject = options.keys.every(
                (item: string) => injectData?.[item],
            );
            // 全部都注入了，则走本地
            if (isAllInject) {
                kconfData.value = injectData;
                // 存在本地，下次当做默认值
                localStorage.setItem(kconfDataKey, JSON.stringify(injectData));
                addAlias(kconfData.value, options.keys, options.alias);
                pageStatus.value = getPageStatus('success');
            } else {
                // 第二层防护：走网络请求
                try {
                    const { data } = await getKConfBatch(options.keys || []);
                    kconfData.value = data;
                    // 存在本地，下次当做默认值
                    localStorage.setItem(kconfDataKey, JSON.stringify(data));
                    addAlias(kconfData.value, options.keys, options.alias);
                    pageStatus.value = getPageStatus(
                        Object.keys(data).length ? 'success' : 'nodata',
                    );
                } catch (error) {
                    // 第三层防护：用localstorage中的数据来兜底
                    kconfData.value = JSON.parse(
                        localStorage.getItem(kconfDataKey) || '{}',
                    );
                    addAlias(kconfData.value, options.keys, options.alias);
                    pageStatus.value = Object.keys(kconfData.value).length
                        ? getPageStatus('success')
                        : getPageStatus('error');
                }
            }
            pageStatus.value.success
                ? res(pageStatus.value)
                : rej(pageStatus.value);
        });
    }

    return {
        pageStatus,
        kconfData,
        initData,
    };
});
