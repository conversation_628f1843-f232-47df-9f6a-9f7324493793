export interface CohesionSchema {
    card: Card;
    page: Page;
    common: Common;
}

export interface Card {
    title: string;
    defaultImg: string;
    anchorNotOnListDesc: string;
    maxImg: string;
    maxDesc: string;
}

export interface Page {
    kv: string;
    hintDesc: string;
    loadingSwitch: boolean;
    countdownDelayDesc: string;
    countdownDelayTime: number;
    countdownTime: number;
}

export interface Common {
    outLiveRoomDesc: string;
}
