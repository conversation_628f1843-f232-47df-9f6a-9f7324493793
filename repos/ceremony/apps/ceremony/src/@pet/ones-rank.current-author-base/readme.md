### 当前基础主播组件

## 功能描述

该组件用于展示当前主播的区域，包括主播的头像、信息和送礼按钮。支持自定义内容和样式，适用于榜单展示场景。

## Props

| 属性名          | 类型                     | 默认值          | 描述                                     |
|-----------------|--------------------------|------------------|------------------------------------------|
| `avatarSize`    | `'lg' | 'sm' | 'xs'`    | `'sm'`          | 头像尺寸                                 |
| `avatarSizeLeft`| `'lg' | 'sm' | 'xs'`    | `'sm'`          | 左侧头像尺寸                             |
| `avatarSizeRight`| `'lg' | 'sm' | 'xs'`   | `'sm'`          | 右侧头像尺寸                             |
| `buttonSize`    | `'lg' | 'sm' | 'xs'`    | `'xs'`          | 送礼按钮尺寸                             |
| `giftBtnText`   | `string`                 | `'助力主播'`     | 送礼按钮文本                             |
| `itemType`      | `string`                 | `'base'`        | 显示的项目类型                           |
| `guildRank`     | `boolean`                | `false`         | 是否为公会排名                           |
| `isCurrent`     | `boolean`                | `true`          | 是否为当前主播                           |
| `currentLabel`  | `string`                 | `'当前主播'`    | 当前主播标签文本                         |
| `defineRight`   | `boolean`                | `false`         | 是否定义右侧内容                         |
| `diffSize`      | `boolean`                | `false`         | 是否使用不同的尺寸                       |
| `isFixed`       | `boolean`                | `false`         | 是否固定在底部                           |
| `needSelfStyle` | `boolean`                | `false`         | 是否需要自定义样式                       |
| `useProps`      | `boolean`                | `false`         | 是否使用 props 传入的数据                |
| `contextName`   | `Symbol`                 | `Symbol.for('ctx')` | 上下文名称，用于数据传递               |
| `needTopIndex`  | `boolean`                | `false`         | 是否需要显示顶部排名                     |
| `needTopLive`   | `boolean`                | `true`          | 是否需要显示直播状态                     |

## 使用方式

```vue
<template>
  <CurrentAnchor
    :data="{ scoreLabel: '1000', otherLogParams: {} }"
    :isCurrent="true"
    :itemType="'double'"
    :giftBtnText="'支持主播'"
    :contextName="Symbol.for('ctx')"
  >
    <template #extra-label>
      <span>额外标签内容</span>
    </template>
    <template #center-info>
      <span>中间信息内容</span>
    </template>
    <template #info>
      <span>主播信息内容</span>
    </template>
    <template #giftButton>
      <button>自定义送礼按钮</button>
    </template>
  </CurrentAnchor>
</template>

<script setup>
import CurrentAnchor from '@pet/ones-rank.current-author-base/index.vue';
</script>
```
## 注意事项
组件支持通过 contextName 进行数据上下文传递。
根据 itemType 的值，组件会选择不同的显示类型（如单头像或双头像）。
