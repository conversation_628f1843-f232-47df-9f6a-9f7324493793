import { computed, inject } from 'vue';
import type { Ref } from 'vue';
import type { QueryRankPostResponse } from '@pet/ones-rank.schema/query-rank';
import type { ItemSchema } from './schema';
export const dataFun = (contextName: symbol) => {
    const rankInfo = contextName
        ? inject<Ref<QueryRankPostResponse>>(contextName)
        : null;
    const data = computed(() => {
        if (!rankInfo?.value?.bottomInfo) {
            return null;
        }
        const hintScoreText =
            rankInfo?.value?.bottomInfo?.displayHint?.split('${hintScore}');
        return {
            ...rankInfo?.value?.bottomInfo?.itemRankInfo,
            liveStreamIdList:
                rankInfo?.value?.rankList?.map((item: any) => {
                    return item?.liveStreamId;
                }) || [],
            h5ShowScore: rankInfo?.value?.bottomInfo?.h5ShowHintScore,
            rankId: rankInfo?.value?.rankId,
            scoreLabel: hintScoreText?.[0],
            otherLogParams: {
                rankId: rankInfo?.value?.rankId,
            },
        };
    });
    return data;
};
