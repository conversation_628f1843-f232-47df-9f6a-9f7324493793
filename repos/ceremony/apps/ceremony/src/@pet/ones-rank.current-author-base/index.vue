<!-- 常规当前主播区域 -->
<template>
    <APBaseRankList
        v-if="newData"
        :log-params="newData?.otherLogParams"
        :class="isFixed ? 'fixed-current-rank gis-fixed-bottom-user-bg' : ''"
    >
        <component
            :is="componentType[itemType]"
            :is-current="isCurrent"
            :item="{
                ...newData,
                disableClick: disableClick,
            }"
            :diff-size="diffSize"
            :avatar-size="avatarSize"
            :avatar-size-left="avatarSizeLeft"
            :avatar-size-right="avatarSizeRight"
            :need-top-index="needTopIndex"
            :need-top-live="needTopLive"
            :need-show-right="
                defineRight || (!!liveStreamId && !bolIsAuthor && !guildRank)
            "
            :current-label="currentLabel"
            :class="isFixed ? 'rank-fixed-width' : ''"
        >
            <template #extra-label>
                <slot name="extra-label"></slot>
            </template>
            <template #center-info>
                <!-- 定义中间内容 -->
                <slot name="center-info" />
            </template>
            <template #info>
                <!-- 放榜单项主播或xx名称下的信息内容 todo：自定义 -->
                <slot name="info" />
            </template>
            <!-- <template #key>{{ newData.scoreLabel }}</template> -->
            <template #right>
                <!-- 送礼按钮抽成插槽 -->
                <slot name="giftButton">
                    <GiftButton
                        v-pcDirectives:hide
                        :size="buttonSize"
                        class="my-gift-btn"
                    >
                        {{ giftBtnText }}
                    </GiftButton>
                </slot>
            </template>
        </component>
    </APBaseRankList>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import RankItemDouble from '@pet/ones-rank.double-avatar-item/index.vue';
import RankItemBase from '@pet/ones-rank.base-item/index.vue';
import { GiftButton, BaseRankList as APBaseRankList } from '@alive-ui/pro';
import { liveStreamId, bolIsAuthor } from '@alive-ui/actions';
import './assets/imgs.less';
import { dataFun } from './data';
import type { CurrentItemSchema } from './schema';
const componentType: { [key: string]: object } = {
    base: RankItemBase,
    double: RankItemDouble,
};
const props = withDefaults(defineProps<CurrentItemSchema>(), {
    avatarSize: 'sm',
    avatarSizeLeft: 'sm',
    avatarSizeRight: 'sm',
    buttonSize: 'xs',
    giftBtnText: '助力主播',
    itemType: 'base',
    guildRank: false,
    isCurrent: true,
    currentLabel: '当前主播',
    defineRight: false,
    diffSize: false,
    isFixed: false,
    needSelfStyle: false,
    useProps: false,
    contextName: Symbol.for('ctx'),
    needTopIndex: false,
    needTopLive: true,
    disableClick: false,
});
const newData = props.useProps ? ref(props.data) : dataFun(props.contextName);
</script>
<style lang="less" scoped>
.fixed-current-rank {
    position: fixed;
    bottom: 0;
    left: 50%;
    z-index: 10;
    overflow: hidden;
    transform: translateX(-50%);
    border-top-right-radius: 18px;
    .rank-item-1-bg,
    .rank-item-2-bg,
    .rank-item-3-bg {
        background-image: none;
    }
    :deep(.rank-current-item-attr) {
        width: 100%;
        height: 100%;
        padding: 0 36px 0 30px;
        background: none;
    }
}
</style>
