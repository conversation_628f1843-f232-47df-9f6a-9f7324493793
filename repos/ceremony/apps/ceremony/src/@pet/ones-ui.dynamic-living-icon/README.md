# 动态直播图标组件文档

## 功能概述
`@pet/ones-ui.dynamic-living-icon` 是一个用于展示动态直播状态的小图标组件。该组件通过 CSS 动画模拟了类似音乐播放器的动画效果，适用于需要显示直播状态的场景。

## 属性和方法
### 属性 (Props)
- **无**

### 方法 (Methods)
- **无**

## 使用示例
```vue
<template>
  <div>
    <dynamic-living-icon />
  </div>
</template>

<script>
import DynamicLivingIcon from '@pet/ones-ui.dynamic-living-icon';

export default {
  components: {
    DynamicLivingIcon
  }
};
</script>
```

## 注意事项
- 组件默认尺寸为 16x16 像素，如果需要调整大小，可以通过 CSS 自定义样式。
- 组件的背景颜色和边框颜色可以通过 CSS 变量或直接修改样式来调整。

## 依赖项
- **无**

## 样式说明
- `.gis-live-corner`: 容器样式，设置为圆形并定义了背景色和边框。
- `.music`: 内部容器样式，用于对齐动画元素。
- `ul li`: 动画元素的样式，每个元素的高度和宽度固定，通过 CSS 动画实现动态效果。
- `.m1`, `.m2`, `.m3`: 分别定义了三个动画元素的不同延迟时间，以实现错落有致的动画效果。

## 动画说明
- `@keyframes living`: 定义了一个从 100% 到 50% 再到 100% 的缩放动画，使动画元素在垂直方向上产生弹跳效果。
