<template>
    <div class="gis-live-corner">
        <ul class="music">
            <li class="m1" />
            <li class="m2" />
            <li class="m3" />
        </ul>
    </div>
</template>

<style lang="less" scoped>
* {
    padding: 0;
    margin: 0;
    border: 0;
}

.gis-live-corner {
    // position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    min-width: 16px;
    min-height: 16px;
    // line-height: 16PX;
    background-color: #fe7193;
    border: 1px solid #fff2cd;
    border-radius: 100%;
}

.music {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    min-width: 16px;
    min-height: 16px;
    margin: 0 auto;
    text-align: center;
}

ul li {
    display: inline-block;
    width: 1.5px;
    height: 9px;
    min-width: 1.5px;
    min-height: 9px;
    margin: 0 1px;
    text-align: center;
    background-color: #fff;
}

.m1 {
    animation: 0.8s 0.1s living linear infinite backwards normal;
    animation-delay: -1.1s;
}

.m2 {
    animation: 0.8s 0.3s living linear infinite backwards normal;
    animation-delay: -1.3s;
}

.m3 {
    animation: 0.8s 0.6s living linear infinite backwards normal;
    animation-delay: -1.6s;
}

@keyframes living {
    0% {
        transform: scaleY(1);
    }

    50% {
        transform: scaleY(0.3);
    }

    100% {
        transform: scaleY(1);
    }
}
</style>
