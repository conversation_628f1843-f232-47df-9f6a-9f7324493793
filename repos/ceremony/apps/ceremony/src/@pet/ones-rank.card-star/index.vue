<template>
    <div
        v-show-log="{
            action: 'OP_ACTIVITY_STAR_CARD',
        }"
        class="star-card"
    >
        <ACard class="star-card-content">
            <ACardTitle>
                <span>夏季之星</span>
            </ACardTitle>
            <ACardContent v-if="checkIn && content">
                <div
                    v-click-log="{
                        action: 'OP_ACTIVITY_STAR_CARD',
                        params: {
                            btn_type: 'VIEW',
                        },
                    }"
                    class="detail"
                    @click="toStar"
                ></div>
                <div class="content-wrap">
                    <img class="star-img" :src="getImg" />
                    <span class="title">{{ content.title }}</span>
                    <div
                        v-if="checkIn.status === TaskStatusEnum.LIMIT"
                        class="desc"
                    >
                        {{ content.subTitle ? content.subTitle : content.desc }}
                        <span
                            v-if="!content.subTitle"
                            class="star-text-highlight"
                            >{{ content.highlightValue }}</span
                        >
                    </div>
                    <div v-else class="desc">
                        <span
                            v-if="checkIn.status === TaskStatusEnum.CUP"
                            class="star-text-highlight"
                            >{{ content.highlightValue }}</span
                        >
                        {{ content.desc }}
                        <span
                            v-if="checkIn.status !== TaskStatusEnum.CUP"
                            class="star-text-highlight"
                            >{{ content.highlightValue }}</span
                        >
                    </div>
                </div>

                <AButton
                    v-click-log="{
                        action: 'OP_ACTIVITY_STAR_CARD',
                        params: {
                            btn_type: 'HELP',
                        },
                    }"
                    class="btn"
                    type="primary"
                    size="lg"
                    @click="handleSupport"
                >
                    立即助力
                </AButton>
            </ACardContent>
        </ACard>
    </div>
</template>

<script setup lang="ts">
import { useRouter, type LocationQueryValueRaw } from 'vue-router';
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconfBatch';
import { TaskStatusEnum } from '@pet/ones-rank.schema/query-rank';
import { Toast } from '@lux/sharp-ui-next';
import { dispatchLiveRouter } from '@alive-ui/system';
import { ACard, ACardTitle, ACardContent, AButton } from '@alive-ui/base';
import { bolIsAuthor } from '@alive-ui/actions';
import type { CheckIn } from '@pet/ones-rank.schema/query-rank';

const props = defineProps<{
    checkIn: CheckIn;
    extraCardKey?: string;
}>();
const router = useRouter();

const handleSupport = () => {
    if (bolIsAuthor) {
        // 如果当前是主播端，不能给自己送礼
        Toast.info('开播中，不能给自己送礼哦~');
        return;
    }
    // 打开礼物面板
    dispatchLiveRouter({
        path: props.checkIn.giftLink,
        keepDisplayWebView: false,
    }).catch(() => {});
};

const toStar = () => {
    router.push({
        path: '/star',
    });
};

const { kconfData } = storeToRefs(useKconfStore());
const config = kconfData.value.star.card;

const content = computed(() => {
    if (props.checkIn.status === TaskStatusEnum.CUP) {
        return {
            ...config.cup,
            desc: config.cup.desc.replace(
                '${totalDayCount}',
                `${props.checkIn.cupTask.totalDayCount}`,
            ),
            subTitle: '',
            highlightValue: props.checkIn.cupTask.finishDayCount,
        };
    }
    if (props.checkIn.status === TaskStatusEnum.FANS) {
        return {
            ...config.fans,
            title: config.fans.title.replace(
                '${h5ShowFansCount}',
                props.checkIn.fansTask.h5ShowFansCount,
            ),
            subTitle: '',
            highlightValue: props.checkIn.fansTask.h5ShowNeedScore,
        };
    }
    if (props.checkIn.status === TaskStatusEnum.LIMIT) {
        return {
            ...config.limit,
            subTitle: props.checkIn.limitTask.subTitle,
            highlightValue: props.checkIn.limitTask.h5ShowNeedScore,
        };
    }
    return null;
});

// 1-奖杯任务 2-涨粉任务 3-限时任务
const getImg = computed(() => {
    if (props.checkIn.status === TaskStatusEnum.CUP) {
        return config.cup.img;
    }
    if (props.checkIn.status === TaskStatusEnum.FANS) {
        return config.fans.img;
    }
    if (props.checkIn.status === TaskStatusEnum.LIMIT) {
        return config.limit.img;
    }
    return '';
});
</script>

<style lang="less" scoped>
.star-card {
    position: relative;
    width: 382px;

    .star-card-content {
        padding-bottom: 24px;
    }

    .process-img {
        width: 167px;
        height: 157px;
        position: absolute;
        top: 0;
        right: 0;
    }

    .content-wrap {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        .star-img {
            width: 114px;
            height: 114px;
            margin-top: 7.5px;
        }
        .title {
            display: inline-block;
            margin-top: 4px;
            font-family: HYYaKuHei;
            font-size: 16px;
            line-height: 20px;
            background: linear-gradient(
                78.49deg,
                #fff8ed 15.2%,
                #f4bc73 87.91%
            );
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .desc {
            margin-top: 2px;
            color: rgba(255, 255, 255, 0.5);
            font-weight: 400;
            font-size: 12px;
            line-height: 18px;

            .star-text-highlight {
                color: #fe3666;
            }
        }
    }

    .btn {
        margin: 22px auto 0;
    }

    .detail {
        position: absolute;
        right: 12px;
        top: -34px;
        width: 62px;
        height: 18px;
        background: url('./imgs/detail.png') center / 100% no-repeat;
    }
}
</style>
