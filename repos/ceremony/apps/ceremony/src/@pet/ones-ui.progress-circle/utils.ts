/**
 * 解析CSS渐变字符串为色标数组
 * @param str - CSS渐变字符串，格式如：linear-gradient(to right, red 0%, blue 100%)
 * @returns 返回色标数组，包含color和offset属性
 */
export function parseCssGradient(str: string) {
    // 匹配linear-gradient模式
    const match = str.match(/^\s*linear-gradient\((.+)\)\s*$/i);
    if (!match) return [];
    const inner = match[1]; // 获取括号内内容

    // 分割渐变参数，处理嵌套括号
    const parts: string[] = [];
    let buf = '';
    let depth = 0;

    for (const ch of inner) {
        if (ch === '(') depth++;
        if (ch === ')') depth--;
        // 只在顶层逗号处分隔
        if (ch === ',' && depth === 0) {
            parts.push(buf.trim());
            buf = '';
            continue;
        }
        buf += ch;
    }
    if (buf) parts.push(buf.trim());

    // 移除方向参数（如to right或90deg）
    if (/^(to\s+\w+|[-0-9.+]+(deg|rad|turn))$/i.test(parts[0])) {
        parts.shift();
    }

    // 将每个色标部分解析为color和offset
    return parts.map((p) => {
        const idx = p.lastIndexOf(' '); // 最后一个空格分隔颜色和位置
        return idx > 0
            ? { color: p.slice(0, idx).trim(), offset: p.slice(idx + 1).trim() }
            : { color: p.trim(), offset: '' };
    });
}

/**
 * 极坐标转笛卡尔坐标
 * @param cx - 圆心x坐标
 * @param cy - 圆心y坐标
 * @param r - 半径
 * @param angleDeg - 角度(度)
 * @returns 返回笛卡尔坐标系下的{x,y}坐标
 * @note 角度0度指向正右方，90度指向正上方
 */
export function polarToCartesian(
    cx: number,
    cy: number,
    r: number,
    angleDeg: number,
) {
    // 转换为弧度并调整角度基准（减去90度使0度指向正右方）
    const rad = ((angleDeg - 90) * Math.PI) / 180;
    return {
        x: cx + r * Math.cos(rad),
        y: cy + r * Math.sin(rad),
    };
}

/**
 * 判断字符串是否是CSS渐变
 * @param str - 待检测字符串
 * @returns 如果是linear-gradient则返回true
 */
export function isGradient(str: string): boolean {
    return /^\s*linear-gradient\(/i.test(str);
}
