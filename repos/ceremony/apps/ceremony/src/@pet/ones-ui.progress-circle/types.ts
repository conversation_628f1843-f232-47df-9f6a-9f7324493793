/**
 * 进度节点接口
 * @property value - 节点对应的进度值
 * @property label - 节点显示的标签文本
 * @property unlockStatus - 解锁状态
 */
export interface ProgressNode {
    value: number;
    label: string;
    unlockStatus?: number;
    [key: string]: any;
}

/**
 * 组件属性接口
 * @property value - 当前进度值
 * @property max - 最大进度值
 * @property angleSpan - 弧线角度跨度(度)
 * @property radius - 圆弧半径
 * @property nodes - 进度节点数组
 * @property trackGradientString - 轨道渐变色CSS字符串
 * @property progressGradientString - 进度条渐变色CSS字符串
 * @property width - 容器宽度(px)
 * @property height - 容器高度(px)
 * @property bottomMargin - 底部边距(px)
 * @property sideMargin - 左右边距(px)
 * @property progressWidth - 进度条宽度(px)
 * @property type - 类型，支持 'default' 和 'even-nodes'
 */
export interface Props {
    value?: number;
    max?: number;
    angleSpan?: number;
    radius?: number;
    nodes?: ProgressNode[];
    trackGradientString?: string;
    progressGradientString?: string;
    width?: number;
    height?: number;
    bottomMargin?: number;
    sideMargin?: number;
    progressWidth?: number;
    type?: 'default' | 'even-nodes';
}
