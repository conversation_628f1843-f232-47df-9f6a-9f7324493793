<template>
    <!-- 进度圆环容器 -->
    <div class="arc-container" :style="containerStyle">
        <!-- SVG画布 -->
        <svg :viewBox="viewBox" class="arc-svg">
            <!-- 渐变定义 -->
            <defs>
                <!-- 轨道渐变 -->
                <linearGradient
                    v-if="isTrackGradient"
                    id="track-gradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="0%"
                >
                    <stop
                        v-for="(stop, idx) in computedTrackStops"
                        :key="idx"
                        :offset="stop.offset"
                        :stop-color="stop.color"
                    />
                </linearGradient>
                <!-- 进度条渐变 -->
                <linearGradient
                    v-if="isProgressGradient"
                    id="progress-gradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="0%"
                >
                    <stop
                        v-for="(stop, idx) in computedProgressStops"
                        :key="idx"
                        :offset="stop.offset"
                        :stop-color="stop.color"
                    />
                </linearGradient>
            </defs>

            <!-- 背景轨道 -->
            <path
                :d="arcPath(0, 1)"
                :stroke="trackStroke"
                fill="none"
                class="arc-track"
                :style="{ strokeWidth: `${progressWidth * pageRatio}px` }"
            />
            <!-- 进度条 -->
            <path
                :d="arcPath(0, clampedAnimatedProgress)"
                :stroke="progressStroke"
                :style="{ strokeWidth: `${progressWidth * pageRatio}px` }"
                fill="none"
                stroke-linecap="round"
                class="arc-progress"
            />

            <!-- 进度节点 -->
            <g
                v-for="(node, index) in processedNodes.filter(
                    (n) => n !== null,
                )"
                :key="index"
                :transform="`translate(${node?.x}, ${node?.y}) scale(${pageRatio})`"
            >
                <!-- 可自定义的节点插槽 -->
                <slot
                    v-if="node"
                    name="node"
                    :node="node"
                    :index="index"
                    :is-active="node.value <= animatedValue"
                >
                    <!-- 默认节点圆点 -->
                    <circle
                        r="6"
                        :fill="node.value <= animatedValue ? 'red' : '#aaa'"
                        stroke="#fff"
                        stroke-width="2"
                    />
                    <!-- 默认节点标签 -->
                    <text
                        y="24"
                        text-anchor="middle"
                        fill="#fff"
                        font-size="14"
                        font-family="HYYakuHei"
                    >
                        {{ node.label }}
                    </text>
                </slot>
            </g>
        </svg>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { parseCssGradient, polarToCartesian, isGradient } from './utils';
import { useAnimation } from './useAnimation';
import type { Props, ProgressNode } from './types';
import { getCurrentWidth } from '@/utils/tools';

// 组件默认属性值
const props = withDefaults(defineProps<Props>(), {
    value: 60,
    max: 100,
    angleSpan: 60, // 默认60度弧线
    nodes: () => [
        { value: 20, label: '20' },
        { value: 40, label: '40' },
        { value: 80, label: '80' },
    ],
    // 默认轨道渐变
    trackGradientString:
        'linear-gradient(90deg, rgba(255, 255, 255, 0) 3.37%, rgba(255, 255, 255, 0.1) 22%, rgba(255, 255, 255, 0.1) 77.89%, rgba(255, 255, 255, 0) 96.52%)',
    // 默认进度条渐变
    progressGradientString:
        'linear-gradient(90deg, rgba(255, 223, 191, 0) 21.95%, #FFDFBF 98.4%)',
    width: 414, // 默认宽度414px
    height: 200, // 默认高度200px
    bottomMargin: 40, // 默认底部边距40px
    sideMargin: 40, // 默认左右边距20px
    radius: undefined, // 默认不设置，使用自动计算
    progressWidth: 6, // 默认进度条宽度6px
    type: 'default', // 类型
});

// === 渐变相关计算 ===
/**
 * 计算轨道渐变色标
 * 将CSS渐变字符串解析为色标数组
 */
const computedTrackStops = computed(() =>
    parseCssGradient(props.trackGradientString),
);

/**
 * 计算进度条渐变色标
 * 将CSS渐变字符串解析为色标数组
 */
const computedProgressStops = computed(() =>
    parseCssGradient(props.progressGradientString),
);

/**
 * 判断轨道是否为渐变
 */
const isTrackGradient = computed(() => isGradient(props.trackGradientString));

/**
 * 判断进度条是否为渐变
 */
const isProgressGradient = computed(() =>
    isGradient(props.progressGradientString),
);

/**
 * 轨道描边样式
 * 如果是渐变则使用渐变ID，否则直接使用颜色值
 */
const trackStroke = computed(() =>
    isTrackGradient.value ? 'url(#track-gradient)' : props.trackGradientString,
);

/**
 * 进度条描边样式
 * 如果是渐变则使用渐变ID，否则直接使用颜色值
 */
const progressStroke = computed(() =>
    isProgressGradient.value
        ? 'url(#progress-gradient)'
        : props.progressGradientString,
);

/**
 * 页面缩放比例(基于414px基准)
 */
const pageRatio = ref(getCurrentWidth() / 414);

/**
 * 更新缩放比例
 */
const updateRatio = () => {
    pageRatio.value = getCurrentWidth() / 414;
};

// 监听窗口大小变化
onMounted(() => window.addEventListener('resize', updateRatio));
onUnmounted(() => window.removeEventListener('resize', updateRatio));

/**
 * 容器宽度(宽度props * 缩放比例)
 */
const CON_W = computed(() => props.width * pageRatio.value);

/**
 * 容器高度(高度props * 缩放比例)
 */
const CON_H = computed(() => props.height * pageRatio.value);

/**
 * 计算弧线半径
 * 基于容器宽度和角度跨度计算，并跟随pageRatio缩放
 */
const radius = computed(() => {
    // 如果props中指定了radius，则直接使用并应用缩放
    if (props.radius !== undefined) {
        return props.radius * pageRatio.value;
    }
    // 否则使用原有计算方式（已经包含了pageRatio的缩放，因为使用了CON_W）
    const w = CON_W.value - props.sideMargin * 2 * pageRatio.value;
    const ang = (props.angleSpan * Math.PI) / 180;
    return w / 2 / Math.sin(ang / 2);
});

/**
 * SVG视口定义
 */
const viewBox = computed(() => `0 0 ${CON_W.value} ${CON_H.value}`);

/**
 * 容器样式
 */
const containerStyle = computed(() => ({
    width: `${CON_W.value}px`,
    height: `${CON_H.value}px`,
}));

/**
 * 弧线圆心坐标
 * 水平居中，垂直方向考虑底部边距
 */
const center = computed(() => ({
    x: CON_W.value / 2,
    y: CON_H.value - props.bottomMargin * pageRatio.value - radius.value,
}));

// === 弧线路径计算 ===
/**
 * 生成弧线路径
 * @param t0 - 起始点比例(0-1)
 * @param t1 - 结束点比例(0-1)
 * @returns SVG路径字符串
 */
const arcPath = (t0: number, t1: number) => {
    const base = 180; // 基准角度(朝下)
    const startAng = base + props.angleSpan / 2 - props.angleSpan * t0; // 起始角度
    const endAng = base + props.angleSpan / 2 - props.angleSpan * t1; // 结束角度

    // 计算起点坐标
    const start = polarToCartesian(
        center.value.x,
        center.value.y,
        radius.value,
        startAng,
    );

    // 计算终点坐标
    const end = polarToCartesian(
        center.value.x,
        center.value.y,
        radius.value,
        endAng,
    );

    // 判断是否是大弧(角度差大于180度)
    const large = Math.abs(endAng - startAng) > 180 ? 1 : 0;

    // 生成SVG路径
    return `M ${start.x} ${start.y} A ${radius.value} ${radius.value} 0 ${large} 0 ${end.x} ${end.y}`;
};

// === 动画相关 ===
/**
 * 使用动画组合式函数
 * @param initialValue - 初始值
 */
const { animatedValue, animate } = useAnimation(0);

// 组件挂载时执行初始动画
onMounted(() => animate(props.value));

// 监听value变化触发动画
watch(() => props.value, animate);

/**
 * 计算动画进度(0-1范围)
 * 确保值在0-1之间
 */
const clampedAnimatedProgress = computed(() => {
    if (props.type === 'even-nodes') {
        const nodes = (props.nodes || [])
            .slice()
            .sort((a, b) => a.value - b.value);
        const N = nodes.length;
        if (N < 2) return 0;
        const v = animatedValue.value;
        if (v < nodes[0].value) return (v / nodes[0].value) * (1 / (N + 1));
        if (v === nodes[N - 1].value) return N / (N + 1); // 停在最后一个节点位置
        if (v > nodes[N - 1].value) return N / (N + 1) + 0.5 * (1 / (N + 1)); // 最后一段展示 50%
        for (let i = 0; i < N - 1; i++) {
            const v0 = nodes[i].value;
            const v1 = nodes[i + 1].value;
            if (v >= v0 && v < v1) {
                const seg = (v - v0) / (v1 - v0);
                return (i + 1) / (N + 1) + seg * (1 / (N + 1));
            }
        }
        return 0;
    }
    return Math.min(Math.max(animatedValue.value / props.max, 0), 1);
});

// === 节点计算 ===
/**
 * 处理节点坐标
 * 将节点值转换为屏幕坐标
 */
const processedNodes = computed(() => {
    if (props.type === 'even-nodes') {
        const nodes = (props.nodes || [])
            .slice()
            .sort((a, b) => a.value - b.value);
        const N = nodes.length;
        if (N < 2) return [];
        // 均匀分布，不在两端
        return nodes.map((n, i) => {
            const t = (i + 1) / (N + 1); // 均匀分布，不在两端
            const ang = 180 + props.angleSpan / 2 - props.angleSpan * t;
            const { x, y } = polarToCartesian(
                center.value.x,
                center.value.y,
                radius.value,
                ang,
            );
            return { ...n, x, y } as ProgressNode;
        });
    }
    return (props.nodes || [])
        .map((n) => {
            // 仅 default 类型参考 max
            const pct =
                props.type === 'even-nodes'
                    ? 0 // even-nodes 下不参考 max，节点均匀分布已处理
                    : Math.min(Math.max(n.value / props.max, 0), 1);
            const ang =
                props.type === 'even-nodes'
                    ? 0 // even-nodes 下节点角度已在上方处理
                    : 180 + props.angleSpan / 2 - props.angleSpan * pct;
            if (props.type === 'even-nodes') return null; // even-nodes 已在上方 return
            const { x, y } = polarToCartesian(
                center.value.x,
                center.value.y,
                radius.value,
                ang,
            );
            return { ...n, x, y } as ProgressNode;
        })
        .filter(Boolean);
});
</script>

<style scoped>
/* SVG容器样式 */
.arc-svg {
    width: 100%;
    height: 100%;
}

/* 轨道和进度条基础样式 */
.arc-track,
.arc-progress {
    stroke-linecap: round;
}
</style>
