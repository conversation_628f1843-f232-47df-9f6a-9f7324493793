import { ref, onUnmounted } from 'vue';

/**
 * 动画组合式函数
 * @param initialValue - 动画初始值
 * @param durationMs - 动画持续时间(毫秒)，默认800ms
 * @returns 返回包含动画值和动画方法的对象
 */
export function useAnimation(initialValue: number, durationMs = 800) {
    // 创建响应式动画值引用
    const animatedValue = ref(initialValue);
    // 动画帧ID，用于取消动画
    let rafId = 0;

    /**
     * 执行动画方法
     * @param to - 目标值
     */
    function animate(to: number) {
        // 取消之前的动画
        cancelAnimationFrame(rafId);
        // 记录动画起始值和开始时间
        const from = animatedValue.value;
        const start = performance.now();

        /**
         * 动画帧回调函数
         * @param now - 当前时间戳
         */
        const tick = (now: number) => {
            // 计算动画进度(0-1)
            const progress = Math.min((now - start) / durationMs, 1);
            // 使用缓动函数计算当前值(三次方缓动效果)
            animatedValue.value =
                from + (to - from) * (1 - Math.pow(1 - progress, 3));
            // 如果动画未完成，继续请求下一帧
            progress < 1 && (rafId = requestAnimationFrame(tick));
        };
        // 启动动画
        rafId = requestAnimationFrame(tick);
    }

    // 组件卸载时取消动画
    onUnmounted(() => cancelAnimationFrame(rafId));

    // 返回动画值和方法
    return { animatedValue, animate };
}
