<template>
    <ProgressCircle
        :value="current"
        :max="100"
        :angle-span="24"
        :radius="900"
        :nodes="nodes"
        :width="414"
        :height="130"
        :progress-width="8"
        progress-gradient-string="linear-gradient(90deg, rgba(255, 223, 191, 0) 21.95%, #FFDFBF 98.4%)"
        track-gradient-string="linear-gradient(90deg, rgba(255, 255, 255, 0) 3.37%, rgba(255, 255, 255, 0.1) 22%, rgba(255, 255, 255, 0.1) 77.89%, rgba(255, 255, 255, 0) 96.52%)"
    >
        <template #node="{ node, isActive }">
            <!-- SVG -->
            <!-- <circle
                r="6"
                :fill="isActive ? '#ff9900' : '#aaa'"
                stroke="#fff"
                stroke-width="2"
            /> -->
            <!-- 图片 -->
            <image
                :href="
                    !isActive
                        ? 'https://p4-live.wskwai.com/kos/nlav12706/dot.png'
                        : 'https://p4-live.wskwai.com/kos/nlav12706/dot2.png'
                "
                x="-8"
                y="-8"
                width="16"
                height="16"
            />
            <!-- 文案描述 -->
            <text
                y="24"
                text-anchor="middle"
                fill="#fff"
                font-size="14"
                font-family="HYYakuHei"
            >
                {{ node.label }}
            </text>
        </template>
    </ProgressCircle>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ProgressCircle from './index.vue';

const current = ref(65);
const nodes: { value: number; label: string }[] = [
    { value: 0, label: '0秒' },
    { value: 20, label: '20秒' },
    { value: 50, label: '50秒' },
    { value: 80, label: '80秒' },
    { value: 100, label: '100秒' },
];
</script>
