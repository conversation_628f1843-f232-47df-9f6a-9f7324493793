
# Progress Circle Component - README

## 组件概述
这是一个基于 Vue 3 的进度圆环组件，用于展示带有动画效果的弧形进度条，支持自定义节点和渐变颜色。

## 功能特性
- ✅ 支持平滑动画过渡效果
- ✅ 可自定义进度节点和标签
- ✅ 支持线性渐变轨道和进度条
- ✅ 响应式设计，自动适应屏幕尺寸
- ✅ 可配置弧线角度、边距等参数

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value | number | 60 | 当前进度值 |
| max | number | 100 | 最大值 |
| angleSpan | number | 60 | 弧线角度跨度(度) |
| radius | number | 计算所得 | 圆弧半径 |
| nodes | ProgressNode[] | [{20}, {40}, {80}] | 进度节点数组 |
| trackGradientString | string | 默认渐变 | 轨道渐变色CSS字符串 |
| progressGradientString | string | 默认渐变 | 进度条渐变色CSS字符串 |
| width | number | 414 | 容器宽度(px) |
| height | number | 200 | 容器高度(px) |
| bottomMargin | number | 40 | 底部边距(px) |
| sideMargin | number | 40 | 左右边距(px) |
| progressWidth | number | 6 | 进度条宽度(px) |

## 使用示例-简单

```vue
<template>
    <ProgressCircle
        :value="75"
        :max="100"
        :nodes="[
            { value: 10, label: '初级' },
            { value: 50, label: '中级' },
            { value: 90, label: '高级' },
        ]"
    />
</template>

<script setup>
import ProgressCircle from './index.vue';
</script>

```

## 使用示例-自定义

```vue
<template>
    <ProgressCircle
        :value="current"
        :max="100"
        :angle-span="24"
        :radius="900"
        :nodes="nodes"
        :width="414"
        :height="130"
        :progress-width="8"
        progress-gradient-string="linear-gradient(90deg, rgba(255, 223, 191, 0) 21.95%, #FFDFBF 98.4%)"
        track-gradient-string="linear-gradient(90deg, rgba(255, 255, 255, 0) 3.37%, rgba(255, 255, 255, 0.1) 22%, rgba(255, 255, 255, 0.1) 77.89%, rgba(255, 255, 255, 0) 96.52%)"
    >
        <template #node="{ node, isActive }">
            <!-- SVG -->
            <!-- <circle
                r="6"
                :fill="isActive ? '#ff9900' : '#aaa'"
                stroke="#fff"
                stroke-width="2"
            /> -->
            <!-- 图片 -->
            <image
                :href="
                    !isActive
                        ? 'https://p4-live.wskwai.com/kos/nlav12706/dot.png'
                        : 'https://p4-live.wskwai.com/kos/nlav12706/dot2.png'
                "
                x="-8"
                y="-8"
                width="16"
                height="16"
            />
            <!-- 文案描述 -->
            <text
                y="24"
                text-anchor="middle"
                fill="#fff"
                font-size="14"
                font-family="HYYakuHei"
            >
                {{ node.label }}
            </text>
        </template>
    </ProgressCircle>
</template>

<script setup>
import ProgressCircle from './index.vue';

const current = ref(65);
const nodes: { value: number; label: string }[] = [
    { value: 0, label: '0秒' },
    { value: 20, label: '20秒' },
    { value: 50, label: '50秒' },
    { value: 80, label: '80秒' },
    { value: 100, label: '100秒' },
];
</script>
```

## 注意事项
1. 组件依赖 `./utils` 和 `./useAnimation` 模块
2. 默认设计基于414px宽度基准，会自动缩放
3. 节点位置会根据进度值自动计算

## 开发说明
组件使用 TypeScript 编写，采用 Composition API 风格，所有计算属性都经过优化以确保性能。