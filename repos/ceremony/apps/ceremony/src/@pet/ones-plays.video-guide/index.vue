<template>
    <div v-if="isShow" class="video-guide">
        <ACard>
            <ACardTitle>{{ title }}</ACardTitle>
            <ACardContent class="video-guide__content">
                <div
                    v-if="leftMaskShow && strategyGuide.length >= 3"
                    class="cover-block left"
                ></div>
                <div
                    v-if="rightMaskShow && strategyGuide.length >= 3"
                    class="cover-block right"
                ></div>
                <div
                    v-pcDirectives:scroll="handleScoll"
                    class="container"
                    @scroll="handleScoll"
                >
                    <div
                        v-for="item in strategyGuide"
                        :key="item.photoId"
                        v-show-log="{
                            action: 'OP_ACTIVITY_AUTHOR_CARD',
                            params: {
                                module: '盛典攻略',
                                author_id: item?.userInfo?.userId,
                                video_id: item?.photoId,
                                id: item?.photoId,
                            },
                        }"
                        v-click-log="{
                            action: 'OP_ACTIVITY_AUTHOR_CARD',
                            params: {
                                module: '盛典攻略',
                                author_id: item?.userInfo?.userId,
                                video_id: item?.photoId,
                                id: item?.photoId,
                            },
                        }"
                        class="poster-item"
                        @click="goDetail(item?.photoId, item?.userInfo?.userId)"
                    >
                        <div
                            class="pic"
                            :style="{ background: getImgStyle(item.imgUrl) }"
                        ></div>
                        <span class="desc a-text-main-o2">
                            <TextLineCut
                                :text="item.name"
                                :max-lines="2"
                            ></TextLineCut>
                        </span>
                    </div>
                    <div class="w-[10px]" style="flex-shrink: 0"></div>
                </div>
            </ACardContent>
        </ACard>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import TextLineCut from '@pet/ones-ui.text-line-cut/index.vue';
import { Toast } from '@lux/sharp-ui-next';
import { ACard, ACardContent, ACardTitle } from '@alive-ui/base';
import {
    bolIsAuthor,
    isYodaPCContainer,
    // nameSlice,
    toWorkVideoPage,
} from '@alive-ui/actions';
import type { ImgItem, StrategyGuideItem } from './schemes';

const leftMaskShow = ref(false);
const rightMaskShow = ref(true);

const props = withDefaults(
    defineProps<{
        title: string;
        strategyGuide: StrategyGuideItem[];
        extraCardKey?: string;
    }>(),
    {
        title: '1111',
        strategyGuide: () => [],
    },
);
const isShow = computed(() => {
    return props.strategyGuide?.length > 0;
});
const getImgStyle = (imgList: ImgItem[]) => {
    const imgUrl = imgList?.[0].url;
    return `#000 url("${imgUrl}") center / 100% no-repeat`;
};
const goDetail = (workId: string, userId: string) => {
    if (isYodaPCContainer) {
        Toast.info('请在快手APP内打开');
    }
    if (bolIsAuthor) {
        // 如果当前是主播端,不跳转
        Toast.info('开播中，不可跳转');
        return;
    }
    toWorkVideoPage(workId, userId);
};
const handleScoll = (e: Event) => {
    const scrollRef = e.target as HTMLElement;
    if (!scrollRef) return;
    if (scrollRef?.scrollLeft <= 10) {
        leftMaskShow.value = false;
    } else if (
        scrollRef?.scrollLeft + scrollRef?.offsetWidth >=
        scrollRef?.scrollWidth
    ) {
        rightMaskShow.value = false;
    } else {
        leftMaskShow.value = true;
        rightMaskShow.value = true;
    }
};
</script>

<style lang="less" scoped>
.video-guide {
    &__content {
        position: relative;
        .cover-block {
            width: 24px;
            height: 100%;
            position: absolute;
            z-index: 1;
            pointer-events: none;
        }
        .left {
            top: 0;
            left: -1px;
            background: linear-gradient(
                90deg,
                #13174a 0%,
                rgba(19, 23, 74, 0) 100%
            );
        }
        .right {
            top: 0;
            right: -1px;
            background: linear-gradient(
                270deg,
                #13174a 0%,
                rgba(19, 23, 74, 0) 100%
            );
        }
        .container {
            display: flex;
            overflow-x: scroll;
            overflow-y: hidden;
            position: relative;
            .poster-item {
                margin-left: 12px;
                box-sizing: border-box;
                width: 120px;
                display: flex;
                flex-direction: column;
                // justify-content: center;
                align-items: flex-start;
                .pic {
                    box-sizing: border-box;
                    width: 120px;
                    height: 160px;
                    border-radius: 8px;
                }
                .desc {
                    margin-top: 4px;
                    width: 120px;
                    // height: 36px;
                    font-size: 12px;
                    line-height: 18px;
                }
            }
            // .poster-item:last-child {
            //     margin-right: 12px;
            // }
        }
    }
}
</style>
