<template>
    <!-- 榜单结算 -->
    <div class="clearing" :class="{ 'absolute-style': isAbsolute }">
        <div class="clearing-icon settlement-icon" />
        <div class="clearing-tip a-text-main">
            {{ data.text }}
            <Countdown
                v-if="data.endTime"
                class="count-text"
                :to="data.endTime"
                :transform="transformTime"
                :immediate-emit="false"
                @end="handleCountDownEnd"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import Countdown from '@pet/ones-ui.countdown/index.vue';
import { msToString } from '@alive-ui/actions';
import { dataFun } from './data';
import type { PropsCommonParams } from '@pet/ones-rank.schema/global';
interface clearData extends PropsCommonParams {
    data?: {
        endTime: number;
        text: string;
    };
    isAbsolute: boolean;
}
const props = withDefaults(defineProps<clearData>(), {
    data: () => {
        return {
            endTime: 0,
            text: '',
        };
    },
    isAbsolute: true,
    useProps: false,
    contextName: Symbol.for('ctx'),
});
console.log('props.useProps', props.contextName);
const data = props.useProps ? ref(props.data) : dataFun(props.contextName);

const emits = defineEmits(['end']);

const transformTime = (t: number) => {
    if (t >= 3600000) {
        return msToString(t, 'HH:mm:ss');
    }

    return msToString(t, 'mm:ss');
};

const handleCountDownEnd = () => {
    emits('end');
};
</script>

<style lang="less" scoped>
.absolute-style {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.clearing {
    min-height: 130px;
    text-align: center;
    min-width: 100px;
    &-icon {
        margin: 0 auto 0;
    }
    &-tip {
        font-size: 12px;
        font-weight: 500;
        line-height: 16px;
        opacity: 0.6;
    }
    .clearing-tip {
        margin-top: 4px;
    }
    .count-text {
        //width: 40px;
        display: inline-block;
    }
}
</style>
