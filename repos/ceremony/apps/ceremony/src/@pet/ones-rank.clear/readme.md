## 榜单结算组件

## 功能描述

该组件用于展示榜单结算信息，包括结算图标、提示文本和倒计时功能。用户可以看到结算的剩余时间，并在倒计时结束时触发相应的事件。

## Props

| 属性名        | 类型                                 | 默认值                     | 描述                             |
| ------------- | ------------------------------------ | -------------------------- | -------------------------------- |
| `data`        | `{ endTime: number; text: string; }` | `{ endTime: 0, text: '' }` | 结算信息，包括结束时间和提示文本 |
| `useProps`    | `boolean`                            | `false`                    | 是否使用 props 传入的数据        |
| `contextName` | `Symbol`                             | `Symbol.for('ctx')`        | 上下文名称，用于数据传递         |

## 使用方式

```vue
<template>
    <Clearing :data="{ endTime: 3600000, text: '结算即将结束' }" :useProps="true" :contextName="Symbol.for('ctx')" @end="handleCountDownEnd" />
</template>

<script setup>
import Clearing from "./path/to/Clearing.vue";

const handleCountDownEnd = () => {
    console.log("倒计时结束");
};
</script>
```

## Mock 数据

以下是一个示例的 mock 数据，用于展示组件的使用：

```javascript
const mockData = {
    endTime: Date.now() + 3600000, // 当前时间加一小时
    text: "结算即将结束",
};
```

## 事件

| 事件名 | 描述               | 参数 |
| ------ | ------------------ | ---- |
| end    | 当倒计时结束时触发 | 无   |
