import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import useKconfStore from '@pet/ones-use.useKconf/index';
import { rankFramework } from '@pet/ones-rank.frame/services/index-home';
import { Toast } from '@lux/sharp-ui-next';
import { query, getPageStatus, Report, isOutLiveRoom } from '@alive-ui/actions';
import useRankStore from './query-rank';
import type {
    TabItem,
    ScheduleViewList,
} from '@pet/ones-rank.schema/index-home';
import type { HomeInfoData } from '@pet/ones-rank.schema/global';
export default defineStore('live-party-home', () => {
    const currentAnchorStageType = ref(Number(query.stageType) || 10);
    const rankAlias = ref('');
    const appointTime = ref(0);
    const kconf = useKconfStore();
    const rankStore = useRankStore();
    const status = ref(getPageStatus('init')); // 页面状态数据定义
    const indexInfoData = ref<HomeInfoData>(); // 初始化接口数据源
    const clickLoadingFlag = ref(false);
    const currentRankId = ref<number>(); // 主播所在赛道的ID
    const kvData = computed(() => {
        return {
            showBack: isOutLiveRoom, // 自定义返回条件
            backAction: 'exit',
            image: kconf.kconfData?.inviteConfigs?.kv ?? '', // todo  kconf 配置， 文字切在图里
            rule: kconf.kconfData?.inviteConfigs?.ruleUrl,
            kvTitle: kconf.kconfData?.inviteConfigs?.kvTitle,
        };
    });
    // 主赛程home相关数据处理（kv、规则、赛程、赛道、）
    const homeData = computed(() => {
        return {
            status: status.value,
            ...indexInfoData.value,
            frameData: {
                kvData: {
                    ...kvData.value,
                    rule: '',
                },
                showRefresh: true,
                homeStatus: status.value,
                rankStatus: rankStore.rankData?.status,
                isClearing: rankStore.rankData.isClearing,
            },
            rankAliasName:
                indexInfoData.value?.anchorDisplayScheduleAndLane
                    ?.rankAliasName || '',
        };
    });
    // 初始化页面，获取接口数据
    const init = async (isTabChange?: boolean) => {
        if (clickLoadingFlag.value) {
            return;
        }
        clickLoadingFlag.value = true;
        if (isTabChange) {
            Toast.loading('正在加载', 0, true);
        } else {
            status.value = getPageStatus('loading');
        }
        try {
            const res = await rankFramework({
                type: currentAnchorStageType.value,
                appointTime: appointTime.value,
                subBiz: 'invite',
            });
            indexInfoData.value = res;
            // eslint-disable-next-line prettier/prettier
            status.value = getPageStatus(
                res?.anchorDisplayScheduleAndLane?.rankId
                    ? 'success'
                    : 'nodata',
            );
            const { rankId, rankAliasName } =
                res?.anchorDisplayScheduleAndLane || {};
            currentRankId.value = rankId; // 主播所在赛道的ID
            currentAnchorStageType.value = res?.anchorStageType;
            rankAlias.value = rankAliasName;
            rankStore.setDyncData({ rankId, bizName: res.queryRankBiz });
        } catch (error) {
            status.value = getPageStatus('error');
            Report.biz.error('线下晚会home接口初始化异常', {
                error,
            });
        } finally {
            Toast.hide();
            clickLoadingFlag.value = false;
        }
    };
    // 刷新整个页面
    const refresh = () => {
        init().finally(async () => {
            await rankStore.init();
        });
    };
    // 赛段切换
    const processChange = (item: ScheduleViewList) => {
        appointTime.value = item.scheduleEndTime;
        init(true).finally(async () => {
            await rankStore.init();
        });
    };
    const changeScheduleViewData = () => {
        if (indexInfoData.value) {
            indexInfoData.value.scheduleViewList =
                indexInfoData.value.scheduleViewList?.map((elem: any) => {
                    return {
                        ...elem,
                        promotionDesc:
                            rankStore.rankData?.extraData
                                ?.scheduleViewNameMap?.[elem?.scheduleType]
                                ?.promotionDesc || elem.promotionDesc,
                    };
                });
        }
    };
    // 榜单tab切换
    const tabChange = (rankId: number, periodId?: number) => {
        rankStore.setDyncData({ rankId, periodId });
        rankStore.init(true).finally(() => {
            changeScheduleViewData();
        });
    };
    return {
        tabChange,
        status,
        homeData,
        indexInfoData,
        processChange,
        init,
        refresh,
    };
});
