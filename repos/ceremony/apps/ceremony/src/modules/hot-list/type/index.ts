export type ItemType = {
    id: number;
    topN: string;
    subTitle: string;
    date: string;
    jumpUrl: string;
    type: number;
};

// 定义 Rank 类型
export type RankType = {
    id: number;
    title: string;
    list: ItemType[]; // list 类型为 ItemType 数组
};

// 定义 allList 的类型
export type AllListType = {
    commonHotRank: RankType;
    entertainmentHotRank: RankType;
};

export type HotRankListPostResponse = {
    commonHotRankList: ItemType[];
    entertainmentHotRankList: ItemType[];
};
