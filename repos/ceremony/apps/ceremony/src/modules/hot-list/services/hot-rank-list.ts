import { request, activityBiz, authorId } from '@alive-ui/actions';
import type { HotRankListPostResponse } from '../type';

// 内容模块主接口：
export const hotRankList = async () => {
    const PATH = '/rest/wd/live/plutus/content/page/yearceremony24/hotRankList';
    const res = await request.post<HotRankListPostResponse>(PATH, {
        biz: activityBiz,
        authorId,
    });

    return res?.data;
};
