{"id": "v1bhpvvr67x", "projectName": "ceremony", "projectChineseName": "直播活动盛典", "template": {"id": 3, "name": "H5-Vue3", "platform": "H5", "framework": "Vue3", "plugins": [{"name": "@gundam/gundam-plugin-yoda", "description": "<PERSON><PERSON>", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5"], "domain": "bridge", "tags": ["容器"], "docs": "yoda", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an"]}, {"name": "@gundam/gundam-plugin-font-min", "description": "字体裁剪", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "font", "tags": ["字体", "性能"], "docs": "font-min", "contributors": ["<PERSON><PERSON>junhe", "<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-mock", "description": "接口Mock", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["mock"], "docs": "mock", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>", "chenzihao03"]}, {"name": "@gundam/gundam-plugin-deploy-staging", "description": "一键部署Staging环境", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "test", "tags": ["部署", "测试"], "docs": "deploy-staging", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "zhong<PERSON>", "ma<PERSON>an"]}, {"name": "@gundam/gundam-plugin-fmp", "description": "自动上报FMP", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "performance", "tags": ["性能", "监控"], "docs": "fmp", "contributors": ["ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-output-filename-normalize", "description": "提供一套默认的build输出配置", "type": "Vite", "framework": ["Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["标准化"], "docs": "output-filename-normalize", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-request", "description": "网络请求", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["请求"], "docs": "request", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an"]}, {"name": "@gundam/gundam-plugin-weblogger", "description": "埋点&监控", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["监控", "稳定性"], "docs": "weblogger", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-led", "description": "自动创建LED项目", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["部署", "测试"], "docs": "led", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "zhong<PERSON>", "ma<PERSON>an"]}, {"name": "@gundam/gundam-plugin-radar", "description": "自动创建雷达项目", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "operation", "tags": ["radar", "监控"], "docs": "radar", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe"]}, {"name": "@gundam/gundam-plugin-radar-seed", "description": "雷达种子包", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "stability", "tags": ["监控", "radar"], "docs": "radar-seed", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>"]}, {"name": "@gundam/gundam-plugin-yoda-seed", "description": "Yoda seed", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5"], "domain": "bridge", "tags": ["yoda", "容器"], "docs": "yoda-seed", "contributors": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-vite-info", "description": "增强Vite的输出", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["效率"], "docs": "", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>"]}, {"name": "@gundam/gundam-plugin-postcss", "description": "自动postcss插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["css"], "docs": "postcss", "contributors": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-css-normalize", "description": "用于统一不同浏览器的默认样式。", "type": "Vite", "framework": ["Vue3"], "platform": ["H5", "PC"], "domain": "compatibility", "tags": ["css", "标准化"], "docs": "css-normalize", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an"]}, {"name": "@gundam/gundam-plugin-sharp-ui-next", "description": "sharp-ui-next的自动按需导入", "type": "Vite", "framework": ["Vue3"], "platform": ["H5"], "domain": "component", "tags": ["UI", "组件"], "docs": "<PERSON><PERSON>-next", "contributors": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-script-error-clarifier", "description": "为Script标签配置crossorigin", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "stability", "tags": ["稳定性"], "docs": "script-error-clarifier", "contributors": ["su<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-hyper", "description": "上传打包产物到性能归因平台", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "performance", "tags": ["性能", "工具"], "docs": "hyper", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-led-debug", "description": "适用于移动端的抓包与Devtools", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["测试", "域名", "工具"], "docs": "", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>"]}, {"name": "@gundam/gundam-plugin-portal", "description": "快捷打开各插件应用页面", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["效率"], "docs": "", "contributors": ["zhong<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an"]}, {"name": "@gundam/gundam-plugin-prefetch-api", "description": "gundam prefetch-api插件", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "performance", "tags": ["性能"], "docs": "prefetch-api", "contributors": ["l<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-cdn", "description": "配置CDN", "type": "Vite", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["部署"], "docs": "cdn", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "zhong<PERSON>"]}, {"name": "@gundam/gundam-plugin-api-generator", "description": "根据 swagger3 接口文档生成 interface 和 service 请求文件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["请求", "效率"], "docs": "api-generator", "contributors": ["chenzihao03", "<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-gitlab", "description": "创建gitlab仓库", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["仓库"], "docs": "gitlab", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe"]}, {"name": "@gundam/gundam-plugin-code-style", "description": "代码规范", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue3", "Vue2"], "platform": ["PC", "H5"], "domain": "standard", "tags": ["规范"], "docs": "code-style", "contributors": ["<PERSON><PERSON>junhe", "<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an"]}, {"name": "@gundam/gundam-plugin-kfx", "description": "创建kfx应用", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "deploy", "tags": ["部署"], "docs": "kfx", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe"]}, {"name": "@gundam/gundam-plugin-kdev", "description": "kdev CD流水线创建插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "standard", "tags": ["CI", "CD", "流水线"], "docs": "kdev", "contributors": ["ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/gundam-plugin-kfc", "description": "创建 kfc 应用插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "standard", "tags": ["规范"], "docs": "kfc", "contributors": ["<PERSON><PERSON>junhe", "ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON>"]}], "type": "Git", "uri": "*************************:mfe/platform/gundam/template-h5-vue3.git", "description": "移动端Vue3的模板"}, "serviceTreeNode": {"name": "live-a", "value": 64830, "path": "/kuaishou/webservice/frontend/mfe/live/activity"}, "plugins": ["@gundam/gundam-plugin-yoda", "@gundam/gundam-plugin-font-min", "@gundam/gundam-plugin-mock", "@gundam/gundam-plugin-deploy-staging", "@gundam/gundam-plugin-fmp", "@gundam/gundam-plugin-output-filename-normalize", "@gundam/gundam-plugin-request", "@gundam/gundam-plugin-weblogger", "@gundam/gundam-plugin-led", "@gundam/gundam-plugin-radar", "@gundam/gundam-plugin-radar-seed", "@gundam/gundam-plugin-yoda-seed", "@gundam/gundam-plugin-vite-info", "@gundam/gundam-plugin-postcss", "@gundam/gundam-plugin-css-normalize", "@gundam/gundam-plugin-sharp-ui-next", "@gundam/gundam-plugin-hyper", "@gundam/gundam-plugin-led-debug", "@gundam/gundam-plugin-portal", "@gundam/gundam-plugin-prefetch-api", "@gundam/gundam-plugin-api-generator", "@gundam/gundam-plugin-gitlab", "@gundam/gundam-plugin-code-style", "@gundam/gundam-plugin-kfx", "@gundam/gundam-plugin-kdev", "@gundam/gundam-plugin-kfc", "@gundam/gundam-plugin-pet-preset", "@gundam/gundam-plugin-pet-vite", "@gundam/gundam-plugin-fallback", "@gundam/gundam-plugin-image-min", "@gundam/gundam-plugin-ones-vite", "@gundam/gundam-plugin-ones", "@gundam/gundam-plugin-kconf-inject", "@gundam/gundam-plugin-ssg", "@gundam/gundam-plugin-ssg-config", "@gundam/gundam-plugin-blank-screen-detector", "@gundam/gundam-plugin-script-error-clarifier", "@gundam/gundam-plugin-vconsole", "@gundam/gundam-plugin-sig3", "@gundam/gundam-plugin-conditional-compilation", "@gundam/gundam-plugin-cdn"], "customConfig": {"@gundam/gundam-plugin-sig3": {"list": ["/rest/wd/live/koi/userAttend/attend"]}, "@gundam/gundam-plugin-font-min": {"fontSrc": "../node_modules/.font-cache", "fontPath": "../font.json", "dest": "../src/assets/font"}, "@gundam/gundam-plugin-weblogger": {"autoCreateWeblog": false}, "@gundam/gundam-plugin-image-min": {"sourcePath": ["../src/assets", "../src/@pet", "../src/components", "../src/modules", "../src/plays", "../src/theme/assets"], "whiteList": [], "compressConfig": {"pngConfig": {"quality": [0.4, 0.8]}, "jpgConfig": {"quality": 0.5}, "sharpConfig": {"quality": 80}, "useSharp": true}}, "@gundam/gundam-plugin-fallback": {"iOSMinVersion": 12, "androidMinVersion": 6, "chromeMinVersion": 70, "disabledModels": "", "fallbackUrl": "https://ppg.viviv.com/doodle/fLGFOwtV.html?uni_src=other_secondary_page&layoutType=4&noBackNavi=true", "fallbackUrlFourTab": "https://ppg.viviv.com/doodle/fLGFOwtV.html?uni_src=activity_tab&layoutType=4&noBackNavi=true"}, "@gundam/gundam-plugin-blank-screen-detector": {"kConfKey": "frontend.activity.summer2025_common"}, "@gundam/gundam-plugin-cdn": {"sdkOptions": {"projectNames": ["live-fe-activity"], "radarSeedEnable": false, "dynamicImportRetryEnable": true, "enableScriptPlaceholderReplace": true, "domains": ["p*-live.a.yximgs.com", "p*-live.ndcj.com", "p*-live.wskwai.com", "p*-live.wsukwai.com", "w*-live.wsukwai.com", "p*-live.wsbkwai.com", "w*-live.wsbkwai.com"]}}, "@gundam/gundam-plugin-kconf-inject": {"kconfKeys": ["frontend.activity.summer2025_party"], "windowKey": "__GUNDAM_KCONF_INJECT_DATA__", "fallbackData": {}}, "@gundam/gundam-plugin-conditional-compilation": {"logToFile": false, "logToConsole": false, "writeEnvToGlobal": true}}, "ledId": "ceremony-xinyangxi", "radarId": "20123bc702", "autoCreateWeblog": false, "gitlabId": 91756, "devCloudProductName": "ceremony", "kfxId": "9608ced9be", "kdevPipeline": [{"name": "staging", "pipelineId": 11326268, "pipelineName": "staging环境流水线"}, {"name": "online", "pipelineId": 11326267, "pipelineName": "prod环境流水线"}, {"name": "prt", "pipelineId": 11326266, "pipelineName": "prt环境流水线"}], "kfcId": "21998"}